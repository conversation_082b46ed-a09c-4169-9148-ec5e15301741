"use client";

import { useEffect, useState, useRef } from "react";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";

const ArchitecturalBackground = () => {
  const [mounted, setMounted] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const backgroundRef = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll();
  
  // Parallax effects for subtle movement
  const gridY = useTransform(scrollY, [0, 300], [0, 30]);
  const smoothGridY = useSpring(gridY, { stiffness: 50, damping: 30 });
  
  useEffect(() => {
    setMounted(true);
    
    const updateDimensions = () => {
      if (backgroundRef.current) {
        setDimensions({
          width: backgroundRef.current.offsetWidth,
          height: backgroundRef.current.offsetHeight
        });
      }
    };
    
    // Initial dimension calculation
    updateDimensions();
    
    // Update dimensions on resize
    window.addEventListener('resize', updateDimensions);
    
    // Check dimensions after a short delay to ensure accurate measurement after DOM updates
    const timeoutId = setTimeout(updateDimensions, 500);
    
    return () => {
      window.removeEventListener('resize', updateDimensions);
      clearTimeout(timeoutId);
    };
  }, []);

  // Generate a more minimal grid that matches hero section
  const generateGrid = () => {
    if (!mounted) return null;
    if (dimensions.width === 0 || dimensions.height === 0) return null;
    
    const gridElements = [];
    
    // Fewer horizontal lines - just enough to provide structure without clutter
    const horizontalLines = 8; 
    for (let y = 0; y <= horizontalLines; y++) {
      const yPos = (y / horizontalLines) * 100;
      
      gridElements.push(
        <div
          key={`h-line-${y}`}
          className="absolute start-0 end-0 h-[1px] bg-white/10"
          style={{ top: `${yPos}%` }}
        />
      );
    }
    
    // Fewer vertical lines
    const verticalLines = dimensions.width < 768 ? 6 : 12;
    for (let x = 0; x <= verticalLines; x++) {
      const xPos = (x / verticalLines) * 100;
      
      gridElements.push(
        <div
          key={`v-line-${x}`}
          className="absolute top-0 bottom-0 w-[1px] bg-white/10"
          style={{ left: `${xPos}%` }}
        />
      );
    }
    
    // Add just a few intersection points at select grid crossings for a cleaner look
    const pointsDensity = dimensions.width < 768 ? 4 : 8;
    for (let x = 0; x <= pointsDensity; x++) {
      for (let y = 0; y <= pointsDensity; y++) {
        if ((x % 2 === 0 && y % 2 === 0) || Math.random() > 0.8) {
          const xPos = (x / pointsDensity) * 100;
          const yPos = (y / pointsDensity) * 100;
          
          gridElements.push(
            <div
              key={`point-${x}-${y}`}
              className="absolute w-1 h-1 rounded-full bg-white/15"
              style={{ 
                left: `calc(${xPos}% - 0.5px)`, 
                top: `calc(${yPos}% - 0.5px)` 
              }}
            />
          );
        }
      }
    }
    
    return gridElements;
  };

  // Generate stars effect to match hero section
  const generateStars = () => {
    if (!mounted) return [];
    
    // Consistent star count based on screen size
    const starCount = dimensions.width < 768 ? 60 : 100;
    
    return Array.from({ length: starCount }).map((_, index) => (
      <div 
        key={`star-${index}`}
        className="absolute rounded-full bg-white"
        style={{
          width: Math.random() * 2 + 0.5 + 'px',
          height: Math.random() * 2 + 0.5 + 'px',
          top: Math.random() * 100 + '%',
          left: Math.random() * 100 + '%',
          opacity: Math.random() * 0.7 + 0.3,
          animation: `twinkle ${Math.floor(Math.random() * 4) + 3}s ease-in-out infinite ${Math.random() * 5}s`
        }}
      />
    ));
  };
  
  return (
    <div 
      ref={backgroundRef}
      className="absolute inset-0 -z-10 overflow-hidden pointer-events-none"
    >
      {/* Background that matches Hero section */}
      <div className="absolute inset-0 bg-[#0A1429] z-0"></div>
      
      {/* Subtle gradient similar to Hero's background */}
      <div className="absolute top-0 end-1/4 w-64 h-64 bg-blue-500/10 rounded-full filter blur-[80px] z-1"></div>
      <div className="absolute bottom-0 start-1/4 w-72 h-72 bg-purple-500/10 rounded-full filter blur-[100px] z-1"></div>
      
      {/* Stars effect */}
      <div className="absolute inset-0 z-1 opacity-70">
        {mounted && generateStars()}
      </div>
      
      {/* Measurement lines and grid with subtle parallax */}
      <motion.div className="absolute inset-0 z-2" style={{ y: mounted ? smoothGridY : 0 }}>
        {generateGrid()}
      </motion.div>
      
      {/* Top blending element that connects with Hero */}
      <div className="absolute top-0 start-0 end-0 h-32 bg-gradient-to-b from-[#0A1429] via-[#0A1429] to-transparent z-3"></div>
      
      {/* Animation Keyframes */}
      <style jsx>{`
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default ArchitecturalBackground; 