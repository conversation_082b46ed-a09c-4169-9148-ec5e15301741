import Link from "next/link";

interface ProjectHeroProps {
  project: any;
  scrollToSection: (sectionId: string) => void;
}

const ProjectHero = ({ project, scrollToSection }: ProjectHeroProps) => {
  return (
    <section className="relative pt-20 pb-2 overflow-visible bg-[#0D1526]">
      {/* Main Image Background with Parallax Effect */}
      <div className="absolute top-0 start-0 w-full h-full max-h-[90vh] bg-black">
        <div className="relative w-full h-full">
          {/* Animated gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-tr from-[#0D1526]/90 via-[#0D1526]/70 to-transparent opacity-80"></div>
          
          {/* In production, replace with actual project image */}
          <div className="w-full h-full bg-[url('/images/project-placeholder.jpg')] bg-cover bg-center">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#0D1526]/50 to-[#0D1526]"></div>
          </div>
          
          {/* Animated accent elements */}
          <div className="absolute top-0 start-0 w-full h-full overflow-hidden">
            <div className="absolute top-10 start-10 w-64 h-64 bg-[#00C2FF]/20 rounded-full filter blur-3xl"></div>
            <div className="absolute bottom-10 end-10 w-80 h-80 bg-[#7B61FF]/20 rounded-full filter blur-3xl"></div>
          </div>
        </div>
      </div>
      
      {/* Content Container */}
      <div className="relative container mx-auto px-4 min-h-[75vh] flex flex-col justify-center z-10 py-20 md:py-28">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-y-10 gap-x-8 w-full">
          {/* Left Content Column */}
          <div className="lg:col-span-7 xl:col-span-8 flex flex-col justify-center">
            {/* Status Badge with subtle animation */}
            <div className="mb-4 md:mb-6 transform hover:scale-105 transition-transform duration-300 inline-block">
              <span className="px-4 py-1.5 md:px-5 md:py-2 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white rounded-full text-xs md:text-sm font-medium shadow-lg shadow-[#7B61FF]/20">
                {project.status}
              </span>
            </div>
            
            {/* Project Title with enhanced typography */}
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 md:mb-8 leading-tight tracking-tight">
              {project.title}
            </h1>
            
            {/* Location with improved icon */}
            <div className="flex items-center text-white/90 mb-4 md:mb-6 group">
              <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-white/10 flex items-center justify-center me-3 md:me-4 group-hover:bg-[#00C2FF]/20 transition-colors duration-300">
                <svg className="h-4 w-4 md:h-5 md:w-5 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <span className="text-base md:text-lg lg:text-xl font-light">{project.locationDetails.address}</span>
            </div>
            
            {/* Description with better typography */}
            <p className="text-white/80 text-base sm:text-lg md:text-xl mb-6 md:mb-10 max-w-2xl font-light leading-relaxed">
              {project.shortSummary}
            </p>
            
            {/* CTA Buttons with hover effects - Updated for compact layout */}
            <div className="flex flex-row gap-2 md:gap-3">
              <button
                onClick={() => scrollToSection("inquiry")}
                className="bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white px-4 md:px-5 py-2 md:py-2.5 rounded-full text-xs md:text-sm font-medium hover:shadow-xl hover:shadow-[#7B61FF]/20 hover:translate-y-[-2px] transition-all duration-300 whitespace-nowrap"
              >
                Request Information
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-4 md:px-5 py-2 md:py-2.5 rounded-full text-xs md:text-sm font-medium hover:bg-white/20 hover:border-white/30 hover:shadow-lg transition-all duration-300 whitespace-nowrap"
              >
                View Gallery
              </button>
            </div>
          </div>
          
          {/* Right Column - Property Details Card */}
          <div className="lg:col-span-5 xl:col-span-4 flex items-center">
            <div className="bg-[#101d31]/80 backdrop-blur-lg border border-white/20 rounded-xl md:rounded-2xl p-5 md:p-8 shadow-2xl hover:shadow-[#00C2FF]/5 transition-all duration-500 w-full">
              <h3 className="text-xl md:text-2xl font-bold text-white mb-4 md:mb-6">Property Details</h3>
              
              <div className="space-y-3 md:space-y-5">
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm md:text-base font-light">Category</span>
                  <span className="text-white text-sm md:text-base font-medium px-3 md:px-4 py-1 bg-white/10 rounded-full">{project.category}</span>
                </div>
                <div className="h-px bg-white/10"></div>
                
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm md:text-base font-light">Status</span>
                  <span className="text-white text-sm md:text-base font-medium px-3 md:px-4 py-1 bg-gradient-to-r from-[#00C2FF]/20 to-[#7B61FF]/20 rounded-full">{project.status}</span>
                </div>
                <div className="h-px bg-white/10"></div>
                
                <div className="flex justify-between items-center">
                  <span className="text-white/70 text-sm md:text-base font-light">Units Available</span>
                  <span className="text-white text-sm md:text-base font-medium">{project.investment.unitsAvailable}</span>
                </div>
                <div className="h-px bg-white/10"></div>
                
                <div>
                  <span className="text-white/70 text-sm md:text-base font-light block mb-2">Property Types</span>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {project.investment.propertyTypes.map((type: string, index: number) => (
                      <span key={index} className="text-white text-xs md:text-sm px-2 md:px-3 py-1 md:py-1.5 rounded-full transition-all duration-300 font-medium bg-white/10 hover:bg-white/20">{type}</span>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 md:mt-8">
                <button
                  onClick={() => scrollToSection("investment")}
                  className="block w-full bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white text-center text-sm md:text-base font-medium py-3 md:py-4 rounded-lg md:rounded-xl hover:shadow-lg hover:shadow-[#7B61FF]/30 transition-all duration-300"
                >
                  View Investment Details
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Scroll indicator - Repositioned and improved */}
      <div className="absolute bottom-6 start-1/2 transform -translate-x-1/2 flex flex-col items-center z-20">
        <svg className="h-5 w-5 md:h-6 md:w-6 text-white/70 animate-bounce" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </section>
  );
};

export default ProjectHero;