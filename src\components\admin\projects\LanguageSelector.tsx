import React from 'react';

interface LanguageSelectorProps {
  language: 'en' | 'ar';
  setLanguage: (language: 'en' | 'ar') => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ language, setLanguage }) => {
  return (
    <div className="mt-6 mb-4">
      <div className="sm:hidden">
        <label htmlFor="language-tabs" className="sr-only">Select a language</label>
        <select
          id="language-tabs"
          name="language-tabs"
          className="block w-full rounded-md border-gray-600 bg-gray-700 text-white focus:border-[#00C2FF] focus:ring-[#00C2FF]"
          value={language}
          onChange={(e) => setLanguage(e.target.value as 'en' | 'ar')}
        >
          <option value="en">English</option>
          <option value="ar">Arabic (العربية)</option>
        </select>
      </div>
      <div className="hidden sm:block">
        <nav className="-mb-px flex space-x-8 border-b border-gray-700" aria-label="Tabs">
          <button
            onClick={() => setLanguage('en')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              language === 'en'
                ? 'border-[#00C2FF] text-[#00C2FF]'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            English
          </button>
          <button
            onClick={() => setLanguage('ar')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              language === 'ar'
                ? 'border-[#00C2FF] text-[#00C2FF]'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            Arabic (العربية)
          </button>
        </nav>
      </div>
    </div>
  );
};

export default LanguageSelector; 