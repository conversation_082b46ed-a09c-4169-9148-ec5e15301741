"use client";

import React, { useState } from 'react';
import { 
  <PERSON>U<PERSON><PERSON><PERSON>ck, <PERSON><PERSON>ock, FiShield, FiWifi, FiA<PERSON>ivity, FiPlus, FiTrash2 
} from 'react-icons/fi';

interface SecuritySettingsData {
  twoFactorAuth: boolean;
  sessionTimeout: number;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireNumbers: boolean;
    requireSymbols: boolean;
  };
  ipWhitelist: string[];
  rateLimiting: {
    enabled: boolean;
    maxRequests: number;
    timeWindow: number;
  };
  bruteForceProtection: {
    enabled: boolean;
    maxAttempts: number;
    lockoutDuration: number;
  };
  sslSettings: {
    enforceHttps: boolean;
    hstsEnabled: boolean;
  };
  auditLogging: {
    enabled: boolean;
    logFailedLogins: boolean;
    logAdminActions: boolean;
    retentionDays: number;
  };
}

interface SecuritySettingsProps {
  data: SecuritySettingsData;
  onChange: (data: SecuritySettingsData) => void;
}

export default function SecuritySettings({ data, onChange }: SecuritySettingsProps) {
  const [newIpAddress, setNewIpAddress] = useState('');

  const updateData = (updates: Partial<SecuritySettingsData>) => {
    onChange({ ...data, ...updates });
  };

  const addIpAddress = () => {
    if (newIpAddress.trim()) {
      updateData({
        ipWhitelist: [...data.ipWhitelist, newIpAddress.trim()]
      });
      setNewIpAddress('');
    }
  };

  const removeIpAddress = (index: number) => {
    updateData({
      ipWhitelist: data.ipWhitelist.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="space-y-6">
      {/* Authentication & Sessions */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiUserCheck className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Authentication & Sessions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Two-Factor Authentication</p>
              <p className="text-gray-400 text-sm">Require 2FA for admin access</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.twoFactorAuth}
                onChange={(e) => updateData({ twoFactorAuth: e.target.checked })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Session Timeout (minutes)</label>
            <input
              type="number"
              value={data.sessionTimeout}
              onChange={(e) => updateData({ sessionTimeout: parseInt(e.target.value) })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              min="5"
              max="480"
            />
          </div>
        </div>
      </div>

      {/* Password Policy */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiLock className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Password Policy
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Minimum Length</label>
            <input
              type="number"
              value={data.passwordPolicy.minLength}
              onChange={(e) => updateData({
                passwordPolicy: { ...data.passwordPolicy, minLength: parseInt(e.target.value) }
              })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              min="6"
              max="32"
            />
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white text-sm">Require Uppercase Letters</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.passwordPolicy.requireUppercase}
                  onChange={(e) => updateData({
                    passwordPolicy: { ...data.passwordPolicy, requireUppercase: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white text-sm">Require Numbers</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.passwordPolicy.requireNumbers}
                  onChange={(e) => updateData({
                    passwordPolicy: { ...data.passwordPolicy, requireNumbers: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-white text-sm">Require Special Characters</span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.passwordPolicy.requireSymbols}
                  onChange={(e) => updateData({
                    passwordPolicy: { ...data.passwordPolicy, requireSymbols: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Brute Force Protection */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiShield className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Brute Force Protection
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Protection</p>
              <p className="text-gray-400 text-sm">Block repeated failed login attempts</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.bruteForceProtection.enabled}
                onChange={(e) => updateData({
                  bruteForceProtection: { ...data.bruteForceProtection, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.bruteForceProtection.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Failed Attempts</label>
                <input
                  type="number"
                  value={data.bruteForceProtection.maxAttempts}
                  onChange={(e) => updateData({
                    bruteForceProtection: { ...data.bruteForceProtection, maxAttempts: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="3"
                  max="20"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Lockout Duration (minutes)</label>
                <input
                  type="number"
                  value={data.bruteForceProtection.lockoutDuration}
                  onChange={(e) => updateData({
                    bruteForceProtection: { ...data.bruteForceProtection, lockoutDuration: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="5"
                  max="1440"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* IP Whitelist */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiWifi className="mr-2 h-5 w-5 text-[#00C2FF]" />
          IP Access Control
        </h3>
        <p className="text-gray-400 text-sm mb-4">Restrict admin access to specific IP addresses. Leave empty to allow all IPs.</p>
        
        <div className="space-y-4">
          <div className="flex space-x-2">
            <input
              type="text"
              value={newIpAddress}
              onChange={(e) => setNewIpAddress(e.target.value)}
              placeholder="Enter IP address (e.g., ***********)"
              className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
            <button
              onClick={addIpAddress}
              className="px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {data.ipWhitelist.map((ip, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-700 p-3 rounded-md">
                <span className="text-white">{ip}</span>
                <button
                  onClick={() => removeIpAddress(index)}
                  className="text-red-400 hover:text-red-300 transition-colors"
                >
                  <FiTrash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
            {data.ipWhitelist.length === 0 && (
              <p className="text-gray-400 text-sm italic">No IP restrictions. Admin access allowed from any IP address.</p>
            )}
          </div>
        </div>
      </div>

      {/* Rate Limiting */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiWifi className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Rate Limiting
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Rate Limiting</p>
              <p className="text-gray-400 text-sm">Limit requests per IP address to prevent abuse</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.rateLimiting.enabled}
                onChange={(e) => updateData({
                  rateLimiting: { ...data.rateLimiting, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.rateLimiting.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Requests</label>
                <input
                  type="number"
                  value={data.rateLimiting.maxRequests}
                  onChange={(e) => updateData({
                    rateLimiting: { ...data.rateLimiting, maxRequests: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="10"
                  max="1000"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Time Window (seconds)</label>
                <input
                  type="number"
                  value={data.rateLimiting.timeWindow}
                  onChange={(e) => updateData({
                    rateLimiting: { ...data.rateLimiting, timeWindow: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="30"
                  max="3600"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* SSL/HTTPS */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiShield className="mr-2 h-5 w-5 text-[#00C2FF]" />
          SSL/HTTPS Settings
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Force HTTPS</p>
              <p className="text-gray-400 text-sm">Redirect all HTTP traffic to HTTPS</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.sslSettings.enforceHttps}
                onChange={(e) => updateData({
                  sslSettings: { ...data.sslSettings, enforceHttps: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable HSTS</p>
              <p className="text-gray-400 text-sm">HTTP Strict Transport Security header</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.sslSettings.hstsEnabled}
                onChange={(e) => updateData({
                  sslSettings: { ...data.sslSettings, hstsEnabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Audit Logging */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiActivity className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Security Logging
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Security Logging</p>
              <p className="text-gray-400 text-sm">Track security-related events</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.auditLogging.enabled}
                onChange={(e) => updateData({
                  auditLogging: { ...data.auditLogging, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.auditLogging.enabled && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-white text-sm">Log Failed Logins</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={data.auditLogging.logFailedLogins}
                      onChange={(e) => updateData({
                        auditLogging: { ...data.auditLogging, logFailedLogins: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                  </label>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-white text-sm">Log Admin Actions</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={data.auditLogging.logAdminActions}
                      onChange={(e) => updateData({
                        auditLogging: { ...data.auditLogging, logAdminActions: e.target.checked }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                  </label>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Log Retention (days)</label>
                <input
                  type="number"
                  value={data.auditLogging.retentionDays}
                  onChange={(e) => updateData({
                    auditLogging: { ...data.auditLogging, retentionDays: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="7"
                  max="365"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 