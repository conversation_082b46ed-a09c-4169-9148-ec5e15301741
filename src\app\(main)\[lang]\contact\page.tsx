"use client";

import ContactForm from "@/components/contact/ContactForm";
import ContactInfo from "@/components/contact/ContactInfo";
import MapLocation from "@/components/contact/MapLocation";
import SocialLinks from "@/components/contact/SocialLinks";
import BusinessHours from "@/components/contact/BusinessHours";
import ContactHero from "@/components/contact/ContactHero";
import ArchitecturalBackground from "@/components/contact/ArchitecturalBackground";

export default function ContactPage() {
  return (
    <div className="flex flex-col space-y-16 pb-16 bg-background">
      <ContactHero />
      
      <section className="relative py-16">
        <div className="container mx-auto px-4">
          <ArchitecturalBackground />
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 relative z-10">
            {/* Main Content - Contact Form */}
            <div className="lg:col-span-2">
              <div className="bg-background/60 backdrop-blur-lg rounded-xl shadow-xl border border-primary/20 p-8 overflow-hidden relative glass-card">
                <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-brand-gradient opacity-15 blur-3xl"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
                
                <h2 className="text-3xl font-bold mb-6 text-primary relative z-10">Get in Touch</h2>
                <p className="text-text-secondary mb-8 relative z-10">
                  Fill out the form below, and a member of our team will get back to you as soon as possible.
                </p>
                <div className="bg-background/50 backdrop-blur-xl rounded-xl p-6 border border-primary/15 shadow-lg glass-card relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
                  <ContactForm />
                </div>
              </div>
            </div>
            
            {/* Sidebar - Contact Information */}
            <div>
              <div className="bg-background/60 backdrop-blur-lg rounded-xl shadow-xl border border-primary/20 p-8 mb-8 hover:border-primary/30 transition-all duration-300 glass-card relative">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
                
                <div className="flex items-center mb-4 relative z-10">
                  <div className="w-8 h-8 rounded-full bg-brand-gradient flex items-center justify-center text-white mr-3 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-primary">Contact Information</h2>
                </div>
                <ContactInfo />
              </div>
              
              <div className="bg-background/60 backdrop-blur-lg rounded-xl shadow-xl border border-primary/20 p-8 mb-8 hover:border-primary/30 transition-all duration-300 glass-card relative">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
                
                <div className="flex items-center mb-4 relative z-10">
                  <div className="w-8 h-8 rounded-full bg-brand-gradient flex items-center justify-center text-white mr-3 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-primary">Business Hours</h2>
                </div>
                <BusinessHours />
              </div>
              
              <div className="bg-background/60 backdrop-blur-lg rounded-xl shadow-xl border border-primary/20 p-8 hover:border-primary/30 transition-all duration-300 glass-card relative">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
                
                <div className="flex items-center mb-4 relative z-10">
                  <div className="w-8 h-8 rounded-full bg-brand-gradient flex items-center justify-center text-white mr-3 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-primary">Connect With Us</h2>
                </div>
                <SocialLinks />
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Map Location */}
      <div className="container mx-auto px-4">
        <div className="flex items-center mb-8">
          <div className="w-10 h-10 rounded-full bg-brand-gradient flex items-center justify-center text-white mr-4 shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h2 className="text-3xl font-bold text-primary">Our Locations</h2>
        </div>
        <div className="rounded-xl overflow-hidden shadow-xl border border-primary/15 glass-card">
          <MapLocation />
        </div>
      </div>
      
      <style jsx global>{`
        .glass-card {
          position: relative;
          isolation: isolate;
          box-shadow: 
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -4px rgba(0, 0, 0, 0.1),
            inset 0 0 0 1px rgba(255, 255, 255, 0.1);
        }
        
        .glass-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 40%;
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.01));
          border-radius: inherit;
          z-index: 1;
          pointer-events: none;
        }
      `}</style>
    </div>
  );
} 