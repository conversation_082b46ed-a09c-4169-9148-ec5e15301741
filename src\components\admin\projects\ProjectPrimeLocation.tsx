import React, { useState, useEffect } from 'react';
import { FiPlus, FiX, FiMove } from 'react-icons/fi';

interface ProjectPrimeLocationProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectPrimeLocation: React.FC<ProjectPrimeLocationProps> = ({ 
  project, 
  language, 
  setProject 
}) => {
  // Get current section info based on selected language
  const currentSectionTitle = project.localizedContent?.[language]?.primeLocationSection?.title || '';
  const currentSectionDescription = project.localizedContent?.[language]?.primeLocationSection?.description || '';
  const currentStrategicTitle = project.localizedContent?.[language]?.primeLocationSection?.strategicTitle || '';
  const currentStrategicDescription = project.localizedContent?.[language]?.primeLocationSection?.strategicDescription || '';
  
  // State for the current language's section info
  const [sectionTitle, setSectionTitle] = useState(currentSectionTitle);
  const [sectionDescription, setSectionDescription] = useState(currentSectionDescription);
  const [strategicTitle, setStrategicTitle] = useState(currentStrategicTitle);
  const [strategicDescription, setStrategicDescription] = useState(currentStrategicDescription);
  
  // State for nearby attractions
  const [newAttraction, setNewAttraction] = useState({ name: '', minutes: '' });
  const [draggedAttraction, setDraggedAttraction] = useState<number | null>(null);
  
  // State for transportation options
  const [newTransportOption, setNewTransportOption] = useState('');
  const [draggedTransportOption, setDraggedTransportOption] = useState<number | null>(null);
  
  // Get current attractions and transportation options
  const currentAttractions = project.localizedContent?.[language]?.primeLocationSection?.nearbyAttractions || [];
  const currentTransportation = project.localizedContent?.[language]?.primeLocationSection?.transportation || [];
  
  // Update the form values when language changes
  useEffect(() => {
    setSectionTitle(project.localizedContent?.[language]?.primeLocationSection?.title || '');
    setSectionDescription(project.localizedContent?.[language]?.primeLocationSection?.description || '');
    setStrategicTitle(project.localizedContent?.[language]?.primeLocationSection?.strategicTitle || '');
    setStrategicDescription(project.localizedContent?.[language]?.primeLocationSection?.strategicDescription || '');
  }, [language, project.localizedContent]);
  
  // Direct handlers for all input fields
  const handleSectionTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setSectionTitle(newTitle);
    
    // Immediately update project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            title: newTitle,
            description: sectionDescription,
            strategicTitle: strategicTitle,
            strategicDescription: strategicDescription
          }
        }
      }
    });
  };
  
  const handleSectionDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setSectionDescription(newDescription);
    
    // Immediately update project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            title: sectionTitle,
            description: newDescription,
            strategicTitle: strategicTitle,
            strategicDescription: strategicDescription
          }
        }
      }
    });
  };
  
  const handleStrategicTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setStrategicTitle(newTitle);
    
    // Immediately update project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            title: sectionTitle,
            description: sectionDescription,
            strategicTitle: newTitle,
            strategicDescription: strategicDescription
          }
        }
      }
    });
  };
  
  const handleStrategicDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setStrategicDescription(newDescription);
    
    // Immediately update project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            title: sectionTitle,
            description: sectionDescription,
            strategicTitle: strategicTitle,
            strategicDescription: newDescription
          }
        }
      }
    });
  };
  
  // Add a new nearby attraction
  const addNearbyAttraction = () => {
    if (!newAttraction.name.trim() || !newAttraction.minutes.trim()) return;
    
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            nearbyAttractions: [
              ...(project.localizedContent?.[language]?.primeLocationSection?.nearbyAttractions || []), 
              newAttraction
            ]
          }
        }
      }
    });
    
    setNewAttraction({ name: '', minutes: '' });
  };
  
  // Add a new transportation option
  const addTransportationOption = () => {
    if (!newTransportOption.trim()) return;
    
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            transportation: [
              ...(project.localizedContent?.[language]?.primeLocationSection?.transportation || []), 
              newTransportOption
            ]
          }
        }
      }
    });
    
    setNewTransportOption('');
  };
  
  // Remove a nearby attraction
  const removeNearbyAttraction = (index: number) => {
    const updatedAttractions = [...(project.localizedContent?.[language]?.primeLocationSection?.nearbyAttractions || [])];
    updatedAttractions.splice(index, 1);
    
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            nearbyAttractions: updatedAttractions
          }
        }
      }
    });
  };
  
  // Remove a transportation option
  const removeTransportationOption = (index: number) => {
    const updatedOptions = [...(project.localizedContent?.[language]?.primeLocationSection?.transportation || [])];
    updatedOptions.splice(index, 1);
    
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            transportation: updatedOptions
          }
        }
      }
    });
  };
  
  // Functions for nearby attractions drag and drop
  const handleAttractionDragStart = (index: number) => {
    setDraggedAttraction(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };

  const handleAttractionDrop = (dropIndex: number) => {
    if (draggedAttraction === null || draggedAttraction === dropIndex) return;
    
    const updatedAttractions = [...(project.localizedContent?.[language]?.primeLocationSection?.nearbyAttractions || [])];
    const draggedItem = updatedAttractions[draggedAttraction];
    
    // Remove the dragged item
    updatedAttractions.splice(draggedAttraction, 1);
    
    // Add it at the new position
    updatedAttractions.splice(dropIndex, 0, draggedItem);
    
    // Update the project state with the new order
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            nearbyAttractions: updatedAttractions
          }
        }
      }
    });
    
    setDraggedAttraction(null);
  };
  
  // Functions for transportation options drag and drop
  const handleTransportationDragStart = (index: number) => {
    setDraggedTransportOption(index);
  };

  const handleTransportationDrop = (dropIndex: number) => {
    if (draggedTransportOption === null || draggedTransportOption === dropIndex) return;
    
    const updatedOptions = [...(project.localizedContent?.[language]?.primeLocationSection?.transportation || [])];
    const draggedItem = updatedOptions[draggedTransportOption];
    
    // Remove the dragged item
    updatedOptions.splice(draggedTransportOption, 1);
    
    // Add it at the new position
    updatedOptions.splice(dropIndex, 0, draggedItem);
    
    // Update the project state with the new order
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          primeLocationSection: {
            ...(project.localizedContent?.[language]?.primeLocationSection || {}),
            transportation: updatedOptions
          }
        }
      }
    });
    
    setDraggedTransportOption(null);
  };

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Prime Location' : 'الموقع المميز'}
      </h2>
      
      {/* Section Title and Description */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Title' : 'عنوان القسم'}
            </label>
            <input
              type="text"
              value={sectionTitle}
              onChange={handleSectionTitleChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Prime Location" : "موقع مميز"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Description' : 'وصف القسم'}
            </label>
            <textarea
              value={sectionDescription}
              onChange={handleSectionDescriptionChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={3}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "Strategically located in the heart of Dubai with easy access to key landmarks and amenities." 
                : "موقع استراتيجي في قلب دبي مع سهولة الوصول إلى المعالم الرئيسية والمرافق."}
            />
          </div>
        </div>
      </div>
      
      {/* Strategic Location Section */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Strategic Location Details' : 'تفاصيل الموقع الاستراتيجي'}
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Strategic Location Title' : 'عنوان الموقع الاستراتيجي'}
            </label>
            <input
              type="text"
              value={strategicTitle}
              onChange={handleStrategicTitleChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "A Strategic Location in Downtown Dubai" : "موقع استراتيجي في وسط مدينة دبي"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Strategic Location Description' : 'وصف الموقع الاستراتيجي'}
            </label>
            <textarea
              value={strategicDescription}
              onChange={handleStrategicDescriptionChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={5}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "Located in the prestigious Downtown Dubai district, Mazaya Heights offers residents unparalleled accessibility to the city's finest attractions and amenities. The strategic location ensures that residents are just minutes away from business hubs, shopping centers, fine dining restaurants, and entertainment venues." 
                : "يقع في منطقة وسط مدينة دبي المرموقة، ويوفر مزايا هايتس للمقيمين إمكانية وصول لا مثيل لها إلى أفضل معالم الجذب والمرافق في المدينة. يضمن الموقع الاستراتيجي أن المقيمين على بعد دقائق فقط من مراكز الأعمال ومراكز التسوق والمطاعم الراقية وأماكن الترفيه."}
            />
          </div>
        </div>
      </div>
      
      {/* Google Maps Link */}
      <div className="mt-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-2">
          {language === 'en' ? 'Google Maps Link' : 'رابط خرائط Google'}
        </h3>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            {language === 'en' ? 'Map Embed Link' : 'رابط تضمين الخريطة'}
          </label>
          <input
            type="text"
            className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
            placeholder="https://www.google.com/maps/embed?pb=!1m18!1m12!..."
            name="mapLink"
            value={project.mapLink || ''}
            onChange={(e) => {
              setProject({
                ...project,
                mapLink: e.target.value
              });
            }}
          />
          <p className="mt-2 text-sm text-gray-400">
            {language === 'en' 
              ? 'To get the embed link: 1) Go to Google Maps 2) Find your location 3) Click "Share" 4) Select "Embed a map" 5) Copy the src URL from the iframe code' 
              : 'للحصول على رابط التضمين: 1) انتقل إلى خرائط Google 2) ابحث عن موقعك 3) انقر على "مشاركة" 4) حدد "تضمين خريطة" 5) انسخ عنوان URL للـ src من كود الـ iframe'}
          </p>
        </div>
      </div>
      
      {/* Nearby Attractions */}
      <div className="mb-6 border-b border-gray-700 pb-6 pt-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Nearby Attractions' : 'المعالم القريبة'}
        </h3>
        
        <div className="space-y-4 mb-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Attraction Name' : 'اسم المعلم'}
              </label>
              <input
                type="text"
                value={newAttraction.name}
                onChange={(e) => setNewAttraction({...newAttraction, name: e.target.value})}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={language === 'en' ? "e.g. Dubai Mall" : "مثال: دبي مول"}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Distance (minutes)' : 'المسافة (دقائق)'}
              </label>
              <input
                type="text"
                value={newAttraction.minutes}
                onChange={(e) => setNewAttraction({...newAttraction, minutes: e.target.value})}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                placeholder="5"
              />
            </div>
          </div>
          
          <button
            type="button"
            onClick={addNearbyAttraction}
            disabled={!newAttraction.name.trim() || !newAttraction.minutes.trim()}
            className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              newAttraction.name.trim() && newAttraction.minutes.trim() 
              ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
              : 'bg-gray-600 cursor-not-allowed'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]`}
          >
            <FiPlus className="-ms-1 me-2 h-4 w-4" />
            {language === 'en' ? 'Add Attraction' : 'إضافة معلم'}
          </button>
        </div>
        
        {currentAttractions.length === 0 ? (
          <p className="text-sm text-gray-400 italic">
            {language === 'en' 
              ? 'No nearby attractions added yet. Add attractions to highlight the convenient location.'
              : 'لم تتم إضافة معالم قريبة بعد. أضف المعالم لإبراز الموقع المناسب.'}
          </p>
        ) : (
          <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
            {currentAttractions.map((attraction: any, index: number) => (
              <div
                key={index}
                className={`flex justify-between items-center bg-gray-700 px-3 py-2 rounded-md ${
                  draggedAttraction === index ? 'opacity-50' : 'opacity-100'
                }`}
                draggable={true}
                onDragStart={() => handleAttractionDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleAttractionDrop(index)}
              >
                <div className="flex items-center space-x-2">
                  <div className="text-[#00C2FF]">
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-300">{attraction.name}</span>
                  <span className="text-sm font-medium text-[#00C2FF]">{attraction.minutes} {language === 'en' ? 'minutes' : 'دقيقة'}</span>
                </div>
                <div className="flex space-x-2">
                  {currentAttractions.length > 1 && (
                    <span className="text-gray-400 cursor-move">
                      <FiMove className="h-4 w-4" />
                    </span>
                  )}
                  <button
                    type="button"
                    onClick={() => removeNearbyAttraction(index)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <FiX className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Transportation Options */}
      <div className="mb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Transportation' : 'وسائل النقل'}
        </h3>
        
        <div className="space-y-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Transportation Option' : 'خيار النقل'}
            </label>
            <input
              type="text"
              value={newTransportOption}
              onChange={(e) => setNewTransportOption(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "e.g. Easy access to Sheikh Zayed Road" : "مثال: سهولة الوصول إلى شارع الشيخ زايد"}
            />
          </div>
          
          <button
            type="button"
            onClick={addTransportationOption}
            disabled={!newTransportOption.trim()}
            className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              newTransportOption.trim() 
              ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
              : 'bg-gray-600 cursor-not-allowed'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]`}
          >
            <FiPlus className="-ml-1 mr-2 h-4 w-4" />
            {language === 'en' ? 'Add Transportation Option' : 'إضافة خيار النقل'}
          </button>
        </div>
        
        {currentTransportation.length === 0 ? (
          <p className="text-sm text-gray-400 italic">
            {language === 'en' 
              ? 'No transportation options added yet. Add options to highlight accessibility.'
              : 'لم تتم إضافة خيارات النقل بعد. أضف الخيارات لإبراز سهولة الوصول.'}
          </p>
        ) : (
          <div className="grid grid-cols-1 gap-2">
            {currentTransportation.map((option: string, index: number) => (
              <div
                key={index}
                className={`flex justify-between items-center bg-gray-700 px-3 py-2 rounded-md ${
                  draggedTransportOption === index ? 'opacity-50' : 'opacity-100'
                }`}
                draggable={true}
                onDragStart={() => handleTransportationDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleTransportationDrop(index)}
              >
                <div className="flex items-center space-x-2">
                  <div className="text-[#00C2FF]">
                    <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <span className="text-sm text-gray-300">{option}</span>
                </div>
                <div className="flex space-x-2">
                  {currentTransportation.length > 1 && (
                    <span className="text-gray-400 cursor-move">
                      <FiMove className="h-4 w-4" />
                    </span>
                  )}
                  <button
                    type="button"
                    onClick={() => removeTransportationOption(index)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <FiX className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectPrimeLocation; 