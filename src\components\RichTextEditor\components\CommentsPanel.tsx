import React, { useState } from 'react';
import { useSlate } from 'slate-react';
import { Editor, Range, Transforms, Text } from 'slate';
import { FiMessageSquare, FiCheckCircle, FiTrash2, FiEdit2 } from 'react-icons/fi';
import { Comment, CommentThread } from '../types';

// Mock comment data (in a real app, this would be fetched from a server)
const mockCommentThreads: CommentThread[] = [
  {
    id: 'thread1',
    comments: [
      {
        id: 'comment1',
        author: '<PERSON>',
        authorId: 'user1',
        content: 'We should revise this section to be more clear.',
        dateCreated: '2023-06-15T14:32:00Z',
        resolved: false,
        reactions: [
          { emoji: '👍', count: 2, users: ['user2', 'user3'] }
        ]
      },
      {
        id: 'comment2',
        author: '<PERSON>',
        authorId: 'user2',
        content: 'Agreed. I suggest mentioning the key benefits more explicitly.',
        dateCreated: '2023-06-15T15:10:00Z',
        resolved: false
      }
    ],
    resolved: false
  },
  {
    id: 'thread2',
    comments: [
      {
        id: 'comment3',
        author: '<PERSON>',
        authorId: 'user3',
        content: 'This data needs to be verified before publishing.',
        dateCreated: '2023-06-16T09:22:00Z',
        resolved: false
      }
    ],
    resolved: false
  }
];

interface CommentsPanelProps {
  isOpen: boolean;
  togglePanel: () => void;
}

const CommentsPanel: React.FC<CommentsPanelProps> = ({ isOpen, togglePanel }) => {
  const editor = useSlate();
  const [commentThreads, setCommentThreads] = useState<CommentThread[]>(mockCommentThreads);
  const [newComment, setNewComment] = useState('');
  const [activeThread, setActiveThread] = useState<string | null>(null);
  const [replyMode, setReplyMode] = useState(false);

  // Add a new comment to the current selection
  const addComment = () => {
    const { selection } = editor;
    
    if (!selection || Range.isCollapsed(selection)) {
      alert('Please select some text to comment on');
      return;
    }
    
    if (!newComment.trim()) {
      alert('Please enter a comment');
      return;
    }
    
    // Create a unique ID for the comment and thread
    const commentId = `comment-${Date.now()}`;
    const threadId = `thread-${Date.now()}`;
    
    // Create a new comment object
    const comment: Comment = {
      id: commentId,
      author: 'Current User', // In a real app, get from auth system
      authorId: 'currentUser',
      content: newComment,
      dateCreated: new Date().toISOString(),
      resolved: false
    };
    
    // Create a new thread with this comment
    const newThread: CommentThread = {
      id: threadId,
      comments: [comment],
      resolved: false
    };
    
    // Add the thread to our state
    setCommentThreads([...commentThreads, newThread]);
    
    // Mark the selected text with the comment thread ID
    // In a real implementation, you would modify the nodes to include commentIds
    Transforms.setNodes(
      editor,
      { commentIds: [threadId] },
      { match: n => Text.isText(n), split: true }
    );
    
    // Reset the new comment input
    setNewComment('');
  };

  // Add a reply to an existing thread
  const addReply = (threadId: string) => {
    if (!newComment.trim()) {
      alert('Please enter a reply');
      return;
    }
    
    // Create a new comment
    const reply: Comment = {
      id: `comment-${Date.now()}`,
      author: 'Current User', // In a real app, get from auth system
      authorId: 'currentUser',
      content: newComment,
      dateCreated: new Date().toISOString(),
      resolved: false
    };
    
    // Update the thread with the new reply
    setCommentThreads(threads => 
      threads.map(thread => 
        thread.id === threadId
          ? { ...thread, comments: [...thread.comments, reply] }
          : thread
      )
    );
    
    // Reset the input and reply mode
    setNewComment('');
    setReplyMode(false);
    setActiveThread(null);
  };

  // Toggle resolved status of a thread
  const toggleResolved = (threadId: string) => {
    setCommentThreads(threads => 
      threads.map(thread => 
        thread.id === threadId
          ? { ...thread, resolved: !thread.resolved }
          : thread
      )
    );
  };

  // Delete a comment thread
  const deleteThread = (threadId: string) => {
    if (confirm('Are you sure you want to delete this comment thread?')) {
      setCommentThreads(threads => 
        threads.filter(thread => thread.id !== threadId)
      );
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isOpen) {
    return (
      <button
        className="p-2 bg-[#1a2349] rounded-full hover:bg-[#141b35] text-white"
        onClick={togglePanel}
        title="Comments"
      >
        <FiMessageSquare size={18} />
        {commentThreads.length > 0 && (
          <span className="absolute -top-1 -right-1 bg-[rgb(var(--color-primary))] text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
            {commentThreads.length}
          </span>
        )}
      </button>
    );
  }

  return (
    <div className="absolute right-0 top-0 w-72 bg-[#141b35] border border-white/10 rounded-lg shadow-lg z-50 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-medium">Comments</h3>
        <button 
          className="text-white/70 hover:text-white"
          onClick={togglePanel}
        >
          ×
        </button>
      </div>
      
      {/* New Comment Form */}
      <div className="mb-4">
        <textarea
          placeholder={replyMode ? "Write a reply..." : "Add a new comment..."}
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="w-full p-2 bg-[#0A0F23] border border-white/10 rounded-lg text-white text-sm min-h-[80px]"
        />
        <div className="flex justify-end mt-2">
          {replyMode && (
            <button
              className="mr-2 text-xs text-white/70 hover:text-white"
              onClick={() => {
                setReplyMode(false);
                setNewComment('');
                setActiveThread(null);
              }}
            >
              Cancel
            </button>
          )}
          <button
            className="text-sm bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white px-3 py-1 rounded-lg"
            onClick={() => {
              if (replyMode && activeThread) {
                addReply(activeThread);
              } else {
                addComment();
              }
            }}
          >
            {replyMode ? "Reply" : "Add Comment"}
          </button>
        </div>
      </div>
      
      {/* Comment Thread List */}
      <div className="max-h-96 overflow-y-auto">
        {commentThreads.length === 0 ? (
          <p className="text-white/50 text-center text-sm py-4">No comments yet</p>
        ) : (
          commentThreads.map(thread => (
            <div 
              key={thread.id} 
              className={`mb-4 p-3 rounded-lg ${thread.resolved ? 'bg-green-900/20' : 'bg-[#0A0F23]'}`}
            >
              {thread.comments.map((comment, index) => (
                <div 
                  key={comment.id} 
                  className={`${index > 0 ? 'mt-3 pt-3 border-t border-white/10' : ''}`}
                >
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-white text-sm font-medium">{comment.author}</span>
                    <span className="text-white/50 text-xs">{formatDate(comment.dateCreated)}</span>
                  </div>
                  <p className="text-white/90 text-sm mb-2">{comment.content}</p>
                  
                  {/* Reactions if any */}
                  {comment.reactions && comment.reactions.length > 0 && (
                    <div className="flex gap-1 mb-2">
                      {comment.reactions.map(reaction => (
                        <span 
                          key={reaction.emoji} 
                          className="text-xs bg-[#1a2349] px-2 py-0.5 rounded-full"
                        >
                          {reaction.emoji} {reaction.count}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              
              {/* Thread actions */}
              <div className="flex justify-between mt-3 pt-2 border-t border-white/10">
                <button
                  className="text-xs text-white/70 hover:text-white flex items-center gap-1"
                  onClick={() => {
                    setReplyMode(true);
                    setActiveThread(thread.id);
                    setNewComment('');
                  }}
                >
                  <FiEdit2 size={12} />
                  Reply
                </button>
                <div className="flex gap-2">
                  <button
                    className={`text-xs ${thread.resolved ? 'text-green-400' : 'text-white/70 hover:text-white'} flex items-center gap-1`}
                    onClick={() => toggleResolved(thread.id)}
                  >
                    <FiCheckCircle size={12} />
                    {thread.resolved ? 'Resolved' : 'Resolve'}
                  </button>
                  <button
                    className="text-xs text-white/70 hover:text-white flex items-center gap-1"
                    onClick={() => deleteThread(thread.id)}
                  >
                    <FiTrash2 size={12} />
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default CommentsPanel; 