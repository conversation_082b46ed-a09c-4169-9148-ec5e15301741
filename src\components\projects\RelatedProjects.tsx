import Link from "next/link";

const RelatedProjects = () => {
  return (
    <section className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Related Projects</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Discover more exceptional properties by Mazaya Capital.</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Placeholders for related projects */}
        {[1, 2, 3].map((item) => (
          <div key={item} className="group">
            <div className="bg-gray-200 h-64 rounded-t-xl overflow-hidden relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-gray-500">Related Project Image {item}</span>
              </div>
              <div className="absolute inset-x-0 bottom-0 h-1/2 bg-gradient-to-t from-black/70 to-transparent"></div>
            </div>
            <div className="bg-white p-6 rounded-b-xl shadow-sm border border-gray-100 border-t-0">
              <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-[#00C2FF] transition-colors duration-300">Mazaya Project {item}</h3>
              <p className="text-gray-600 mb-4">Another exceptional development in Dubai</p>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Residential</span>
                <Link href={`/projects/mazaya-project-${item}`} className="text-[#00C2FF] font-medium text-sm hover:underline">
                  View Details
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default RelatedProjects; 