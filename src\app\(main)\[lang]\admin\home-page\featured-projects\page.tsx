"use client";

import { useState, useEffect } from 'react';
import { FiSave, FiEdit3, FiX, FiEye, FiStar, FiLoader } from 'react-icons/fi';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/contexts/ToastContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { createToast } from '@/utils/toast';
import { authenticatedApiCall } from '@/utils/api';

interface FeaturedProjectsContent {
  id?: number;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  button: {
    text: {
      en: string;
      ar: string;
    };
    link: string;
  };
  created_at?: string;
  updated_at?: string;
  updated_by?: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export default function HomePageFeaturedProjectsManagement() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { showToast } = useToast();
  const { forceUpdateKey } = useLanguage();
  
  const [featuredProjectsContent, setFeaturedProjectsContent] = useState<FeaturedProjectsContent>({
    title: { en: "", ar: "" },
    description: { en: "", ar: "" },
    button: {
      text: { en: "", ar: "" },
      link: ""
    }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [originalContent, setOriginalContent] = useState<FeaturedProjectsContent | null>(null);

  const handleContentChange = (newContent: FeaturedProjectsContent) => {
    setFeaturedProjectsContent(newContent);
    setHasChanges(JSON.stringify(newContent) !== JSON.stringify(originalContent));
  };

  // Fetch featured projects content when authentication is ready
  useEffect(() => {
    // Only fetch when auth is not loading and user is authenticated
    if (!authLoading && isAuthenticated) {
      fetchFeaturedProjectsContent();
    } else if (!authLoading && !isAuthenticated) {
      // Auth is ready but user is not authenticated
      setIsLoading(false);
      showToast(createToast.error(
        'Authentication required',
        'Please log in to access this page'
      ));
    }
  }, [authLoading, isAuthenticated]);

  const fetchFeaturedProjectsContent = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Making authenticated API call to fetch featured projects content...');
      const response = await authenticatedApiCall<FeaturedProjectsContent>('/api/admin/home-page/featured-projects/', {
        method: 'GET'
      });

      console.log('📥 Featured projects content response:', response);

      if (response.success && response.data) {
        setFeaturedProjectsContent(response.data);
        setOriginalContent(response.data);
        setHasChanges(false);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || 'Failed to load featured projects content',
            'Please try refreshing the page'
          ));
        }
      }
    } catch (error) {
      console.error('Error fetching featured projects content:', error);
      showToast(createToast.error(
        'Failed to load featured projects content',
        'Please check your connection and try again'
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAll = async () => {
    console.log('🚀 Save button clicked');
    console.log('📊 Current state:', {
      hasChanges,
      isAuthenticated,
      isSaving
    });
    
    if (!hasChanges) {
      console.log('❌ No changes to save');
      showToast(createToast.info('No changes to save'));
      return;
    }

    // Check authentication before saving
    if (!isAuthenticated) {
      console.log('❌ Not authenticated');
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    console.log('✅ Starting save process...');
    setIsSaving(true);
    try {
      const response = await authenticatedApiCall<FeaturedProjectsContent>('/api/admin/home-page/featured-projects/', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: featuredProjectsContent.title,
          description: featuredProjectsContent.description,
          button: featuredProjectsContent.button
        })
      });

      console.log('📥 Save response:', response);

      if (response.success) {
        showToast(createToast.success(
          response.message || 'Featured projects section updated successfully',
          'All changes have been saved'
        ));
        
        // Update the content with the response data
        if (response.data) {
          setFeaturedProjectsContent(response.data);
          setOriginalContent(response.data);
        }
        setHasChanges(false);
        setEditingSection(null);
      } else {
        // Handle validation errors
        if (response.errors) {
          const errorMessages = Object.entries(response.errors)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          
          showToast(createToast.error(
            'Validation failed',
            errorMessages
          ));
        } else {
          // Check if it's an authentication error
          if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
            showToast(createToast.error(
              'Authentication expired',
              'Please log in again to continue'
            ));
          } else {
            showToast(createToast.error(
              response.message || 'Failed to save changes',
              'Please check your input and try again'
            ));
          }
        }
      }
    } catch (error) {
      console.error('Error saving featured projects content:', error);
      showToast(createToast.error(
        'Failed to save changes',
        'Please check your connection and try again'
      ));
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSave = async (section: string, data: any) => {
    // Check authentication before saving
    if (!isAuthenticated) {
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    try {
      const response = await authenticatedApiCall('/api/admin/home-page/featured-projects/section/', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section,
          data
        })
      });

      if (response.success) {
        showToast(createToast.success(
          response.message || `${section} section updated successfully`
        ));
        
        // Refresh the content to get the latest data
        await fetchFeaturedProjectsContent();
        setEditingSection(null);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || `Failed to update ${section} section`
          ));
        }
      }
    } catch (error) {
      console.error(`Error updating ${section} section:`, error);
      showToast(createToast.error(
        `Failed to update ${section} section`,
        'Please try again'
      ));
    }
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">
            {authLoading ? 'Checking authentication...' : 'Loading featured projects content...'}
          </p>
        </div>
      </div>
    );
  }

  // If auth is ready but user is not authenticated, the useEffect will handle the redirect
  if (!authLoading && !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <p className="text-gray-400">Authentication required. Please log in to continue.</p>
        </div>
      </div>
    );
  }

  return (
    <div key={forceUpdateKey}>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Featured Projects Section</h1>
          <p className="text-gray-400 mt-1">Manage the featured projects section content and call-to-action</p>
          {featuredProjectsContent.updated_at && (
            <p className="text-gray-500 text-sm mt-2">
              Last updated: {new Date(featuredProjectsContent.updated_at).toLocaleString()}
              {featuredProjectsContent.updated_by && (
                <span className="ml-2">
                  by {featuredProjectsContent.updated_by.first_name} {featuredProjectsContent.updated_by.last_name}
                </span>
              )}
            </p>
          )}
        </div>
        <button
          onClick={handleSaveAll}
          disabled={(!hasChanges) || isSaving}
          className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
            (hasChanges) && !isSaving
              ? 'bg-[#00C2FF] text-white hover:bg-[#00C2FF]/90'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isSaving ? (
            <>
              <FiLoader className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
            </>
          )}
        </button>
      </div>

      {hasChanges && (
        <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
          <p className="text-yellow-400 text-sm">
            You have unsaved changes. Don't forget to save your work!
          </p>
        </div>
      )}

      <div className="mt-6 space-y-8">
        {/* Section Title */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiStar className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Title
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'title' ? null : 'title')}
              disabled={isSaving}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 disabled:opacity-50"
            >
              {editingSection === 'title' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'title' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'title' ? (
            <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                    <input
                      type="text"
                      value={featuredProjectsContent.title.en}
                    onChange={(e) => handleContentChange({
                      ...featuredProjectsContent,
                      title: { ...featuredProjectsContent.title, en: e.target.value }
                    })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={100}
                    />
                  <p className="text-xs text-gray-500 mt-1">{featuredProjectsContent.title.en.length}/100 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                    <input
                      type="text"
                      value={featuredProjectsContent.title.ar}
                    onChange={(e) => handleContentChange({
                      ...featuredProjectsContent,
                      title: { ...featuredProjectsContent.title, ar: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right"
                    maxLength={100}
                    dir="rtl"
                  />
                  <p className="text-xs text-gray-500 mt-1">{featuredProjectsContent.title.ar.length}/100 characters</p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('title', featuredProjectsContent.title)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <h2 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600 bg-gray-700 p-3 rounded">
                      {featuredProjectsContent.title.en}
                    </h2>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <h2 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600 bg-gray-700 p-3 rounded text-right" dir="rtl">
                      {featuredProjectsContent.title.ar}
                    </h2>
                  </div>
                </div>
          )}
        </div>

        {/* Section Description */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiStar className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Description
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'description' ? null : 'description')}
              disabled={isSaving}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 disabled:opacity-50"
            >
              {editingSection === 'description' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'description' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'description' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                  <textarea
                    value={featuredProjectsContent.description.en}
                    onChange={(e) => handleContentChange({
                      ...featuredProjectsContent,
                      description: { ...featuredProjectsContent.description, en: e.target.value }
                    })}
                    rows={5}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-1">{featuredProjectsContent.description.en.length}/500 characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                  <textarea
                    value={featuredProjectsContent.description.ar}
                    onChange={(e) => handleContentChange({
                      ...featuredProjectsContent,
                      description: { ...featuredProjectsContent.description, ar: e.target.value }
                    })}
                    rows={5}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right"
                    maxLength={500}
                    dir="rtl"
                  />
                  <p className="text-xs text-gray-500 mt-1">{featuredProjectsContent.description.ar.length}/500 characters</p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('description', featuredProjectsContent.description)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-lg">{featuredProjectsContent.description.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-lg text-right" dir="rtl">{featuredProjectsContent.description.ar}</p>
                  </div>
                </div>
          )}
        </div>

        {/* Call-to-Action Button */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiStar className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Call-to-Action Button
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'button' ? null : 'button')}
              disabled={isSaving}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 disabled:opacity-50"
            >
              {editingSection === 'button' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'button' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'button' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Button Text (English)</label>
                  <input
                    type="text"
                    value={featuredProjectsContent.button.text.en}
                    onChange={(e) => handleContentChange({
                      ...featuredProjectsContent,
                      button: { 
                        ...featuredProjectsContent.button, 
                        text: { ...featuredProjectsContent.button.text, en: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={50}
                  />
                  <p className="text-xs text-gray-500 mt-1">{featuredProjectsContent.button.text.en.length}/50 characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Button Text (Arabic)</label>
                  <input
                    type="text"
                    value={featuredProjectsContent.button.text.ar}
                    onChange={(e) => handleContentChange({
                      ...featuredProjectsContent,
                      button: { 
                        ...featuredProjectsContent.button, 
                        text: { ...featuredProjectsContent.button.text, ar: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right"
                    maxLength={50}
                    dir="rtl"
                  />
                  <p className="text-xs text-gray-500 mt-1">{featuredProjectsContent.button.text.ar.length}/50 characters</p>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Button Link</label>
                <input
                  type="url"
                  value={featuredProjectsContent.button.link}
                  onChange={(e) => handleContentChange({
                    ...featuredProjectsContent,
                    button: { ...featuredProjectsContent.button, link: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  placeholder="https://example.com or /projects"
                />
                <p className="text-xs text-gray-500 mt-1">Enter a full URL or relative path</p>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('button', featuredProjectsContent.button)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-xs text-gray-500 block mb-1">English</span>
                  <button className="px-6 py-3 bg-[#00C2FF] text-white rounded-lg font-medium">
                    {featuredProjectsContent.button.text.en}
                  </button>
                </div>
                <div>
                  <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                  <button className="px-6 py-3 bg-[#00C2FF] text-white rounded-lg font-medium" dir="rtl">
                    {featuredProjectsContent.button.text.ar}
                  </button>
                </div>
              </div>
              <div>
                <span className="text-xs text-gray-500 block mb-1">Link</span>
                <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{featuredProjectsContent.button.link || 'No link set'}</p>
              </div>
            </div>
          )}
        </div>

        {/* Live Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
                {featuredProjectsContent.title.en}
              </h2>
              <p className="text-gray-300 max-w-3xl mx-auto text-lg mb-8">
                {featuredProjectsContent.description.en}
              </p>
              {featuredProjectsContent.button.text.en && (
                <button className="px-8 py-4 bg-[#00C2FF] text-white rounded-lg font-medium text-lg hover:bg-[#00C2FF]/90 transition-colors">
                  {featuredProjectsContent.button.text.en}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 