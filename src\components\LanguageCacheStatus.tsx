"use client";

import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { clearAllLanguageCaches, clearLanguageSpecificCache, clearSessionCache } from '@/utils/cache';
import { FiTrash2, FiRefreshCw, FiDatabase, FiGlobe, FiCheck } from 'react-icons/fi';

interface CacheStats {
  localStorageItems: number;
  sessionStorageItems: number;
  languageSpecificItems: number;
}

const LanguageCacheStatus: React.FC = () => {
  const { locale, forceUpdateKey, isChangingLanguage } = useLanguage();
  const [cacheStats, setCacheStats] = useState<CacheStats>({
    localStorageItems: 0,
    sessionStorageItems: 0,
    languageSpecificItems: 0
  });
  const [isClearing, setIsClearing] = useState(false);
  const [lastCleared, setLastCleared] = useState<string | null>(null);

  // Update cache stats
  const updateCacheStats = () => {
    if (typeof window === 'undefined') return;

    const localStorageCount = Object.keys(localStorage).length;
    const sessionStorageCount = Object.keys(sessionStorage).length;
    
    // Count language-specific items
    const languageSpecificCount = Object.keys(localStorage).filter(key => 
      key.includes('cache') || 
      key.includes('content') || 
      key.includes('data') ||
      key.includes('hero') ||
      key.includes('about') ||
      key.includes('articles') ||
      key.includes('projects') ||
      key.includes('featured')
    ).length;

    setCacheStats({
      localStorageItems: localStorageCount,
      sessionStorageItems: sessionStorageCount,
      languageSpecificItems: languageSpecificCount
    });
  };

  // Update stats when language changes or component mounts
  useEffect(() => {
    updateCacheStats();
    const interval = setInterval(updateCacheStats, 2000); // Update every 2 seconds
    return () => clearInterval(interval);
  }, [locale, forceUpdateKey]);

  // Manual cache clear
  const handleManualClear = async () => {
    setIsClearing(true);
    try {
      await clearAllLanguageCaches();
      setLastCleared(new Date().toLocaleTimeString());
      updateCacheStats();
    } catch (error) {
      console.error('Error clearing cache:', error);
    } finally {
      setIsClearing(false);
    }
  };

  // Clear only localStorage
  const handleClearLocalStorage = () => {
    clearLanguageSpecificCache();
    updateCacheStats();
    setLastCleared(new Date().toLocaleTimeString());
  };

  // Clear only sessionStorage
  const handleClearSessionStorage = () => {
    clearSessionCache();
    updateCacheStats();
    setLastCleared(new Date().toLocaleTimeString());
  };

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-white flex items-center">
          <FiDatabase className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Cache Status
        </h3>
        <div className="flex items-center text-sm text-gray-400">
          <FiGlobe className="mr-1 h-4 w-4" />
          {locale.toUpperCase()}
          {isChangingLanguage && (
            <div className="ml-2 animate-pulse bg-[#00C2FF]/20 text-[#00C2FF] px-2 py-1 rounded text-xs">
              Switching...
            </div>
          )}
        </div>
      </div>

      {/* Cache Statistics */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="bg-gray-700 rounded p-3 text-center">
          <div className="text-2xl font-bold text-white">{cacheStats.localStorageItems}</div>
          <div className="text-xs text-gray-400">localStorage</div>
        </div>
        <div className="bg-gray-700 rounded p-3 text-center">
          <div className="text-2xl font-bold text-white">{cacheStats.sessionStorageItems}</div>
          <div className="text-xs text-gray-400">sessionStorage</div>
        </div>
        <div className="bg-gray-700 rounded p-3 text-center">
          <div className="text-2xl font-bold text-[#00C2FF]">{cacheStats.languageSpecificItems}</div>
          <div className="text-xs text-gray-400">Language Cache</div>
        </div>
      </div>

      {/* Cache Control Buttons */}
      <div className="space-y-2">
        <button
          onClick={handleManualClear}
          disabled={isClearing}
          className={`w-full flex items-center justify-center px-3 py-2 rounded text-sm font-medium transition-colors ${
            isClearing
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-red-600 hover:bg-red-700 text-white'
          }`}
        >
          {isClearing ? (
            <>
              <FiRefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Clearing All Caches...
            </>
          ) : (
            <>
              <FiTrash2 className="mr-2 h-4 w-4" />
              Clear All Language Caches
            </>
          )}
        </button>

        <div className="flex space-x-2">
          <button
            onClick={handleClearLocalStorage}
            className="flex-1 flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm"
          >
            <FiTrash2 className="mr-1 h-3 w-3" />
            Clear Local
          </button>
          <button
            onClick={handleClearSessionStorage}
            className="flex-1 flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm"
          >
            <FiTrash2 className="mr-1 h-3 w-3" />
            Clear Session
          </button>
        </div>
      </div>

      {/* Last Cleared Info */}
      {lastCleared && (
        <div className="mt-3 flex items-center text-xs text-green-400">
          <FiCheck className="mr-1 h-3 w-3" />
          Last cleared: {lastCleared}
        </div>
      )}

      {/* Force Update Key Display */}
      <div className="mt-3 text-xs text-gray-500">
        Force Update Key: {forceUpdateKey.slice(-8)}
      </div>
    </div>
  );
};

export default LanguageCacheStatus; 