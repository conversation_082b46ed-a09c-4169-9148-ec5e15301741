"use client";

import { useState } from 'react';
import { FiSave, FiUpload, FiEye, FiX, FiTrendingUp, FiBookOpen, FiUsers } from 'react-icons/fi';
import Image from 'next/image';

// Interface for hero data
interface ArticlesHeroData {
  badge: {
    english: string;
    arabic: string;
  };
  title: {
    english: string;
    arabic: string;
  };
  subtitle: {
    english: string;
    arabic: string;
  };
  statistics: {
    articles: {
      number: string;
      label: {
        english: string;
        arabic: string;
      };
    };
    categories: {
      number: string;
      label: {
        english: string;
        arabic: string;
      };
    };
    insights: {
      number: string;
      label: {
        english: string;
        arabic: string;
      };
    };
  };
  featuredCard: {
    badge: {
      english: string;
      arabic: string;
    };
    title: {
      english: string;
      arabic: string;
    };
    image: string;
  };
}

export default function ArticlesHeroPage() {
  const [heroData, setHeroData] = useState<ArticlesHeroData>({
    badge: {
      english: "Latest Insights",
      arabic: "أحدث الرؤى"
    },
    title: {
      english: "Articles & Insights",
      arabic: "المقالات والرؤى"
    },
    subtitle: {
      english: "Stay updated with the latest news, industry trends, and expert insights in real estate development and investment.",
      arabic: "ابق على اطلاع مع أحدث الأخبار واتجاهات الصناعة ورؤى الخبراء في تطوير واستثمار العقارات."
    },
    statistics: {
      articles: {
        number: "6+",
        label: {
          english: "Articles",
          arabic: "مقالات"
        }
      },
      categories: {
        number: "5+",
        label: {
          english: "Categories",
          arabic: "فئات"
        }
      },
      insights: {
        number: "12+",
        label: {
          english: "Insights",
          arabic: "رؤى"
        }
      }
    },
    featuredCard: {
      badge: {
        english: "Featured",
        arabic: "مميز"
      },
      title: {
        english: "Real Estate Insights & Expert Analysis",
        arabic: "رؤى العقارات والتحليل المتخصص"
      },
      image: "/images/hero/featured-article.jpg"
    }
  });

  const [showPreview, setShowPreview] = useState(false);

  // Handle input changes
  const handleInputChange = (section: string, field: string, value: string | number | boolean, language?: 'english' | 'arabic', subfield?: string) => {
    setHeroData(prev => {
      const newData = { ...prev };
      
      if (language && subfield) {
        // For nested language objects with subfields
        (newData as any)[section][subfield][language] = value;
      } else if (language) {
        // For language objects
        (newData as any)[section][language] = value;
      } else if (subfield) {
        // For nested non-language objects
        (newData as any)[section][subfield][field] = value;
      } else {
        // For direct fields
        (newData as any)[section][field] = value;
      }
      
      return newData;
    });
  };

  const handleSave = () => {
    // Here you would typically save to your backend
    console.log('Saving hero data:', heroData);
    alert('Articles hero data saved successfully!');
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real application, you would upload this to your server
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setHeroData(prev => ({ 
          ...prev, 
          featuredCard: { ...prev.featuredCard, image: result }
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Articles Hero Section</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage the complete hero section content for the articles page
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
              <div className="bg-white p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Hero Section Preview</h3>
                  <button
                    onClick={() => setShowPreview(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                
                {/* Preview Content - Matching the template structure */}
                <div 
                  className="relative min-h-[500px] rounded-lg overflow-hidden bg-gray-900"
                >
                  <div className="relative z-10 p-8 h-full">
                    <div className="flex flex-col md:flex-row items-center md:items-start justify-between w-full h-full">
                      {/* Left Content */}
                      <div className="max-w-2xl md:flex-1">
                        <div className="inline-block px-4 py-1 rounded-full bg-blue-500/20 backdrop-blur-sm text-blue-400 text-sm font-medium mb-6">
                          {heroData.badge.english}
                        </div>
                        
                        <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
                          <span className="relative">
                            {heroData.title.english}
                            <span 
                              className="absolute bottom-2 left-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500"
                              style={{ width: '100%' }}
                            ></span>
                          </span>
                        </h1>
                        
                        <p className="text-xl leading-relaxed text-white/70 max-w-xl mb-8">
                          {heroData.subtitle.english}
                        </p>
                        
                        <div className="grid grid-cols-3 gap-4 max-w-md">
                          <div className="text-center">
                            <p className="text-3xl font-bold text-blue-500">
                              {heroData.statistics.articles.number}
                            </p>
                            <p className="text-sm text-white/70">{heroData.statistics.articles.label.english}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-3xl font-bold text-blue-500">
                              {heroData.statistics.categories.number}
                            </p>
                            <p className="text-sm text-white/70">{heroData.statistics.categories.label.english}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-3xl font-bold text-blue-500">
                              {heroData.statistics.insights.number}
                            </p>
                            <p className="text-sm text-white/70">{heroData.statistics.insights.label.english}</p>
                          </div>
                        </div>
                      </div>

                      {/* Right Visual Element */}
                      <div className="hidden md:block relative w-80 h-80 lg:w-96 lg:h-96 md:ml-8 mt-12 md:mt-0 md:self-center">
                        <div className="relative w-full h-full overflow-hidden rounded-xl border border-white/10 backdrop-blur-sm">
                          <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
                            {heroData.featuredCard.image ? (
                              <Image
                                src={heroData.featuredCard.image}
                                alt="Featured"
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <FiBookOpen className="w-24 h-24 text-white/10" />
                            )}
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                            <div className="text-xs text-white/60 mb-1">{heroData.featuredCard.badge.english}</div>
                            <h3 className="text-lg font-bold text-white">{heroData.featuredCard.title.english}</h3>
                          </div>
                        </div>
                        
                        <div 
                          className="absolute -top-6 -right-6 w-24 h-24 backdrop-blur-sm rounded-full flex items-center justify-center border bg-blue-500/20 border-blue-500/30"
                        >
                          <FiTrendingUp 
                            className="w-10 h-10 text-blue-500"
                          />
                        </div>
                        <div 
                          className="absolute -bottom-4 -left-4 w-20 h-20 backdrop-blur-sm rounded-lg flex items-center justify-center border rotate-12 bg-purple-500/20 border-purple-500/30"
                        >
                          <FiUsers 
                            className="w-8 h-8 text-purple-500"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Badge Section */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4 flex items-center">
          <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
          Badge Settings
        </h3>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-gray-300">English Text</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={heroData.badge.english}
              onChange={e => handleInputChange('badge', '', e.target.value, 'english')}
              placeholder="Latest Insights"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Arabic Text</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={heroData.badge.arabic}
              onChange={e => handleInputChange('badge', '', e.target.value, 'arabic')}
              placeholder="أحدث الرؤى"
            />
          </div>
        </div>
      </div>

      {/* Title & Subtitle */}
      <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* English Content */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4">English Content</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">Main Title</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.title.english}
                onChange={e => handleInputChange('title', '', e.target.value, 'english')}
                placeholder="Articles & Insights"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300">Subtitle/Description</label>
              <textarea
                rows={4}
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.subtitle.english}
                onChange={e => handleInputChange('subtitle', '', e.target.value, 'english')}
                placeholder="Stay updated with the latest news..."
              />
            </div>
          </div>
        </div>

        {/* Arabic Content */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4">Arabic Content</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">العنوان الرئيسي</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.title.arabic}
                onChange={e => handleInputChange('title', '', e.target.value, 'arabic')}
                placeholder="المقالات والرؤى"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300">العنوان الفرعي/الوصف</label>
              <textarea
                rows={4}
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.subtitle.arabic}
                onChange={e => handleInputChange('subtitle', '', e.target.value, 'arabic')}
                placeholder="ابق على اطلاع مع أحدث الأخبار..."
              />
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Section */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 h-5 w-5 text-blue-500" />
          Statistics Section
        </h3>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Articles Stat */}
          <div className="space-y-3">
            <h4 className="text-md font-medium text-blue-400">Articles</h4>
            <div>
              <label className="block text-sm font-medium text-gray-300">Number</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.articles.number}
                onChange={e => handleInputChange('statistics', 'number', e.target.value, undefined, 'articles')}
                placeholder="6+"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Label (EN)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.articles.label.english}
                onChange={e => handleInputChange('statistics', 'label', e.target.value, 'english', 'articles')}
                placeholder="Articles"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Label (AR)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.articles.label.arabic}
                onChange={e => handleInputChange('statistics', 'label', e.target.value, 'arabic', 'articles')}
                placeholder="مقالات"
              />
            </div>
          </div>

          {/* Categories Stat */}
          <div className="space-y-3">
            <h4 className="text-md font-medium text-blue-400">Categories</h4>
            <div>
              <label className="block text-sm font-medium text-gray-300">Number</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.categories.number}
                onChange={e => handleInputChange('statistics', 'number', e.target.value, undefined, 'categories')}
                placeholder="5+"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Label (EN)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.categories.label.english}
                onChange={e => handleInputChange('statistics', 'label', e.target.value, 'english', 'categories')}
                placeholder="Categories"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Label (AR)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.categories.label.arabic}
                onChange={e => handleInputChange('statistics', 'label', e.target.value, 'arabic', 'categories')}
                placeholder="فئات"
              />
            </div>
          </div>

          {/* Insights Stat */}
          <div className="space-y-3">
            <h4 className="text-md font-medium text-blue-400">Insights</h4>
            <div>
              <label className="block text-sm font-medium text-gray-300">Number</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.insights.number}
                onChange={e => handleInputChange('statistics', 'number', e.target.value, undefined, 'insights')}
                placeholder="12+"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Label (EN)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.insights.label.english}
                onChange={e => handleInputChange('statistics', 'label', e.target.value, 'english', 'insights')}
                placeholder="Insights"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Label (AR)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.statistics.insights.label.arabic}
                onChange={e => handleInputChange('statistics', 'label', e.target.value, 'arabic', 'insights')}
                placeholder="رؤى"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Featured Card Section */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4 flex items-center">
          <FiBookOpen className="mr-2 h-5 w-5 text-purple-500" />
          Featured Card
        </h3>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">Badge Text (EN)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.featuredCard.badge.english}
                onChange={e => handleInputChange('featuredCard', '', e.target.value, 'english', 'badge')}
                placeholder="Featured"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Title (EN)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.featuredCard.title.english}
                onChange={e => handleInputChange('featuredCard', '', e.target.value, 'english', 'title')}
                placeholder="Real Estate Insights & Expert Analysis"
              />
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">Badge Text (AR)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.featuredCard.badge.arabic}
                onChange={e => handleInputChange('featuredCard', '', e.target.value, 'arabic', 'badge')}
                placeholder="مميز"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Title (AR)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.featuredCard.title.arabic}
                onChange={e => handleInputChange('featuredCard', '', e.target.value, 'arabic', 'title')}
                placeholder="رؤى العقارات والتحليل المتخصص"
              />
            </div>
          </div>
        </div>

        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-300">Featured Image</label>
          <div className="mt-1 flex items-center space-x-4">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              id="featured-upload"
            />
            <label
              htmlFor="featured-upload"
              className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
            >
              <FiUpload className="-ml-1 mr-2 h-5 w-5" />
              Upload Image
            </label>
            {heroData.featuredCard.image && (
              <div className="w-16 h-16 rounded overflow-hidden">
                <Image
                  src={heroData.featuredCard.image}
                  alt="Featured card"
                  width={64}
                  height={64}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 