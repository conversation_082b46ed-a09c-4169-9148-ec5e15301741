"use client";

import React, { useState, useMemo } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, FiSearch, FiFilter, <PERSON>Eye, FiEyeOff, FiClock, 
  FiUser, FiMail, FiPhone, FiCalendar, FiMoreVertical,
  FiTrash2, FiStar, FiArchive, FiDownload, FiRefreshCw, FiPlus,
  FiEdit, FiShield, FiUserCheck, FiUserX, FiSettings
} from 'react-icons/fi';
import UserDetailModal from './components/UserDetailModal';
import BulkActionsBar from './components/BulkActionsBar';
import CreateUserModal from './components/CreateUserModal';
import EditUserModal from './components/EditUserModal';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: 'admin' | 'manager' | 'agent' | 'client';
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  avatar?: string;
  department?: string;
  position?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
  permissions: string[];
  notes?: string;
  address?: {
    street: string;
    city: string;
    country: string;
    zipCode: string;
  };
  preferences: {
    language: 'en' | 'ar';
    timezone: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
}

// Mock data for demonstration
const mockUsers: User[] = [
  {
    id: '1',
    firstName: 'Ahmed',
    lastName: 'Al-Rashid',
    email: '<EMAIL>',
    phone: '+971 50 123 4567',
    role: 'admin',
    status: 'active',
    avatar: '/images/avatars/ahmed.jpg',
    department: 'Management',
    position: 'CEO',
    isEmailVerified: true,
    isPhoneVerified: true,
    lastLogin: new Date('2024-01-15T10:30:00'),
    createdAt: new Date('2023-01-01T09:00:00'),
    updatedAt: new Date('2024-01-15T10:30:00'),
    permissions: ['all'],
    notes: 'Company founder and CEO',
    address: {
      street: 'Sheikh Zayed Road',
      city: 'Dubai',
      country: 'UAE',
      zipCode: '12345'
    },
    preferences: {
      language: 'en',
      timezone: 'Asia/Dubai',
      notifications: {
        email: true,
        sms: true,
        push: true
      }
    }
  },
  {
    id: '2',
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+971 55 987 6543',
    role: 'manager',
    status: 'active',
    avatar: '/images/avatars/sarah.jpg',
    department: 'Sales',
    position: 'Sales Manager',
    isEmailVerified: true,
    isPhoneVerified: true,
    lastLogin: new Date('2024-01-14T16:45:00'),
    createdAt: new Date('2023-03-15T09:00:00'),
    updatedAt: new Date('2024-01-14T16:45:00'),
    permissions: ['sales', 'clients', 'reports'],
    notes: 'Experienced sales manager with excellent track record',
    address: {
      street: 'Dubai Marina',
      city: 'Dubai',
      country: 'UAE',
      zipCode: '54321'
    },
    preferences: {
      language: 'en',
      timezone: 'Asia/Dubai',
      notifications: {
        email: true,
        sms: false,
        push: true
      }
    }
  },
  {
    id: '3',
    firstName: 'Mohammed',
    lastName: 'Hassan',
    email: '<EMAIL>',
    phone: '+971 52 456 7890',
    role: 'agent',
    status: 'active',
    avatar: '/images/avatars/mohammed.jpg',
    department: 'Sales',
    position: 'Senior Agent',
    isEmailVerified: true,
    isPhoneVerified: false,
    lastLogin: new Date('2024-01-13T11:30:00'),
    createdAt: new Date('2023-06-01T09:00:00'),
    updatedAt: new Date('2024-01-13T11:30:00'),
    permissions: ['clients', 'properties'],
    notes: 'Specializes in luxury properties',
    address: {
      street: 'Business Bay',
      city: 'Dubai',
      country: 'UAE',
      zipCode: '67890'
    },
    preferences: {
      language: 'ar',
      timezone: 'Asia/Dubai',
      notifications: {
        email: true,
        sms: true,
        push: false
      }
    }
  },
  {
    id: '4',
    firstName: 'Emily',
    lastName: 'Chen',
    email: '<EMAIL>',
    phone: '+971 56 789 0123',
    role: 'client',
    status: 'active',
    department: 'N/A',
    position: 'Client',
    isEmailVerified: true,
    isPhoneVerified: true,
    lastLogin: new Date('2024-01-12T14:20:00'),
    createdAt: new Date('2023-12-01T10:00:00'),
    updatedAt: new Date('2024-01-12T14:20:00'),
    permissions: ['profile'],
    notes: 'VIP client interested in luxury properties',
    address: {
      street: 'Emirates Hills',
      city: 'Dubai',
      country: 'UAE',
      zipCode: '13579'
    },
    preferences: {
      language: 'en',
      timezone: 'Asia/Dubai',
      notifications: {
        email: true,
        sms: false,
        push: true
      }
    }
  },
  {
    id: '5',
    firstName: 'David',
    lastName: 'Wilson',
    email: '<EMAIL>',
    role: 'agent',
    status: 'suspended',
    department: 'Sales',
    position: 'Junior Agent',
    isEmailVerified: false,
    isPhoneVerified: false,
    lastLogin: new Date('2024-01-10T09:15:00'),
    createdAt: new Date('2023-11-15T09:00:00'),
    updatedAt: new Date('2024-01-11T13:15:00'),
    permissions: ['properties'],
    notes: 'Account suspended pending review',
    preferences: {
      language: 'en',
      timezone: 'Asia/Dubai',
      notifications: {
        email: false,
        sms: false,
        push: false
      }
    }
  }
];

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterDepartment, setFilterDepartment] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'name' | 'email' | 'role' | 'lastLogin' | 'createdAt'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [userToEdit, setUserToEdit] = useState<User | null>(null);

  // Filter and search logic
  const filteredUsers = useMemo(() => {
    let filtered = users.filter(user => {
      const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
      const matchesSearch = 
        fullName.includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.phone && user.phone.includes(searchTerm)) ||
        (user.department && user.department.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (user.position && user.position.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesRole = filterRole === 'all' || user.role === filterRole;
      const matchesStatus = filterStatus === 'all' || user.status === filterStatus;
      const matchesDepartment = filterDepartment === 'all' || user.department === filterDepartment;
      
      return matchesSearch && matchesRole && matchesStatus && matchesDepartment;
    });

    // Sort users
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
          break;
        case 'email':
          comparison = a.email.localeCompare(b.email);
          break;
        case 'role':
          comparison = a.role.localeCompare(b.role);
          break;
        case 'lastLogin':
          const aLogin = a.lastLogin?.getTime() || 0;
          const bLogin = b.lastLogin?.getTime() || 0;
          comparison = aLogin - bLogin;
          break;
        case 'createdAt':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [users, searchTerm, filterRole, filterStatus, filterDepartment, sortBy, sortOrder]);

  // Statistics
  const stats = useMemo(() => {
    const total = users.length;
    const active = users.filter(u => u.status === 'active').length;
    const admins = users.filter(u => u.role === 'admin').length;
    const clients = users.filter(u => u.role === 'client').length;
    
    return { total, active, admins, clients };
  }, [users]);

  // Get unique departments for filter
  const departments = useMemo(() => {
    const depts = users
      .map(u => u.department)
      .filter((dept, index, arr) => dept && arr.indexOf(dept) === index)
      .sort();
    return depts;
  }, [users]);

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    setIsDetailModalOpen(true);
  };

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedUser(null);
  };

  const handleCreateUser = (userData: Partial<User>) => {
    const newUser: User = {
      id: Date.now().toString(),
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      email: userData.email || '',
      phone: userData.phone,
      role: userData.role || 'client',
      status: 'pending',
      isEmailVerified: false,
      isPhoneVerified: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      permissions: userData.role === 'admin' ? ['all'] : ['profile'],
      preferences: {
        language: 'en',
        timezone: 'Asia/Dubai',
        notifications: {
          email: true,
          sms: false,
          push: true
        }
      },
      ...userData
    } as User;

    setUsers(prev => [newUser, ...prev]);
    setIsCreateModalOpen(false);
  };

  const handleEditUser = (userData: Partial<User>) => {
    if (!userToEdit) return;
    
    setUsers(prev => prev.map(user => 
      user.id === userToEdit.id 
        ? { ...user, ...userData, updatedAt: new Date() }
        : user
    ));
    setIsEditModalOpen(false);
    setUserToEdit(null);
  };

  const handleDeleteUser = (id: string) => {
    setUsers(prev => prev.filter(user => user.id !== id));
  };

  const handleStatusChange = (id: string, status: User['status']) => {
    setUsers(prev => prev.map(user => 
      user.id === id ? { ...user, status, updatedAt: new Date() } : user
    ));
  };

  // Bulk actions
  const handleSelectUser = (id: string) => {
    setSelectedUsers(prev => 
      prev.includes(id) 
        ? prev.filter(userId => userId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    setSelectedUsers(filteredUsers.map(user => user.id));
  };

  const handleClearSelection = () => {
    setSelectedUsers([]);
  };

  const handleBulkStatusChange = (status: User['status']) => {
    setUsers(prev => prev.map(user => 
      selectedUsers.includes(user.id)
        ? { ...user, status, updatedAt: new Date() }
        : user
    ));
    setSelectedUsers([]);
  };

  const handleBulkDelete = () => {
    setUsers(prev => prev.filter(user => !selectedUsers.includes(user.id)));
    setSelectedUsers([]);
  };

  const handleBulkExport = () => {
    const selectedData = users.filter(user => selectedUsers.includes(user.id));
    const csvContent = [
      ['First Name', 'Last Name', 'Email', 'Phone', 'Role', 'Status', 'Department', 'Position', 'Created At'],
      ...selectedData.map(user => [
        user.firstName,
        user.lastName,
        user.email,
        user.phone || '',
        user.role,
        user.status,
        user.department || '',
        user.position || '',
        user.createdAt.toISOString()
      ])
    ].map(row => row.map(field => `"${field}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    setSelectedUsers([]);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'text-red-400 bg-red-400/10';
      case 'manager': return 'text-blue-400 bg-blue-400/10';
      case 'agent': return 'text-green-400 bg-green-400/10';
      case 'client': return 'text-purple-400 bg-purple-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-400/10';
      case 'inactive': return 'text-gray-400 bg-gray-400/10';
      case 'suspended': return 'text-red-400 bg-red-400/10';
      case 'pending': return 'text-yellow-400 bg-yellow-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center">
              <FiUsers className="mr-3 h-8 w-8 text-[#00C2FF]" />
              User Management
            </h1>
            <p className="text-gray-400 mt-1">Manage system users, roles, and permissions</p>
          </div>
          <div className="flex space-x-3">
            <button 
              onClick={handleBulkExport}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center"
            >
              <FiDownload className="mr-2 h-4 w-4" />
              Export All
            </button>
            <button 
              onClick={() => setIsCreateModalOpen(true)}
              className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors flex items-center"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add User
            </button>
            <button className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center">
              <FiRefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Users</p>
                <p className="text-2xl font-bold text-white">{stats.total}</p>
              </div>
              <FiUsers className="h-8 w-8 text-[#00C2FF]" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Users</p>
                <p className="text-2xl font-bold text-green-400">{stats.active}</p>
              </div>
              <FiUserCheck className="h-8 w-8 text-green-400" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Administrators</p>
                <p className="text-2xl font-bold text-red-400">{stats.admins}</p>
              </div>
              <FiShield className="h-8 w-8 text-red-400" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Clients</p>
                <p className="text-2xl font-bold text-purple-400">{stats.clients}</p>
              </div>
              <FiUser className="h-8 w-8 text-purple-400" />
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <FiSearch className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search users by name, email, phone, department, or position..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center"
            >
              <FiFilter className="mr-2 h-4 w-4" />
              Filters
            </button>
          </div>
          
          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Role</label>
                <select
                  value={filterRole}
                  onChange={(e) => setFilterRole(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="all">All Roles</option>
                  <option value="admin">Admin</option>
                  <option value="manager">Manager</option>
                  <option value="agent">Agent</option>
                  <option value="client">Client</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Status</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Department</label>
                <select
                  value={filterDepartment}
                  onChange={(e) => setFilterDepartment(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="all">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Sort By</label>
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [by, order] = e.target.value.split('-');
                    setSortBy(by as typeof sortBy);
                    setSortOrder(order as 'asc' | 'desc');
                  }}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="createdAt-desc">Newest First</option>
                  <option value="createdAt-asc">Oldest First</option>
                  <option value="name-asc">Name A-Z</option>
                  <option value="name-desc">Name Z-A</option>
                  <option value="email-asc">Email A-Z</option>
                  <option value="email-desc">Email Z-A</option>
                  <option value="role-asc">Role A-Z</option>
                  <option value="lastLogin-desc">Last Login (Recent)</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bulk Actions Bar */}
      <BulkActionsBar
        selectedCount={selectedUsers.length}
        totalCount={filteredUsers.length}
        onActivateAll={() => handleBulkStatusChange('active')}
        onSuspendAll={() => handleBulkStatusChange('suspended')}
        onDeleteAll={handleBulkDelete}
        onExportSelected={handleBulkExport}
        onClearSelection={handleClearSelection}
        onSelectAll={handleSelectAll}
      />

      {/* Users List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">
              Users ({filteredUsers.length})
            </h2>
            {filteredUsers.length > 0 && (
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedUsers.length === filteredUsers.length}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleSelectAll();
                    } else {
                      handleClearSelection();
                    }
                  }}
                  className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-gray-800"
                />
                <span className="text-sm text-gray-400">Select all</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="divide-y divide-gray-700">
          {filteredUsers.length === 0 ? (
            <div className="p-8 text-center">
              <FiUsers className="mx-auto h-12 w-12 text-gray-500 mb-4" />
              <p className="text-gray-400">No users found matching your criteria.</p>
            </div>
          ) : (
            filteredUsers.map((user) => (
              <div
                key={user.id}
                className="p-4 hover:bg-gray-700/50 transition-colors"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex items-center pt-1">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                      onClick={(e) => e.stopPropagation()}
                      className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-gray-800"
                    />
                  </div>
                  
                  <div className="flex-shrink-0">
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={`${user.firstName} ${user.lastName}`}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center">
                        <FiUser className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div 
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => handleUserClick(user)}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-white">
                          {user.firstName} {user.lastName}
                        </span>
                        {!user.isEmailVerified && (
                          <span className="inline-block w-2 h-2 bg-yellow-400 rounded-full" title="Email not verified"></span>
                        )}
                      </div>
                      
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                        {user.role.toUpperCase()}
                      </span>
                      
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                        {user.status.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-gray-400">
                          <FiMail className="mr-2 h-4 w-4" />
                          {user.email}
                        </div>
                        {user.phone && (
                          <div className="flex items-center text-sm text-gray-400">
                            <FiPhone className="mr-2 h-4 w-4" />
                            {user.phone}
                          </div>
                        )}
                        {user.department && (
                          <div className="flex items-center text-sm text-gray-400">
                            <FiSettings className="mr-2 h-4 w-4" />
                            {user.department} - {user.position}
                          </div>
                        )}
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-gray-400">
                          <FiCalendar className="mr-2 h-4 w-4" />
                          Joined: {formatDate(user.createdAt)}
                        </div>
                        {user.lastLogin && (
                          <div className="flex items-center text-sm text-gray-400">
                            <FiClock className="mr-2 h-4 w-4" />
                            Last login: {formatDate(user.lastLogin)}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {user.notes && (
                      <div className="mb-3">
                        <p className="text-gray-400 text-sm line-clamp-2">{user.notes}</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setUserToEdit(user);
                        setIsEditModalOpen(true);
                      }}
                      className="p-2 text-gray-400 hover:text-blue-400 transition-colors"
                      title="Edit user"
                    >
                      <FiEdit className="h-4 w-4" />
                    </button>
                    
                    <div className="relative group">
                      <button 
                        onClick={(e) => e.stopPropagation()}
                        className="p-2 text-gray-400 hover:text-white transition-colors"
                      >
                        <FiMoreVertical className="h-4 w-4" />
                      </button>
                      
                      <div className="absolute right-0 top-8 w-48 bg-gray-700 rounded-lg shadow-lg border border-gray-600 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                        <div className="py-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStatusChange(user.id, user.status === 'active' ? 'inactive' : 'active');
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-600"
                          >
                            {user.status === 'active' ? 'Deactivate' : 'Activate'}
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStatusChange(user.id, user.status === 'suspended' ? 'active' : 'suspended');
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-600"
                          >
                            {user.status === 'suspended' ? 'Unsuspend' : 'Suspend'}
                          </button>
                          <hr className="border-gray-600 my-1" />
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (confirm('Are you sure you want to delete this user?')) {
                                handleDeleteUser(user.id);
                              }
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-600 flex items-center"
                          >
                            <FiTrash2 className="mr-2 h-4 w-4" />
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Modals */}
      <UserDetailModal
        user={selectedUser}
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        onEdit={(user) => {
          setUserToEdit(user);
          setIsEditModalOpen(true);
          setIsDetailModalOpen(false);
        }}
        onDelete={handleDeleteUser}
        onStatusChange={handleStatusChange}
      />

      <CreateUserModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateUser}
      />

      <EditUserModal
        user={userToEdit}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setUserToEdit(null);
        }}
        onSubmit={handleEditUser}
      />
    </div>
  );
} 