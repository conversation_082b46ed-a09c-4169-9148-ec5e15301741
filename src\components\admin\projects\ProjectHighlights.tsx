import React from 'react';

interface ProjectHighlightsProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectHighlights: React.FC<ProjectHighlightsProps> = ({ 
  project, 
  language, 
  setProject 
}) => {
  return (
    <div>
      <h3 className="text-md font-medium text-gray-300 mt-6 mb-4">
        {language === 'en' ? 'Project Highlights' : 'أبرز معالم المشروع'}
      </h3>
        
      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
        <div>
          <label htmlFor="projectHighlights.stories" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Number of Stories' : 'عدد الطوابق'}
          </label>
          <div className="mt-1">
            <input
              type="number"
              name="projectHighlights.stories"
              id="projectHighlights.stories"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={project.projectHighlights.stories || ''}
              onChange={(e) => {
                const value = e.target.value === '' ? 0 : parseInt(e.target.value);
                setProject({
                  ...project,
                  projectHighlights: {
                    ...project.projectHighlights,
                    stories: value
                  }
                });
              }}
              onFocus={(e) => e.target.select()}
              min="0"
              dir="ltr"
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="projectHighlights.luxuryUnits" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Luxury Units' : 'الوحدات الفاخرة'}
          </label>
          <div className="mt-1">
            <input
              type="number"
              name="projectHighlights.luxuryUnits"
              id="projectHighlights.luxuryUnits"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={project.projectHighlights.luxuryUnits || ''}
              onChange={(e) => {
                const value = e.target.value === '' ? 0 : parseInt(e.target.value);
                setProject({
                  ...project,
                  projectHighlights: {
                    ...project.projectHighlights,
                    luxuryUnits: value
                  }
                });
              }}
              onFocus={(e) => e.target.select()}
              min="0"
              dir="ltr"
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="projectHighlights.completionYear" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Completion Year' : 'سنة الإنجاز'}
          </label>
          <div className="mt-1">
            <input
              type="number"
              name="projectHighlights.completionYear"
              id="projectHighlights.completionYear"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={project.projectHighlights.completionYear || ''}
              onChange={(e) => {
                const value = e.target.value === '' ? 0 : parseInt(e.target.value);
                setProject({
                  ...project,
                  projectHighlights: {
                    ...project.projectHighlights,
                    completionYear: value
                  }
                });
              }}
              onFocus={(e) => e.target.select()}
              min="2020"
              max="2050"
              dir="ltr"
            />
          </div>
        </div>
        
        <div>
          <label htmlFor="projectHighlights.avgROI" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Average ROI' : 'متوسط ​​العائد على الاستثمار'}
          </label>
          <div className="mt-1 flex items-center">
            <input
              type="text"
              name="projectHighlights.avgROI"
              id="projectHighlights.avgROI"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={project.projectHighlights.avgROI}
              onChange={(e) => {
                setProject({
                  ...project,
                  projectHighlights: {
                    ...project.projectHighlights,
                    avgROI: e.target.value
                  }
                });
              }}
              placeholder="8%"
              dir="ltr"
            />
          </div>
          <p className="mt-1 text-xs text-gray-400">
            {language === 'en' ? 'Format: 8% (include the % symbol)' : 'الصيغة: 8٪ (يرجى تضمين رمز ٪)'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProjectHighlights; 