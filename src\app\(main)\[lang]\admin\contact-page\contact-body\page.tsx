"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { FiSave, FiEye } from 'react-icons/fi';

// Import components
import FormConfigurationSection from './components/FormConfigurationSection';
import FormFieldsManagementSection from './components/FormFieldsManagementSection';
import ContactInformationSection from './components/ContactInformationSection';
import BusinessHoursSection from './components/BusinessHoursSection';
import SocialLinksSection from './components/SocialLinksSection';
import ContactBodyPreview from './components/ContactBodyPreview';

interface FormField {
  id: string;
  name: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select';
  label: {
    english: string;
    arabic: string;
  };
  placeholder: {
    english: string;
    arabic: string;
  };
  required: boolean;
  enabled: boolean;
  options?: {
    english: string[];
    arabic: string[];
  };
  icon?: string;
}

interface ContactBodyData {
  formSection: {
    title: {
      english: string;
      arabic: string;
    };
    description: {
      english: string;
      arabic: string;
    };
    buttonText: {
      english: string;
      arabic: string;
    };
    privacyPolicy: {
      text: {
        english: string;
        arabic: string;
      };
      links: {
        privacyPolicyUrl: string;
        termsOfServiceUrl: string;
        privacyPolicyText: {
          english: string;
          arabic: string;
        };
        termsOfServiceText: {
          english: string;
          arabic: string;
        };
      };
    };
    fields: FormField[];
  };
  contactInfo: {
    title: {
      english: string;
      arabic: string;
    };
    sections: {
      phone: {
        title: {
          english: string;
          arabic: string;
        };
        numbers: {
          id: string;
          number: string;
          label: {
            english: string;
            arabic: string;
          };
        }[];
      };
      email: {
        title: {
          english: string;
          arabic: string;
        };
        addresses: {
          id: string;
          address: string;
          label: {
            english: string;
            arabic: string;
          };
        }[];
      };
      address: {
        title: {
          english: string;
          arabic: string;
        };
        company: {
          english: string;
          arabic: string;
        };
        lines: {
          id: string;
          line: {
            english: string;
            arabic: string;
          };
        }[];
      };
    };
  };
  businessHours: {
    title: {
      english: string;
      arabic: string;
    };
    schedule: {
      id: string;
      day: {
        english: string;
        arabic: string;
      };
      hours: {
        english: string;
        arabic: string;
      };
    }[];
    holidaySection: {
      title: {
        english: string;
        arabic: string;
      };
      note: {
        english: string;
        arabic: string;
      };
    };
  };
  socialLinks: {
    title: {
      english: string;
      arabic: string;
    };
    description: {
      english: string;
      arabic: string;
    };
    connectOnlineTitle: {
      english: string;
      arabic: string;
    };
    connectOnlineDescription: {
      english: string;
      arabic: string;
    };
    links: {
      id: string;
      platform: string;
      url: string;
      enabled: boolean;
      icon: string;
      ariaLabel: {
        english: string;
        arabic: string;
      };
    }[];
  };
}

export default function ContactBodyPage() {
  const [bodyData, setBodyData] = useState<ContactBodyData>({
    formSection: {
      title: {
        english: "Get in Touch",
        arabic: "تواصل معنا"
      },
      description: {
        english: "Fill out the form below, and a member of our team will get back to you as soon as possible.",
        arabic: "املأ النموذج أدناه، وسيقوم أحد أعضاء فريقنا بالرد عليك في أقرب وقت ممكن."
      },
      buttonText: {
        english: "Send Message",
        arabic: "إرسال الرسالة"
      },
      privacyPolicy: {
        text: {
          english: "By submitting this form, you agree to our",
          arabic: "بإرسال هذا النموذج، فإنك توافق على"
        },
        links: {
          privacyPolicyUrl: "/privacy-policy",
          termsOfServiceUrl: "/terms-of-service",
          privacyPolicyText: {
            english: "Privacy Policy",
            arabic: "سياسة الخصوصية"
          },
          termsOfServiceText: {
            english: "Terms of Service",
            arabic: "شروط الخدمة"
          }
        }
      },
      fields: [
        {
          id: "name",
          name: "name",
          type: "text",
          label: { english: "Full Name", arabic: "الاسم الكامل" },
          placeholder: { english: "Enter your full name", arabic: "أدخل اسمك الكامل" },
          required: true,
          enabled: true,
          icon: "FiUser"
        },
        {
          id: "email",
          name: "email",
          type: "email",
          label: { english: "Email Address", arabic: "عنوان البريد الإلكتروني" },
          placeholder: { english: "Enter your email", arabic: "أدخل بريدك الإلكتروني" },
          required: true,
          enabled: true,
          icon: "FiMail"
        },
        {
          id: "phone",
          name: "phone",
          type: "tel",
          label: { english: "Phone Number", arabic: "رقم الهاتف" },
          placeholder: { english: "Enter your phone number", arabic: "أدخل رقم هاتفك" },
          required: false,
          enabled: true,
          icon: "FiPhone"
        },
        {
          id: "interest",
          name: "interest",
          type: "select",
          label: { english: "I am interested in", arabic: "أنا مهتم بـ" },
          placeholder: { english: "Please select", arabic: "يرجى الاختيار" },
          required: true,
          enabled: true,
          icon: "FiInfo",
          options: {
            english: [
              "Residential Properties",
              "Commercial Properties", 
              "Investment Opportunities",
              "Property Management",
              "Consultation Services"
            ],
            arabic: [
              "العقارات السكنية",
              "العقارات التجارية",
              "فرص الاستثمار",
              "إدارة العقارات",
              "خدمات الاستشارة"
            ]
          }
        },
        {
          id: "subject",
          name: "subject",
          type: "text",
          label: { english: "Subject", arabic: "الموضوع" },
          placeholder: { english: "Enter subject", arabic: "أدخل الموضوع" },
          required: true,
          enabled: true,
          icon: "FiInfo"
        },
        {
          id: "message",
          name: "message",
          type: "textarea",
          label: { english: "Message", arabic: "الرسالة" },
          placeholder: { english: "Enter your message", arabic: "أدخل رسالتك" },
          required: true,
          enabled: true,
          icon: "FiEdit"
        }
      ]
    },
    contactInfo: {
      title: {
        english: "Contact Information",
        arabic: "معلومات التواصل"
      },
      sections: {
        phone: {
          title: {
            english: "Phone",
            arabic: "الهاتف"
          },
          numbers: [
            {
              id: "1",
              number: "+971 12 345 6789",
              label: {
                english: "Main",
                arabic: "الرئيسي"
              }
            },
            {
              id: "2",
              number: "+971 12 345 7890",
              label: {
                english: "Sales",
                arabic: "المبيعات"
              }
            }
          ]
        },
        email: {
          title: {
            english: "Email",
            arabic: "البريد الإلكتروني"
          },
          addresses: [
            {
              id: "1",
              address: "<EMAIL>",
              label: {
                english: "General Info",
                arabic: "معلومات عامة"
              }
            },
            {
              id: "2",
              address: "<EMAIL>",
              label: {
                english: "Sales",
                arabic: "المبيعات"
              }
            }
          ]
        },
        address: {
          title: {
            english: "Address",
            arabic: "العنوان"
          },
          company: {
            english: "Mazaya Capital Headquarters",
            arabic: "مقر مزايا كابيتال"
          },
          lines: [
            {
              id: "1",
              line: {
                english: "123 Business Avenue",
                arabic: "123 شارع الأعمال"
              }
            },
            {
              id: "2",
              line: {
                english: "Downtown Dubai, UAE",
                arabic: "وسط دبي، الإمارات العربية المتحدة"
              }
            }
          ]
        }
      }
    },
    businessHours: {
      title: {
        english: "Business Hours",
        arabic: "ساعات العمل"
      },
      schedule: [
        {
          id: "1",
          day: { english: "Monday - Thursday", arabic: "الاثنين - الخميس" },
          hours: { english: "9:00 AM - 6:00 PM", arabic: "9:00 ص - 6:00 م" },
        },
        {
          id: "2",
          day: { english: "Friday", arabic: "الجمعة" },
          hours: { english: "9:00 AM - 1:00 PM", arabic: "9:00 ص - 1:00 م" },
        },
        {
          id: "3",
          day: { english: "Saturday", arabic: "السبت" },
          hours: { english: "10:00 AM - 4:00 PM", arabic: "10:00 ص - 4:00 م" },
        },
        {
          id: "4",
          day: { english: "Sunday", arabic: "الأحد" },
          hours: { english: "Closed", arabic: "مغلق" },
        }
      ],
      holidaySection: {
        title: {
          english: "Holiday Schedule",
          arabic: "جدول العطل"
        },
        note: {
          english: "Our offices will be closed on all UAE national and religious holidays.",
          arabic: "ستكون مكاتبنا مغلقة في جميع العطل الوطنية والدينية في دولة الإمارات العربية المتحدة."
        }
      }
    },
    socialLinks: {
      title: {
        english: "Connect With Us",
        arabic: "تواصل معنا"
      },
      description: {
        english: "Stay updated with our latest projects and investment opportunities by following us on social media.",
        arabic: "ابق على اطلاع على أحدث مشاريعنا وفرص الاستثمار من خلال متابعتنا على وسائل التواصل الاجتماعي."
      },
      connectOnlineTitle: {
        english: "Connect Online",
        arabic: "تواصل عبر الإنترنت"
      },
      connectOnlineDescription: {
        english: "Connect with us online to stay updated and get the latest news and updates.",
        arabic: "تواصل معنا عبر الإنترنت ليبقى على اطلاع واحصل على آخر الأخبار والتحديثات."
      },
      links: [
        {
          id: "1",
          platform: "Facebook",
          url: "https://facebook.com",
          enabled: true,
          icon: "FaFacebook",
          ariaLabel: {
            english: "Facebook",
            arabic: "فيسبوك"
          }
        },
        {
          id: "2",
          platform: "Twitter",
          url: "https://twitter.com",
          enabled: true,
          icon: "FaTwitter",
          ariaLabel: {
            english: "Twitter",
            arabic: "تويتر"
          }
        },
        {
          id: "3",
          platform: "Instagram",
          url: "https://instagram.com",
          enabled: true,
          icon: "FaInstagram",
          ariaLabel: {
            english: "Instagram",
            arabic: "إنستغرام"
          }
        },
        {
          id: "4",
          platform: "LinkedIn",
          url: "https://linkedin.com",
          enabled: true,
          icon: "FaLinkedin",
          ariaLabel: {
            english: "LinkedIn",
            arabic: "لينكدإن"
          }
        },
        {
          id: "5",
          platform: "YouTube",
          url: "https://youtube.com",
          enabled: true,
          icon: "FaYoutube",
          ariaLabel: {
            english: "YouTube",
            arabic: "يوتيوب"
          }
        }
      ]
    }
  });

  const [showPreview, setShowPreview] = useState(false);
  const lastAddTimeRef = useRef<{[key: string]: number}>({});

  // Debug effect to monitor socialLinks data structure
  useEffect(() => {
    if (!bodyData.socialLinks) {
      console.error('socialLinks is undefined or null:', bodyData.socialLinks);
    } else if (!Array.isArray(bodyData.socialLinks.links)) {
      console.error('socialLinks.links is not an array:', bodyData.socialLinks.links, 'Type:', typeof bodyData.socialLinks.links);
    } else {
      console.log('socialLinks data is valid, links count:', bodyData.socialLinks.links.length);
    }
  }, [bodyData.socialLinks]);

  // Helper function to update nested data using path array
  const updateNestedData = (obj: any, path: string[], value: any): any => {
    if (path.length === 1) {
      const key = path[0];
      // Check if we're updating an array and the key is numeric
      if (Array.isArray(obj) && !isNaN(Number(key))) {
        const newArray = [...obj];
        newArray[Number(key)] = value;
        return newArray;
      }
      return { ...obj, [key]: value };
    }
    
    const [first, ...rest] = path;
    
    // Check if we're dealing with an array and the first path element is numeric
    if (Array.isArray(obj) && !isNaN(Number(first))) {
      const newArray = [...obj];
      newArray[Number(first)] = updateNestedData(obj[Number(first)], rest, value);
      return newArray;
    }
    
    return {
      ...obj,
      [first]: updateNestedData(obj[first], rest, value)
    };
  };

  // Handle form section updates
  const handleFormSectionUpdate = (section: 'formSection', subsection: string, field: string, value: string | boolean, language?: 'english' | 'arabic', subfield?: string) => {
    setBodyData(prev => {
      const newData = { ...prev };
      if (language && subfield) {
        (newData[section] as any)[subsection][field][subfield][language] = value;
      } else if (language) {
        (newData[section] as any)[subsection][field][language] = value;
      } else if (subfield) {
        (newData[section] as any)[subsection][field][subfield] = value;
      } else {
        (newData[section] as any)[subsection][field] = value;
      }
      return newData;
    });
  };

  // Handle form field changes
  const handleFieldChange = (fieldId: string, property: string, value: any, language?: 'english' | 'arabic') => {
    setBodyData(prev => ({
      ...prev,
      formSection: {
        ...prev.formSection,
        fields: prev.formSection.fields.map(field => {
          if (field.id === fieldId) {
            if (language && typeof field[property as keyof FormField] === 'object') {
              return {
                ...field,
                [property]: {
                  ...(field[property as keyof FormField] as any),
                  [language]: value
                }
              };
            } else {
              return {
                ...field,
                [property]: value
              };
            }
          }
          return field;
        })
      }
    }));
  };

  // Handle contact info updates
  const handleContactInfoUpdate = (path: string[], value: string) => {
    setBodyData(prev => ({
      ...prev,
      contactInfo: updateNestedData(prev.contactInfo, path, value)
    }));
  };

  // Handle business hours updates
  const handleBusinessHoursUpdate = (path: string[], value: string | boolean) => {
    setBodyData(prev => ({
      ...prev,
      businessHours: updateNestedData(prev.businessHours, path, value)
    }));
  };

  // Handle social links updates
  const handleSocialLinksUpdate = (path: string[], value: string | boolean) => {
    console.log('handleSocialLinksUpdate called with path:', path, 'value:', value);
    
    setBodyData(prev => {
      // Safety check: ensure socialLinks.links exists and is an array
      if (!prev.socialLinks || !Array.isArray(prev.socialLinks.links)) {
        console.error('socialLinks.links is not an array:', prev.socialLinks);
        return prev;
      }
      
      console.log('Before update - socialLinks.links length:', prev.socialLinks.links.length);
      const updatedSocialLinks = updateNestedData(prev.socialLinks, path, value);
      console.log('After update - socialLinks.links length:', updatedSocialLinks.links?.length);
      
      // Ensure links array is preserved
      if (!Array.isArray(updatedSocialLinks.links)) {
        console.error('Updated socialLinks.links is not an array, preserving original. Updated value:', updatedSocialLinks.links);
        return prev;
      }
      
      return {
        ...prev,
        socialLinks: updatedSocialLinks
      };
    });
  };

  // Handle adding new contact items with timestamp-based duplicate prevention
  const handleAddContactItem = useCallback((section: 'phone' | 'email' | 'address') => {
    const now = Date.now();
    const lastAddTime = lastAddTimeRef.current[section] || 0;
    
    // Prevent additions within 200ms of each other (React Strict Mode protection)
    if (now - lastAddTime < 200) {
      console.log('Prevented rapid successive addition for:', section, 'Time diff:', now - lastAddTime);
      return;
    }
    
    lastAddTimeRef.current[section] = now;
    console.log('Adding item to section:', section);
    
    setBodyData(prev => {
      const newId = now.toString() + Math.random().toString(36).substr(2, 9);
      
      switch (section) {
        case 'phone':
          console.log('Adding phone, current count:', prev.contactInfo.sections.phone.numbers.length);
          const newPhoneData = {
            ...prev,
            contactInfo: {
              ...prev.contactInfo,
              sections: {
                ...prev.contactInfo.sections,
                phone: {
                  ...prev.contactInfo.sections.phone,
                  numbers: [
                    ...prev.contactInfo.sections.phone.numbers,
                    {
                      id: newId,
                      number: "",
                      label: { english: "", arabic: "" }
                    }
                  ]
                }
              }
            }
          };
          console.log('After adding phone, new count:', newPhoneData.contactInfo.sections.phone.numbers.length);
          return newPhoneData;
          
        case 'email':
          console.log('Adding email, current count:', prev.contactInfo.sections.email.addresses.length);
          const newEmailData = {
            ...prev,
            contactInfo: {
              ...prev.contactInfo,
              sections: {
                ...prev.contactInfo.sections,
                email: {
                  ...prev.contactInfo.sections.email,
                  addresses: [
                    ...prev.contactInfo.sections.email.addresses,
                    {
                      id: newId,
                      address: "",
                      label: { english: "", arabic: "" }
                    }
                  ]
                }
              }
            }
          };
          console.log('After adding email, new count:', newEmailData.contactInfo.sections.email.addresses.length);
          return newEmailData;
          
        case 'address':
          console.log('Adding address line, current count:', prev.contactInfo.sections.address.lines.length);
          const newAddressData = {
            ...prev,
            contactInfo: {
              ...prev.contactInfo,
              sections: {
                ...prev.contactInfo.sections,
                address: {
                  ...prev.contactInfo.sections.address,
                  lines: [
                    ...prev.contactInfo.sections.address.lines,
                    {
                      id: newId,
                      line: { english: "", arabic: "" }
                    }
                  ]
                }
              }
            }
          };
          console.log('After adding address line, new count:', newAddressData.contactInfo.sections.address.lines.length);
          return newAddressData;
          
        default:
          return prev;
      }
    });
  }, []);

  // Handle removing contact items
  const handleRemoveContactItem = useCallback((section: 'phone' | 'email' | 'address', id: string) => {
    console.log('Removing item from section:', section, 'with id:', id);
    
    setBodyData(prev => {
      const newData = { ...prev };
      
      switch (section) {
        case 'phone':
          if (newData.contactInfo.sections.phone.numbers.length > 1) {
            newData.contactInfo.sections.phone.numbers = newData.contactInfo.sections.phone.numbers.filter(item => item.id !== id);
          }
          break;
        case 'email':
          if (newData.contactInfo.sections.email.addresses.length > 1) {
            newData.contactInfo.sections.email.addresses = newData.contactInfo.sections.email.addresses.filter(item => item.id !== id);
          }
          break;
        case 'address':
          if (newData.contactInfo.sections.address.lines.length > 1) {
            newData.contactInfo.sections.address.lines = newData.contactInfo.sections.address.lines.filter(item => item.id !== id);
          }
          break;
      }
      
      return newData;
    });
  }, []);

  // Handle adding business hours schedule
  const handleAddSchedule = useCallback(() => {
    const now = Date.now();
    const lastAddTime = lastAddTimeRef.current['schedule'] || 0;
    
    // Prevent additions within 200ms of each other (React Strict Mode protection)
    if (now - lastAddTime < 200) {
      console.log('Prevented rapid successive addition for schedule, Time diff:', now - lastAddTime);
      return;
    }
    
    lastAddTimeRef.current['schedule'] = now;
    console.log('Adding schedule item');
    
    setBodyData(prev => {
      const newId = now.toString() + Math.random().toString(36).substr(2, 9);
      const newScheduleData = {
        ...prev,
        businessHours: {
          ...prev.businessHours,
          schedule: [
            ...prev.businessHours.schedule,
            {
              id: newId,
              day: { english: "", arabic: "" },
              hours: { english: "", arabic: "" }
            }
          ]
        }
      };
      console.log('After adding schedule, new count:', newScheduleData.businessHours.schedule.length);
      return newScheduleData;
    });
  }, []);

  // Handle removing business hours schedule
  const handleRemoveSchedule = useCallback((id: string) => {
    console.log('Removing schedule item with id:', id);
    
    setBodyData(prev => {
      if (prev.businessHours.schedule.length > 1) {
        return {
          ...prev,
          businessHours: {
            ...prev.businessHours,
            schedule: prev.businessHours.schedule.filter(item => item.id !== id)
          }
        };
      }
      return prev;
    });
  }, []);

  // Handle adding social media platform
  const handleAddSocialPlatform = useCallback(() => {
    const now = Date.now();
    const lastAddTime = lastAddTimeRef.current['social'] || 0;
    
    // Prevent additions within 200ms of each other (React Strict Mode protection)
    if (now - lastAddTime < 200) {
      console.log('Prevented rapid successive addition for social platform, Time diff:', now - lastAddTime);
      return;
    }
    
    lastAddTimeRef.current['social'] = now;
    console.log('Adding social platform');
    
    setBodyData(prev => {
      // Safety check: ensure socialLinks.links exists and is an array
      if (!prev.socialLinks || !Array.isArray(prev.socialLinks.links)) {
        console.error('socialLinks.links is not an array when adding platform:', prev.socialLinks);
        return prev;
      }
      
      const newId = now.toString() + Math.random().toString(36).substr(2, 9);
      const newSocialData = {
        ...prev,
        socialLinks: {
          ...prev.socialLinks,
          links: [
            ...prev.socialLinks.links,
            {
              id: newId,
              platform: "",
              url: "",
              enabled: true,
              icon: "",
              ariaLabel: {
                english: "",
                arabic: ""
              }
            }
          ]
        }
      };
      console.log('After adding social platform, new count:', newSocialData.socialLinks.links.length);
      return newSocialData;
    });
  }, []);

  // Handle removing social media platform
  const handleRemoveSocialPlatform = useCallback((id: string) => {
    console.log('Removing social platform with id:', id);
    
    setBodyData(prev => {
      // Safety check: ensure socialLinks.links exists and is an array
      if (!prev.socialLinks || !Array.isArray(prev.socialLinks.links)) {
        console.error('socialLinks.links is not an array when removing platform:', prev.socialLinks);
        return prev;
      }
      
      if (prev.socialLinks.links.length > 1) {
        return {
          ...prev,
          socialLinks: {
            ...prev.socialLinks,
            links: prev.socialLinks.links.filter(item => item.id !== id)
          }
        };
      }
      return prev;
    });
  }, []);

  // Handle save
  const handleSave = () => {
    console.log('Saving contact body data:', bodyData);
    alert('Contact body content saved successfully!');
  };

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Contact Body Section</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage the complete contact page layout, form fields, contact information, business hours, and social links
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Enhanced Preview Section */}
      {showPreview && (
        <ContactBodyPreview bodyData={bodyData} />
      )}

      <div className="mt-6 space-y-6">
        {/* Form Configuration Section */}
        <FormConfigurationSection 
          formSection={bodyData.formSection}
          onUpdate={handleFormSectionUpdate}
        />

        {/* Form Fields Management */}
        <FormFieldsManagementSection 
          fields={bodyData.formSection.fields}
          onFieldChange={handleFieldChange}
        />

        {/* Contact Information */}
        <ContactInformationSection 
          contactInfo={bodyData.contactInfo}
          onUpdate={handleContactInfoUpdate}
          onAddItem={handleAddContactItem}
          onRemoveItem={handleRemoveContactItem}
        />

        {/* Business Hours */}
        <BusinessHoursSection 
          businessHours={bodyData.businessHours}
          onUpdate={handleBusinessHoursUpdate}
          onAddSchedule={handleAddSchedule}
          onRemoveSchedule={handleRemoveSchedule}
        />

        {/* Social Links */}
        {bodyData.socialLinks && Array.isArray(bodyData.socialLinks.links) ? (
          <SocialLinksSection 
            socialLinks={bodyData.socialLinks}
            onUpdate={handleSocialLinksUpdate}
            onAddPlatform={handleAddSocialPlatform}
            onRemovePlatform={handleRemoveSocialPlatform}
          />
        ) : (
          <div className="bg-red-800 shadow rounded-lg p-6 border border-red-700">
            <h3 className="text-lg font-medium text-white mb-4">Social Links Section - Data Error</h3>
            <p className="text-red-300">There is an issue with the social links data structure. Please refresh the page.</p>
            <button 
              onClick={() => window.location.reload()} 
              className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Refresh Page
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 