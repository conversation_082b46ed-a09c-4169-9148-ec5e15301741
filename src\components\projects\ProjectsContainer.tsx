"use client";

import dynamic from "next/dynamic";
import { ReactNode } from "react";

// Dynamically import the AnimatedBackground component with no SSR
const AnimatedBackground = dynamic(
  () => import("@/components/projects/AnimatedBackground"),
  { ssr: false }
);

interface ProjectsContainerProps {
  children: ReactNode;
}

const ProjectsContainer = ({ children }: ProjectsContainerProps) => {
  return (
    <div className="relative py-16 overflow-hidden">
      {/* Animated background with lights */}
      <AnimatedBackground />
      
      {/* Projects grid container */}
      <section className="container mx-auto px-4 relative z-10">
        <div className="mb-8 pb-8 border-b border-[rgb(var(--color-text))]/10">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-4">
            <span className="brand-gradient-text">Premium</span> Developments
          </h2>
          <p className="text-center text-[rgb(var(--color-text))]/70 max-w-2xl mx-auto">
            Explore our exclusive collection of luxury properties, featuring innovative designs 
            and premium locations across the UAE.
          </p>
        </div>
        
        {children}
      </section>
    </div>
  );
};

export default ProjectsContainer; 