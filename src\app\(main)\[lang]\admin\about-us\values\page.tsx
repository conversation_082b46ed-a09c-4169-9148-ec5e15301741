"use client";

import React, { useState } from 'react';
import { 
  FaHandshake, 
  FaStar, 
  FaLightbulb, 
  FaLeaf, 
  FaUsers, 
  FaPeopleCarry,
  FaShieldAlt,
  FaHeart,
  FaBullseye,
  FaTrophy,
  FaEye,
  FaSmile,
  FaChartLine,
  FaGlobe,
  FaLock,
  FaGem,
  FaRocket,
  FaBalanceScale
} from "react-icons/fa";
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiMove, FiArrowUp, FiArrowDown } from 'react-icons/fi';

// Available icons for value items
const availableIcons = [
  { name: "handshake", icon: FaHandshake, label: "Handshake" },
  { name: "star", icon: FaStar, label: "Star" },
  { name: "lightbulb", icon: FaLightbulb, label: "Innovation" },
  { name: "leaf", icon: FaLeaf, label: "Sustainability" },
  { name: "users", icon: FaUsers, label: "Community" },
  { name: "people-carry", icon: FaPeopleCarry, label: "Teamwork" },
  { name: "shield", icon: FaShieldAlt, label: "Security" },
  { name: "heart", icon: FaHeart, label: "Care" },
  { name: "target", icon: FaBullseye, label: "Focus" },
  { name: "trophy", icon: FaTrophy, label: "Achievement" },
  { name: "eye", icon: FaEye, label: "Vision" },
  { name: "smile", icon: FaSmile, label: "Happiness" },
  { name: "growth", icon: FaChartLine, label: "Growth" },
  { name: "globe", icon: FaGlobe, label: "Global" },
  { name: "lock", icon: FaLock, label: "Trust" },
  { name: "diamond", icon: FaGem, label: "Premium" },
  { name: "rocket", icon: FaRocket, label: "Innovation" },
  { name: "balance", icon: FaBalanceScale, label: "Balance" },
];

interface ValueItem {
  id: string;
  iconName: string;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  order: number;
}

interface ValuesSection {
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  values: ValueItem[];
}

export default function ValuesManagementPage() {
  const [valuesData, setValuesData] = useState<ValuesSection>({
    title: {
      en: "Our Core Values",
      ar: "قيمنا الأساسية"
    },
    description: {
      en: "The principles that guide everything we do and define who we are as a company.",
      ar: "المبادئ التي توجه كل ما نقوم به وتحدد هويتنا كشركة."
    },
    values: [
      {
        id: "integrity",
        iconName: "handshake",
        title: {
          en: "Integrity",
          ar: "النزاهة"
        },
        description: {
          en: "We conduct business with honesty, transparency, and ethical practices in all our dealings.",
          ar: "نقوم بأعمالنا بصدق وشفافية وممارسات أخلاقية في جميع تعاملاتنا."
        },
        order: 1
      },
      {
        id: "excellence",
        iconName: "star",
        title: {
          en: "Excellence",
          ar: "التميز"
        },
        description: {
          en: "We strive for excellence in every project, delivering quality that exceeds expectations.",
          ar: "نسعى للتميز في كل مشروع، ونقدم جودة تفوق التوقعات."
        },
        order: 2
      },
      {
        id: "innovation",
        iconName: "lightbulb",
        title: {
          en: "Innovation",
          ar: "الابتكار"
        },
        description: {
          en: "We embrace new technologies and creative solutions to stay ahead in the real estate market.",
          ar: "نتبنى التقنيات الجديدة والحلول الإبداعية للبقاء في المقدمة في السوق العقارية."
        },
        order: 3
      },
      {
        id: "sustainability",
        iconName: "leaf",
        title: {
          en: "Sustainability",
          ar: "الاستدامة"
        },
        description: {
          en: "We are committed to sustainable development practices that benefit both communities and the environment.",
          ar: "نحن ملتزمون بممارسات التنمية المستدامة التي تفيد المجتمعات والبيئة."
        },
        order: 4
      },
      {
        id: "community",
        iconName: "users",
        title: {
          en: "Community",
          ar: "المجتمع"
        },
        description: {
          en: "We build more than properties; we create communities that enhance quality of life.",
          ar: "نحن نبني أكثر من مجرد عقارات؛ نحن ننشئ مجتمعات تعزز جودة الحياة."
        },
        order: 5
      },
      {
        id: "teamwork",
        iconName: "people-carry",
        title: {
          en: "Teamwork",
          ar: "العمل الجماعي"
        },
        description: {
          en: "We believe in the power of collaboration and working together to achieve common goals.",
          ar: "نؤمن بقوة التعاون والعمل معًا لتحقيق الأهداف المشتركة."
        },
        order: 6
      }
    ]
  });

  const [editingHeader, setEditingHeader] = useState(false);
  const [editingValue, setEditingValue] = useState<string | null>(null);
  const [showAddValueForm, setShowAddValueForm] = useState(false);
  const [draggedValue, setDraggedValue] = useState<string | null>(null);

  const getIconComponent = (iconName: string) => {
    const iconOption = availableIcons.find(icon => icon.name === iconName);
    return iconOption ? iconOption.icon : FaStar;
  };

  const handleHeaderSave = (newData: Partial<ValuesSection>) => {
    setValuesData(prev => ({
      ...prev,
      ...newData
    }));
    setEditingHeader(false);
  };

  const handleValueSave = (valueId: string, newValue: ValueItem) => {
    setValuesData(prev => ({
      ...prev,
      values: prev.values.map(v => v.id === valueId ? newValue : v)
    }));
    setEditingValue(null);
  };

  const handleValueDelete = (valueId: string) => {
    if (confirm('Are you sure you want to delete this value?')) {
      setValuesData(prev => ({
        ...prev,
        values: prev.values.filter(v => v.id !== valueId)
      }));
    }
  };

  const handleValueAdd = (newValue: ValueItem) => {
    const maxOrder = Math.max(...valuesData.values.map(v => v.order), 0);
    const valueWithOrder = { ...newValue, order: maxOrder + 1 };
    setValuesData(prev => ({
      ...prev,
      values: [...prev.values, valueWithOrder]
    }));
    setShowAddValueForm(false);
  };

  const handleValueMove = (valueId: string, direction: 'up' | 'down') => {
    const currentIndex = valuesData.values.findIndex(v => v.id === valueId);
    
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === valuesData.values.length - 1)
    ) {
      return;
    }

    const newValues = [...valuesData.values];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // Swap the items
    [newValues[currentIndex], newValues[targetIndex]] = [newValues[targetIndex], newValues[currentIndex]];
    
    // Update order numbers
    newValues[currentIndex].order = currentIndex + 1;
    newValues[targetIndex].order = targetIndex + 1;

    setValuesData(prev => ({
      ...prev,
      values: newValues
    }));
  };

  const handleValueDragStart = (valueId: string) => {
    setDraggedValue(valueId);
  };

  const handleValueDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedValue || draggedValue === targetId) return;
    
    const draggedItem = valuesData.values.find(v => v.id === draggedValue);
    const targetItem = valuesData.values.find(v => v.id === targetId);
    
    if (!draggedItem || !targetItem) return;
    
    setValuesData(prev => ({
      ...prev,
      values: prev.values.map(value => {
        if (value.id === draggedValue) {
          return { ...value, order: targetItem.order };
        } else if (value.id === targetId) {
          return { ...value, order: draggedItem.order };
        }
        return value;
      })
    }));
    
    setDraggedValue(null);
  };

  const handleSaveAll = () => {
    // Here you would typically send the data to your backend
    console.log('Saving values data:', valuesData);
    alert('Values data saved successfully!');
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">About Us - Core Values</h1>
          <p className="text-gray-400 mt-1">Manage the company's core values and principles</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">Section Header</h2>
            <button
              onClick={() => setEditingHeader(!editingHeader)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingHeader ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingHeader ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingHeader ? (
            <HeaderEditForm
              data={valuesData}
              onSave={handleHeaderSave}
              onCancel={() => setEditingHeader(false)}
            />
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title</label>
                <p className="text-white">{valuesData.title.en}</p>
                <p className="text-gray-400 text-sm">{valuesData.title.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description</label>
                <p className="text-gray-300">{valuesData.description.en}</p>
                <p className="text-gray-400 text-sm">{valuesData.description.ar}</p>
              </div>
            </div>
          )}
        </div>

        {/* Values Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">
              Core Values ({valuesData.values.length})
            </h2>
            <button
              onClick={() => setShowAddValueForm(true)}
              className="inline-flex items-center px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
            >
              <FiPlus className="mr-1 h-4 w-4" />
              Add Value
            </button>
          </div>

          {/* Add Value Form */}
          {showAddValueForm && (
            <div className="mb-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <ValueAddForm
                availableIcons={availableIcons}
                onSave={handleValueAdd}
                onCancel={() => setShowAddValueForm(false)}
              />
            </div>
          )}

          {/* Values List */}
          <div className="space-y-4">
            {valuesData.values
              .sort((a, b) => a.order - b.order)
              .map((value, index) => {
                const IconComponent = getIconComponent(value.iconName);
                
                return (
                  <div
                    key={value.id}
                    className="bg-gray-700 rounded-lg p-6 border border-gray-600 hover:border-gray-500 transition-colors"
                    draggable
                    onDragStart={() => handleValueDragStart(value.id)}
                    onDrop={(e) => handleValueDrop(e, value.id)}
                    onDragOver={(e) => e.preventDefault()}
                  >
                    {editingValue === value.id ? (
                      <ValueEditForm
                        value={value}
                        availableIcons={availableIcons}
                        onSave={(newValue) => handleValueSave(value.id, newValue)}
                        onCancel={() => setEditingValue(null)}
                      />
                    ) : (
                      <div>
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                              <IconComponent className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-white">{value.title.en}</h3>
                              <p className="text-gray-400 text-sm">{value.title.ar}</p>
                              <p className="text-gray-500 text-xs">Order: {value.order}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleValueMove(value.id, 'up')}
                              disabled={index === 0}
                              className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              title="Move up"
                            >
                              <FiArrowUp className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleValueMove(value.id, 'down')}
                              disabled={index === valuesData.values.length - 1}
                              className="p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              title="Move down"
                            >
                              <FiArrowDown className="h-4 w-4" />
                            </button>
                            <div className="p-2 text-gray-400 cursor-move" title="Drag to reorder">
                              <FiMove className="h-4 w-4" />
                            </div>
                            <button
                              onClick={() => setEditingValue(value.id)}
                              className="p-2 text-gray-400 hover:text-blue-400 transition-colors"
                              title="Edit"
                            >
                              <FiEdit3 className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleValueDelete(value.id)}
                              className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                              title="Delete"
                            >
                              <FiTrash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h4 className="font-medium text-gray-300 mb-2">English</h4>
                            <p className="text-gray-300 text-sm">{value.description.en}</p>
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-300 mb-2">Arabic</h4>
                            <p className="text-gray-300 text-sm text-right" dir="rtl">{value.description.ar}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
        </div>
      </div>
    </div>
  );
}

// Header Edit Form Component
function HeaderEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: ValuesSection;
  onSave: (data: Partial<ValuesSection>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            dir="rtl"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            dir="rtl"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Value Edit Form Component
function ValueEditForm({ 
  value, 
  availableIcons,
  onSave, 
  onCancel 
}: { 
  value: ValueItem;
  availableIcons: Array<{ name: string; icon: React.ComponentType<any>; label: string }>;
  onSave: (value: ValueItem) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(value);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
        <div className="grid grid-cols-6 sm:grid-cols-9 gap-2">
          {availableIcons.map((iconOption) => {
            const IconComponent = iconOption.icon;
            return (
              <button
                key={iconOption.name}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, iconName: iconOption.name }))}
                className={`p-3 rounded-lg border-2 transition-all ${
                  formData.iconName === iconOption.name
                    ? 'border-blue-500 bg-blue-500/20'
                    : 'border-gray-600 hover:border-gray-500 bg-gray-800'
                }`}
                title={iconOption.label}
              >
                <IconComponent className="w-5 h-5 text-white mx-auto" />
              </button>
            );
          })}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            dir="rtl"
            required
          />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            dir="rtl"
            required
          />
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Value Add Form Component
function ValueAddForm({ 
  availableIcons,
  onSave, 
  onCancel 
}: { 
  availableIcons: Array<{ name: string; icon: React.ComponentType<any>; label: string }>;
  onSave: (value: ValueItem) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<ValueItem>({
    id: '',
    iconName: availableIcons[0].name,
    title: { en: '', ar: '' },
    description: { en: '', ar: '' },
    order: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.en) {
      alert('Please enter an English title');
      return;
    }

    const newValue = {
      ...formData,
      id: `value-${Date.now()}`
    };

    onSave(newValue);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Core Value</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <div className="grid grid-cols-6 sm:grid-cols-9 gap-2">
            {availableIcons.map((iconOption) => {
              const IconComponent = iconOption.icon;
              return (
                <button
                  key={iconOption.name}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, iconName: iconOption.name }))}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    formData.iconName === iconOption.name
                      ? 'border-blue-500 bg-blue-500/20'
                      : 'border-gray-600 hover:border-gray-500 bg-gray-800'
                  }`}
                  title={iconOption.label}
                >
                  <IconComponent className="w-5 h-5 text-white mx-auto" />
                </button>
              );
            })}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
            <input
              type="text"
              value={formData.title.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter English title"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
            <input
              type="text"
              value={formData.title.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="العنوان بالعربية"
              dir="rtl"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
            <textarea
              value={formData.description.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: { ...prev.description, en: e.target.value }
              }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter English description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
            <textarea
              value={formData.description.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: { ...prev.description, ar: e.target.value }
              }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="الوصف بالعربية"
              dir="rtl"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <FiSave className="mr-2 h-4 w-4 inline" />
            Add Value
          </button>
        </div>
      </form>
    </div>
  );
} 