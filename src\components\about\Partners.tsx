"use client";

import React, { useEffect, useRef } from "react";
import { FaBuilding, FaCity, FaChartLine, FaLandmark, FaHammer, FaHome, FaMoneyBillWave, FaPencilRuler } from "react-icons/fa";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

const Partners = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const partnerRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Add language context
  const { locale, isRTL } = useLanguage();

  useEffect(() => {
    // Initialize the array with the correct number of refs
    partnerRefs.current = partnerRefs.current.slice(0, 8);
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in");
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px 0px -100px 0px"
      }
    );
    
    // Observe the section header
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    // Observe each partner card
    partnerRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });
    
    return () => {
      observer.disconnect();
    };
  }, []);

  const partners = [
    { 
      name: "Global Investments Ltd.",
      icon: <FaChartLine className="h-8 w-8" />,
      category: "Investment"
    },
    { 
      name: "Urban Horizon Development",
      icon: <FaCity className="h-8 w-8" />,
      category: "Development"
    },
    { 
      name: "Summit Property Group",
      icon: <FaBuilding className="h-8 w-8" />,
      category: "Property Management"
    },
    { 
      name: "Cornerstone Financial",
      icon: <FaLandmark className="h-8 w-8" />,
      category: "Financial Services"
    },
    { 
      name: "Premier Construction",
      icon: <FaHammer className="h-8 w-8" />,
      category: "Construction"
    },
    { 
      name: "Meridian Real Estate",
      icon: <FaHome className="h-8 w-8" />,
      category: "Real Estate"
    },
    { 
      name: "Vantage Capital Partners",
      icon: <FaMoneyBillWave className="h-8 w-8" />,
      category: "Capital Investment"
    },
    { 
      name: "Elite Architecture Group",
      icon: <FaPencilRuler className="h-8 w-8" />,
      category: "Architecture"
    },
  ];

  return (
    <section className="relative py-24 overflow-hidden bg-gradient-to-b from-[rgb(var(--color-background))] to-[rgb(var(--color-background))]">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-40 ltr:right-0 rtl:left-0 w-96 h-96 rounded-full bg-[rgb(var(--color-primary))]/5 opacity-50 blur-3xl"></div>
        <div className="absolute bottom-0 ltr:left-0 rtl:right-0 w-96 h-96 rounded-full bg-[rgb(var(--color-secondary))]/5 opacity-50 blur-3xl"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section header */}
        <div 
          ref={sectionRef}
          className="max-w-3xl mx-auto text-center mb-16 opacity-0 transform translate-y-8 transition-all duration-1000 ease-out"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-[rgb(var(--color-text))] mb-6">
            {t('partners.title', locale).includes('Partners') || t('partners.title', locale).includes('شركاؤنا') ? (
              <>
                {t('partners.title', locale).split(' ').slice(0, -1).join(' ')} <span className="text-[rgb(var(--color-primary))]">{t('partners.title', locale).split(' ').slice(-1)[0]}</span>
              </>
            ) : (
              t('partners.title', locale)
            )}
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] mx-auto mb-6"></div>
          <p className="text-xl text-[rgb(var(--color-text-secondary))]">
            {t('partners.description', locale)}
          </p>
        </div>
        
        {/* Partners grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {partners.map((partner, index) => (
            <div
              key={index}
              ref={(el) => {
                if (el) partnerRefs.current[index] = el;
              }}
              className="bg-[rgb(var(--color-background))] ltr:rounded-[2px_12px_2px_12px] rtl:rounded-[12px_2px_12px_2px] shadow-md overflow-hidden group hover:shadow-lg transition-all duration-300 opacity-0 transform translate-y-8 partner-card border border-[rgb(var(--color-text))]/10"
              style={{ transitionDelay: `${index * 100}ms` }}
            >
              <div className="h-1 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))]"></div>
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="w-14 h-14 rounded-md bg-[rgb(var(--color-background))]/80 flex items-center justify-center ltr:mr-4 rtl:ml-4 text-[rgb(var(--color-primary))] group-hover:text-[rgb(var(--color-secondary))] transition-colors duration-300">
                    {partner.icon}
                  </div>
                  <div>
                    <span className="text-xs font-medium text-[rgb(var(--color-primary))] uppercase tracking-wider">{partner.category}</span>
                    <h3 className="font-semibold text-[rgb(var(--color-text))]">{partner.name}</h3>
                  </div>
                </div>
                <div className="w-0 h-0.5 bg-[rgb(var(--color-primary))]/20 group-hover:w-full transition-all duration-500"></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <p className="text-[rgb(var(--color-text-secondary))] mb-6">
            {t('partners.ctaText', locale)}
          </p>
          <a 
            href="/contact" 
            className="inline-flex items-center px-6 py-3 rounded-lg bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] text-white font-medium hover:shadow-lg hover:opacity-90 transition-all duration-300"
          >
            {t('partners.ctaButton', locale)}
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
            </svg>
          </a>
        </div>
      </div>
      
      <style jsx>{`
        .animate-in {
          opacity: 1 !important;
          transform: translateY(0) !important;
        }
        
        .partner-card {
          transition: all 600ms cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
      `}</style>
    </section>
  );
};

export default Partners;