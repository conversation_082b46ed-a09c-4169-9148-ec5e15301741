"use client";

import React, { useState, useRef } from 'react';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';

// Define types
interface LocalizedContentType {
  description: string;
  category: string;
  status: string;
  keyPoints: string[];
  amenities: any[];
  locationDetails: {
    address: string;
    description: string;
  };
  amenitiesSection: {
    title: string;
    description: string;
  };
  primeLocationSection: {
    title: string;
    description: string;
    strategicTitle: string;
    strategicDescription: string;
    nearbyAttractions: {name: string, minutes: string}[];
    transportation: string[];
    mapLink: string;
  };
  investmentSection: {
    title: string;
    description: string;
    whyInvestPoints: {
      title: string;
      description: string;
      icon: string;
    }[];
    propertyTypes: {
      type: string;
      size: string;
      price: string;
      availability: string;
    }[];
    overview: {
    priceRange: string;
    rentalYield: string;
    completionDate: string;
    paymentPlan: string;
    };
    contactForm: {
      title: string;
    description: string;
    };
  };
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
    ogTitle: string;
    ogDescription: string;
    twitterTitle: string;
    twitterDescription: string;
  };
  [key: string]: any; // Add index signature
}

// Import components
import LanguageSelector from '@/components/admin/projects/LanguageSelector';
import ProjectHeader from '@/components/admin/projects/ProjectHeader';
import ProjectBasicInfo from '@/components/admin/projects/ProjectBasicInfo';
import ProjectHeroSection from '@/components/admin/projects/ProjectHeroSection';
import ProjectHighlights from '@/components/admin/projects/ProjectHighlights';
import ProjectKeyPoints from '@/components/admin/projects/ProjectKeyPoints';
import ProjectGallery from '@/components/admin/projects/ProjectGallery';
import ProjectAmenitiesSection from '@/components/admin/projects/ProjectAmenitiesSection';
import ProjectPrimeLocation from '@/components/admin/projects/ProjectPrimeLocation';
import ProjectInvestmentSection from '@/components/admin/projects/ProjectInvestmentSection';
import ProjectConstructionProgress from '@/components/admin/projects/ProjectConstructionProgress';
import ConstructionPhotos from '@/components/admin/projects/ConstructionPhotos';
import ProjectInquirySection from '@/components/admin/projects/ProjectInquirySection';
import ProjectSEO from '@/components/admin/projects/ProjectSEO';

const NewProjectPage = () => {
  // State for language selection
  const [language, setLanguage] = useState<'en' | 'ar'>('en');
  
  // State for project data
  const [project, setProject] = useState({
    title: '',
    titleAr: '',
    slug: '',
    slugAr: '',
    location: '',
    locationAr: '',
    heroTitle: '',
    heroTitleAr: '',
    heroSubtitle: '',
    heroSubtitleAr: '',
    shortSummary: '',
    shortSummaryAr: '',
    galleryTitle: '',
    galleryTitleAr: '',
    gallerySubtitle: '',
    gallerySubtitleAr: '',
    gallery: [] as any[],
    constructionPhotos: [] as any[],
    mapLocation: {
      lat: 25.204849,  // Default to Dubai coordinates
      lng: 55.270783
    },
    mapLink: '',
    investmentSection: {
      contactFormPhone: ''
    },
    inquirySection: {
      contactPhone: '',
      contactEmail: ''
    },
    projectHighlights: {
      stories: 0,
      luxuryUnits: 0,
      completionYear: new Date().getFullYear() + 2,
      avgROI: ''
    },
    featuredImage: null as File | null,
    localizedContent: {
      en: {
        description: '',
        category: 'Residential',
        status: 'In Progress',
        keyPoints: [] as string[],
        amenities: [] as any[],
        locationDetails: {
          address: '',
          description: ''
        },
        amenitiesSection: {
          title: '',
          description: ''
        },
        primeLocationSection: {
          title: '',
          description: '',
          strategicTitle: '',
          strategicDescription: '',
          nearbyAttractions: [] as {name: string, minutes: string}[],
          transportation: [] as string[],
          mapLink: ''
        },
        investmentSection: {
          title: '',
          description: '',
          whyInvestPoints: [] as {title: string, description: string, icon: string}[],
          propertyTypes: [] as {type: string, size: string, price: string, availability: string}[],
          overview: {
            priceRange: '',
            rentalYield: '',
            completionDate: '',
            paymentPlan: ''
          },
          contactForm: {
            title: '',
            description: ''
          }
        },
        inquirySection: {
          title: '',
          description: '',
          contactInfo: {
            phone: '',
            phoneNote: '',
            email: '',
            emailNote: '',
            address: '',
            addressNote: ''
          },
          whyChoosePoints: []
        },
        seo: {
          metaTitle: '',
          metaDescription: '',
          keywords: '',
          ogTitle: '',
          ogDescription: '',
          twitterTitle: '',
          twitterDescription: ''
        }
      } as LocalizedContentType,
      ar: {
        description: '',
        category: 'Residential',
        status: 'In Progress',
        keyPoints: [] as string[],
        amenities: [] as any[],
        locationDetails: {
          address: '',
          description: ''
        },
        amenitiesSection: {
          title: '',
          description: ''
        },
        primeLocationSection: {
          title: '',
          description: '',
          strategicTitle: '',
          strategicDescription: '',
          nearbyAttractions: [] as {name: string, minutes: string}[],
          transportation: [] as string[],
          mapLink: ''
        },
        investmentSection: {
          title: '',
          description: '',
          whyInvestPoints: [] as {title: string, description: string, icon: string}[],
          propertyTypes: [] as {type: string, size: string, price: string, availability: string}[],
          overview: {
            priceRange: '',
            rentalYield: '',
            completionDate: '',
            paymentPlan: ''
          },
          contactForm: {
            title: '',
            description: ''
          }
        },
        inquirySection: {
          title: '',
          description: '',
          contactInfo: {
            phone: '',
            phoneNote: '',
            email: '',
            emailNote: '',
            address: '',
            addressNote: ''
          },
          whyChoosePoints: []
        },
        seo: {
          metaTitle: '',
          metaDescription: '',
          keywords: '',
          ogTitle: '',
          ogDescription: '',
          twitterTitle: '',
          twitterDescription: ''
        }
      } as LocalizedContentType
    }
  });
  
  // State for saving progress
  const [isSaving, setIsSaving] = useState(false);
  
  // State for amenities
  const [editingAmenityIndex, setEditingAmenityIndex] = useState<number | null>(null);
  const [editingAmenity, setEditingAmenity] = useState({ icon: '', title: '', description: '' });
  const [draggedAmenity, setDraggedAmenity] = useState<number | null>(null);
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [showAmenityForm, setShowAmenityForm] = useState(false);
  const [selectedIconSet, setSelectedIconSet] = useState<
    'fc' | 'fa' | 'fa6' | 'bs' | 'ri' | 'gi' | 'tb' | 'md' | 'hi' | 'ai' | 'io' | 'io5' | 'pi'
  >('fc');
  
  // Helper function to get the current localized content
  const getCurrentContent = () => {
    return project.localizedContent[language];
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle nested localizedContent properties
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
        setProject({
          ...project,
        localizedContent: {
          ...project.localizedContent,
          [language]: {
            ...project.localizedContent[language],
          [parent]: {
              ...project.localizedContent[language][parent],
            [child]: value
          }
            }
          }
        });
      }
    // Handle category and status fields (sync between languages)
    else if (['category', 'status'].includes(name)) {
        setProject({
          ...project,
        localizedContent: {
          ...project.localizedContent,
          en: {
            ...project.localizedContent.en,
          [name]: value
          },
          ar: {
            ...project.localizedContent.ar,
            [name]: value
          }
        }
      });
    }
    // Handle regular localizedContent properties
    else if (['description'].includes(name)) {
          setProject({
            ...project,
        localizedContent: {
          ...project.localizedContent,
          [language]: {
            ...project.localizedContent[language],
          [name]: value
          }
        }
      });
    } 
    // Handle language-specific fields
    else if (language === 'ar' && ['title', 'slug', 'location', 'heroTitle', 'heroSubtitle', 'shortSummary', 'galleryTitle', 'gallerySubtitle'].includes(name)) {
        setProject({
          ...project,
        [`${name}Ar`]: value
      });
    } 
    // Handle all other fields
    else {
          setProject({
            ...project,
        [name]: value
      });
    }
  };
  
  // Handle adding items to arrays
  const addItem = (array: string, value: string | File | null) => {
    if (typeof value === 'string' && !value.trim()) return;
    
        setProject({
          ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent[language],
          [array]: [...project.localizedContent[language][array], value]
        }
      }
    });
  };
  
  // Handle removing items from arrays
  const removeItem = (array: string, index: number) => {
    const newArray = [...project.localizedContent[language][array]];
    newArray.splice(index, 1);
    
      setProject({
        ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent[language],
          [array]: newArray
        }
      }
    });
  };
  
  // Handle save action
  const handleSave = () => {
    setIsSaving(true);
    
    // Here you would send the data to your API
    setTimeout(() => {
      setIsSaving(false);
      alert('Project created successfully!');
    }, 1500);
  };
  
  // Function to determine if a library has any icons matching the search term
  const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
    if (!searchTerm.trim()) return true;
    
    let library;
    switch (libraryId) {
      case 'fc': library = FcIcons; break;
      case 'fa': library = FaIcons; break;
      case 'fa6': library = FaIconsSolid; break;
      case 'bs': library = BsIcons; break;
      case 'ri': library = RiIcons; break;
      case 'gi': library = GiIcons; break;
      case 'tb': library = TbIcons; break;
      case 'md': library = MdIcons; break;
      case 'hi': library = HiIcons; break;
      case 'ai': library = AiIcons; break;
      case 'io': library = IoIcons; break;
      case 'io5': library = Io5Icons; break;
      case 'pi': library = PiIcons; break;
      default: return false;
    }
    
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).some(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    );
  };
  
  // Function to get the icon component based on the icon name
  const getIconComponent = (iconName: string): React.ReactElement | null => {
    if (!iconName) return null;
    
    // Extract the library prefix and the actual icon name
    const [prefix, ...rest] = iconName.split(/(?=[A-Z])/);
    
    switch (prefix.toLowerCase()) {
      case 'fc': {
        const IconFc = FcIcons[iconName as keyof typeof FcIcons];
        return IconFc ? <IconFc className="text-2xl" /> : null;
      }
      case 'fa': {
        const IconFa = FaIcons[iconName as keyof typeof FaIcons];
        return IconFa ? <IconFa className="text-2xl" /> : null;
      }
      case 'fa6': {
        const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
        return IconFa6 ? <IconFa6 className="text-2xl" /> : null;
      }
      case 'bs': {
        const IconBs = BsIcons[iconName as keyof typeof BsIcons];
        return IconBs ? <IconBs className="text-2xl" /> : null;
      }
      case 'ri': {
        const IconRi = RiIcons[iconName as keyof typeof RiIcons];
        return IconRi ? <IconRi className="text-2xl" /> : null;
      }
      case 'gi': {
        const IconGi = GiIcons[iconName as keyof typeof GiIcons];
        return IconGi ? <IconGi className="text-2xl" /> : null;
      }
      case 'tb': {
        const IconTb = TbIcons[iconName as keyof typeof TbIcons];
        return IconTb ? <IconTb className="text-2xl" /> : null;
      }
      case 'md': {
        const IconMd = MdIcons[iconName as keyof typeof MdIcons];
        return IconMd ? <IconMd className="text-2xl" /> : null;
      }
      case 'hi': {
        const IconHi = HiIcons[iconName as keyof typeof HiIcons];
        return IconHi ? <IconHi className="text-2xl" /> : null;
      }
      case 'ai': {
        const IconAi = AiIcons[iconName as keyof typeof AiIcons];
        return IconAi ? <IconAi className="text-2xl" /> : null;
      }
      case 'io': {
        const IconIo = IoIcons[iconName as keyof typeof IoIcons];
        return IconIo ? <IconIo className="text-2xl" /> : null;
      }
      case 'io5': {
        const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
        return IconIo5 ? <IconIo5 className="text-2xl" /> : null;
      }
      case 'pi': {
        const IconPi = PiIcons[iconName as keyof typeof PiIcons];
        return IconPi ? <IconPi className="text-2xl" /> : null;
      }
      default:
        return null;
    }
  };

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      {/* Page Header with Save Button */}
      <ProjectHeader 
        title={language === 'en' ? 'Create New Project' : 'إنشاء مشروع جديد'}
        subtitle={language === 'en' ? 'Add details about a new real estate project' : 'أضف تفاصيل حول مشروع عقاري جديد'}
        handleSave={handleSave}
        isSaving={isSaving}
        language={language}
      />
      
      {/* Language Selector */}
      <LanguageSelector language={language} setLanguage={setLanguage} />
      
      {/* Project Basic Information */}
      <ProjectBasicInfo 
        project={project} 
        language={language} 
        handleInputChange={handleInputChange}
        getCurrentContent={getCurrentContent}
        setProject={setProject}
      />
      
      {/* Project Hero Section */}
      <ProjectHeroSection 
        project={project} 
        language={language} 
        handleInputChange={handleInputChange}
      />
      
      {/* Features and Key Points */}
      <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
        <h2 className="text-lg font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Project Features & Highlights' : 'ميزات وأبرز معالم المشروع'}
        </h2>
        
        {/* Project Highlights */}
        <ProjectHighlights 
          project={project} 
          language={language} 
          setProject={setProject}
        />
        
        {/* Project Key Points */}
        <ProjectKeyPoints
          language={language}
          getCurrentContent={getCurrentContent}
          addItem={addItem}
          removeItem={removeItem}
        />
      </div>
      
      {/* Project Gallery */}
      <ProjectGallery
        project={project}
        language={language}
        handleInputChange={handleInputChange}
        setProject={setProject}
      />
      
      {/* Project Amenities */}
      <ProjectAmenitiesSection
        project={project}
        language={language}
        getCurrentContent={getCurrentContent}
        setProject={setProject}
        editingAmenityIndex={editingAmenityIndex}
        setEditingAmenityIndex={setEditingAmenityIndex}
        editingAmenity={editingAmenity}
        setEditingAmenity={setEditingAmenity}
        draggedAmenity={draggedAmenity}
        setDraggedAmenity={setDraggedAmenity}
        showIconSelector={showIconSelector}
        setShowIconSelector={setShowIconSelector}
        iconSearchTerm={iconSearchTerm}
        setIconSearchTerm={setIconSearchTerm}
        showAmenityForm={showAmenityForm}
        setShowAmenityForm={setShowAmenityForm}
        selectedIconSet={selectedIconSet}
        setSelectedIconSet={setSelectedIconSet}
        hasMatchingIcons={hasMatchingIcons}
        getIconComponent={getIconComponent}
      />
      
      {/* Prime Location */}
      <ProjectPrimeLocation
        project={project}
        language={language}
        setProject={setProject}
      />
      
      {/* Investment Section */}
      <ProjectInvestmentSection
        project={project}
        language={language}
        setProject={setProject}
      />
      
      {/* Construction Progress */}
      <ProjectConstructionProgress
        project={project}
        language={language}
        setProject={setProject}
      />
      
      {/* Construction Photos */}
      <ConstructionPhotos
        project={project}
        language={language}
        setProject={setProject}
      />
      
      {/* Inquiry Section */}
      <ProjectInquirySection
        project={project}
        language={language}
        setProject={setProject}
      />
      
      {/* SEO Sections */}
      <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
        <h2 className="text-lg font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Search Engine Optimization (SEO)' : 'تحسين محركات البحث (SEO)'}
        </h2>
        
        {/* SEO Content based on main language */}
        <ProjectSEO
          project={project}
          language={language}
          setProject={setProject}
        />
      </div>
      
      {/* Submit Buttons */}
      <div className="mt-8 flex justify-end">
        <button
          type="button"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          onClick={handleSave}
          disabled={isSaving}
        >
          {isSaving 
            ? (language === 'en' ? 'Creating Project...' : 'جاري إنشاء المشروع...') 
            : (language === 'en' ? 'Create Project' : 'إنشاء المشروع')}
        </button>
      </div>
    </div>
  );
};

export default NewProjectPage; 