"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import Button from "../ui/Button";
import LanguageSwitcher from "../LanguageSwitcher";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { locale } = useLanguage();
  const pathname = usePathname();

  // Don't render header on admin routes
  if (pathname && pathname.includes('/admin')) {
    return null;
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="bg-[rgb(var(--color-background))]/95 backdrop-blur-md shadow-lg border-b border-gray-200/10 sticky top-0 z-50">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo Section */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center group">
              <div className="h-12 w-64 relative transition-transform duration-300 group-hover:scale-105">
                <Image
                  src="/images/logo/mazaya-logo-dark.svg"
                  alt="Mazaya Capital Logo"
                  className="w-full h-full object-cover"
                  width={280}
                  height={64}
                  priority
                />
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1 rtl:space-x-reverse">
            <Link 
              href="/" 
              className="relative px-4 py-2 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] transition-all duration-300 rounded-lg hover:bg-[rgb(var(--color-primary))]/5 group"
            >
              {t('home', locale)}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link 
              href="/about" 
              className="relative px-4 py-2 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] transition-all duration-300 rounded-lg hover:bg-[rgb(var(--color-primary))]/5 group"
            >
              {t('about', locale)}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link 
              href="/projects" 
              className="relative px-4 py-2 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] transition-all duration-300 rounded-lg hover:bg-[rgb(var(--color-primary))]/5 group"
            >
              {t('projects', locale)}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link 
              href="/articles" 
              className="relative px-4 py-2 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] transition-all duration-300 rounded-lg hover:bg-[rgb(var(--color-primary))]/5 group"
            >
              {t('articles', locale)}
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] group-hover:w-full transition-all duration-300"></span>
            </Link>
          </nav>

          {/* Right Section - Language Switcher and Contact Button */}
          <div className="hidden lg:flex items-center gap-6">
            <div className="relative">
              <LanguageSwitcher />
            </div>
            <Button 
              href="/contact" 
              size="md"
              className="bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] text-white px-6 py-2.5 rounded-full font-medium shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border-0"
            >
              {t('contactUs', locale)}
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden flex items-center gap-3">
            <div className="relative">
              <LanguageSwitcher />
            </div>
            <button
              type="button"
              className="p-2 text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary))]/10 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[rgb(var(--color-primary))]/20"
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
            >
              <div className="relative w-6 h-6">
                <span 
                  className={`absolute block w-full h-0.5 bg-current transform transition-all duration-300 ${
                    isMobileMenuOpen ? 'rotate-45 top-3' : 'top-1'
                  }`}
                />
                <span 
                  className={`absolute block w-full h-0.5 bg-current transform transition-all duration-300 top-3 ${
                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'
                  }`}
                />
                <span 
                  className={`absolute block w-full h-0.5 bg-current transform transition-all duration-300 ${
                    isMobileMenuOpen ? '-rotate-45 top-3' : 'top-5'
                  }`}
                />
              </div>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div className={`lg:hidden overflow-hidden transition-all duration-500 ease-in-out ${
          isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className="py-6 space-y-2 border-t border-gray-200/10 mt-4">
            <Link 
              href="/" 
              className="block px-4 py-3 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary))]/5 rounded-lg transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('home', locale)}
            </Link>
            <Link 
              href="/about" 
              className="block px-4 py-3 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary))]/5 rounded-lg transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('about', locale)}
            </Link>
            <Link 
              href="/projects" 
              className="block px-4 py-3 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary))]/5 rounded-lg transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('projects', locale)}
            </Link>
            <Link 
              href="/articles" 
              className="block px-4 py-3 text-sm font-medium text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary))]/5 rounded-lg transition-all duration-300"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t('articles', locale)}
            </Link>
            <div className="pt-4 mt-4 border-t border-gray-200/10">
              <Button 
                href="/contact" 
                className="w-full bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] text-white py-3 px-6 rounded-lg font-medium shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 border-0"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {t('contactUs', locale)}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;