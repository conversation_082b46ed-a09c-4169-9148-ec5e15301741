"use client";

import React, { useState, useEffect } from 'react';
import { Inter } from "next/font/google";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { 
  FiMail, FiLock, FiEye, FiEyeOff, FiLogIn, FiShield, 
  FiAlertCircle, FiCheck, FiHome 
} from 'react-icons/fi';
import NoSSR from "@/components/NoSSR";

// Configure the Inter font
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
});

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface FormErrors {
  email?: string;
  password?: string;
  general?: string;
}

function LoginPageContent() {
  const router = useRouter();
  const { login, isLoading: authLoading, isAuthenticated, forceUpdate } = useAuth();
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect if already authenticated (only after mounting)
  useEffect(() => {
    if (isMounted && isAuthenticated && !authLoading) {
      router.push('/en/admin');
    }
  }, [isMounted, isAuthenticated, authLoading, router, forceUpdate]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear specific field error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      const result = await login({
        email: formData.email,
        password: formData.password,
        remember_me: formData.rememberMe
      });

      if (result.success) {
        // Success - redirect immediately
        console.log('Login successful, redirecting...');
        // Small delay to ensure state is updated
        setTimeout(() => {
          router.push('/en/admin');
        }, 100);
      } else {
        // Login failed
        setLoginAttempts(prev => prev + 1);
        setErrors({
          general: result.message || 'Login failed. Please try again.'
        });
      }
    } catch (error) {
      console.error('Login error:', error);
      setErrors({
        general: 'An error occurred during login. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fix: Recalculate form validity when formData or errors change
  const isFormValid = formData.email && formData.password && Object.keys(errors).filter(key => key !== 'general').length === 0;

  // Show loading while checking authentication or not mounted yet
  if (!isMounted) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${inter.className}`} suppressHydrationWarning>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Only check authentication after mounting to prevent hydration mismatch
  if (authLoading) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${inter.className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Checking authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${inter.className}`} suppressHydrationWarning>
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="h-20 w-20 relative">
              <Image
                src="/images/logo/mazaya-logo-mark.svg"
                alt="Mazaya Capital Logo"
                className="w-full h-full object-cover"
                width={80}
                height={80}
                priority
              />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-white">Admin Portal</h2>
          <p className="mt-2 text-gray-400">Sign in to your admin account</p>
          
          {/* Back to website link */}
          <Link 
            href="/"
            className="mt-4 inline-flex items-center text-sm text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
          >
            <FiHome className="mr-1 h-4 w-4" />
            Back to Website
          </Link>
        </div>

        {/* Login Form */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 shadow-xl">
          {/* General Error Message */}
          {errors.general && (
            <div className="mb-4 p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
              <div className="flex items-center">
                <FiAlertCircle className="h-5 w-5 text-red-400 mr-3" />
                <p className="text-red-400 text-sm">{errors.general}</p>
              </div>
            </div>
          )}

          {/* Login Attempts Warning */}
          {loginAttempts >= 3 && (
            <div className="mb-4 p-4 bg-yellow-900/20 border border-yellow-600/30 rounded-lg">
              <div className="flex items-center">
                <FiAlertCircle className="h-5 w-5 text-yellow-400 mr-3" />
                <p className="text-yellow-400 text-sm">
                  Multiple failed attempts detected. Please verify your credentials.
                </p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-400 mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`block w-full pl-10 pr-3 py-3 border rounded-lg bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                    errors.email 
                      ? 'border-red-500 focus:ring-red-500' 
                      : 'border-gray-600 focus:ring-[#00C2FF] focus:border-[#00C2FF]'
                  }`}
                  placeholder="Enter your email"
                />
              </div>
              {errors.email && (
                <p className="mt-2 text-sm text-red-400 flex items-center">
                  <FiAlertCircle className="h-4 w-4 mr-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-400 mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`block w-full pl-10 pr-12 py-3 border rounded-lg bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                    errors.password 
                      ? 'border-red-500 focus:ring-red-500' 
                      : 'border-gray-600 focus:ring-[#00C2FF] focus:border-[#00C2FF]'
                  }`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-400"
                >
                  {showPassword ? (
                    <FiEyeOff className="h-5 w-5" />
                  ) : (
                    <FiEye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-2 text-sm text-red-400 flex items-center">
                  <FiAlertCircle className="h-4 w-4 mr-1" />
                  {errors.password}
                </p>
              )}
            </div>

            {/* Remember Me */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-[#00C2FF] focus:ring-[#00C2FF] border-gray-600 rounded bg-gray-700"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-400">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link 
                  href="/en/forgot-password" 
                  className="text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || !isFormValid}
              className={`group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white transition-all duration-200 ${
                isLoading || !isFormValid
                  ? 'bg-gray-600 cursor-not-allowed'
                  : 'bg-[#00C2FF] hover:bg-[#00C2FF]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] focus:ring-offset-gray-800'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Signing in...
                </div>
              ) : (
                <div className="flex items-center">
                  <FiLogIn className="mr-2 h-4 w-4" />
                  Sign in to Admin Panel
                </div>
              )}
            </button>
          </form>

          {/* Additional Links */}
          <div className="mt-6 text-center">
            <p className="text-gray-400 text-sm">
              Need help? {' '}
              <Link 
                href="/contact" 
                className="text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
              >
                Contact Support
              </Link>
            </p>
          </div>
        </div>

        {/* Security Notice */}
        <div className="text-center">
          <div className="flex items-center justify-center text-gray-500 text-xs">
            <FiShield className="h-4 w-4 mr-2" />
            <span>Secured by Mazaya Capital Security Protocol</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <NoSSR>
      <LoginPageContent />
    </NoSSR>
  );
}