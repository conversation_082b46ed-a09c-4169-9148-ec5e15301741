"use client";

import React, { useRef } from 'react';
import { FiImage } from 'react-icons/fi';

interface AppearanceSettingsData {
  logo: {
    favicon: string;
    appleTouchIcon: string;
  };
}

interface AppearanceSettingsProps {
  data: AppearanceSettingsData;
  onChange: (data: AppearanceSettingsData) => void;
}

export default function AppearanceSettings({ data, onChange }: AppearanceSettingsProps) {
  const faviconInputRef = useRef<HTMLInputElement>(null);
  const appleIconInputRef = useRef<HTMLInputElement>(null);

  const updateData = (updates: Partial<AppearanceSettingsData>) => {
    onChange({ ...data, ...updates });
  };

  const handleFileUpload = (type: 'favicon' | 'appleTouchIcon') => {
    if (type === 'favicon') {
      faviconInputRef.current?.click();
    } else {
      appleIconInputRef.current?.click();
    }
  };

  const handleFileChange = (type: 'favicon' | 'appleTouchIcon', file: File | null) => {
    if (file) {
      console.log(`${type} file selected:`, file.name);
      // Handle file upload logic here
      // For now, we'll just create a URL for preview
      const fileUrl = URL.createObjectURL(file);
      updateData({
        logo: {
          ...data.logo,
          [type]: fileUrl
        }
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Hidden file inputs */}
      <input
        ref={faviconInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => handleFileChange('favicon', e.target.files?.[0] || null)}
      />
      <input
        ref={appleIconInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => handleFileChange('appleTouchIcon', e.target.files?.[0] || null)}
      />

      {/* Website Icons */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Website Icons</h3>
        <p className="text-gray-400 text-sm mb-6">Upload icons that will appear in browser tabs and when users save your site to their devices.</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Favicon (16x16 or 32x32 pixels)</label>
            <p className="text-xs text-gray-500 mb-3">Small icon displayed in browser tabs and bookmarks</p>
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-[#00C2FF] transition-colors cursor-pointer">
              {data.logo.favicon ? (
                <div className="flex flex-col items-center">
                  <img src={data.logo.favicon} alt="Favicon" className="max-h-8 mb-3" />
                  <p className="text-xs text-gray-400 mb-2">Current favicon</p>
                </div>
              ) : (
                <FiImage className="h-8 w-8 text-gray-500 mx-auto mb-3" />
              )}
              <button
                onClick={() => handleFileUpload('favicon')}
                className="text-[#00C2FF] text-sm hover:underline font-medium"
              >
                {data.logo.favicon ? 'Change Favicon' : 'Upload Favicon'}
              </button>
              <p className="text-xs text-gray-500 mt-2">Recommended: ICO, PNG format</p>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Apple Touch Icon (180x180 pixels)</label>
            <p className="text-xs text-gray-500 mb-3">Icon for iOS devices when site is saved to home screen</p>
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-[#00C2FF] transition-colors cursor-pointer">
              {data.logo.appleTouchIcon ? (
                <div className="flex flex-col items-center">
                  <img src={data.logo.appleTouchIcon} alt="Apple Touch Icon" className="max-h-12 mb-3 rounded-lg" />
                  <p className="text-xs text-gray-400 mb-2">Current apple touch icon</p>
                </div>
              ) : (
                <FiImage className="h-12 w-12 text-gray-500 mx-auto mb-3" />
              )}
              <button
                onClick={() => handleFileUpload('appleTouchIcon')}
                className="text-[#00C2FF] text-sm hover:underline font-medium"
              >
                {data.logo.appleTouchIcon ? 'Change Icon' : 'Upload Icon'}
              </button>
              <p className="text-xs text-gray-500 mt-2">Recommended: PNG format, 180x180px</p>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-700/50 rounded-lg">
          <h4 className="text-white font-medium text-sm mb-2">💡 Tips for better icons:</h4>
          <ul className="text-xs text-gray-400 space-y-1">
            <li>• Use high contrast colors for better visibility</li>
            <li>• Keep designs simple and recognizable at small sizes</li>
            <li>• Test icons in different browsers and devices</li>
            <li>• Favicon should work well in both light and dark browser themes</li>
          </ul>
        </div>
      </div>
    </div>
  );
} 