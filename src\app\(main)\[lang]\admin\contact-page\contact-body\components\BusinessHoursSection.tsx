"use client";

import { <PERSON><PERSON>lock, FiPlus, FiTrash2 } from 'react-icons/fi';

interface BusinessHours {
  title: {
    english: string;
    arabic: string;
  };
  schedule: {
    id: string;
    day: {
      english: string;
      arabic: string;
    };
    hours: {
      english: string;
      arabic: string;
    };
  }[];
  holidaySection: {
    title: {
      english: string;
      arabic: string;
    };
    note: {
      english: string;
      arabic: string;
    };
  };
}

interface BusinessHoursSectionProps {
  businessHours: BusinessHours;
  onUpdate: (path: string[], value: string | boolean) => void;
  onAddSchedule: () => void;
  onRemoveSchedule: (id: string) => void;
}

export default function BusinessHoursSection({ businessHours, onUpdate, onAddSchedule, onRemoveSchedule }: BusinessHoursSectionProps) {
  return (
    <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
      <h3 className="text-lg font-medium text-white mb-4 flex items-center">
        <div className="w-6 h-6 bg-orange-500 rounded mr-2 flex items-center justify-center">
          <span className="text-white text-xs font-bold">4</span>
        </div>
        Business Hours
      </h3>
      <p className="text-gray-400 text-sm mb-6">Configure business operating hours and holiday schedules with bilingual support</p>
      
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-300">Section Title (English)</label>
          <input
            type="text"
            className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
            value={businessHours.title.english}
            onChange={e => onUpdate(['title', 'english'], e.target.value)}
            placeholder="Business Hours"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300">Section Title (Arabic)</label>
          <input
            type="text"
            dir="rtl"
            className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
            value={businessHours.title.arabic}
            onChange={e => onUpdate(['title', 'arabic'], e.target.value)}
            placeholder="ساعات العمل"
          />
        </div>
      </div>

      {/* Schedule Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-gray-300">Schedule</h4>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onAddSchedule();
            }}
            className="inline-flex items-center px-3 py-1 border border-green-500 rounded-md text-sm font-medium text-green-400 hover:bg-green-500 hover:text-white transition-colors"
          >
            <FiPlus className="mr-1 h-4 w-4" />
            Add Schedule
          </button>
        </div>

        <div className="space-y-4">
          {businessHours.schedule.map((item, index) => (
            <div key={item.id} className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h5 className="text-sm font-medium text-gray-200">Schedule #{index + 1}</h5>
                {businessHours.schedule.length > 1 && (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveSchedule(item.id);
                    }}
                    className="inline-flex items-center px-2 py-1 border border-red-500 rounded text-xs font-medium text-red-400 hover:bg-red-500 hover:text-white transition-colors"
                  >
                    <FiTrash2 className="mr-1 h-3 w-3" />
                    Remove
                  </button>
                )}
              </div>
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300">Day (English)</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={item.day.english}
                    onChange={e => onUpdate(['schedule', index.toString(), 'day', 'english'], e.target.value)}
                    placeholder="Monday"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Day (Arabic)</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={item.day.arabic}
                    onChange={e => onUpdate(['schedule', index.toString(), 'day', 'arabic'], e.target.value)}
                    placeholder="الاثنين"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Hours (English)</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={item.hours.english}
                    onChange={e => onUpdate(['schedule', index.toString(), 'hours', 'english'], e.target.value)}
                    placeholder="9:00 AM - 6:00 PM"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Hours (Arabic)</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={item.hours.arabic}
                    onChange={e => onUpdate(['schedule', index.toString(), 'hours', 'arabic'], e.target.value)}
                    placeholder="9:00 ص - 6:00 م"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Holiday Section */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <h4 className="text-md font-medium text-gray-300 mb-4">Holiday Schedule</h4>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300">Holiday Title (English)</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={businessHours.holidaySection.title.english}
              onChange={e => onUpdate(['holidaySection', 'title', 'english'], e.target.value)}
              placeholder="Holiday Schedule"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Holiday Title (Arabic)</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={businessHours.holidaySection.title.arabic}
              onChange={e => onUpdate(['holidaySection', 'title', 'arabic'], e.target.value)}
              placeholder="جدول العطل"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-gray-300">Holiday Note (English)</label>
            <textarea
              rows={2}
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={businessHours.holidaySection.note.english}
              onChange={e => onUpdate(['holidaySection', 'note', 'english'], e.target.value)}
              placeholder="Holiday note..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Holiday Note (Arabic)</label>
            <textarea
              rows={2}
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={businessHours.holidaySection.note.arabic}
              onChange={e => onUpdate(['holidaySection', 'note', 'arabic'], e.target.value)}
              placeholder="ملاحظة العطل..."
            />
          </div>
        </div>
      </div>
    </div>
  );
} 