import { useState, useEffect, useRef, RefObject } from "react";

type SectionRefs = {
  [key: string]: RefObject<HTMLElement>;
};

export default function useScrollSpy() {
  const [activeTab, setActiveTab] = useState("overview");
  const [isScrolled, setIsScrolled] = useState(false);

  // References for sections to scroll to
  const overviewRef = useRef<HTMLElement>(null);
  const galleryRef = useRef<HTMLElement>(null);
  const amenitiesRef = useRef<HTMLElement>(null);
  const locationRef = useRef<HTMLElement>(null);
  const investmentRef = useRef<HTMLElement>(null);
  const progressRef = useRef<HTMLElement>(null);
  const inquiryRef = useRef<HTMLElement>(null);

  // Create a map of all refs
  const refs: SectionRefs = {
    overview: overviewRef,
    gallery: galleryRef,
    amenities: amenitiesRef,
    location: locationRef,
    investment: investmentRef, 
    progress: progressRef,
    inquiry: inquiryRef
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 100);
      
      // Determine which section is currently in view
      const scrollPosition = window.scrollY + 100; // Adding offset for the sticky header
      
      // Order is important - check from bottom to top
      if (inquiryRef.current && scrollPosition >= inquiryRef.current.offsetTop) {
        setActiveTab("inquiry");
      } else if (progressRef.current && scrollPosition >= progressRef.current.offsetTop) {
        setActiveTab("progress");
      } else if (investmentRef.current && scrollPosition >= investmentRef.current.offsetTop) {
        setActiveTab("investment");
      } else if (locationRef.current && scrollPosition >= locationRef.current.offsetTop) {
        setActiveTab("location");
      } else if (amenitiesRef.current && scrollPosition >= amenitiesRef.current.offsetTop) {
        setActiveTab("amenities");
      } else if (galleryRef.current && scrollPosition >= galleryRef.current.offsetTop) {
        setActiveTab("gallery");
      } else if (overviewRef.current && scrollPosition >= overviewRef.current.offsetTop) {
        setActiveTab("overview");
      }
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Function to scroll to a section
  const scrollToSection = (sectionId: string) => {
    setActiveTab(sectionId);
    
    const sectionRef = refs[sectionId];
    
    if (sectionRef?.current) {
      const yOffset = isScrolled ? -70 : -160; // Adjust offset based on sticky header
      const y = sectionRef.current.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }
  };

  return {
    activeTab,
    isScrolled,
    refs,
    scrollToSection,
    overviewRef,
    galleryRef,
    amenitiesRef,
    locationRef,
    investmentRef,
    progressRef,
    inquiryRef
  };
} 