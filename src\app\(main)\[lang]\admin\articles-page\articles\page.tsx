"use client";

import { useState } from 'react';
import Link from 'next/link';
import { <PERSON>P<PERSON>, FiSearch, FiEdit3, <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiDownload } from 'react-icons/fi';
import Image from 'next/image';

// Mock data
const MOCK_ARTICLES = [
  {
    id: 1,
    title: {
      english: "Real Estate Market Trends for 2024",
      arabic: "اتجاهات سوق العقارات لعام 2024"
    },
    excerpt: {
      english: "Explore the emerging trends shaping the real estate market in 2024.",
      arabic: "استكشف الاتجاهات الناشئة التي تشكل سوق العقارات في عام 2024."
    },
    featuredImage: "/images/articles/article-1.jpg",
    status: "published",
    category: "Market Analysis",
    author: "<PERSON>",
    publishedAt: "2024-01-15T10:00:00Z",
    createdAt: "2024-01-14T14:30:00Z",
    views: 1250,
    readingTime: 8,
    tags: ["trends", "analysis", "2024"]
  },
  {
    id: 2,
    title: {
      english: "Investment Opportunities in Commercial Real Estate",
      arabic: "فرص الاستثمار في العقارات التجارية"
    },
    excerpt: {
      english: "Discover lucrative investment opportunities in the commercial real estate sector.",
      arabic: "اكتشف فرص الاستثمار المربحة في قطاع العقارات التجارية."
    },
    featuredImage: "/images/articles/article-2.jpg",
    status: "draft",
    category: "Investment Tips",
    author: "Sarah Johnson",
    publishedAt: null,
    createdAt: "2024-01-16T09:15:00Z",
    views: 0,
    readingTime: 12,
    tags: ["investment", "commercial", "opportunities"]
  },
  {
    id: 3,
    title: {
      english: "Sustainable Building Practices: The Future of Construction",
      arabic: "ممارسات البناء المستدام: مستقبل البناء"
    },
    excerpt: {
      english: "Learn about sustainable building practices that are revolutionizing the construction industry.",
      arabic: "تعرف على ممارسات البناء المستدام التي تحدث ثورة في صناعة البناء."
    },
    featuredImage: "/images/articles/article-3.jpg",
    status: "published",
    category: "Industry Insights",
    author: "Ahmed Al-Rashid",
    publishedAt: "2024-01-12T16:45:00Z",
    createdAt: "2024-01-11T11:20:00Z",
    views: 892,
    readingTime: 10,
    tags: ["sustainability", "construction", "future"]
  }
];

const MOCK_CATEGORIES = [
  { id: 1, name: "All Categories" },
  { id: 2, name: "Real Estate News" },
  { id: 3, name: "Investment Tips" },
  { id: 4, name: "Market Analysis" },
  { id: 5, name: "Property Guides" },
  { id: 6, name: "Industry Insights" }
];

export default function ArticlesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [articles, setArticles] = useState(MOCK_ARTICLES);

  // Filter articles based on search and filters
  const filteredArticles = articles.filter(article => {
    const matchesSearch = 
      article.title.english.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.title.arabic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.excerpt.english.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.excerpt.arabic.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'All Categories' || article.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || article.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const handleDeleteArticle = (id: number) => {
    if (confirm('Are you sure you want to delete this article?')) {
      setArticles(articles.filter(article => article.id !== id));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-900/50 text-green-400';
      case 'draft':
        return 'bg-yellow-900/50 text-yellow-400';
      case 'archived':
        return 'bg-gray-900/50 text-gray-400';
      default:
        return 'bg-gray-900/50 text-gray-400';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not published';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStats = () => {
    const total = articles.length;
    const published = articles.filter(a => a.status === 'published').length;
    const draft = articles.filter(a => a.status === 'draft').length;
    const totalViews = articles.reduce((sum, a) => sum + a.views, 0);

    return { total, published, draft, totalViews };
  };

  const stats = getStats();

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Articles Management</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage your articles, create new content, and track performance
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link
            href="/admin/articles-page/articles/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiPlus className="-ml-1 mr-2 h-5 w-5" />
            New Article
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-700">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiEdit3 className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">Total Articles</dt>
                  <dd className="text-lg font-medium text-white">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-700">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiEye className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">Published</dt>
                  <dd className="text-lg font-medium text-white">{stats.published}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-700">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiEdit3 className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">Drafts</dt>
                  <dd className="text-lg font-medium text-white">{stats.draft}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-700">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiEye className="h-6 w-6 text-purple-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-300 truncate">Total Views</dt>
                  <dd className="text-lg font-medium text-white">{stats.totalViews.toLocaleString()}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div className="col-span-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md leading-5 bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <select
              className="block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {MOCK_CATEGORIES.map(category => (
                <option key={category.id} value={category.name}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select
              className="block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>
      </div>

      {/* Articles List */}
      <div className="mt-6 bg-gray-800 shadow overflow-hidden rounded-lg border border-gray-700">
        <div className="px-6 py-4 border-b border-gray-700">
          <h3 className="text-lg font-medium text-white">
            Articles ({filteredArticles.length})
          </h3>
        </div>

        {filteredArticles.length === 0 ? (
          <div className="text-center py-12">
            <FiEdit3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-300">No articles found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedCategory !== 'All Categories' || selectedStatus !== 'all'
                ? 'Try adjusting your search or filters.'
                : 'Get started by creating a new article.'}
            </p>
            <div className="mt-6">
              <Link
                href="/admin/articles-page/articles/new"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
              >
                <FiPlus className="-ml-1 mr-2 h-5 w-5" />
                New Article
              </Link>
            </div>
          </div>
        ) : (
          <ul className="divide-y divide-gray-700">
            {filteredArticles.map((article) => (
              <li key={article.id} className="p-6 hover:bg-gray-750 transition-colors duration-200">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {article.featuredImage ? (
                      <Image
                        src={article.featuredImage}
                        alt={article.title.english}
                        width={80}
                        height={80}
                        className="h-20 w-20 rounded-lg object-cover"
                      />
                    ) : (
                      <div className="h-20 w-20 rounded-lg bg-gray-600 flex items-center justify-center">
                        <FiEdit3 className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-lg font-medium text-white truncate">
                          {article.title.english}
                        </h4>
                        <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                          {article.excerpt.english}
                        </p>
                        <div className="mt-2 flex items-center text-sm text-gray-500 space-x-4">
                          <span>By {article.author}</span>
                          <span>•</span>
                          <span>{article.category}</span>
                          <span>•</span>
                          <span>{formatDate(article.publishedAt)}</span>
                          <span>•</span>
                          <span>{article.views} views</span>
                          <span>•</span>
                          <span>{article.readingTime} min read</span>
                        </div>
                        <div className="mt-2 flex items-center space-x-2">
                          <span className={`inline-flex px-2 py-1 text-xs leading-5 font-semibold rounded-full ${getStatusColor(article.status)}`}>
                            {article.status}
                          </span>
                          {article.tags.map(tag => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <Link
                          href={`/admin/articles-page/articles/${article.id}/edit`}
                          className="inline-flex items-center px-3 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                        >
                          <FiEdit3 className="h-4 w-4" />
                        </Link>
                        <button
                          onClick={() => handleDeleteArticle(article.id)}
                          className="inline-flex items-center px-3 py-2 border border-red-600 rounded-md text-sm font-medium text-red-400 hover:text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
} 