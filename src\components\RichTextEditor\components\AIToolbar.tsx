import React, { useState } from 'react';
import { Editor, Range, Transforms, Text } from 'slate';
import { useSlate } from 'slate-react';
import { FiCpu, FiAlertCircle, FiMessageCircle, FiZap, FiGlobe, FiCheckCircle, FiX, FiEdit3 } from 'react-icons/fi';
import { AISuggestion } from '../types';

interface AIToolbarProps {
  icon: React.ReactNode;
}

// Mock AI suggestions (in a real app, these would come from an AI API)
const mockAISuggestions: AISuggestion[] = [
  {
    id: 'sug1',
    type: 'grammar',
    description: 'Grammar suggestion',
    suggestion: 'Consider rephrasing for clarity',
    confidence: 0.85,
  },
  {
    id: 'sug2',
    type: 'style',
    description: 'Style improvement',
    suggestion: 'Use more engaging language here',
    confidence: 0.78,
  }
];

const AIToolbar: React.FC<AIToolbarProps> = ({ icon }) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<AISuggestion[]>(mockAISuggestions);
  const [loading, setLoading] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [aiAction, setAiAction] = useState<
    'improve' | 'summarize' | 'expand' | 'suggest' | 'translate' | 'tone' | null
  >(null);
  const [actionResult, setActionResult] = useState<string | null>(null);

  // Get the currently selected text
  const getSelectedText = (): string => {
    const { selection } = editor;
    
    if (!selection || Range.isCollapsed(selection)) {
      return '';
    }
    
    // Get the selected text
    const selectedText = Editor.string(editor, selection);
    return selectedText;
  };

  // Apply an AI suggestion to the selected text
  const applySuggestion = (newText: string) => {
    const { selection } = editor;
    
    if (!selection) return;
    
    // Delete the selected text and insert the new text
    Transforms.delete(editor);
    Transforms.insertText(editor, newText);
  };

  // Handle the AI action based on the selected option
  const handleAIAction = (action: 'improve' | 'summarize' | 'expand' | 'suggest' | 'translate' | 'tone') => {
    const text = getSelectedText();
    
    if (!text) {
      alert('Please select some text first');
      return;
    }
    
    setSelectedText(text);
    setAiAction(action);
    setShowOptions(false);
    setLoading(true);
    
    // In a real app, this would call an AI API
    setTimeout(() => {
      setLoading(false);
      
      // Mock responses based on the action
      let result = '';
      
      switch (action) {
        case 'improve':
          result = `${text} [Improved with clearer wording and better flow]`;
          break;
        case 'summarize':
          result = `[Summary of: "${text}"]`;
          break;
        case 'expand':
          result = `${text} [Expanded with additional supporting details and examples]`;
          break;
        case 'suggest':
          // Generate mock suggestions
          setSuggestions([
            {
              id: `sug-${Date.now()}-1`,
              type: 'grammar',
              description: 'Grammar improvement',
              suggestion: `${text} [with grammar fixes]`,
              confidence: 0.92,
            },
            {
              id: `sug-${Date.now()}-2`,
              type: 'style',
              description: 'Style enhancement',
              suggestion: `${text} [with more engaging style]`,
              confidence: 0.87,
            }
          ]);
          setShowSuggestions(true);
          return;
        case 'translate':
          result = `${text} [Translated to French]`;
          break;
        case 'tone':
          result = `${text} [Adjusted to professional tone]`;
          break;
      }
      
      setActionResult(result);
    }, 1500);
  };

  // Apply or reject the AI result
  const handleActionResult = (apply: boolean) => {
    if (apply && actionResult) {
      applySuggestion(actionResult);
    }
    
    // Reset state
    setSelectedText('');
    setAiAction(null);
    setActionResult(null);
  };

  // Handle accepting or rejecting an AI suggestion
  const handleSuggestion = (suggestionId: string, accept: boolean) => {
    const suggestion = suggestions.find(s => s.id === suggestionId);
    
    if (accept && suggestion) {
      applySuggestion(suggestion.suggestion);
    }
    
    // Mark as accepted/rejected
    setSuggestions(prev => 
      prev.map(s => 
        s.id === suggestionId
          ? { ...s, accepted: accept, rejected: !accept }
          : s
      )
    );
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      {showOptions && (
        <div className="absolute top-full left-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[200px]">
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleAIAction('improve');
            }}
          >
            <FiEdit3 size={16} />
            <span>Improve Writing</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleAIAction('summarize');
            }}
          >
            <FiMessageCircle size={16} />
            <span>Summarize</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleAIAction('expand');
            }}
          >
            <FiZap size={16} />
            <span>Expand Content</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleAIAction('suggest');
            }}
          >
            <FiAlertCircle size={16} />
            <span>Get Suggestions</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleAIAction('translate');
            }}
          >
            <FiGlobe size={16} />
            <span>Translate</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleAIAction('tone');
            }}
          >
            <FiCpu size={16} />
            <span>Adjust Tone</span>
          </button>
        </div>
      )}
      
      {loading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#141b35] p-6 rounded-lg max-w-md w-full text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[rgb(var(--color-primary))] mx-auto mb-4"></div>
            <h3 className="text-white text-lg font-medium mb-2">AI is processing your request</h3>
            <p className="text-white/70">
              {aiAction === 'improve' && 'Improving your writing...'}
              {aiAction === 'summarize' && 'Generating summary...'}
              {aiAction === 'expand' && 'Expanding your content...'}
              {aiAction === 'suggest' && 'Analyzing your text...'}
              {aiAction === 'translate' && 'Translating your text...'}
              {aiAction === 'tone' && 'Adjusting tone...'}
            </p>
          </div>
        </div>
      )}
      
      {actionResult && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#141b35] p-6 rounded-lg max-w-2xl w-full">
            <h3 className="text-white text-lg font-medium mb-4">
              {aiAction === 'improve' && 'Improved Text'}
              {aiAction === 'summarize' && 'Summary'}
              {aiAction === 'expand' && 'Expanded Content'}
              {aiAction === 'translate' && 'Translation'}
              {aiAction === 'tone' && 'Adjusted Tone'}
            </h3>
            
            <div className="mb-6">
              <div className="text-white/70 mb-2">Original Text</div>
              <div className="p-3 bg-[#0A0F23] border border-white/10 rounded-lg text-white">
                {selectedText}
              </div>
            </div>
            
            <div className="mb-6">
              <div className="text-white/70 mb-2">AI Result</div>
              <div className="p-3 bg-[#0A0F23] border border-[rgb(var(--color-primary))]/30 rounded-lg text-white">
                {actionResult}
              </div>
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg"
                onClick={() => handleActionResult(false)}
              >
                Discard
              </button>
              <button
                className="px-4 py-2 bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white rounded-lg"
                onClick={() => handleActionResult(true)}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
      
      {showSuggestions && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#141b35] p-6 rounded-lg max-w-2xl w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-white text-lg font-medium">AI Suggestions</h3>
              <button 
                className="text-white/70 hover:text-white"
                onClick={() => setShowSuggestions(false)}
              >
                ×
              </button>
            </div>
            
            <div className="mb-4">
              <div className="text-white/70 mb-2">Your Text</div>
              <div className="p-3 bg-[#0A0F23] border border-white/10 rounded-lg text-white">
                {selectedText}
              </div>
            </div>
            
            <div className="text-white/70 mb-2">Suggestions</div>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {suggestions.map((suggestion) => (
                <div 
                  key={suggestion.id} 
                  className={`p-3 rounded-lg border ${
                    suggestion.accepted 
                      ? 'bg-green-900/20 border-green-500/30' 
                      : suggestion.rejected
                      ? 'bg-red-900/20 border-red-500/30'
                      : 'bg-[#0A0F23] border-white/10'
                  }`}
                >
                  <div className="flex justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {suggestion.type === 'grammar' && <FiAlertCircle size={16} className="text-blue-400" />}
                      {suggestion.type === 'style' && <FiEdit3 size={16} className="text-purple-400" />}
                      {suggestion.type === 'content' && <FiMessageCircle size={16} className="text-yellow-400" />}
                      <span className="text-white/90">{suggestion.description}</span>
                    </div>
                    <span className="text-white/50 text-sm">
                      {Math.round(suggestion.confidence * 100)}% confidence
                    </span>
                  </div>
                  
                  <p className="text-white p-2 bg-[#1a2349] rounded-lg mb-3">
                    {suggestion.suggestion}
                  </p>
                  
                  {!suggestion.accepted && !suggestion.rejected && (
                    <div className="flex justify-end gap-2">
                      <button
                        className="text-sm px-3 py-1 bg-white/10 hover:bg-white/20 text-white rounded-lg flex items-center gap-1"
                        onClick={() => handleSuggestion(suggestion.id, false)}
                      >
                        <FiX size={14} />
                        <span>Reject</span>
                      </button>
                      <button
                        className="text-sm px-3 py-1 bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white rounded-lg flex items-center gap-1"
                        onClick={() => handleSuggestion(suggestion.id, true)}
                      >
                        <FiCheckCircle size={14} />
                        <span>Apply</span>
                      </button>
                    </div>
                  )}
                  
                  {suggestion.accepted && (
                    <div className="flex justify-end">
                      <span className="text-green-400 text-sm flex items-center gap-1">
                        <FiCheckCircle size={14} />
                        Applied
                      </span>
                    </div>
                  )}
                  
                  {suggestion.rejected && (
                    <div className="flex justify-end">
                      <span className="text-red-400 text-sm flex items-center gap-1">
                        <FiX size={14} />
                        Rejected
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIToolbar; 