"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";

// Define the Project type
type Project = {
  id: number;
  title: string;
  location: string;
  description: string;
  image: string;
  slug: string;
  category: string;
  status: string;
  features: string[];
};

type ProjectsClientProps = {
  projects: Project[];
};

const ProjectsClient = ({ projects }: ProjectsClientProps) => {
  const [activeCategory, setActiveCategory] = useState("All");
  const [activeStatus, setActiveStatus] = useState("All");

  const categories = ["All", "Residential", "Commercial", "Mixed-Use"];
  const statuses = ["All", "Completed", "In Progress", "Planned"];

  const filteredProjects = projects.filter((project) => {
    return (
      (activeCategory === "All" || project.category === activeCategory) &&
      (activeStatus === "All" || project.status === activeStatus)
    );
  });

  return (
    <>
      {/* Filters */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 space-y-4 md:space-y-0">
        <div>
          <h2 className="text-2xl font-bold mb-2">Project Categories</h2>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeCategory === category
                    ? "bg-white text-gray-900"
                    : "bg-white/10 text-white border border-white/20 hover:bg-white/15"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
        <div>
          <h2 className="text-2xl font-bold mb-2">Project Status</h2>
          <div className="flex flex-wrap gap-2">
            {statuses.map((status) => (
              <button
                key={status}
                onClick={() => setActiveStatus(status)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeStatus === status
                    ? "bg-white text-gray-900"
                    : "bg-white/10 text-white border border-white/20 hover:bg-white/15"
                }`}
              >
                {status}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      {filteredProjects.length > 0 ? (
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="group h-full flex flex-col bg-white/10 backdrop-blur-md border border-white/20 rounded-xl overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:border-white/30">
                {/* Image container - Adjusted for better aspect ratio */}
                <div className="relative w-full aspect-[4/3] overflow-hidden">
                  {/* Category badge */}
                  <div className="absolute top-3 start-3 z-10">
                    <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-white/10 backdrop-blur-md border border-white/20 text-white">
                      {project.category}
                    </span>
                  </div>
                  
                  {/* Status badge */}
                  <div className="absolute top-3 end-3 z-10">
                    <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full backdrop-blur-md border border-white/20 ${
                      project.status === "Completed" 
                        ? "bg-[#0ec6e0]/80 text-white" 
                        : project.status === "In Progress" 
                        ? "bg-amber-500/80 text-white"
                        : "bg-purple-500/80 text-white"
                    }`}>
                      {project.status}
                    </span>
                  </div>
                  
                  {/* Project image */}
                  <div className="absolute inset-0 bg-[#1a1a2e] flex items-center justify-center group-hover:scale-110 transition-transform duration-700 ease-in-out">
                    <span className="text-[rgb(var(--color-text))]/70">{project.title}</span>
                  </div>
                  
                  {/* Overlay gradient - matching Hero style */}
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-950 via-gray-950/40 to-transparent opacity-80 z-0"></div>
                </div>
                
                {/* Content */}
                <div className="flex flex-col flex-grow p-5 relative">
                  {/* Title */}
                  <h3 className="text-xl font-bold mb-1 text-white transition-all duration-300 relative inline-block">
                    <span className="relative z-10 group-hover:text-[rgb(var(--color-primary))]">{project.title}</span>
                    <span className="absolute bottom-0 start-0 w-0 h-0.5 bg-[rgb(var(--color-primary))]/70 group-hover:w-full transition-all duration-500"></span>
                  </h3>
                  
                  {/* Location */}
                  <div className="flex items-center text-gray-300 mb-2 text-sm">
                    <svg className="h-4 w-4 me-1 text-[rgb(var(--color-primary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {project.location}
                  </div>
                  
                  {/* Description */}
                  <p className="text-gray-300 text-sm mb-4">
                    {project.description}
                  </p>
                  
                  {/* Features */}
                  <div className="flex flex-wrap gap-2 mb-5">
                    {project.features.slice(0, 3).map((feature, index) => (
                      <span 
                        key={index} 
                        className="px-3 py-1 bg-white/10 text-white text-xs rounded-full border border-white/20 backdrop-blur-sm transition-all duration-300 hover:bg-white/15"
                      >
                        {feature}
                      </span>
                    ))}
                    {project.features.length > 3 && (
                      <span className="px-3 py-1 bg-[rgb(var(--color-primary))]/10 text-[rgb(var(--color-primary))] text-xs rounded-full border border-[rgb(var(--color-primary))]/20">
                        +{project.features.length - 3} more
                      </span>
                    )}
                  </div>
                  
                  {/* View Details Link - updated to match Hero buttons */}
                  <div className="mt-auto">
                    <Link
                      href={`/projects/${project.slug}`}
                      className="inline-flex items-center rounded-full border border-white/30 bg-transparent px-4 py-2 text-sm font-medium text-white transition hover:bg-white/10"
                    >
                      <span>View Details</span>
                      <svg className="ms-2 h-4 w-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="text-center py-12 bg-[rgb(var(--color-text))]/5 rounded-xl border border-[rgb(var(--color-text))]/10">
          <svg className="w-16 h-16 mx-auto mb-4 text-[rgb(var(--color-text))]/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 2a10 10 0 110 20 10 10 0 010-20z" />
          </svg>
          <h3 className="text-xl font-bold mb-2">No projects found</h3>
          <p className="text-[rgb(var(--color-text))]/70">Try changing your filter criteria</p>
        </div>
      )}
    </>
  );
};

export default ProjectsClient;