"use client";

import React, { useState } from 'react';
import { 
  FiSearch, FiGlobe, FiCode, FiFileText, FiTrendingUp, 
  FiSettings, FiEye, FiEyeOff, FiPlus, FiTrash2, FiMapPin,
  FiImage, FiLink, FiTarget, FiBarChart3, FiCheckCircle, FiAlertCircle
} from 'react-icons/fi';

interface SEOSettingsData {
  general: {
    siteTitle: {
      en: string;
      ar: string;
    };
    siteDescription: {
      en: string;
      ar: string;
    };
    keywords: {
      en: string[];
      ar: string[];
    };
    canonicalUrl: string;
    hreflang: {
      enabled: boolean;
      defaultLanguage: 'en' | 'ar';
      alternateUrls: {
        en: string;
        ar: string;
      };
    };
  };
  metaTags: {
    robots: {
      index: boolean;
      follow: boolean;
      archive: boolean;
      snippet: boolean;
      imageIndex: boolean;
      maxSnippet: number;
      maxImagePreview: 'none' | 'standard' | 'large';
      maxVideoPreview: number;
    };
    openGraph: {
      enabled: boolean;
      type: 'website' | 'business.business';
      title: {
        en: string;
        ar: string;
      };
      description: {
        en: string;
        ar: string;
      };
      image: string;
      imageAlt: {
        en: string;
        ar: string;
      };
      url: string;
      siteName: {
        en: string;
        ar: string;
      };
      locale: {
        en: string;
        ar: string;
      };
    };
    twitter: {
      enabled: boolean;
      card: 'summary' | 'summary_large_image' | 'app' | 'player';
      site: string;
      creator: string;
      title: {
        en: string;
        ar: string;
      };
      description: {
        en: string;
        ar: string;
      };
      image: string;
      imageAlt: {
        en: string;
        ar: string;
      };
    };
    customMeta: {
      name: string;
      content: string;
      property?: string;
    }[];
  };
  structuredData: {
    enabled: boolean;
    organization: {
      enabled: boolean;
      name: {
        en: string;
        ar: string;
      };
      description: {
        en: string;
        ar: string;
      };
      url: string;
      logo: string;
      contactPoint: {
        telephone: string;
        contactType: string;
        areaServed: string[];
        availableLanguage: string[];
      };
      address: {
        streetAddress: {
          en: string;
          ar: string;
        };
        addressLocality: {
          en: string;
          ar: string;
        };
        addressRegion: {
          en: string;
          ar: string;
        };
        postalCode: string;
        addressCountry: string;
      };
      sameAs: string[];
    };
    realEstate: {
      enabled: boolean;
      businessType: 'RealEstateAgent' | 'RealEstateBusiness';
      priceRange: string;
      paymentAccepted: string[];
      currenciesAccepted: string[];
      openingHours: string[];
    };
    breadcrumbs: {
      enabled: boolean;
      separator: string;
      showHome: boolean;
    };
    faq: {
      enabled: boolean;
      questions: {
        question: {
          en: string;
          ar: string;
        };
        answer: {
          en: string;
          ar: string;
        };
      }[];
    };
  };
  sitemaps: {
    enabled: boolean;
    autoGenerate: boolean;
    includeImages: boolean;
    includeVideos: boolean;
    changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
    priority: number;
    excludePages: string[];
    customSitemaps: {
      name: string;
      url: string;
      type: 'xml' | 'txt';
    }[];
  };
  robotsTxt: {
    enabled: boolean;
    userAgent: string;
    disallow: string[];
    allow: string[];
    crawlDelay: number;
    sitemapUrl: string;
    customDirectives: {
      directive: string;
      value: string;
    }[];
  };
  analytics: {
    googleSearchConsole: {
      enabled: boolean;
      verificationCode: string;
      siteUrl: string;
    };
    bingWebmaster: {
      enabled: boolean;
      verificationCode: string;
    };
    yandexWebmaster: {
      enabled: boolean;
      verificationCode: string;
    };
    googleAnalytics: {
      enabled: boolean;
      trackingId: string;
      enhancedEcommerce: boolean;
    };
  };
  localSEO: {
    enabled: boolean;
    businessName: {
      en: string;
      ar: string;
    };
    businessType: string;
    address: {
      street: {
        en: string;
        ar: string;
      };
      city: {
        en: string;
        ar: string;
      };
      state: {
        en: string;
        ar: string;
      };
      zipCode: string;
      country: {
        en: string;
        ar: string;
      };
    };
    coordinates: {
      latitude: number;
      longitude: number;
    };
    phone: string;
    email: string;
    website: string;
    hours: {
      [key: string]: {
        open: string;
        close: string;
        closed: boolean;
      };
    };
    googleMyBusiness: {
      enabled: boolean;
      placeId: string;
      reviewsWidget: boolean;
    };
  };
  realEstateSpecific: {
    propertyTypes: string[];
    serviceAreas: {
      name: {
        en: string;
        ar: string;
      };
      coordinates: {
        latitude: number;
        longitude: number;
      };
      radius: number;
    }[];
    specializations: {
      en: string[];
      ar: string[];
    };
    certifications: string[];
    languages: string[];
  };
  monitoring: {
    enabled: boolean;
    keywordTracking: {
      enabled: boolean;
      keywords: {
        keyword: string;
        language: 'en' | 'ar';
        targetUrl: string;
        priority: 'high' | 'medium' | 'low';
      }[];
    };
    competitorAnalysis: {
      enabled: boolean;
      competitors: {
        name: string;
        url: string;
        trackKeywords: boolean;
      }[];
    };
    rankingReports: {
      enabled: boolean;
      frequency: 'daily' | 'weekly' | 'monthly';
      recipients: string[];
    };
  };
}

interface SEOSettingsProps {
  data: SEOSettingsData;
  onChange: (data: SEOSettingsData) => void;
}

export default function SEOSettings({ data, onChange }: SEOSettingsProps) {
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({});
  const [newKeyword, setNewKeyword] = useState('');
  const [newKeywordLang, setNewKeywordLang] = useState<'en' | 'ar'>('en');
  const [newEmail, setNewEmail] = useState('');

  const updateData = (updates: Partial<SEOSettingsData>) => {
    onChange({ ...data, ...updates });
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const addKeyword = (language: 'en' | 'ar') => {
    if (newKeyword.trim()) {
      updateData({
        general: {
          ...data.general,
          keywords: {
            ...data.general.keywords,
            [language]: [...data.general.keywords[language], newKeyword.trim()]
          }
        }
      });
      setNewKeyword('');
    }
  };

  const removeKeyword = (language: 'en' | 'ar', index: number) => {
    updateData({
      general: {
        ...data.general,
        keywords: {
          ...data.general.keywords,
          [language]: data.general.keywords[language].filter((_, i) => i !== index)
        }
      }
    });
  };

  const addCustomMeta = () => {
    updateData({
      metaTags: {
        ...data.metaTags,
        customMeta: [...data.metaTags.customMeta, { name: '', content: '', property: '' }]
      }
    });
  };

  const removeCustomMeta = (index: number) => {
    updateData({
      metaTags: {
        ...data.metaTags,
        customMeta: data.metaTags.customMeta.filter((_, i) => i !== index)
      }
    });
  };

  const addFAQ = () => {
    updateData({
      structuredData: {
        ...data.structuredData,
        faq: {
          ...data.structuredData.faq,
          questions: [...data.structuredData.faq.questions, {
            question: { en: '', ar: '' },
            answer: { en: '', ar: '' }
          }]
        }
      }
    });
  };

  const removeFAQ = (index: number) => {
    updateData({
      structuredData: {
        ...data.structuredData,
        faq: {
          ...data.structuredData.faq,
          questions: data.structuredData.faq.questions.filter((_, i) => i !== index)
        }
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* General SEO Settings */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiSearch className="mr-2 h-5 w-5 text-[#00C2FF]" />
          General SEO Settings
        </h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Site Title (English)</label>
              <input
                type="text"
                value={data.general.siteTitle.en}
                onChange={(e) => updateData({
                  general: {
                    ...data.general,
                    siteTitle: { ...data.general.siteTitle, en: e.target.value }
                  }
                })}
                placeholder="Mazaya Capital - Premier Real Estate in Dubai"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Site Title (Arabic)</label>
              <input
                type="text"
                value={data.general.siteTitle.ar}
                onChange={(e) => updateData({
                  general: {
                    ...data.general,
                    siteTitle: { ...data.general.siteTitle, ar: e.target.value }
                  }
                })}
                placeholder="مزايا كابيتال - العقارات الراقية في دبي"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Meta Description (English)</label>
              <textarea
                value={data.general.siteDescription.en}
                onChange={(e) => updateData({
                  general: {
                    ...data.general,
                    siteDescription: { ...data.general.siteDescription, en: e.target.value }
                  }
                })}
                rows={3}
                placeholder="Discover luxury properties in Dubai with Mazaya Capital. Expert real estate services, premium locations, and exceptional investment opportunities."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
              <p className="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Meta Description (Arabic)</label>
              <textarea
                value={data.general.siteDescription.ar}
                onChange={(e) => updateData({
                  general: {
                    ...data.general,
                    siteDescription: { ...data.general.siteDescription, ar: e.target.value }
                  }
                })}
                rows={3}
                placeholder="اكتشف العقارات الفاخرة في دبي مع مزايا كابيتال. خدمات عقارية متخصصة ومواقع مميزة وفرص استثمارية استثنائية."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
              <p className="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Canonical URL</label>
            <input
              type="url"
              value={data.general.canonicalUrl}
              onChange={(e) => updateData({
                general: { ...data.general, canonicalUrl: e.target.value }
              })}
              placeholder="https://mazayacapital.com"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          {/* Keywords */}
          <div>
            <h4 className="text-white font-medium mb-3">SEO Keywords</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">English Keywords</label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    value={newKeywordLang === 'en' ? newKeyword : ''}
                    onChange={(e) => {
                      setNewKeyword(e.target.value);
                      setNewKeywordLang('en');
                    }}
                    placeholder="real estate dubai, luxury properties"
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    onClick={() => addKeyword('en')}
                    className="px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#00C2FF]/90 transition-colors"
                  >
                    <FiPlus className="h-4 w-4" />
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {data.general.keywords.en.map((keyword, index) => (
                    <span key={index} className="inline-flex items-center px-3 py-1 bg-gray-700 text-white text-sm rounded-full">
                      {keyword}
                      <button
                        onClick={() => removeKeyword('en', index)}
                        className="ml-2 text-red-400 hover:text-red-300"
                      >
                        <FiTrash2 className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Arabic Keywords</label>
                <div className="flex space-x-2 mb-2">
                  <input
                    type="text"
                    value={newKeywordLang === 'ar' ? newKeyword : ''}
                    onChange={(e) => {
                      setNewKeyword(e.target.value);
                      setNewKeywordLang('ar');
                    }}
                    placeholder="عقارات دبي، عقارات فاخرة"
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    onClick={() => addKeyword('ar')}
                    className="px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#00C2FF]/90 transition-colors"
                  >
                    <FiPlus className="h-4 w-4" />
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {data.general.keywords.ar.map((keyword, index) => (
                    <span key={index} className="inline-flex items-center px-3 py-1 bg-gray-700 text-white text-sm rounded-full">
                      {keyword}
                      <button
                        onClick={() => removeKeyword('ar', index)}
                        className="ml-2 text-red-400 hover:text-red-300"
                      >
                        <FiTrash2 className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Hreflang */}
          <div className="p-4 bg-gray-700/30 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="text-white font-medium">Hreflang Tags</h4>
                <p className="text-gray-400 text-sm">International targeting for multilingual content</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.general.hreflang.enabled}
                  onChange={(e) => updateData({
                    general: {
                      ...data.general,
                      hreflang: { ...data.general.hreflang, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.general.hreflang.enabled && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Default Language</label>
                  <select
                    value={data.general.hreflang.defaultLanguage}
                    onChange={(e) => updateData({
                      general: {
                        ...data.general,
                        hreflang: { ...data.general.hreflang, defaultLanguage: e.target.value as 'en' | 'ar' }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  >
                    <option value="en">English</option>
                    <option value="ar">Arabic</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">English URL</label>
                  <input
                    type="url"
                    value={data.general.hreflang.alternateUrls.en}
                    onChange={(e) => updateData({
                      general: {
                        ...data.general,
                        hreflang: {
                          ...data.general.hreflang,
                          alternateUrls: { ...data.general.hreflang.alternateUrls, en: e.target.value }
                        }
                      }
                    })}
                    placeholder="https://mazayacapital.com/en"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Arabic URL</label>
                  <input
                    type="url"
                    value={data.general.hreflang.alternateUrls.ar}
                    onChange={(e) => updateData({
                      general: {
                        ...data.general,
                        hreflang: {
                          ...data.general.hreflang,
                          alternateUrls: { ...data.general.hreflang.alternateUrls, ar: e.target.value }
                        }
                      }
                    })}
                    placeholder="https://mazayacapital.com/ar"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Meta Tags & Social Media */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiCode className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Meta Tags & Social Media
        </h3>
        <div className="space-y-6">
          {/* Robots Meta */}
          <div>
            <h4 className="text-white font-medium mb-3">Robots Meta Tags</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Index</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.metaTags.robots.index}
                    onChange={(e) => updateData({
                      metaTags: {
                        ...data.metaTags,
                        robots: { ...data.metaTags.robots, index: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Follow</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.metaTags.robots.follow}
                    onChange={(e) => updateData({
                      metaTags: {
                        ...data.metaTags,
                        robots: { ...data.metaTags.robots, follow: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Archive</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.metaTags.robots.archive}
                    onChange={(e) => updateData({
                      metaTags: {
                        ...data.metaTags,
                        robots: { ...data.metaTags.robots, archive: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Image Index</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.metaTags.robots.imageIndex}
                    onChange={(e) => updateData({
                      metaTags: {
                        ...data.metaTags,
                        robots: { ...data.metaTags.robots, imageIndex: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Snippet Length</label>
                <input
                  type="number"
                  value={data.metaTags.robots.maxSnippet}
                  onChange={(e) => updateData({
                    metaTags: {
                      ...data.metaTags,
                      robots: { ...data.metaTags.robots, maxSnippet: parseInt(e.target.value) }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="-1"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Image Preview</label>
                <select
                  value={data.metaTags.robots.maxImagePreview}
                  onChange={(e) => updateData({
                    metaTags: {
                      ...data.metaTags,
                      robots: { ...data.metaTags.robots, maxImagePreview: e.target.value as 'none' | 'standard' | 'large' }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="none">None</option>
                  <option value="standard">Standard</option>
                  <option value="large">Large</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Video Preview (seconds)</label>
                <input
                  type="number"
                  value={data.metaTags.robots.maxVideoPreview}
                  onChange={(e) => updateData({
                    metaTags: {
                      ...data.metaTags,
                      robots: { ...data.metaTags.robots, maxVideoPreview: parseInt(e.target.value) }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="-1"
                />
              </div>
            </div>
          </div>

          {/* Open Graph */}
          <div className="p-4 bg-gray-700/30 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="text-white font-medium">Open Graph (Facebook)</h4>
                <p className="text-gray-400 text-sm">Social media sharing optimization</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.metaTags.openGraph.enabled}
                  onChange={(e) => updateData({
                    metaTags: {
                      ...data.metaTags,
                      openGraph: { ...data.metaTags.openGraph, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.metaTags.openGraph.enabled && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">OG Type</label>
                    <select
                      value={data.metaTags.openGraph.type}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          openGraph: { ...data.metaTags.openGraph, type: e.target.value as 'website' | 'business.business' }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    >
                      <option value="website">Website</option>
                      <option value="business.business">Business</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">OG URL</label>
                    <input
                      type="url"
                      value={data.metaTags.openGraph.url}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          openGraph: { ...data.metaTags.openGraph, url: e.target.value }
                        }
                      })}
                      placeholder="https://mazayacapital.com"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">OG Title (English)</label>
                    <input
                      type="text"
                      value={data.metaTags.openGraph.title.en}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          openGraph: {
                            ...data.metaTags.openGraph,
                            title: { ...data.metaTags.openGraph.title, en: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">OG Title (Arabic)</label>
                    <input
                      type="text"
                      value={data.metaTags.openGraph.title.ar}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          openGraph: {
                            ...data.metaTags.openGraph,
                            title: { ...data.metaTags.openGraph.title, ar: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">OG Image URL</label>
                  <input
                    type="url"
                    value={data.metaTags.openGraph.image}
                    onChange={(e) => updateData({
                      metaTags: {
                        ...data.metaTags,
                        openGraph: { ...data.metaTags.openGraph, image: e.target.value }
                      }
                    })}
                    placeholder="https://mazayacapital.com/og-image.jpg"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <p className="text-xs text-gray-500 mt-1">Recommended: 1200x630 pixels</p>
                </div>
              </div>
            )}
          </div>

          {/* Twitter Cards */}
          <div className="p-4 bg-gray-700/30 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div>
                <h4 className="text-white font-medium">Twitter Cards</h4>
                <p className="text-gray-400 text-sm">Twitter sharing optimization</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.metaTags.twitter.enabled}
                  onChange={(e) => updateData({
                    metaTags: {
                      ...data.metaTags,
                      twitter: { ...data.metaTags.twitter, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.metaTags.twitter.enabled && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Card Type</label>
                    <select
                      value={data.metaTags.twitter.card}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          twitter: { ...data.metaTags.twitter, card: e.target.value as 'summary' | 'summary_large_image' | 'app' | 'player' }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    >
                      <option value="summary">Summary</option>
                      <option value="summary_large_image">Summary Large Image</option>
                      <option value="app">App</option>
                      <option value="player">Player</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Site</label>
                    <input
                      type="text"
                      value={data.metaTags.twitter.site}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          twitter: { ...data.metaTags.twitter, site: e.target.value }
                        }
                      })}
                      placeholder="@mazayacapital"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Creator</label>
                    <input
                      type="text"
                      value={data.metaTags.twitter.creator}
                      onChange={(e) => updateData({
                        metaTags: {
                          ...data.metaTags,
                          twitter: { ...data.metaTags.twitter, creator: e.target.value }
                        }
                      })}
                      placeholder="@mazayacapital"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Structured Data */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiFileText className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Structured Data (Schema.org)
        </h3>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Structured Data</p>
              <p className="text-gray-400 text-sm">Help search engines understand your content better</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.structuredData.enabled}
                onChange={(e) => updateData({
                  structuredData: { ...data.structuredData, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.structuredData.enabled && (
            <div className="space-y-6">
              {/* Organization Schema */}
              <div className="p-4 bg-gray-700/30 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-medium">Organization Schema</h4>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={data.structuredData.organization.enabled}
                      onChange={(e) => updateData({
                        structuredData: {
                          ...data.structuredData,
                          organization: { ...data.structuredData.organization, enabled: e.target.checked }
                        }
                      })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                  </label>
                </div>
                
                {data.structuredData.organization.enabled && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-2">Organization Name (English)</label>
                        <input
                          type="text"
                          value={data.structuredData.organization.name.en}
                          onChange={(e) => updateData({
                            structuredData: {
                              ...data.structuredData,
                              organization: {
                                ...data.structuredData.organization,
                                name: { ...data.structuredData.organization.name, en: e.target.value }
                              }
                            }
                          })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-2">Organization Name (Arabic)</label>
                        <input
                          type="text"
                          value={data.structuredData.organization.name.ar}
                          onChange={(e) => updateData({
                            structuredData: {
                              ...data.structuredData,
                              organization: {
                                ...data.structuredData.organization,
                                name: { ...data.structuredData.organization.name, ar: e.target.value }
                              }
                            }
                          })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-2">Organization URL</label>
                        <input
                          type="url"
                          value={data.structuredData.organization.url}
                          onChange={(e) => updateData({
                            structuredData: {
                              ...data.structuredData,
                              organization: { ...data.structuredData.organization, url: e.target.value }
                            }
                          })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-2">Logo URL</label>
                        <input
                          type="url"
                          value={data.structuredData.organization.logo}
                          onChange={(e) => updateData({
                            structuredData: {
                              ...data.structuredData,
                              organization: { ...data.structuredData.organization, logo: e.target.value }
                            }
                          })}
                          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* FAQ Schema */}
              <div className="p-4 bg-gray-700/30 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-medium">FAQ Schema</h4>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={addFAQ}
                      className="px-3 py-1 bg-[#00C2FF] text-white rounded-md hover:bg-[#00C2FF]/90 transition-colors text-sm"
                    >
                      <FiPlus className="h-4 w-4" />
                    </button>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.structuredData.faq.enabled}
                        onChange={(e) => updateData({
                          structuredData: {
                            ...data.structuredData,
                            faq: { ...data.structuredData.faq, enabled: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                </div>
                
                {data.structuredData.faq.enabled && (
                  <div className="space-y-4">
                    {data.structuredData.faq.questions.map((faq, index) => (
                      <div key={index} className="p-3 bg-gray-800 rounded-lg">
                        <div className="flex justify-between items-center mb-3">
                          <h5 className="text-white text-sm font-medium">FAQ #{index + 1}</h5>
                          <button
                            onClick={() => removeFAQ(index)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <FiTrash2 className="h-4 w-4" />
                          </button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-400 mb-2">Question (English)</label>
                            <input
                              type="text"
                              value={faq.question.en}
                              onChange={(e) => {
                                const newQuestions = [...data.structuredData.faq.questions];
                                newQuestions[index].question.en = e.target.value;
                                updateData({
                                  structuredData: {
                                    ...data.structuredData,
                                    faq: { ...data.structuredData.faq, questions: newQuestions }
                                  }
                                });
                              }}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-400 mb-2">Question (Arabic)</label>
                            <input
                              type="text"
                              value={faq.question.ar}
                              onChange={(e) => {
                                const newQuestions = [...data.structuredData.faq.questions];
                                newQuestions[index].question.ar = e.target.value;
                                updateData({
                                  structuredData: {
                                    ...data.structuredData,
                                    faq: { ...data.structuredData.faq, questions: newQuestions }
                                  }
                                });
                              }}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            />
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-400 mb-2">Answer (English)</label>
                            <textarea
                              value={faq.answer.en}
                              onChange={(e) => {
                                const newQuestions = [...data.structuredData.faq.questions];
                                newQuestions[index].answer.en = e.target.value;
                                updateData({
                                  structuredData: {
                                    ...data.structuredData,
                                    faq: { ...data.structuredData.faq, questions: newQuestions }
                                  }
                                });
                              }}
                              rows={3}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-400 mb-2">Answer (Arabic)</label>
                            <textarea
                              value={faq.answer.ar}
                              onChange={(e) => {
                                const newQuestions = [...data.structuredData.faq.questions];
                                newQuestions[index].answer.ar = e.target.value;
                                updateData({
                                  structuredData: {
                                    ...data.structuredData,
                                    faq: { ...data.structuredData.faq, questions: newQuestions }
                                  }
                                });
                              }}
                              rows={3}
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Local SEO */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiMapPin className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Local SEO
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Local SEO</p>
              <p className="text-gray-400 text-sm">Optimize for local search results in Dubai and UAE</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.localSEO.enabled}
                onChange={(e) => updateData({
                  localSEO: { ...data.localSEO, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.localSEO.enabled && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Business Name (English)</label>
                  <input
                    type="text"
                    value={data.localSEO.businessName.en}
                    onChange={(e) => updateData({
                      localSEO: {
                        ...data.localSEO,
                        businessName: { ...data.localSEO.businessName, en: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Business Name (Arabic)</label>
                  <input
                    type="text"
                    value={data.localSEO.businessName.ar}
                    onChange={(e) => updateData({
                      localSEO: {
                        ...data.localSEO,
                        businessName: { ...data.localSEO.businessName, ar: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Latitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    value={data.localSEO.coordinates.latitude}
                    onChange={(e) => updateData({
                      localSEO: {
                        ...data.localSEO,
                        coordinates: { ...data.localSEO.coordinates, latitude: parseFloat(e.target.value) }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Longitude</label>
                  <input
                    type="number"
                    step="0.000001"
                    value={data.localSEO.coordinates.longitude}
                    onChange={(e) => updateData({
                      localSEO: {
                        ...data.localSEO,
                        coordinates: { ...data.localSEO.coordinates, longitude: parseFloat(e.target.value) }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Phone Number</label>
                  <input
                    type="tel"
                    value={data.localSEO.phone}
                    onChange={(e) => updateData({
                      localSEO: { ...data.localSEO, phone: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Email</label>
                  <input
                    type="email"
                    value={data.localSEO.email}
                    onChange={(e) => updateData({
                      localSEO: { ...data.localSEO, email: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Website</label>
                  <input
                    type="url"
                    value={data.localSEO.website}
                    onChange={(e) => updateData({
                      localSEO: { ...data.localSEO, website: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* SEO Tips */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 h-5 w-5 text-[#00C2FF]" />
          SEO Optimization Tips
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <FiCheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Optimize Title Tags</h4>
                <p className="text-gray-400 text-xs">Keep titles under 60 characters and include target keywords</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiCheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Use Structured Data</h4>
                <p className="text-gray-400 text-xs">Help search engines understand your real estate content</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiCheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Local SEO Focus</h4>
                <p className="text-gray-400 text-xs">Target Dubai and UAE-specific keywords and locations</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <FiAlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Mobile Optimization</h4>
                <p className="text-gray-400 text-xs">Ensure all pages are mobile-friendly and fast loading</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiAlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Image Alt Text</h4>
                <p className="text-gray-400 text-xs">Add descriptive alt text to all property images</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiAlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Regular Content Updates</h4>
                <p className="text-gray-400 text-xs">Keep property listings and blog content fresh and updated</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 