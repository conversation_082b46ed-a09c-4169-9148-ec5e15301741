"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { motion, useAnimation } from "framer-motion";

// Mock article data
const articles = [
  {
    id: 1,
    title: "The Future of Urban Development in the UAE",
    excerpt: "Exploring innovative approaches to urban planning and sustainable development in the UAE's growing cities.",
    date: "June 20, 2023",
    category: "Urban Planning",
    image: "/images/article-1.jpg",
    slug: "future-urban-development-uae",
  },
  {
    id: 2,
    title: "Investment Opportunities in Dubai's Real Estate Market",
    excerpt: "A comprehensive analysis of current trends and future projections for Dubai's dynamic real estate market.",
    date: "May 15, 2023",
    category: "Investment",
    image: "/images/article-2.jpg",
    slug: "investment-opportunities-dubai-real-estate",
  },
  {
    id: 3,
    title: "Sustainable Architecture Trends in Commercial Buildings",
    excerpt: "How sustainability is reshaping commercial architecture and creating value for investors and occupants.",
    date: "April 8, 2023",
    category: "Architecture",
    image: "/images/article-3.jpg",
    slug: "sustainable-architecture-trends-commercial",
  },
  {
    id: 4,
    title: "The Rise of Smart Homes in Residential Developments",
    excerpt: "Examining the integration of smart home technology in modern residential properties and its impact on lifestyle.",
    date: "March 22, 2023",
    category: "Technology",
    image: "/images/article-4.jpg",
    slug: "rise-smart-homes-residential",
  },
  {
    id: 5,
    title: "Navigating Regulatory Changes in UAE Real Estate",
    excerpt: "A guide to understanding recent regulatory developments and their implications for developers and investors.",
    date: "February 10, 2023",
    category: "Regulations",
    image: "/images/article-5.jpg",
    slug: "navigating-regulatory-changes-uae",
  },
  {
    id: 6,
    title: "Mixed-Use Developments: Creating Vibrant Communities",
    excerpt: "How mixed-use projects are transforming urban landscapes and creating sustainable, livable communities.",
    date: "January 5, 2023",
    category: "Development",
    image: "/images/article-6.jpg",
    slug: "mixed-use-developments-vibrant-communities",
  },
  {
    id: 7,
    title: "Luxury Real Estate Trends in Dubai for 2024",
    excerpt: "An insider look at the evolving luxury property market in Dubai and what high-net-worth investors are seeking today.",
    date: "July 12, 2023",
    category: "Investment",
    image: "/images/article-7.jpg",
    slug: "luxury-real-estate-trends-dubai-2024",
  },
  {
    id: 8,
    title: "Green Building Certifications in the Middle East",
    excerpt: "Understanding LEED, BREEAM, and other sustainability certifications that are reshaping construction standards in the region.",
    date: "August 5, 2023",
    category: "Architecture",
    image: "/images/article-8.jpg",
    slug: "green-building-certifications-middle-east",
  },
  {
    id: 9,
    title: "PropTech Revolution: How Technology is Transforming Real Estate",
    excerpt: "From AI property management to blockchain transactions, discover how technology is revolutionizing every aspect of real estate.",
    date: "September 18, 2023",
    category: "Technology",
    image: "/images/article-9.jpg",
    slug: "proptech-revolution-technology-real-estate",
  },
];

// Categories for filter buttons
const categories = ["All", "Investment", "Development", "Architecture", "Technology", "Regulations", "Urban Planning"];

// Background Decorative Element Component
const BackgroundDecorations = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none -z-10">
      {/* Grid lines */}
      <div className="absolute inset-0">
        {Array.from({ length: 6 }).map((_, i) => (
          <motion.div
            key={`h-${i}`}
            className="absolute start-0 end-0 h-[1px] bg-[rgb(var(--color-primary))]/10"
            style={{ top: `${(i + 1) * 15}%` }}
            initial={{ scaleX: 0, opacity: 0 }}
            animate={{ 
              scaleX: 1, 
              opacity: [0.1, 0.3, 0.1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut",
            }}
          />
        ))}
        
        {Array.from({ length: 6 }).map((_, i) => (
          <motion.div
            key={`v-${i}`}
            className="absolute top-0 bottom-0 w-[1px] bg-[rgb(var(--color-secondary))]/10"
            style={{ left: `${(i + 1) * 15}%` }}
            initial={{ scaleY: 0, opacity: 0 }}
            animate={{ 
              scaleY: 1, 
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              delay: i * 0.3,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
      
      {/* Pulsing dots - Static positions for SSR compatibility */}
      {[
        { size: 4, x: 69, y: 48, isPrimary: true, duration: 2.5, delay: 0.5 },
        { size: 7, x: 10, y: 27, isPrimary: false, duration: 1.8, delay: 1.2 },
        { size: 2, x: 67, y: 31, isPrimary: false, duration: 3.2, delay: 0.8 },
        { size: 4, x: 11, y: 33, isPrimary: true, duration: 2.1, delay: 1.5 },
        { size: 3, x: 33, y: 85, isPrimary: false, duration: 2.8, delay: 0.3 },
        { size: 5, x: 48, y: 25, isPrimary: true, duration: 1.5, delay: 1.8 },
        { size: 3, x: 27, y: 88, isPrimary: false, duration: 2.3, delay: 0.9 },
        { size: 7, x: 26, y: 29, isPrimary: true, duration: 3.0, delay: 0.1 },
        { size: 3, x: 50, y: 34, isPrimary: true, duration: 1.9, delay: 1.6 },
        { size: 4, x: 26, y: 65, isPrimary: true, duration: 2.7, delay: 0.6 },
        { size: 2, x: 19, y: 66, isPrimary: true, duration: 1.7, delay: 1.1 },
        { size: 2, x: 61, y: 68, isPrimary: true, duration: 2.4, delay: 1.3 }
      ].map((dot, i) => (
        <motion.div
          key={`dot-${i}`}
          className={`absolute ${dot.isPrimary ? 'bg-[rgb(var(--color-primary))]' : 'bg-[rgb(var(--color-secondary))]'} rounded-full`}
          style={{
            width: `${dot.size}px`,
            height: `${dot.size}px`,
            left: `${dot.x}%`,
            top: `${dot.y}%`,
          }}
          animate={{ 
            opacity: [0.2, 0.7, 0.2],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: dot.duration,
            repeat: Infinity,
            delay: dot.delay,
            ease: "easeInOut",
          }}
        />
      ))}
      
      {/* Floating shapes */}
      <motion.div
        className="absolute top-[20%] start-[10%] w-32 h-32 rounded-full bg-[rgb(var(--color-primary))]/5 blur-xl"
        animate={{ 
          scale: [1, 1.2, 1],
          opacity: [0.3, 0.5, 0.3],
          y: [0, -20, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div
        className="absolute bottom-[10%] end-[5%] w-40 h-40 rounded-full bg-[rgb(var(--color-secondary))]/5 blur-xl"
        animate={{ 
          scale: [1, 1.3, 1],
          opacity: [0.2, 0.4, 0.2],
          y: [0, 20, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />
      
      {/* Light beams */}
      <motion.div
        className="absolute top-0 start-1/4 w-[1px] h-[40%] bg-gradient-to-b from-[rgb(var(--color-primary))]/0 via-[rgb(var(--color-primary))]/20 to-[rgb(var(--color-primary))]/0 rotate-12"
        animate={{ 
          opacity: [0.1, 0.3, 0.1],
          height: ["30%", "40%", "30%"],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div
        className="absolute bottom-0 end-1/3 w-[1px] h-[30%] bg-gradient-to-b from-[rgb(var(--color-secondary))]/0 via-[rgb(var(--color-secondary))]/15 to-[rgb(var(--color-secondary))]/0 -rotate-12"
        animate={{ 
          opacity: [0.1, 0.2, 0.1],
          height: ["20%", "30%", "20%"],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />
    </div>
  );
};

// Border effect component that follows mouse movement
const InteractiveBorder = ({ children }: { children: React.ReactNode }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left; // x position within the element
    const y = e.clientY - rect.top; // y position within the element
    
    // Calculate position relative to element (0-1)
    const relativeX = x / rect.width;
    const relativeY = y / rect.height;
    
    setMousePosition({ x: relativeX, y: relativeY });
  };

  return (
    <div 
      ref={containerRef}
      className="relative h-full" 
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Enhanced card glow effects */}
      <div className="absolute -inset-[2px] rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none z-0 overflow-hidden">
        {/* Overall card glow */}
        <div 
          className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-all duration-700 shadow-[0_0_30px_15px_rgba(var(--color-primary),0.3)]"
        ></div>
        
        {/* Dynamic corner glows that pulse */}
        <div className="absolute top-0 start-0 w-[100px] h-[100px] rounded-full bg-[rgb(var(--color-primary))]/20 blur-xl -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 group-hover:animate-pulse-slow"></div>
        <div className="absolute top-0 end-0 w-[100px] h-[100px] rounded-full bg-[rgb(var(--color-secondary))]/20 blur-xl translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 group-hover:animate-pulse-slow delay-300"></div>
        <div className="absolute bottom-0 start-0 w-[100px] h-[100px] rounded-full bg-[rgb(var(--color-secondary))]/20 blur-xl -translate-x-1/2 translate-y-1/2 opacity-0 group-hover:opacity-100 group-hover:animate-pulse-slow delay-150"></div>
        <div className="absolute bottom-0 end-0 w-[100px] h-[100px] rounded-full bg-[rgb(var(--color-primary))]/20 blur-xl translate-x-1/2 translate-y-1/2 opacity-0 group-hover:opacity-100 group-hover:animate-pulse-slow delay-450"></div>
        
        {/* New: Adding a moving glow effect around the card */}
        <div 
          className="absolute -inset-1 bg-gradient-to-r from-[rgb(var(--color-primary))]/30 via-[rgb(var(--color-secondary))]/30 to-[rgb(var(--color-primary))]/30 rounded-xl blur-xl opacity-0 group-hover:opacity-100 group-hover:animate-gradient-x"
        ></div>
      </div>
      
      {/* Mouse-following spotlight glow */}
      <div className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none z-0">
        <div 
          className="absolute w-[250px] h-[250px] rounded-full bg-gradient-radial from-[rgb(var(--color-primary))]/15 to-transparent blur-2xl opacity-0 transition-opacity duration-300"
          style={{ 
            opacity: isHovering ? 0.8 : 0,
            left: `calc(${mousePosition.x * 100}% - 125px)`,
            top: `calc(${mousePosition.y * 100}% - 125px)`,
            transform: 'translateZ(0)',
          }}
        />
      </div>
      
      {/* Border effects that follow mouse */}
      <div className="absolute inset-0 rounded-xl overflow-hidden pointer-events-none z-10">
        {/* Top border - follows mouse X */}
        <div 
          className="absolute top-0 start-0 w-[45%] h-[2px] bg-gradient-to-r from-[rgb(var(--color-primary))]/90 to-[rgb(var(--color-secondary))]/90 opacity-0 transition-all duration-300"
          style={{ 
            opacity: isHovering ? 1 : 0,
            transform: `translateX(${mousePosition.x * 110}%)`,
            width: '45%',
            left: `${Math.min(Math.max(mousePosition.x * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 10px 1px rgb(var(--color-primary))',
          }}
        />
        
        {/* Right border - follows mouse Y */}
        <div 
          className="absolute top-0 end-0 w-[2px] h-[45%] bg-gradient-to-b from-[rgb(var(--color-primary))]/90 to-[rgb(var(--color-secondary))]/90 opacity-0 transition-all duration-300"
          style={{ 
            opacity: isHovering ? 1 : 0,
            transform: `translateY(${mousePosition.y * 110}%)`,
            height: '45%',
            top: `${Math.min(Math.max(mousePosition.y * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 10px 1px rgb(var(--color-primary))',
          }}
        />
        
        {/* Bottom border - follows mouse X */}
        <div 
          className="absolute bottom-0 end-0 w-[45%] h-[2px] bg-gradient-to-l from-[rgb(var(--color-primary))]/90 to-[rgb(var(--color-secondary))]/90 opacity-0 transition-all duration-300"
          style={{ 
            opacity: isHovering ? 1 : 0,
            transform: `translateX(-${mousePosition.x * 110}%)`,
            width: '45%',
            right: `${Math.min(Math.max((1-mousePosition.x) * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 10px 1px rgb(var(--color-secondary))',
          }}
        />
        
        {/* Left border - follows mouse Y */}
        <div 
          className="absolute bottom-0 start-0 w-[2px] h-[45%] bg-gradient-to-t from-[rgb(var(--color-primary))]/90 to-[rgb(var(--color-secondary))]/90 opacity-0 transition-all duration-300"
          style={{ 
            opacity: isHovering ? 1 : 0,
            transform: `translateY(-${mousePosition.y * 110}%)`,
            height: '45%',
            bottom: `${Math.min(Math.max((1-mousePosition.y) * 100 - 22.5, 0), 55)}%`,
            boxShadow: '0 0 10px 1px rgb(var(--color-secondary))',
          }}
        />
      </div>
      
      {/* Ambient inner glow */}
      <div className="absolute inset-[2px] rounded-lg bg-gradient-to-br from-[rgb(var(--color-primary))]/10 to-[rgb(var(--color-secondary))]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none blur-sm z-0"></div>
      
      {children}
    </div>
  );
};

const ArticlesGrid = () => {
  const [activeCategory, setActiveCategory] = useState("All");
  const gridRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();
  
  // Filter articles based on selected category
  const filteredArticles = activeCategory === "All"
    ? articles
    : articles.filter(article => article.category === activeCategory);

  useEffect(() => {
    controls.start({ opacity: 0, y: 20 })
      .then(() => {
        controls.start(i => ({
          opacity: 1,
          y: 0,
          transition: { delay: i * 0.05, duration: 0.4 }
        }));
      });
  }, [activeCategory, controls]);

  return (
    <div className="relative space-y-10">
      {/* Animated background decorations */}
      <BackgroundDecorations />
      
      {/* Category filters */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Browse by Category</h2>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category
                  ? "bg-[rgb(var(--color-primary))] text-white shadow-lg shadow-[rgb(var(--color-primary))]/20"
                  : "bg-[rgb(var(--color-text))]/10 backdrop-blur-sm text-[rgb(var(--color-text))] hover:bg-[rgb(var(--color-text))]/20"
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>
      
      {/* Articles grid */}
      <div ref={gridRef}>
        {filteredArticles.length > 0 ? (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {filteredArticles.map((article, index) => (
              <motion.div
                key={article.id}
                custom={index}
                initial={{ opacity: 0, y: 20 }}
                animate={controls}
              >
                <Link href={`/articles/${article.slug}`} className="block h-full">
                  <InteractiveBorder>
                    <div className="group h-full bg-[rgb(var(--color-text))]/5 backdrop-blur-sm border border-[rgb(var(--color-text))]/10 rounded-xl overflow-hidden transition-all duration-500 hover:shadow-lg hover:shadow-[rgb(var(--color-primary))]/20 hover:scale-[1.02] relative flex flex-col">
                      {/* Article image */}
                      <div className="relative w-full aspect-[16/9] overflow-hidden">
                        {/* Category badge */}
                        <div className="absolute top-3 start-3 z-10">
                          <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-[rgb(var(--color-primary))]/80 text-white backdrop-blur-sm">
                            {article.category}
                          </span>
                        </div>
                        
                        {/* Placeholder for article image */}
                        <div className="absolute inset-0 bg-[#1a1a2e] flex items-center justify-center transform group-hover:scale-105 transition-transform duration-700 ease-in-out">
                          <span className="text-[rgb(var(--color-text))]/70">{article.title.substring(0, 20)}...</span>
                        </div>
                        
                        {/* Gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-[#0a0f23] via-transparent to-transparent opacity-60"></div>
                      </div>
                      
                      {/* Article content - more compact */}
                      <div className="p-4 flex flex-col flex-grow">
                        <h3 className="text-lg font-bold mb-2 line-clamp-2 group-hover:text-[rgb(var(--color-primary))] group-hover:text-shadow-sm group-hover:text-shadow-[rgb(var(--color-primary))]/30 transition-all">
                          {article.title}
                        </h3>
                        
                        <div className="text-[rgb(var(--color-text))]/70 text-xs mb-2 flex items-center">
                          <svg className="w-3 h-3 me-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          {article.date}
                        </div>
                        
                        <p className="text-[rgb(var(--color-text))]/70 text-sm mb-3 line-clamp-3 flex-grow">
                          {article.excerpt}
                        </p>
                        
                        <div className="mt-auto pt-2 border-t border-[rgb(var(--color-text))]/10">
                          <span className="inline-flex items-center text-[rgb(var(--color-primary))] group-hover:text-[rgb(var(--color-primary-hover))] group-hover:text-shadow-sm group-hover:text-shadow-[rgb(var(--color-primary))]/50 font-medium text-xs group transition-all duration-300">
                            READ MORE
                            <svg className="ms-1 h-3 w-3 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </span>
                        </div>
                        
                        {/* Decorative corner dot */}
                        <div className="absolute bottom-3 end-3 w-2 h-2 rounded-full bg-[rgb(var(--color-primary))]/60 group-hover:bg-[rgb(var(--color-primary))] group-hover:shadow-sm group-hover:shadow-[rgb(var(--color-primary))]/50 transition-all"></div>
                      </div>
                    </div>
                  </InteractiveBorder>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <div className="text-center py-12 bg-[rgb(var(--color-text))]/5 backdrop-blur-sm rounded-xl border border-[rgb(var(--color-text))]/10">
            <svg className="w-16 h-16 mx-auto mb-4 text-[rgb(var(--color-text))]/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M15 12h4.01M15 16h4.01M19 8h-7" />
            </svg>
            <h3 className="text-xl font-bold mb-2">No articles found</h3>
            <p className="text-[rgb(var(--color-text))]/70">We couldn't find any articles in this category.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ArticlesGrid; 