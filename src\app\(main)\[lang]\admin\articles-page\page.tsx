"use client";

import { useState } from 'react';
import Link from 'next/link';
import { FiEye, FiTag, FiFileText, FiSearch, FiPlus, FiEdit2, FiTrash2, FiFilter } from 'react-icons/fi';

export default function ArticlesPageOverview() {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for quick stats
  const stats = [
    {
      title: "Articles Hero",
      description: "Manage the hero section of the articles page",
      icon: FiEye,
      href: "/admin/articles-page/articles-hero",
      status: "configured",
      lastUpdated: "2024-01-15"
    },
    {
      title: "Articles Categories",
      description: "Manage article categories and their properties",
      icon: FiTag,
      href: "/admin/articles-page/categories",
      status: "configured",
      lastUpdated: "2024-01-14",
      count: 8
    },
    {
      title: "Articles List",
      description: "Manage all articles and their content",
      icon: FiFileText,
      href: "/admin/articles-page/articles",
      status: "needs-attention",
      lastUpdated: "2024-01-13",
      count: 24
    },
    {
      title: "SEO Settings",
      description: "Configure SEO settings for the articles page",
      icon: FiSearch,
      href: "/admin/articles-page/seo",
      status: "draft",
      lastUpdated: "2024-01-10"
    }
  ];

  const filteredStats = stats.filter(stat =>
    stat.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    stat.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'configured':
        return 'bg-green-900/50 text-green-400';
      case 'needs-attention':
        return 'bg-yellow-900/50 text-yellow-400';
      case 'draft':
        return 'bg-gray-900/50 text-gray-400';
      default:
        return 'bg-gray-900/50 text-gray-400';
    }
  };

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Articles Page Management</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage all aspects of your articles page content and settings
          </p>
        </div>
        <Link
          href="/admin/articles-page/articles/new"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
        >
          <FiPlus className="-ml-1 mr-2 h-5 w-5" />
          New Article
        </Link>
      </div>

      {/* Search */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-4 border border-gray-700">
        <div className="relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FiSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-gray-700 focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full pl-10 py-2 sm:text-sm border-gray-600 rounded-md text-white h-10"
            placeholder="Search sections..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Overview Cards */}
      <div className="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
        {filteredStats.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link
              key={item.title}
              href={item.href}
              className="relative group bg-gray-800 p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-[#00C2FF] rounded-lg border border-gray-700 hover:border-[#00C2FF] transition-colors"
            >
              <div>
                <span className={`rounded-lg inline-flex p-3 text-[#00C2FF] bg-gray-700 group-hover:bg-gray-600 transition-colors`}>
                  <IconComponent className="h-6 w-6" aria-hidden="true" />
                </span>
              </div>
              <div className="mt-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-white group-hover:text-[#00C2FF] transition-colors">
                    <span className="absolute inset-0" aria-hidden="true" />
                    {item.title}
                  </h3>
                  {item.count && (
                    <span className="bg-gray-700 text-gray-300 px-2 py-1 rounded-full text-xs">
                      {item.count}
                    </span>
                  )}
                </div>
                <p className="mt-2 text-sm text-gray-400">
                  {item.description}
                </p>
                <div className="mt-4 flex items-center justify-between">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(item.status)}`}>
                    {item.status.replace('-', ' ')}
                  </span>
                  <span className="text-xs text-gray-500">
                    Updated {item.lastUpdated}
                  </span>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg leading-6 font-medium text-white mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <Link
            href="/admin/articles-page/categories"
            className="flex items-center px-4 py-3 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-[#00C2FF] hover:border-[#00C2FF] focus:outline-none focus:ring-2 focus:ring-[#00C2FF] transition-colors"
          >
            <FiTag className="mr-3 h-5 w-5" />
            Manage Categories
          </Link>
          <Link
            href="/admin/articles-page/articles-hero"
            className="flex items-center px-4 py-3 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-[#00C2FF] hover:border-[#00C2FF] focus:outline-none focus:ring-2 focus:ring-[#00C2FF] transition-colors"
          >
            <FiEye className="mr-3 h-5 w-5" />
            Edit Hero Section
          </Link>
          <Link
            href="/admin/articles-page/seo"
            className="flex items-center px-4 py-3 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-[#00C2FF] hover:border-[#00C2FF] focus:outline-none focus:ring-2 focus:ring-[#00C2FF] transition-colors"
          >
            <FiSearch className="mr-3 h-5 w-5" />
            SEO Settings
          </Link>
        </div>
      </div>
    </div>
  );
} 