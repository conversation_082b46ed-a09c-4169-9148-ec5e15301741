'use client';

import React, { createContext, useContext, useEffect } from 'react';

type Theme = 'dark';

interface ThemeContextType {
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Always use dark theme for Mazaya Capital
  const theme: Theme = 'dark';

  // Set the dark theme on document element
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove any other theme class just in case
    root.classList.remove('light');
    
    // Add dark theme class
    root.classList.add(theme);
    
    // Store the theme preference in local storage
    localStorage.setItem('mazaya-theme', theme);
  }, []);

  return (
    <ThemeContext.Provider value={{ theme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
} 