import React, { useState } from 'react';
import { Transforms } from 'slate';
import { useSlate } from 'slate-react';
import { FiDownload, FiUpload, FiFileText, FiCode, FiFile } from 'react-icons/fi';
import { ExportFormat } from '../types';

interface ImportExportToolbarProps {
  icon: React.ReactNode;
  value: any;
}

const ImportExportToolbar: React.FC<ImportExportToolbarProps> = ({ icon, value }) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [importText, setImportText] = useState('');
  const [importFormat, setImportFormat] = useState<'markdown' | 'html' | 'json'>('markdown');

  // Export document to different formats
  const exportDocument = (format: ExportFormat) => {
    // In a real implementation, these would be proper conversions
    try {
      let content;
      let mimeType;
      let fileExtension;
      let filename = `document-${new Date().toISOString().slice(0, 10)}`;

      switch (format) {
        case 'markdown':
          content = convertToMarkdown(value);
          mimeType = 'text/markdown';
          fileExtension = 'md';
          break;
        case 'html':
          content = convertToHtml(value);
          mimeType = 'text/html';
          fileExtension = 'html';
          break;
        case 'pdf':
          alert('PDF export would be implemented with a library like jsPDF');
          return;
        case 'docx':
          alert('DOCX export would be implemented with a library like docx-js');
          return;
        case 'txt':
          content = convertToPlainText(value);
          mimeType = 'text/plain';
          fileExtension = 'txt';
          break;
        case 'json':
        default:
          content = JSON.stringify(value, null, 2);
          mimeType = 'application/json';
          fileExtension = 'json';
          break;
      }

      // Create a downloadable file
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename}.${fileExtension}`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export error:', error);
      alert('Error exporting document. See console for details.');
    }
  };

  // Mock conversion functions (in a real app, these would be more sophisticated)
  const convertToMarkdown = (content: any): string => {
    // This is a simplified example - in a real app, use a proper conversion library
    return "# Converted to Markdown\n\nThis is a placeholder for proper Markdown conversion.";
  };

  const convertToHtml = (content: any): string => {
    // This is a simplified example - in a real app, use a proper conversion library
    return "<h1>Converted to HTML</h1><p>This is a placeholder for proper HTML conversion.</p>";
  };

  const convertToPlainText = (content: any): string => {
    // Extract all text nodes
    let text = '';
    
    const extractText = (nodes: any[]) => {
      nodes.forEach(node => {
        if (typeof node.text === 'string') {
          text += node.text;
        } else if (node.children) {
          extractText(node.children);
          // Add newlines between blocks
          if (node.type && node.type !== 'list-item' && node.type !== 'table-cell') {
            text += '\n\n';
          }
        }
      });
    };
    
    extractText(content);
    return text;
  };

  // Import content from text
  const importContent = () => {
    if (!importText.trim()) {
      alert('Please enter content to import');
      return;
    }

    try {
      let newContent;

      switch (importFormat) {
        case 'markdown':
          newContent = convertFromMarkdown(importText);
          break;
        case 'html':
          newContent = convertFromHtml(importText);
          break;
        case 'json':
        default:
          newContent = JSON.parse(importText);
          break;
      }

      // Replace editor content with the imported content
      Transforms.delete(editor, {
        at: {
          anchor: { path: [0, 0], offset: 0 },
          focus: { path: [editor.children.length - 1, 0], offset: 1000 }
        }
      });
      
      Transforms.insertNodes(editor, newContent);
      setShowImportModal(false);
      setImportText('');
    } catch (error) {
      console.error('Import error:', error);
      alert(`Error importing content: ${error.message}`);
    }
  };

  // Mock conversion functions for import
  const convertFromMarkdown = (markdown: string): any[] => {
    // This is a simplified example - in a real app, use a proper conversion library
    return [
      {
        type: 'paragraph',
        children: [{ text: 'Imported from Markdown (mockup conversion)' }]
      },
      {
        type: 'paragraph',
        children: [{ text: markdown.substring(0, 100) + '...' }]
      }
    ];
  };

  const convertFromHtml = (html: string): any[] => {
    // This is a simplified example - in a real app, use a proper conversion library
    return [
      {
        type: 'paragraph',
        children: [{ text: 'Imported from HTML (mockup conversion)' }]
      },
      {
        type: 'paragraph',
        children: [{ text: html.replace(/<[^>]*>/g, '').substring(0, 100) + '...' }]
      }
    ];
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      {showOptions && (
        <div className="absolute top-full right-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[180px]">
          <div className="px-3 py-1 text-white/70 text-xs uppercase">Export As</div>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              exportDocument('markdown');
              setShowOptions(false);
            }}
          >
            <FiCode size={16} />
            <span>Markdown (.md)</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              exportDocument('html');
              setShowOptions(false);
            }}
          >
            <FiCode size={16} />
            <span>HTML (.html)</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              exportDocument('pdf');
              setShowOptions(false);
            }}
          >
            <FiFileText size={16} />
            <span>PDF (.pdf)</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              exportDocument('docx');
              setShowOptions(false);
            }}
          >
            <FiFileText size={16} />
            <span>Word (.docx)</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              exportDocument('txt');
              setShowOptions(false);
            }}
          >
            <FiFile size={16} />
            <span>Plain Text (.txt)</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              exportDocument('json');
              setShowOptions(false);
            }}
          >
            <FiCode size={16} />
            <span>JSON (.json)</span>
          </button>
          
          <div className="border-t border-white/10 my-1"></div>
          <div className="px-3 py-1 text-white/70 text-xs uppercase">Import</div>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              setImportFormat('markdown');
              setShowImportModal(true);
              setShowOptions(false);
            }}
          >
            <FiUpload size={16} />
            <span>From Markdown</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              setImportFormat('html');
              setShowImportModal(true);
              setShowOptions(false);
            }}
          >
            <FiUpload size={16} />
            <span>From HTML</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              setImportFormat('json');
              setShowImportModal(true);
              setShowOptions(false);
            }}
          >
            <FiUpload size={16} />
            <span>From JSON</span>
          </button>
        </div>
      )}
      
      {showImportModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#141b35] p-6 rounded-lg max-w-2xl w-full">
            <h3 className="text-white text-lg font-medium mb-4">
              Import from {importFormat === 'markdown' ? 'Markdown' : importFormat === 'html' ? 'HTML' : 'JSON'}
            </h3>
            
            <div className="mb-4">
              <label className="block text-white/70 mb-2">
                Paste your {importFormat} content below:
              </label>
              <textarea
                className="w-full p-3 bg-[#0A0F23] border border-white/10 rounded-lg text-white font-mono"
                rows={10}
                value={importText}
                onChange={(e) => setImportText(e.target.value)}
                placeholder={importFormat === 'markdown' 
                  ? '# Markdown content...' 
                  : importFormat === 'html' 
                    ? '<h1>HTML content...</h1>' 
                    : '[ {"type": "paragraph", "children": [{ "text": "JSON content..." }] } ]'
                }
              />
              <p className="text-white/50 text-sm mt-1">
                This will replace your current document content.
              </p>
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg"
                onClick={() => {
                  setShowImportModal(false);
                  setImportText('');
                }}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white rounded-lg"
                onClick={importContent}
              >
                Import
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImportExportToolbar; 