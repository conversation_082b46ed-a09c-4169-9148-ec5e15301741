"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { FiSave, FiUpload, FiEye, FiX, FiCalendar, FiTag, FiUser, FiArrowLeft, FiPlus, FiTrash2 } from 'react-icons/fi';
import Image from 'next/image';
import dynamic from 'next/dynamic';

// Dynamically import RichTextEditor to avoid SSR issues
const RichTextEditor = dynamic(() => import('@/components/ui/RichTextEditor'), { ssr: false });

// Interface for article data
interface ArticleData {
  id: number;
  title: {
    english: string;
    arabic: string;
  };
  slug: {
    english: string;
    arabic: string;
  };
  excerpt: {
    english: string;
    arabic: string;
  };
  content: {
    english: string;
    arabic: string;
  };
  featuredImage: string;
  categoryId: number | null;
  authorId: number | null;
  status: 'draft' | 'published' | 'archived';
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  views: number;
  readingTime: number;
  tags: string[];
  seo: {
    title: {
      english: string;
      arabic: string;
    };
    description: {
      english: string;
      arabic: string;
    };
    keywords: {
      english: string[];
      arabic: string[];
    };
  };
}

// Mock data
const MOCK_CATEGORIES = [
  { id: 1, name: "Real Estate News" },
  { id: 2, name: "Investment Tips" },
  { id: 3, name: "Market Analysis" },
  { id: 4, name: "Property Guides" },
  { id: 5, name: "Industry Insights" }
];

const MOCK_AUTHORS = [
  { id: 1, name: "John Smith" },
  { id: 2, name: "Sarah Johnson" },
  { id: 3, name: "Ahmed Al-Rashid" },
  { id: 4, name: "Maria Garcia" }
];

// Mock article data for editing
const MOCK_ARTICLE: ArticleData = {
  id: 1,
  title: {
    english: "Real Estate Market Trends for 2024",
    arabic: "اتجاهات سوق العقارات لعام 2024"
  },
  slug: {
    english: "real-estate-market-trends-2024",
    arabic: "اتجاهات-سوق-العقارات-2024"
  },
  excerpt: {
    english: "Explore the emerging trends shaping the real estate market in 2024.",
    arabic: "استكشف الاتجاهات الناشئة التي تشكل سوق العقارات في عام 2024."
  },
  content: {
    english: "The real estate market is experiencing significant changes in 2024. Technology, sustainability, and changing demographics are driving new patterns in property investment and development.\n\nKey trends include:\n\n1. Smart building technologies\n2. Sustainable construction practices\n3. Remote work impact on commercial spaces\n4. Urban vs suburban preferences\n5. AI and data analytics in property valuation",
    arabic: "يشهد سوق العقارات تغييرات كبيرة في عام 2024. التكنولوجيا والاستدامة والتغيرات الديموغرافية تقود أنماطاً جديدة في الاستثمار العقاري والتطوير.\n\nالاتجاهات الرئيسية تشمل:\n\n1. تقنيات البناء الذكي\n2. ممارسات البناء المستدام\n3. تأثير العمل عن بُعد على المساحات التجارية\n4. تفضيلات المناطق الحضرية مقابل الضواحي\n5. الذكاء الاصطناعي وتحليل البيانات في تقييم العقارات"
  },
  featuredImage: "/images/articles/article-1.jpg",
  categoryId: 3,
  authorId: 1,
  status: "published",
  publishedAt: "2024-01-15T10:00:00Z",
  createdAt: "2024-01-14T14:30:00Z",
  updatedAt: "2024-01-15T09:45:00Z",
  views: 1250,
  readingTime: 8,
  tags: ["trends", "analysis", "2024", "market", "investment"],
  seo: {
    title: {
      english: "Real Estate Market Trends 2024 - Expert Analysis | Mazaya Capital",
      arabic: "اتجاهات سوق العقارات 2024 - تحليل خبراء | مزايا كابيتال"
    },
    description: {
      english: "Discover the key real estate market trends shaping 2024. Expert analysis on technology, sustainability, and investment opportunities.",
      arabic: "اكتشف اتجاهات سوق العقارات الرئيسية التي تشكل عام 2024. تحليل خبراء حول التكنولوجيا والاستدامة وفرص الاستثمار."
    },
    keywords: {
      english: ["real estate trends", "market analysis", "2024", "property investment", "smart buildings"],
      arabic: ["اتجاهات العقارات", "تحليل السوق", "2024", "استثمار عقاري", "المباني الذكية"]
    }
  }
};

export default function EditArticlePage() {
  const params = useParams();
  const articleId = params?.id as string;
  
  const [articleData, setArticleData] = useState<ArticleData | null>(null);
  const [loading, setLoading] = useState(true);
  const [newTag, setNewTag] = useState('');
  const [newKeyword, setNewKeyword] = useState({ english: '', arabic: '' });
  const [showPreview, setShowPreview] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Load article data
  useEffect(() => {
    // In a real application, you would fetch from your API
    // For now, we'll use mock data
    setTimeout(() => {
      setArticleData(MOCK_ARTICLE);
      setLoading(false);
    }, 500);
  }, [articleId]);

  // Generate slug from title
  const generateSlug = (title: string, isArabic: boolean = false) => {
    if (isArabic) {
      return title.toLowerCase().replace(/\s+/g, '-');
    }
    return title.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
  };

  // Handle input changes
  const handleInputChange = (section: string, field: string, value: string | number | null, language?: 'english' | 'arabic') => {
    if (!articleData) return;
    
    setArticleData(prev => {
      if (!prev) return prev;
      const newData = { ...prev };
      
      if (language && section === 'seo') {
        (newData as any)[section][field][language] = value;
      } else if (language) {
        (newData as any)[section][language] = value;
        // Auto-generate slug when title changes
        if (field === 'title' && value && typeof value === 'string') {
          (newData as any).slug[language] = generateSlug(value, language === 'arabic');
        }
      } else {
        (newData as any)[section] = value;
      }
      
      return newData;
    });
  };

  // Handle SEO field changes
  const handleSEOChange = (field: string, value: string, language: 'english' | 'arabic') => {
    if (!articleData) return;
    
    setArticleData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        seo: {
          ...prev.seo,
          [field]: {
            ...prev.seo[field as keyof typeof prev.seo],
            [language]: value
          }
        }
      };
    });
  };

  // Add tag
  const addTag = () => {
    if (!articleData) return;
    
    const tag = newTag.trim();
    if (tag && !articleData.tags.includes(tag)) {
      setArticleData(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          tags: [...prev.tags, tag]
        };
      });
      setNewTag('');
    }
  };

  // Remove tag
  const removeTag = (tag: string) => {
    if (!articleData) return;
    
    setArticleData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        tags: prev.tags.filter(t => t !== tag)
      };
    });
  };

  // Add keyword
  const addKeyword = (language: 'english' | 'arabic') => {
    if (!articleData) return;
    
    const keyword = newKeyword[language].trim();
    if (keyword && !articleData.seo.keywords[language].includes(keyword)) {
      setArticleData(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          seo: {
            ...prev.seo,
            keywords: {
              ...prev.seo.keywords,
              [language]: [...prev.seo.keywords[language], keyword]
            }
          }
        };
      });
      setNewKeyword(prev => ({ ...prev, [language]: '' }));
    }
  };

  // Remove keyword
  const removeKeyword = (language: 'english' | 'arabic', keyword: string) => {
    if (!articleData) return;
    
    setArticleData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        seo: {
          ...prev.seo,
          keywords: {
            ...prev.seo.keywords,
            [language]: prev.seo.keywords[language].filter(k => k !== keyword)
          }
        }
      };
    });
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!articleData) return;
    
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setArticleData(prev => {
          if (!prev) return prev;
          return { 
            ...prev, 
            featuredImage: e.target?.result as string 
          };
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save
  const handleSave = () => {
    if (!articleData) return;
    
    // Validation
    if (!articleData.title.english || !articleData.title.arabic) {
      alert('Please fill in both English and Arabic titles');
      return;
    }
    
    if (!articleData.categoryId || !articleData.authorId) {
      alert('Please select category and author');
      return;
    }

    // Set published date if status is published and not set
    const finalData = {
      ...articleData,
      publishedAt: articleData.status === 'published' && !articleData.publishedAt 
        ? new Date().toISOString() 
        : articleData.publishedAt,
      updatedAt: new Date().toISOString()
    };

    console.log('Updating article:', finalData);
    alert('Article updated successfully!');
  };

  // Handle delete
  const handleDelete = () => {
    console.log('Deleting article:', articleId);
    alert('Article deleted successfully!');
    setShowDeleteModal(false);
    // Here you would typically navigate back to the articles list
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-900/50 text-green-400';
      case 'draft':
        return 'bg-yellow-900/50 text-yellow-400';
      case 'archived':
        return 'bg-gray-900/50 text-gray-400';
      default:
        return 'bg-gray-900/50 text-gray-400';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not published';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-white text-lg">Loading article...</div>
      </div>
    );
  }

  if (!articleData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-white text-lg">Article not found</div>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <div className="flex items-center space-x-3">
            <Link
              href="/admin/articles-page/articles"
              className="inline-flex items-center text-gray-400 hover:text-white"
            >
              <FiArrowLeft className="h-5 w-5 mr-1" />
              Back to Articles
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-white mt-2">Edit Article</h1>
          <p className="mt-2 text-sm text-gray-400">
            Edit article content, settings, and SEO information
          </p>
          <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
            <span>ID: {articleData.id}</span>
            <span>Views: {articleData.views}</span>
            <span>Reading Time: {articleData.readingTime} min</span>
            <span>Created: {formatDate(articleData.createdAt)}</span>
            <span>Updated: {formatDate(articleData.updatedAt)}</span>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowDeleteModal(true)}
            className="inline-flex items-center px-4 py-2 border border-red-600 rounded-md shadow-sm text-sm font-medium text-red-400 hover:text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <FiTrash2 className="-ml-1 mr-2 h-5 w-5" />
            Delete
          </button>
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            Preview
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Update Article
          </button>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-700">
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-white">Delete Article</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-300">
                        Are you sure you want to delete this article? This action cannot be undone and will permanently remove the article and all its content.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-700">
                <button
                  onClick={handleDelete}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Delete Article
                </button>
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Article Preview</h3>
                  <button
                    onClick={() => setShowPreview(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                
                <div className="prose max-w-none">
                  <div className="mb-4">
                    {articleData.featuredImage && (
                      <Image
                        src={articleData.featuredImage}
                        alt="Featured"
                        width={800}
                        height={400}
                        className="w-full h-64 object-cover rounded-lg"
                      />
                    )}
                  </div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {articleData.title.english}
                  </h1>
                  <p className="text-lg text-gray-600 mb-4">
                    {articleData.excerpt.english}
                  </p>
                  <div className="text-gray-800 whitespace-pre-line">
                    {articleData.content.english}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Content - 2 columns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Title & Slug */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Title & URL</h3>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div className="space-y-4">
                <h4 className="text-md font-medium text-blue-400">English</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Title *</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.title.english}
                    onChange={e => handleInputChange('title', 'title', e.target.value, 'english')}
                    placeholder="Enter article title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">URL Slug</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.slug.english}
                    onChange={e => handleInputChange('slug', 'slug', e.target.value, 'english')}
                    placeholder="auto-generated"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <h4 className="text-md font-medium text-blue-400">العربية</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-300">العنوان *</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.title.arabic}
                    onChange={e => handleInputChange('title', 'title', e.target.value, 'arabic')}
                    placeholder="أدخل عنوان المقال"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">الرابط</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.slug.arabic}
                    onChange={e => handleInputChange('slug', 'slug', e.target.value, 'arabic')}
                    placeholder="تلقائي"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Excerpt */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Excerpt</h3>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-300">English Excerpt</label>
                <textarea
                  rows={4}
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.excerpt.english}
                  onChange={e => handleInputChange('excerpt', 'excerpt', e.target.value, 'english')}
                  placeholder="Brief description of the article..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">الملخص العربي</label>
                <textarea
                  rows={4}
                  dir="rtl"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.excerpt.arabic}
                  onChange={e => handleInputChange('excerpt', 'excerpt', e.target.value, 'arabic')}
                  placeholder="وصف مختصر للمقال..."
                />
              </div>
            </div>
          </div>

          {/* Content */}          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">            <h3 className="text-lg font-medium text-white mb-4">Content</h3>            <div className="space-y-6">              <div>                <label className="block text-sm font-medium text-gray-300 mb-2">English Content</label>                <RichTextEditor                  content={articleData.content.english}                  onChange={(value) => handleInputChange('content', 'content', value, 'english')}                  placeholder="Write the full article content here..."                  direction="ltr"                />              </div>              <div>                <label className="block text-sm font-medium text-gray-300 mb-2">المحتوى العربي</label>                <RichTextEditor                  content={articleData.content.arabic}                  onChange={(value) => handleInputChange('content', 'content', value, 'arabic')}                  placeholder="اكتب محتوى المقال الكامل هنا..."                  direction="rtl"                />              </div>            </div>          </div>

          {/* SEO Settings */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">SEO Settings</h3>
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-blue-400">English SEO</h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Meta Title</label>
                    <input
                      type="text"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={articleData.seo.title.english}
                      onChange={e => handleSEOChange('title', e.target.value, 'english')}
                      placeholder="SEO title"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Meta Description</label>
                    <textarea
                      rows={3}
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={articleData.seo.description.english}
                      onChange={e => handleSEOChange('description', e.target.value, 'english')}
                      placeholder="SEO description"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Keywords</label>
                    <div className="flex mt-1">
                      <input
                        type="text"
                        className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={newKeyword.english}
                        onChange={e => setNewKeyword(prev => ({ ...prev, english: e.target.value }))}
                        placeholder="Add keyword"
                        onKeyPress={e => e.key === 'Enter' && addKeyword('english')}
                      />
                      <button
                        onClick={() => addKeyword('english')}
                        className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5]"
                      >
                        Add
                      </button>
                    </div>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {articleData.seo.keywords.english.map(keyword => (
                        <span
                          key={keyword}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                        >
                          {keyword}
                          <button
                            onClick={() => removeKeyword('english', keyword)}
                            className="ml-1 text-gray-400 hover:text-white"
                          >
                            <FiX className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="text-md font-medium text-blue-400">تحسين محركات البحث</h4>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">عنوان الميتا</label>
                    <input
                      type="text"
                      dir="rtl"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={articleData.seo.title.arabic}
                      onChange={e => handleSEOChange('title', e.target.value, 'arabic')}
                      placeholder="عنوان تحسين محركات البحث"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">وصف الميتا</label>
                    <textarea
                      rows={3}
                      dir="rtl"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={articleData.seo.description.arabic}
                      onChange={e => handleSEOChange('description', e.target.value, 'arabic')}
                      placeholder="وصف تحسين محركات البحث"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">الكلمات المفتاحية</label>
                    <div className="flex mt-1">
                      <input
                        type="text"
                        dir="rtl"
                        className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={newKeyword.arabic}
                        onChange={e => setNewKeyword(prev => ({ ...prev, arabic: e.target.value }))}
                        placeholder="أضف كلمة مفتاحية"
                        onKeyPress={e => e.key === 'Enter' && addKeyword('arabic')}
                      />
                      <button
                        onClick={() => addKeyword('arabic')}
                        className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5]"
                      >
                        إضافة
                      </button>
                    </div>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {articleData.seo.keywords.arabic.map(keyword => (
                        <span
                          key={keyword}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                          dir="rtl"
                        >
                          {keyword}
                          <button
                            onClick={() => removeKeyword('arabic', keyword)}
                            className="mr-1 text-gray-400 hover:text-white"
                          >
                            <FiX className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar - 1 column */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center">
              <FiCalendar className="mr-2 h-5 w-5 text-green-500" />
              Publish Settings
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300">Status</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.status}
                  onChange={e => handleInputChange('status', 'status', e.target.value as 'draft' | 'published' | 'archived')}
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
                <span className={`mt-2 inline-flex px-2 py-1 text-xs leading-5 font-semibold rounded-full ${getStatusColor(articleData.status)}`}>
                  {articleData.status}
                </span>
              </div>
              
              {articleData.status === 'published' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300">Publish Date</label>
                  <input
                    type="datetime-local"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.publishedAt ? articleData.publishedAt.slice(0, 16) : ''}
                    onChange={e => handleInputChange('publishedAt', 'publishedAt', e.target.value ? new Date(e.target.value).toISOString() : null)}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Category & Author */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center">
              <FiTag className="mr-2 h-5 w-5 text-purple-500" />
              Classification
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300">Category *</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.categoryId || ''}
                  onChange={e => handleInputChange('categoryId', 'categoryId', parseInt(e.target.value) || null)}
                >
                  <option value="">Select Category</option>
                  {MOCK_CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300">Author *</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.authorId || ''}
                  onChange={e => handleInputChange('authorId', 'authorId', parseInt(e.target.value) || null)}
                >
                  <option value="">Select Author</option>
                  {MOCK_AUTHORS.map(author => (
                    <option key={author.id} value={author.id}>
                      {author.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Featured Image</h3>
            <div className="space-y-4">
              <div>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="featured-upload"
                />
                <label
                  htmlFor="featured-upload"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] w-full justify-center"
                >
                  <FiUpload className="-ml-1 mr-2 h-5 w-5" />
                  Change Image
                </label>
              </div>
              
              {articleData.featuredImage && (
                <div className="mt-4">
                  <Image
                    src={articleData.featuredImage}
                    alt="Featured image"
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Tags</h3>
            <div className="space-y-4">
              <div className="flex">
                <input
                  type="text"
                  className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={newTag}
                  onChange={e => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  onKeyPress={e => e.key === 'Enter' && addTag()}
                />
                <button
                  onClick={addTag}
                  className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                >
                  <FiPlus className="h-4 w-4" />
                </button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {articleData.tags.map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                  >
                    {tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-1 text-gray-400 hover:text-white"
                    >
                      <FiX className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 