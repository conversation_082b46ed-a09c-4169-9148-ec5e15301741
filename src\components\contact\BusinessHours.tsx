const BusinessHours = () => {
  const hours = [
    { day: "Monday - Thursday", hours: "9:00 AM - 6:00 PM" },
    { day: "Friday", hours: "9:00 AM - 1:00 PM" },
    { day: "Saturday", hours: "10:00 AM - 4:00 PM" },
    { day: "Sunday", hours: "Closed" },
  ];

  return (
    <div>
      <ul className="space-y-3">
        {hours.map((item, index) => (
          <li 
            key={index} 
            className="flex justify-between items-center p-2 hover:bg-primary/10 rounded-lg transition-colors duration-300"
          >
            <span className="text-text font-medium">{item.day}</span>
            <span 
              className={`rounded-full py-1 px-3 ${
                item.day === "Sunday" 
                  ? "bg-primary/20 text-primary font-medium" 
                  : "bg-brand-gradient text-white"
              }`}
            >
              {item.hours}
            </span>
          </li>
        ))}
      </ul>
      <div className="mt-6 pt-4 border-t border-primary/20">
        <div className="flex items-center mb-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary me-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold text-primary">Holiday Schedule</h3>
        </div>
        <p className="text-text">
          Our offices will be closed on all UAE national and religious holidays.
        </p>
      </div>
    </div>
  );
};

export default BusinessHours; 