"use client";

import React from 'react';
import Image from 'next/image';
import { getMediaUrl } from '@/utils/api';

interface AvatarProps {
  user: {
    first_name?: string;
    last_name?: string;
    email?: string;
    avatar?: string;
  };
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const Avatar: React.FC<AvatarProps> = ({ user, size = 'md', className = '' }) => {
  const getUserInitials = () => {
    // Priority 1: Use first_name and last_name if both exist
    if (user?.first_name && user?.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
    }
    
    // Priority 2: Use first_name only if last_name doesn't exist
    if (user?.first_name) {
      return user.first_name.substring(0, 2).toUpperCase();
    }
    
    // Priority 3: Use last_name only if first_name doesn't exist
    if (user?.last_name) {
      return user.last_name.substring(0, 2).toUpperCase();
    }
    
    // Priority 4: Use email if no names exist
    if (user?.email) {
      const emailParts = user.email.split('@')[0];
      if (emailParts.length >= 2) {
        return emailParts.substring(0, 2).toUpperCase();
      }
      return emailParts[0].toUpperCase();
    }
    
    // Fallback: Default initials
    return 'AD'; // Admin Default
  };

  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-xs',
    lg: 'h-12 w-12 text-sm',
    xl: 'h-24 w-24 text-2xl'
  };

  const sizePixels = {
    sm: 24,
    md: 32,
    lg: 48,
    xl: 96
  };

  return (
    <div className={`${sizeClasses[size]} bg-gradient-to-br from-[#00C2FF] to-[#0099CC] rounded-full flex items-center justify-center border border-gray-600 overflow-hidden relative ${className}`}>
      {/* Always show initials as background */}
      <span className="text-white font-bold tracking-wide drop-shadow-sm absolute inset-0 flex items-center justify-center">
        {getUserInitials()}
      </span>
      
      {/* Show avatar image on top if exists */}
      {user.avatar && (
        <Image
          key={user.avatar}
          src={getMediaUrl(user.avatar)}
          alt="User Avatar"
          width={sizePixels[size]}
          height={sizePixels[size]}
          className="rounded-full object-cover w-full h-full relative z-10"
          onError={(e) => {
            // If image fails to load, hide it and show initials
            console.log('Avatar failed to load:', getMediaUrl(user.avatar));
            e.currentTarget.style.display = 'none';
          }}
        />
      )}
    </div>
  );
};

export default Avatar; 