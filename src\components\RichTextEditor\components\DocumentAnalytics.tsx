import React, { useState, useEffect } from 'react';
import { Editor, Node } from 'slate';
import { useSlate } from 'slate-react';
import { FiBarChart2, <PERSON><PERSON>lock, FiBookOpen, FiFileText, FiCheck } from 'react-icons/fi';
import { DocumentAnalytics as DocumentAnalyticsType } from '../types';

interface DocumentAnalyticsProps {
  isOpen: boolean;
  togglePanel: () => void;
}

const DocumentAnalytics: React.FC<DocumentAnalyticsProps> = ({ isOpen, togglePanel }) => {
  const editor = useSlate();
  const [analytics, setAnalytics] = useState<DocumentAnalyticsType>({
    wordCount: 0,
    charCount: 0,
    readingTime: 0,
    readabilityScore: 0,
    paragraphCount: 0,
    sentenceCount: 0,
    averageSentenceLength: 0,
    complexWordCount: 0
  });

  // Calculate analytics whenever the editor content changes
  useEffect(() => {
    calculateDocumentAnalytics();
  }, [editor.children]);

  const calculateDocumentAnalytics = () => {
    // Get all text nodes from the editor
    const text = Node.string(editor);
    
    // Calculate basic metrics
    const charCount = text.length;
    const words = text.trim().split(/\s+/).filter(Boolean);
    const wordCount = words.length;
    
    // Estimate reading time (average reading speed: 200-250 words per minute)
    const readingTime = Math.max(1, Math.ceil(wordCount / 225));
    
    // Count paragraphs (non-empty nodes with type 'paragraph')
    const paragraphCount = editor.children.filter(
      node => node.type === 'paragraph' && Node.string(node).trim().length > 0
    ).length;
    
    // Count sentences (approximately by splitting on ., !, ?)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const sentenceCount = sentences.length;
    
    // Calculate average sentence length
    const averageSentenceLength = sentenceCount > 0 
      ? Math.round(wordCount / sentenceCount * 10) / 10
      : 0;
    
    // Count complex words (words with 3+ syllables, simplified approach)
    const complexWordCount = words.filter(word => {
      const syllables = countSyllables(word);
      return syllables >= 3;
    }).length;
    
    // Calculate readability score (simplified Flesch-Kincaid)
    const readabilityScore = calculateReadabilityScore(
      wordCount, 
      sentenceCount, 
      complexWordCount
    );
    
    setAnalytics({
      wordCount,
      charCount,
      readingTime,
      readabilityScore,
      paragraphCount,
      sentenceCount,
      averageSentenceLength,
      complexWordCount
    });
  };

  // Very simplified syllable counter (not perfect but gives an approximation)
  const countSyllables = (word: string): number => {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    // Remove ending e
    word = word.replace(/e$/, '');
    
    // Count vowel groups
    const vowelGroups = word.match(/[aeiouy]+/g);
    return vowelGroups ? vowelGroups.length : 1;
  };

  // Simplified readability score calculation
  const calculateReadabilityScore = (
    wordCount: number, 
    sentenceCount: number, 
    complexWordCount: number
  ): number => {
    if (wordCount === 0 || sentenceCount === 0) return 0;
    
    // Calculate percentage of complex words
    const complexWordPercentage = (complexWordCount / wordCount) * 100;
    
    // Calculate average words per sentence
    const avgWordsPerSentence = wordCount / sentenceCount;
    
    // Simplified Flesch-Kincaid formula (higher is more readable)
    const readabilityScore = Math.min(
      100,
      Math.max(
        0,
        Math.round(206.835 - (1.015 * avgWordsPerSentence) - (0.846 * complexWordPercentage))
      )
    );
    
    return readabilityScore;
  };

  // Get readability level description
  const getReadabilityLevel = (score: number): string => {
    if (score >= 90) return 'Very Easy';
    if (score >= 80) return 'Easy';
    if (score >= 70) return 'Fairly Easy';
    if (score >= 60) return 'Standard';
    if (score >= 50) return 'Fairly Difficult';
    if (score >= 30) return 'Difficult';
    return 'Very Difficult';
  };

  if (!isOpen) {
    return (
      <button
        className="p-2 bg-[#1a2349] rounded-full hover:bg-[#141b35] text-white"
        onClick={togglePanel}
        title="Document Analytics"
      >
        <FiBarChart2 size={18} />
      </button>
    );
  }

  return (
    <div className="absolute right-0 top-0 w-72 bg-[#141b35] border border-white/10 rounded-lg shadow-lg z-50 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-medium">Document Analytics</h3>
        <button 
          className="text-white/70 hover:text-white"
          onClick={togglePanel}
        >
          ×
        </button>
      </div>
      
      <div className="space-y-4">
        {/* Word and Character Count */}
        <div className="bg-[#0A0F23] rounded-lg p-3">
          <div className="flex items-center gap-2 text-white mb-2">
            <FiFileText size={16} />
            <span className="font-medium">Text Statistics</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <div className="text-white/50 text-xs">Words</div>
              <div className="text-white text-2xl font-semibold">{analytics.wordCount}</div>
            </div>
            <div>
              <div className="text-white/50 text-xs">Characters</div>
              <div className="text-white text-2xl font-semibold">{analytics.charCount}</div>
            </div>
            <div>
              <div className="text-white/50 text-xs">Paragraphs</div>
              <div className="text-white font-medium">{analytics.paragraphCount}</div>
            </div>
            <div>
              <div className="text-white/50 text-xs">Sentences</div>
              <div className="text-white font-medium">{analytics.sentenceCount}</div>
            </div>
          </div>
        </div>
        
        {/* Reading Time */}
        <div className="bg-[#0A0F23] rounded-lg p-3">
          <div className="flex items-center gap-2 text-white mb-2">
            <FiClock size={16} />
            <span className="font-medium">Reading Time</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-white text-2xl font-semibold">
              {analytics.readingTime} <span className="text-sm font-normal">min</span>
            </div>
            <div className="text-white/50 text-sm">
              Based on average reading speed of 225 WPM
            </div>
          </div>
        </div>
        
        {/* Readability */}
        <div className="bg-[#0A0F23] rounded-lg p-3">
          <div className="flex items-center gap-2 text-white mb-2">
            <FiBookOpen size={16} />
            <span className="font-medium">Readability</span>
          </div>
          <div className="mb-2">
            <div className="flex justify-between mb-1">
              <div className="text-white/50 text-xs">Score</div>
              <div className="text-white/50 text-xs">
                {getReadabilityLevel(analytics.readabilityScore)}
              </div>
            </div>
            <div className="h-2 bg-[#141b35] rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500"
                style={{ width: `${analytics.readabilityScore}%` }}
              ></div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <div className="text-white/50 text-xs">Avg. Sentence Length</div>
              <div className="text-white font-medium">
                {analytics.averageSentenceLength} words
              </div>
            </div>
            <div>
              <div className="text-white/50 text-xs">Complex Words</div>
              <div className="text-white font-medium">
                {analytics.complexWordCount} ({Math.round((analytics.complexWordCount / analytics.wordCount) * 100) || 0}%)
              </div>
            </div>
          </div>
        </div>
        
        {/* Writing Tips Based on Analytics */}
        {analytics.wordCount > 0 && (
          <div className="bg-[#0A0F23] rounded-lg p-3">
            <div className="flex items-center gap-2 text-white mb-2">
              <FiCheck size={16} />
              <span className="font-medium">Writing Tips</span>
            </div>
            <ul className="text-white/70 text-sm space-y-2">
              {analytics.averageSentenceLength > 25 && (
                <li className="flex items-start gap-2">
                  <span className="text-yellow-500 mt-1">•</span>
                  <span>Consider shortening your sentences for better readability.</span>
                </li>
              )}
              {(analytics.complexWordCount / analytics.wordCount) > 0.2 && (
                <li className="flex items-start gap-2">
                  <span className="text-yellow-500 mt-1">•</span>
                  <span>Try using simpler words to improve clarity.</span>
                </li>
              )}
              {analytics.readabilityScore < 50 && (
                <li className="flex items-start gap-2">
                  <span className="text-yellow-500 mt-1">•</span>
                  <span>Your text may be difficult for some readers to understand.</span>
                </li>
              )}
              {analytics.paragraphCount < 3 && analytics.wordCount > 200 && (
                <li className="flex items-start gap-2">
                  <span className="text-yellow-500 mt-1">•</span>
                  <span>Consider breaking up your text into more paragraphs.</span>
                </li>
              )}
              {analytics.readingTime > 5 && (
                <li className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span>Good length for an in-depth article.</span>
                </li>
              )}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentAnalytics; 