# Achievements API Endpoints Specification

## Overview
This document specifies the API endpoints required for the **Achievements** section of the home page admin panel and public display. The implementation follows the **exact same pattern** as the existing **Hero** and **Featured Projects** endpoints.

---

## 📋 Admin API Endpoints

### 1. **GET** `/api/admin/home-page/achievements/`
**Purpose**: Fetch achievements section content for admin management

**Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Response**:
```json
{
  "success": true,
  "message": "Achievements content retrieved successfully",
  "data": {
    "id": 1,
    "badge": {
      "en": "Our Track Record",
      "ar": "سجلنا المثبت"
    },
    "title": {
      "en": "Our Achievements",
      "ar": "إنجازاتنا"
    },
    "description": {
      "en": "Mazaya Capital has established a strong track record of successful developments and satisfied clients over the years",
      "ar": "مزايا كابيتال أنشأت سجلاً قوياً من التطويرات الناجحة والعملاء الراضين على مر السنين"
    },
    "achievements": [
      {
        "id": "1",
        "icon": "clock",
        "value": "15",
        "label": {
          "en": "Years of Experience",
          "ar": "سنة من الخبرة"
        }
      },
      {
        "id": "2",
        "icon": "home",
        "value": "50",
        "label": {
          "en": "Completed Projects",
          "ar": "مشروع مكتمل"
        }
      },
      {
        "id": "3",
        "icon": "smile",
        "value": "500+",
        "label": {
          "en": "Satisfied Clients",
          "ar": "عميل راضي"
        }
      },
      {
        "id": "4",
        "icon": "dollar",
        "value": "$2B",
        "label": {
          "en": "Project Value",
          "ar": "قيمة المشاريع"
        }
      }
    ],
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

---

### 2. **PUT** `/api/admin/home-page/achievements/`
**Purpose**: Update all achievements section content

**Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "badge": {
    "en": "Our Track Record",
    "ar": "سجلنا المثبت"
  },
  "title": {
    "en": "Our Achievements",
    "ar": "إنجازاتنا"
  },
  "description": {
    "en": "Mazaya Capital has established a strong track record of successful developments and satisfied clients over the years",
    "ar": "مزايا كابيتال أنشأت سجلاً قوياً من التطويرات الناجحة والعملاء الراضين على مر السنين"
  },
  "achievements": [
    {
      "id": "1",
      "icon": "clock",
      "value": "15",
      "label": {
        "en": "Years of Experience",
        "ar": "سنة من الخبرة"
      }
    },
    {
      "id": "2",
      "icon": "home",
      "value": "50",
      "label": {
        "en": "Completed Projects",
        "ar": "مشروع مكتمل"
      }
    },
    {
      "id": "3",
      "icon": "smile",
      "value": "500+",
      "label": {
        "en": "Satisfied Clients",
        "ar": "عميل راضي"
      }
    },
    {
      "id": "4",
      "icon": "dollar",
      "value": "$2B",
      "label": {
        "en": "Project Value",
        "ar": "قيمة المشاريع"
      }
    }
  ]
}
```

**Response**: Same as GET response with updated data

---

### 3. **PATCH** `/api/admin/home-page/achievements/section/`
**Purpose**: Update specific section of achievements content

**Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body Examples**:

**Update Badge**:
```json
{
  "section": "badge",
  "data": {
    "en": "Updated Badge",
    "ar": "شارة محدثة"
  }
}
```

**Update Title**:
```json
{
  "section": "title",
  "data": {
    "en": "Updated Achievements",
    "ar": "إنجازات محدثة"
  }
}
```

**Update Description**:
```json
{
  "section": "description",
  "data": {
    "en": "Updated description text",
    "ar": "نص الوصف المحدث"
  }
}
```

**Update Achievements Array**:
```json
{
  "section": "achievements",
  "data": [
    {
      "id": "1",
      "icon": "clock",
      "value": "20",
      "label": {
        "en": "Years of Experience",
        "ar": "سنة من الخبرة"
      }
    }
  ]
}
```

**Response**: Same as GET response with updated section

---

## 📋 Public API Endpoints

### 4. **GET** `/api/home-page/achievements/en/`
**Purpose**: Fetch English achievements section content for public display

**Headers**:
```http
Content-Type: application/json
```

**Authentication**: None required (Public endpoint)

**Response**:
```json
{
  "success": true,
  "message": "Achievements content retrieved successfully",
  "data": {
    "id": 1,
    "badge": "Our Track Record",
    "title": "Our Achievements",
    "description": "Mazaya Capital has established a strong track record of successful developments and satisfied clients over the years",
    "achievements": [
      {
        "id": "1",
        "icon": "clock",
        "value": "15",
        "label": "Years of Experience"
      },
      {
        "id": "2",
        "icon": "home",
        "value": "50",
        "label": "Completed Projects"
      },
      {
        "id": "3",
        "icon": "smile",
        "value": "500+",
        "label": "Satisfied Clients"
      },
      {
        "id": "4",
        "icon": "dollar",
        "value": "$2B",
        "label": "Project Value"
      }
    ],
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```

### 5. **GET** `/api/home-page/achievements/ar/`
**Purpose**: Fetch Arabic achievements section content for public display

**Headers**:
```http
Content-Type: application/json
```

**Authentication**: None required (Public endpoint)

**Response**:
```json
{
  "success": true,
  "message": "تم استرداد محتوى الإنجازات بنجاح",
  "data": {
    "id": 1,
    "badge": "سجلنا المثبت",
    "title": "إنجازاتنا",
    "description": "مزايا كابيتال أنشأت سجلاً قوياً من التطويرات الناجحة والعملاء الراضين على مر السنين",
    "achievements": [
      {
        "id": "1",
        "icon": "clock",
        "value": "15",
        "label": "سنة من الخبرة"
      },
      {
        "id": "2",
        "icon": "home",
        "value": "50",
        "label": "مشروع مكتمل"
      },
      {
        "id": "3",
        "icon": "smile",
        "value": "500+",
        "label": "عميل راضي"
      },
      {
        "id": "4",
        "icon": "dollar",
        "value": "$2B",
        "label": "قيمة المشاريع"
      }
    ],
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z"
  }
}
```