import { forwardRef, useState, useEffect, useRef } from "react";

interface ProjectGalleryProps {
  project: any;
}

const ProjectGallery = forwardRef<HTMLElement, ProjectGalleryProps>(({ project }, ref) => {
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  
  // Check if we're on mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint is 1024px in Tailwind
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);
  
  // Generate all gallery images - combining side images with bottom carousel in mobile view
  const allImages = isMobile 
    ? [...Array(project.gallery?.length || 10).keys()] 
    : [...Array(project.gallery?.length || 10).keys()];
  
  // Check if carousel is scrollable
  useEffect(() => {
    const checkScrollability = () => {
      if (carouselRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
        setCanScrollLeft(scrollLeft > 0);
        setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1); // -1 for rounding errors
      }
    };
    
    checkScrollability();
    
    // Create a listener for the scroll event
    const scrollHandler = () => checkScrollability();
    const resizeHandler = () => checkScrollability();
    
    if (carouselRef.current) {
      carouselRef.current.addEventListener('scroll', scrollHandler);
      window.addEventListener('resize', resizeHandler);
    }
    
    return () => {
      if (carouselRef.current) {
        carouselRef.current.removeEventListener('scroll', scrollHandler);
      }
      window.removeEventListener('resize', resizeHandler);
    };
  }, []);
  
  // Carousel scrolling functions
  const scrollLeft = () => {
    if (carouselRef.current) {
      const scrollAmount = 240; // Width of one thumbnail + gap
      carouselRef.current.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
      setScrollPosition(Math.max(0, scrollPosition - scrollAmount));
    }
  };
  
  const scrollRight = () => {
    if (carouselRef.current) {
      const scrollAmount = 240; // Width of one thumbnail + gap
      carouselRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      setScrollPosition(scrollPosition + scrollAmount);
    }
  };
  
  return (
    <section ref={ref} id="gallery" className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Project Gallery</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Experience the extraordinary design and premium finishes of {project.title}.</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Main Large Image */}
        <div className="lg:col-span-8 relative rounded-xl overflow-hidden h-[500px] bg-gray-200">
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-gray-500 text-xl">Main Gallery Image {activeImageIndex + 1}</span>
          </div>
          
          {/* Navigation Arrows */}
          <div className="absolute inset-0 flex items-center justify-between px-4">
            <button 
              className="w-10 h-10 rounded-full bg-black/30 text-white flex items-center justify-center hover:bg-black/50 transition-all duration-300"
              onClick={() => setActiveImageIndex(prev => (prev === 0 ? allImages.length - 1 : prev - 1))}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button 
              className="w-10 h-10 rounded-full bg-black/30 text-white flex items-center justify-center hover:bg-black/50 transition-all duration-300"
              onClick={() => setActiveImageIndex(prev => (prev === allImages.length - 1 ? 0 : prev + 1))}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
        
        {/* Side Images - Hidden on mobile */}
        <div className="hidden lg:grid lg:col-span-4 grid-cols-2 gap-4">
          {[0, 1, 2, 3].map((index) => (
            <div 
              key={index} 
              className="relative rounded-xl overflow-hidden h-[240px] bg-gray-200 cursor-pointer transition-all duration-300 hover:opacity-90"
              onClick={() => setActiveImageIndex(index)}
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-gray-500">Gallery Image {index + 1}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Thumbnails Carousel */}
      <div className="relative mt-6">
        {/* Carousel Navigation for Desktop - Left Arrow */}
        {canScrollLeft && (
          <div className="absolute -start-2 top-1/2 -translate-y-1/2 z-10 hidden md:block">
            <button 
              onClick={scrollLeft}
              className="w-8 h-8 rounded-full bg-black/30 text-white flex items-center justify-center hover:bg-black/50 transition-all duration-300"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
        )}
        
        <div 
          ref={carouselRef}
          className="flex space-x-4 overflow-x-auto pb-2 px-2 md:px-12 carousel-container pt-2"
          style={{ 
            scrollBehavior: 'smooth',
          }}
        >
          {allImages.map((index) => (
            <button
              key={index}
              onClick={() => setActiveImageIndex(index)}
              className={`flex-shrink-0 h-24 w-24 md:w-40 rounded-lg overflow-hidden relative ${
                activeImageIndex === index ? "ring-2 ring-[#00C2FF]" : ""
              }`}
            >
              <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500 text-xs">Image {index + 1}</span>
              </div>
            </button>
          ))}
        </div>
        
        {/* Carousel Navigation for Desktop - Right Arrow */}
        {canScrollRight && (
          <div className="absolute -end-2 top-1/2 -translate-y-1/2 z-10 hidden md:block">
            <button 
              onClick={scrollRight}
              className="w-8 h-8 rounded-full bg-black/30 text-white flex items-center justify-center hover:bg-black/50 transition-all duration-300"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Adding global style for carousel */}
      <style jsx global>{`
        .carousel-container::-webkit-scrollbar {
          display: none;
        }
        .carousel-container {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </section>
  );
});

ProjectGallery.displayName = "ProjectGallery";

export default ProjectGallery; 