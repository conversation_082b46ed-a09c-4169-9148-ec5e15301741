"use client";

import React, { useState } from 'react';
import { 
  FiDownload, FiUpload, FiRefreshCw, FiDatabase, FiHardDrive, 
  FiCloud, FiClock, FiShield, FiAlertCircle, FiCheckCircle,
  FiSettings, FiTrash2, FiEye, FiEyeOff
} from 'react-icons/fi';

interface BackupSettingsData {
  autoBackup: {
    enabled: boolean;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
    time: string; // HH:MM format
    retention: number;
    maxBackups: number;
  };
  backupTypes: {
    database: boolean;
    files: boolean;
    media: boolean;
    configurations: boolean;
    userUploads: boolean;
  };
  storage: {
    local: {
      enabled: boolean;
      path: string;
      maxSize: number; // in GB
    };
    cloud: {
      primary: 'aws' | 'google' | 'azure' | 'dropbox' | 'onedrive';
      aws: {
        enabled: boolean;
        bucket: string;
        region: string;
        accessKeyId: string;
        secretAccessKey: string;
        storageClass: 'STANDARD' | 'STANDARD_IA' | 'GLACIER' | 'DEEP_ARCHIVE';
      };
      google: {
        enabled: boolean;
        bucket: string;
        projectId: string;
        keyFile: string;
        storageClass: 'STANDARD' | 'NEARLINE' | 'COLDLINE' | 'ARCHIVE';
      };
      azure: {
        enabled: boolean;
        containerName: string;
        accountName: string;
        accountKey: string;
        tier: 'Hot' | 'Cool' | 'Archive';
      };
      dropbox: {
        enabled: boolean;
        accessToken: string;
        appKey: string;
      };
      onedrive: {
        enabled: boolean;
        clientId: string;
        clientSecret: string;
        tenantId: string;
      };
    };
  };
  compression: {
    enabled: boolean;
    level: number; // 1-9
    format: 'zip' | 'tar.gz' | '7z';
  };
  encryption: {
    enabled: boolean;
    algorithm: 'AES-256' | 'AES-128';
    password: string;
  };
  notifications: {
    email: {
      enabled: boolean;
      recipients: string[];
      onSuccess: boolean;
      onFailure: boolean;
      onWarning: boolean;
    };
    webhook: {
      enabled: boolean;
      url: string;
      secret: string;
    };
  };
  monitoring: {
    healthCheck: boolean;
    integrityCheck: boolean;
    performanceMetrics: boolean;
    alertThresholds: {
      backupSize: number; // in GB
      duration: number; // in minutes
      failureCount: number;
    };
  };
  restore: {
    pointInTimeRecovery: boolean;
    incrementalBackups: boolean;
    testRestores: {
      enabled: boolean;
      frequency: 'weekly' | 'monthly';
    };
  };
}

interface BackupSettingsProps {
  data: BackupSettingsData;
  onChange: (data: BackupSettingsData) => void;
}

export default function BackupSettings({ data, onChange }: BackupSettingsProps) {
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({});
  const [newRecipient, setNewRecipient] = useState('');

  const updateData = (updates: Partial<BackupSettingsData>) => {
    onChange({ ...data, ...updates });
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const addEmailRecipient = () => {
    if (newRecipient.trim() && !data.notifications.email.recipients.includes(newRecipient.trim())) {
      updateData({
        notifications: {
          ...data.notifications,
          email: {
            ...data.notifications.email,
            recipients: [...data.notifications.email.recipients, newRecipient.trim()]
          }
        }
      });
      setNewRecipient('');
    }
  };

  const removeEmailRecipient = (index: number) => {
    updateData({
      notifications: {
        ...data.notifications,
        email: {
          ...data.notifications.email,
          recipients: data.notifications.email.recipients.filter((_, i) => i !== index)
        }
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Automatic Backup Configuration */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiClock className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Automatic Backup Schedule
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Automatic Backups</p>
              <p className="text-gray-400 text-sm">Automatically backup your website data on schedule</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.autoBackup.enabled}
                onChange={(e) => updateData({
                  autoBackup: { ...data.autoBackup, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.autoBackup.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Backup Frequency</label>
                <select
                  value={data.autoBackup.frequency}
                  onChange={(e) => updateData({
                    autoBackup: { ...data.autoBackup, frequency: e.target.value as 'hourly' | 'daily' | 'weekly' | 'monthly' }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="hourly">Every Hour</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Backup Time</label>
                <input
                  type="time"
                  value={data.autoBackup.time}
                  onChange={(e) => updateData({
                    autoBackup: { ...data.autoBackup, time: e.target.value }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Retention Period (days)</label>
                <input
                  type="number"
                  value={data.autoBackup.retention}
                  onChange={(e) => updateData({
                    autoBackup: { ...data.autoBackup, retention: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="1"
                  max="365"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Backups to Keep</label>
                <input
                  type="number"
                  value={data.autoBackup.maxBackups}
                  onChange={(e) => updateData({
                    autoBackup: { ...data.autoBackup, maxBackups: parseInt(e.target.value) }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="1"
                  max="100"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Backup Types */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiDatabase className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Backup Content Types
        </h3>
        <p className="text-gray-400 text-sm mb-4">Select what data to include in your backups</p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
            <div>
              <p className="text-white font-medium text-sm">Database</p>
              <p className="text-gray-400 text-xs">User data, properties, settings</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.backupTypes.database}
                onChange={(e) => updateData({
                  backupTypes: { ...data.backupTypes, database: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
            <div>
              <p className="text-white font-medium text-sm">Application Files</p>
              <p className="text-gray-400 text-xs">Code, templates, configurations</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.backupTypes.files}
                onChange={(e) => updateData({
                  backupTypes: { ...data.backupTypes, files: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
            <div>
              <p className="text-white font-medium text-sm">Media Files</p>
              <p className="text-gray-400 text-xs">Images, videos, documents</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.backupTypes.media}
                onChange={(e) => updateData({
                  backupTypes: { ...data.backupTypes, media: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
            <div>
              <p className="text-white font-medium text-sm">Configurations</p>
              <p className="text-gray-400 text-xs">Settings, preferences, themes</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.backupTypes.configurations}
                onChange={(e) => updateData({
                  backupTypes: { ...data.backupTypes, configurations: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700/50 rounded-lg">
            <div>
              <p className="text-white font-medium text-sm">User Uploads</p>
              <p className="text-gray-400 text-xs">Property photos, documents</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.backupTypes.userUploads}
                onChange={(e) => updateData({
                  backupTypes: { ...data.backupTypes, userUploads: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Storage Options */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiCloud className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Storage Destinations
        </h3>
        
        {/* Local Storage */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <div>
              <p className="text-white font-medium">Local Storage</p>
              <p className="text-gray-400 text-sm">Store backups on the server's local storage</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.storage.local.enabled}
                onChange={(e) => updateData({
                  storage: {
                    ...data.storage,
                    local: { ...data.storage.local, enabled: e.target.checked }
                  }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.storage.local.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Storage Path</label>
                <input
                  type="text"
                  value={data.storage.local.path}
                  onChange={(e) => updateData({
                    storage: {
                      ...data.storage,
                      local: { ...data.storage.local, path: e.target.value }
                    }
                  })}
                  placeholder="/var/backups/mazaya"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Max Storage Size (GB)</label>
                <input
                  type="number"
                  value={data.storage.local.maxSize}
                  onChange={(e) => updateData({
                    storage: {
                      ...data.storage,
                      local: { ...data.storage.local, maxSize: parseInt(e.target.value) }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  min="1"
                />
              </div>
            </div>
          )}
        </div>

        {/* Cloud Storage */}
        <div>
          <h4 className="text-white font-medium mb-3">Cloud Storage Providers</h4>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-400 mb-2">Primary Cloud Provider</label>
            <select
              value={data.storage.cloud.primary}
              onChange={(e) => updateData({
                storage: {
                  ...data.storage,
                  cloud: { ...data.storage.cloud, primary: e.target.value as 'aws' | 'google' | 'azure' | 'dropbox' | 'onedrive' }
                }
              })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            >
              <option value="aws">Amazon S3</option>
              <option value="google">Google Cloud Storage</option>
              <option value="azure">Azure Blob Storage</option>
              <option value="dropbox">Dropbox</option>
              <option value="onedrive">Microsoft OneDrive</option>
            </select>
          </div>

          {/* AWS S3 Configuration */}
          {data.storage.cloud.primary === 'aws' && (
            <div className="space-y-4 p-4 bg-gray-700/30 rounded-lg">
              <div className="flex items-center justify-between">
                <h5 className="text-white font-medium">Amazon S3 Configuration</h5>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.storage.cloud.aws.enabled}
                    onChange={(e) => updateData({
                      storage: {
                        ...data.storage,
                        cloud: {
                          ...data.storage.cloud,
                          aws: { ...data.storage.cloud.aws, enabled: e.target.checked }
                        }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              {data.storage.cloud.aws.enabled && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">S3 Bucket Name</label>
                    <input
                      type="text"
                      value={data.storage.cloud.aws.bucket}
                      onChange={(e) => updateData({
                        storage: {
                          ...data.storage,
                          cloud: {
                            ...data.storage.cloud,
                            aws: { ...data.storage.cloud.aws, bucket: e.target.value }
                          }
                        }
                      })}
                      placeholder="mazaya-backups"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">AWS Region</label>
                    <select
                      value={data.storage.cloud.aws.region}
                      onChange={(e) => updateData({
                        storage: {
                          ...data.storage,
                          cloud: {
                            ...data.storage.cloud,
                            aws: { ...data.storage.cloud.aws, region: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    >
                      <option value="us-east-1">US East (N. Virginia)</option>
                      <option value="us-west-2">US West (Oregon)</option>
                      <option value="eu-west-1">Europe (Ireland)</option>
                      <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                      <option value="me-south-1">Middle East (Bahrain)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Access Key ID</label>
                    <div className="relative">
                      <input
                        type={showPasswords['awsAccessKey'] ? 'text' : 'password'}
                        value={data.storage.cloud.aws.accessKeyId}
                        onChange={(e) => updateData({
                          storage: {
                            ...data.storage,
                            cloud: {
                              ...data.storage.cloud,
                              aws: { ...data.storage.cloud.aws, accessKeyId: e.target.value }
                            }
                          }
                        })}
                        className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('awsAccessKey')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                      >
                        {showPasswords['awsAccessKey'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Secret Access Key</label>
                    <div className="relative">
                      <input
                        type={showPasswords['awsSecretKey'] ? 'text' : 'password'}
                        value={data.storage.cloud.aws.secretAccessKey}
                        onChange={(e) => updateData({
                          storage: {
                            ...data.storage,
                            cloud: {
                              ...data.storage.cloud,
                              aws: { ...data.storage.cloud.aws, secretAccessKey: e.target.value }
                            }
                          }
                        })}
                        className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('awsSecretKey')}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                      >
                        {showPasswords['awsSecretKey'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-400 mb-2">Storage Class</label>
                    <select
                      value={data.storage.cloud.aws.storageClass}
                      onChange={(e) => updateData({
                        storage: {
                          ...data.storage,
                          cloud: {
                            ...data.storage.cloud,
                            aws: { ...data.storage.cloud.aws, storageClass: e.target.value as 'STANDARD' | 'STANDARD_IA' | 'GLACIER' | 'DEEP_ARCHIVE' }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    >
                      <option value="STANDARD">Standard (Frequent Access)</option>
                      <option value="STANDARD_IA">Standard-IA (Infrequent Access)</option>
                      <option value="GLACIER">Glacier (Archive)</option>
                      <option value="DEEP_ARCHIVE">Deep Archive (Long-term)</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Compression & Encryption */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiShield className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Compression & Encryption
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Compression */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Enable Compression</p>
                <p className="text-gray-400 text-sm">Reduce backup file sizes</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.compression.enabled}
                  onChange={(e) => updateData({
                    compression: { ...data.compression, enabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.compression.enabled && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Compression Level (1-9)</label>
                  <input
                    type="range"
                    min="1"
                    max="9"
                    value={data.compression.level}
                    onChange={(e) => updateData({
                      compression: { ...data.compression, level: parseInt(e.target.value) }
                    })}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>Fast (1)</span>
                    <span>Current: {data.compression.level}</span>
                    <span>Best (9)</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Compression Format</label>
                  <select
                    value={data.compression.format}
                    onChange={(e) => updateData({
                      compression: { ...data.compression, format: e.target.value as 'zip' | 'tar.gz' | '7z' }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  >
                    <option value="zip">ZIP (Universal)</option>
                    <option value="tar.gz">TAR.GZ (Linux/Unix)</option>
                    <option value="7z">7Z (High Compression)</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Encryption */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Enable Encryption</p>
                <p className="text-gray-400 text-sm">Encrypt backup files for security</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.encryption.enabled}
                  onChange={(e) => updateData({
                    encryption: { ...data.encryption, enabled: e.target.checked }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.encryption.enabled && (
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Encryption Algorithm</label>
                  <select
                    value={data.encryption.algorithm}
                    onChange={(e) => updateData({
                      encryption: { ...data.encryption, algorithm: e.target.value as 'AES-256' | 'AES-128' }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  >
                    <option value="AES-256">AES-256 (Recommended)</option>
                    <option value="AES-128">AES-128 (Faster)</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Encryption Password</label>
                  <div className="relative">
                    <input
                      type={showPasswords['encryption'] ? 'text' : 'password'}
                      value={data.encryption.password}
                      onChange={(e) => updateData({
                        encryption: { ...data.encryption, password: e.target.value }
                      })}
                      placeholder="Enter strong encryption password"
                      className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility('encryption')}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                    >
                      {showPasswords['encryption'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiAlertCircle className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Backup Notifications
        </h3>
        <div className="space-y-6">
          {/* Email Notifications */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <div>
                <p className="text-white font-medium">Email Notifications</p>
                <p className="text-gray-400 text-sm">Get notified about backup status via email</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.notifications.email.enabled}
                  onChange={(e) => updateData({
                    notifications: {
                      ...data.notifications,
                      email: { ...data.notifications.email, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.notifications.email.enabled && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Email Recipients</label>
                  <div className="flex space-x-2 mb-2">
                    <input
                      type="email"
                      value={newRecipient}
                      onChange={(e) => setNewRecipient(e.target.value)}
                      placeholder="<EMAIL>"
                      className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                    <button
                      onClick={addEmailRecipient}
                      className="px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#00C2FF]/90 transition-colors"
                    >
                      Add
                    </button>
                  </div>
                  
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {data.notifications.email.recipients.map((email, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded-md">
                        <span className="text-white text-sm">{email}</span>
                        <button
                          onClick={() => removeEmailRecipient(index)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Success Notifications</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.notifications.email.onSuccess}
                        onChange={(e) => updateData({
                          notifications: {
                            ...data.notifications,
                            email: { ...data.notifications.email, onSuccess: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Failure Notifications</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.notifications.email.onFailure}
                        onChange={(e) => updateData({
                          notifications: {
                            ...data.notifications,
                            email: { ...data.notifications.email, onFailure: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Warning Notifications</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.notifications.email.onWarning}
                        onChange={(e) => updateData({
                          notifications: {
                            ...data.notifications,
                            email: { ...data.notifications.email, onWarning: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Manual Backup Actions */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiSettings className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Manual Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center justify-center px-4 py-3 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors">
            <FiDownload className="mr-2 h-4 w-4" />
            Create Backup Now
          </button>
          
          <button className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <FiUpload className="mr-2 h-4 w-4" />
            Restore Backup
          </button>
          
          <button className="flex items-center justify-center px-4 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
            <FiRefreshCw className="mr-2 h-4 w-4" />
            Test Backup
          </button>
          
          <button className="flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            <FiCheckCircle className="mr-2 h-4 w-4" />
            Verify Integrity
          </button>
        </div>
        
        <div className="mt-6 p-4 bg-gray-700/50 rounded-lg">
          <h4 className="text-white font-medium text-sm mb-2">💡 Backup Best Practices:</h4>
          <ul className="text-xs text-gray-400 space-y-1">
            <li>• Test your backups regularly to ensure they can be restored</li>
            <li>• Use multiple storage locations (local + cloud) for redundancy</li>
            <li>• Enable encryption for sensitive data protection</li>
            <li>• Monitor backup sizes and adjust retention policies accordingly</li>
            <li>• Keep backup credentials secure and regularly rotated</li>
          </ul>
        </div>
      </div>
    </div>
  );
}