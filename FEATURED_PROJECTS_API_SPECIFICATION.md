# Featured Projects API Endpoints Specification

## Overview
This document specifies the API endpoints required for the **Featured Projects** section of the home page admin panel. The implementation follows the **exact same pattern** as the existing **Hero section** endpoints.

---

## 📋 Required API Endpoints

### 1. **GET** `/api/admin/home-page/featured-projects/`
**Purpose**: Fetch featured projects section content

**Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Query Parameters**:
- `lang` (optional): `en` | `ar` - Language preference

**Response**:
```json
{
  "success": true,
  "message": "Featured projects content retrieved successfully",
  "data": {
    "id": 1,
    "title": {
      "en": "Featured Projects",
      "ar": "المشاريع المميزة"
    },
    "description": {
      "en": "Discover our exceptional real estate developments with premium locations and world-class amenities",
      "ar": "اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى"
    },
    "button": {
      "text": {
        "en": "View All Projects",
        "ar": "عرض جميع المشاريع"
      },
      "link": "/projects"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

**Error Response**:
```json
{
  "success": false,
  "message": "Authentication required",
  "errors": {}
}
```

---

### 2. **PUT** `/api/admin/home-page/featured-projects/`
**Purpose**: Update all featured projects section content

**Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "title": {
    "en": "Featured Projects",
    "ar": "المشاريع المميزة"
  },
  "description": {
    "en": "Discover our exceptional real estate developments with premium locations and world-class amenities",
    "ar": "اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى"
  },
  "button": {
    "text": {
      "en": "View All Projects",
      "ar": "عرض جميع المشاريع"
    },
    "link": "/projects"
  }
}
```

**Response**:
```json
{
  "success": true,
  "message": "Featured projects section updated successfully",
  "data": {
    "id": 1,
    "title": {
      "en": "Featured Projects",
      "ar": "المشاريع المميزة"
    },
    "description": {
      "en": "Discover our exceptional real estate developments with premium locations and world-class amenities",
      "ar": "اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى"
    },
    "button": {
      "text": {
        "en": "View All Projects",
        "ar": "عرض جميع المشاريع"
      },
      "link": "/projects"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

**Validation Error Response**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "title.en": ["This field is required"],
    "title.ar": ["This field is required"],
    "button.link": ["Enter a valid URL"]
  }
}
```

---

### 3. **PATCH** `/api/admin/home-page/featured-projects/section/`
**Purpose**: Update specific section of featured projects content

**Headers**:
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "section": "title",
  "data": {
    "en": "Updated Featured Projects",
    "ar": "المشاريع المميزة المحدثة"
  }
}
```

**Valid Section Names**:
- `title` - Section title
- `description` - Section description
- `button` - Call-to-action button

**Response**:
```json
{
  "success": true,
  "message": "Title section updated successfully",
  "data": {
    "id": 1,
    "title": {
      "en": "Updated Featured Projects",
      "ar": "المشاريع المميزة المحدثة"
    },
    "description": {
      "en": "Discover our exceptional real estate developments...",
      "ar": "اكتشف تطويراتنا العقارية الاستثنائية..."
    },
    "button": {
      "text": {
        "en": "View All Projects",
        "ar": "عرض جميع المشاريع"
      },
      "link": "/projects"
    },
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T14:45:00Z",
    "updated_by": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

---

## 🗄️ Database Model

### Django Model Example:
```python
from django.db import models
from django.contrib.auth.models import User

class FeaturedProjectsContent(models.Model):
    # Title fields
    title_en = models.CharField(max_length=100, help_text="English title (max 100 characters)")
    title_ar = models.CharField(max_length=100, help_text="Arabic title (max 100 characters)")
    
    # Description fields
    description_en = models.TextField(max_length=500, help_text="English description (max 500 characters)")
    description_ar = models.TextField(max_length=500, help_text="Arabic description (max 500 characters)")
    
    # Button fields
    button_text_en = models.CharField(max_length=50, help_text="English button text (max 50 characters)")
    button_text_ar = models.CharField(max_length=50, help_text="Arabic button text (max 50 characters)")
    button_link = models.URLField(help_text="Button link URL")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        verbose_name = "Featured Projects Content"
        verbose_name_plural = "Featured Projects Content"

    def __str__(self):
        return f"Featured Projects Content (Updated: {self.updated_at})"
```

---

## 🔒 Authentication & Permissions

### Required Permissions:
- **GET**: Authenticated admin users only
- **PUT**: Authenticated admin users with content management permissions
- **PATCH**: Authenticated admin users with content management permissions

### Authentication Method:
- **JWT Bearer Token** in Authorization header
- Token must be valid and not expired
- User must have appropriate permissions

---

## ✅ Validation Rules

### Title Fields:
- **Required**: Yes
- **Max Length**: 100 characters each (EN/AR)
- **Type**: String

### Description Fields:
- **Required**: Yes
- **Max Length**: 500 characters each (EN/AR)
- **Type**: Text

### Button Fields:
- **Text Required**: Yes
- **Text Max Length**: 50 characters each (EN/AR)
- **Link Required**: Yes
- **Link Type**: Valid URL or relative path
- **Link Format**: Must be valid URL format

---

## 📝 Implementation Notes

### Language Support:
- All content fields support **English (en)** and **Arabic (ar)**
- API responses should include both language versions
- Query parameter `lang` can be used for language-specific responses

### Response Format:
- Always include `success` boolean field
- Include descriptive `message` field
- Include `data` object with full content structure
- Include `errors` object for validation failures

### Error Handling:
- **401**: Authentication required/invalid token
- **403**: Insufficient permissions
- **400**: Validation errors
- **404**: Content not found
- **500**: Internal server error

### Cache Headers:
- Include appropriate cache control headers
- Support cache busting for content updates
- Language-aware caching

---

## 🧪 Test Cases

### Successful GET Request:
```bash
curl -X GET "http://localhost:8000/api/admin/home-page/featured-projects/" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

### Successful PUT Request:
```bash
curl -X PUT "http://localhost:8000/api/admin/home-page/featured-projects/" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": {"en": "New Title", "ar": "عنوان جديد"},
    "description": {"en": "New description", "ar": "وصف جديد"},
    "button": {
      "text": {"en": "Click Here", "ar": "انقر هنا"},
      "link": "/projects"
    }
  }'
```

### Successful PATCH Request:
```bash
curl -X PATCH "http://localhost:8000/api/admin/home-page/featured-projects/section/" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "section": "title",
    "data": {"en": "Updated Title", "ar": "عنوان محدث"}
  }'
```

---

## 🔄 Integration with Frontend

The frontend expects:
1. **Exact response structure** as specified above
2. **Proper error handling** with structured error messages  
3. **Authentication enforcement** on all endpoints
4. **Language parameter support** for all requests
5. **Metadata inclusion** (timestamps, updated_by)
6. **Validation** following the specified rules

---

## 🚀 Implementation Priority

1. **HIGH**: GET endpoint - Required for content loading
2. **HIGH**: PUT endpoint - Required for bulk content updates
3. **MEDIUM**: PATCH endpoint - Used for section-specific updates
4. **HIGH**: Authentication & permissions setup
5. **MEDIUM**: Validation & error handling
6. **LOW**: Advanced caching features

---

This specification ensures **100% compatibility** with the existing frontend implementation and follows the **exact same pattern** as the Hero section endpoints. 