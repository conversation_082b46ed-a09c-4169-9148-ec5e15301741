"use client";

import React, { useState } from 'react';
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiMove, FiUser, FiUpload } from 'react-icons/fi';

interface TeamMember {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  position: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  initials: string;
  imageUrl?: string;
  order: number;
}

interface TeamSection {
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  members: TeamMember[];
}

export default function TeamManagementPage() {
  // Initial data based on the current team cards
  const [teamData, setTeamData] = useState<TeamSection>({
    title: {
      en: "Our Leadership Team",
      ar: "فريق القيادة"
    },
    description: {
      en: "Meet the exceptional individuals who drive our vision forward and lead our company to success.",
      ar: "تعرف على الأفراد الاستثنائيين الذين يقودون رؤيتنا إلى الأمام ويقودون شركتنا نحو النجاح."
    },
    members: [
      {
        id: "ah<PERSON>-<PERSON>-man<PERSON><PERSON>",
        name: { en: "<PERSON>", ar: "أحمد المنصور" },
        position: { en: "Chief Executive Officer", ar: "الرئيس التنفيذي" },
        description: {
          en: "Ahmed brings over 20 years of experience in real estate development and investment. He has led the development of multiple award-winning projects across the region.",
          ar: "يجلب أحمد أكثر من 20 عامًا من الخبرة في التطوير العقاري والاستثمار. قاد تطوير مشاريع متعددة حائزة على جوائز في جميع أنحاء المنطقة."
        },
        initials: "AA",
        order: 1
      },
      {
        id: "sara-khalid",
        name: { en: "Sara Khalid", ar: "سارة خالد" },
        position: { en: "Chief Financial Officer", ar: "المدير المالي" },
        description: {
          en: "Sara oversees all financial operations with her extensive background in investment banking and financial management for major development projects.",
          ar: "تشرف سارة على جميع العمليات المالية بخلفيتها الواسعة في الاستثمار المصرفي والإدارة المالية لمشاريع التطوير الكبرى."
        },
        initials: "SK",
        order: 2
      },
      {
        id: "omar-rashid",
        name: { en: "Omar Rashid", ar: "عمر راشد" },
        position: { en: "Head of Development", ar: "رئيس التطوير" },
        description: {
          en: "Omar leads our development team with his extensive expertise in architectural design and project management for luxury residential and commercial properties.",
          ar: "يقود عمر فريق التطوير لدينا بخبرته الواسعة في التصميم المعماري وإدارة المشاريع للعقارات السكنية والتجارية الفاخرة."
        },
        initials: "OR",
        order: 3
      },
      {
        id: "layla-mahmoud",
        name: { en: "Layla Mahmoud", ar: "ليلى محمود" },
        position: { en: "Director of Investments", ar: "مدير الاستثمارات" },
        description: {
          en: "Layla's strategic vision guides our investment portfolio, leveraging her background in real estate finance and market analysis to maximize returns.",
          ar: "تسترشد رؤية ليلى الاستراتيجية بمحفظة الاستثمارات لدينا، مستفيدة من خلفيتها في التمويل العقاري وتحليل السوق لتعظيم العائدات."
        },
        initials: "LM",
        order: 4
      }
    ]
  });

  const [editingSection, setEditingSection] = useState(false);
  const [editingMember, setEditingMember] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);

  const handleSectionSave = (newData: Partial<TeamSection>) => {
    setTeamData(prev => ({ ...prev, ...newData }));
    setEditingSection(false);
  };

  const handleMemberSave = (memberId: string, newMember: TeamMember) => {
    setTeamData(prev => ({
      ...prev,
      members: prev.members.map(m => m.id === memberId ? newMember : m)
    }));
    setEditingMember(null);
  };

  const handleMemberDelete = (memberId: string) => {
    if (confirm('Are you sure you want to delete this team member?')) {
      setTeamData(prev => ({
        ...prev,
        members: prev.members.filter(m => m.id !== memberId)
      }));
    }
  };

  const handleMemberAdd = (newMember: TeamMember) => {
    const maxOrder = Math.max(...teamData.members.map(m => m.order), 0);
    const memberWithOrder = { ...newMember, order: maxOrder + 1 };
    setTeamData(prev => ({
      ...prev,
      members: [...prev.members, memberWithOrder]
    }));
    setShowAddForm(false);
  };

  const handleDragStart = (memberId: string) => {
    setDraggedItem(memberId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (!draggedItem || draggedItem === targetId) return;

    const draggedIndex = teamData.members.findIndex(m => m.id === draggedItem);
    const targetIndex = teamData.members.findIndex(m => m.id === targetId);

    const newMembers = [...teamData.members];
    const [draggedMember] = newMembers.splice(draggedIndex, 1);
    newMembers.splice(targetIndex, 0, draggedMember);

    // Update order values
    const updatedMembers = newMembers.map((member, index) => ({
      ...member,
      order: index + 1
    }));

    setTeamData(prev => ({ ...prev, members: updatedMembers }));
    setDraggedItem(null);
  };

  const generateInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSaveAll = () => {
    // Here you would typically send the data to your backend
    console.log('Saving team data:', teamData);
    alert('Team data saved successfully!');
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">About Us - Team Section</h1>
          <p className="text-gray-400 mt-1">Manage the leadership team section of the about page</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Section Header</h2>
            <button
              onClick={() => setEditingSection(!editingSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection ? (
            <SectionEditForm
              data={teamData}
              onSave={handleSectionSave}
              onCancel={() => setEditingSection(false)}
            />
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                <p className="text-white">{teamData.title.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                <p className="text-white">{teamData.title.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                <p className="text-gray-300 text-sm">{teamData.description.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                <p className="text-gray-300 text-sm">{teamData.description.ar}</p>
              </div>
            </div>
          )}
        </div>

        {/* Team Members Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">Team Members ({teamData.members.length})</h2>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add New Member
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {teamData.members
              .sort((a, b) => a.order - b.order)
              .map((member) => (
                <div
                  key={member.id}
                  draggable
                  onDragStart={() => handleDragStart(member.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, member.id)}
                  className={`bg-gray-700 rounded-lg border border-gray-600 transition-all hover:border-gray-500 cursor-move ${
                    draggedItem === member.id ? 'opacity-50' : ''
                  }`}
                >
                  {/* Card Header */}
                  <div className="h-1.5 w-full bg-gradient-to-r from-[#00C2FF] to-[#9747FF]"></div>
                  
                  {/* Card Actions */}
                  <div className="flex justify-between items-center p-3 border-b border-gray-600">
                    <div className="flex items-center">
                      <FiMove className="h-4 w-4 text-gray-500 mr-2" />
                      <span className="text-xs text-gray-400">Order: {member.order}</span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingMember(member.id)}
                        className="p-1 text-gray-400 hover:text-[#00C2FF] transition-colors"
                      >
                        <FiEdit3 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleMemberDelete(member.id)}
                        className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {editingMember === member.id ? (
                    <div className="p-4">
                      <MemberEditForm
                        member={member}
                        onSave={(newMember) => handleMemberSave(member.id, newMember)}
                        onCancel={() => setEditingMember(null)}
                      />
                    </div>
                  ) : (
                    <div className="p-4 text-center">
                      {/* Profile Avatar */}
                      <div className="w-16 h-16 mx-auto relative mb-4">
                        {member.imageUrl ? (
                          <img
                            src={member.imageUrl}
                            alt={member.name.en}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full rounded-full flex items-center justify-center bg-[#00C2FF]/10">
                            <span className="text-[#00C2FF] font-bold text-lg">
                              {member.initials}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Member Info */}
                      <h3 className="text-lg font-bold text-white mb-1.5">{member.name.en}</h3>
                      <p className="text-[#00C2FF] font-medium text-sm tracking-wide mb-3">
                        {member.position.en}
                      </p>
                      <p className="text-gray-300 text-sm leading-relaxed line-clamp-3">
                        {member.description.en}
                      </p>

                      {/* Arabic Info (collapsed) */}
                      <div className="mt-3 pt-3 border-t border-gray-600">
                        <p className="text-gray-400 text-sm">{member.name.ar}</p>
                        <p className="text-gray-500 text-xs">{member.position.ar}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
          </div>

          {showAddForm && (
            <div className="mt-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <MemberAddForm
                onSave={handleMemberAdd}
                onCancel={() => setShowAddForm(false)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Section Edit Form Component
function SectionEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: TeamSection;
  onSave: (data: Partial<TeamSection>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
        <textarea
          value={formData.description.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, en: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
        <textarea
          value={formData.description.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, ar: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Member Edit Form Component
function MemberEditForm({ 
  member, 
  onSave, 
  onCancel 
}: { 
  member: TeamMember;
  onSave: (member: TeamMember) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(member);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(member.imageUrl || '');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the image persists
      setFormData(prev => ({ ...prev, imageUrl: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById(`image-upload-edit-${member.id}`)?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const generateInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {/* Hidden File Input */}
      <input
        id={`image-upload-edit-${member.id}`}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Name (English)"
          value={formData.name.en}
          onChange={(e) => {
            const newName = e.target.value;
            setFormData(prev => ({
              ...prev,
              name: { ...prev.name, en: newName },
              initials: generateInitials(newName)
            }));
          }}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Name (Arabic)"
          value={formData.name.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            name: { ...prev.name, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Position (English)"
          value={formData.position.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            position: { ...prev.position, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Position (Arabic)"
          value={formData.position.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            position: { ...prev.position, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Initials"
          value={formData.initials}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            initials: e.target.value.toUpperCase().slice(0, 3)
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
          maxLength={3}
        />
        {/* Clickable Image Upload Area */}
        <div className="relative">
          <div 
            onClick={handleImageClick}
            className="w-full h-8 bg-gray-600 rounded cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group flex items-center justify-center"
          >
            {previewUrl ? (
              <div className="relative w-full h-full">
                <img 
                  src={previewUrl} 
                  alt="Preview"
                  className="w-full h-full object-cover rounded"
                  onError={() => setPreviewUrl('')}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                  <FiUpload className="text-white opacity-0 group-hover:opacity-100 h-3 w-3" />
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-gray-400 group-hover:text-[#00C2FF]">
                <FiUpload className="h-3 w-3" />
                <span className="text-xs">Upload</span>
              </div>
            )}
          </div>
          {selectedFile && (
            <p className="text-xs text-gray-400 mt-1 truncate">
              {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          )}
        </div>
      </div>

      <textarea
        placeholder="Description (English)"
        value={formData.description.en}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          description: { ...prev.description, en: e.target.value }
        }))}
        rows={2}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      />

      <textarea
        placeholder="Description (Arabic)"
        value={formData.description.ar}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          description: { ...prev.description, ar: e.target.value }
        }))}
        rows={2}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      />

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save
        </button>
      </div>
    </form>
  );
}

// Member Add Form Component
function MemberAddForm({ 
  onSave, 
  onCancel 
}: { 
  onSave: (member: TeamMember) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<TeamMember>({
    id: '',
    name: { en: '', ar: '' },
    position: { en: '', ar: '' },
    description: { en: '', ar: '' },
    initials: '',
    imageUrl: '',
    order: 0
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the image persists
      setFormData(prev => ({ ...prev, imageUrl: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('image-upload-add-member')?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.en || !formData.position.en) {
      alert('Please fill in all required fields');
      return;
    }

    const newMember = {
      ...formData,
      id: formData.name.en.toLowerCase().replace(/\s+/g, '-'),
      initials: formData.initials || generateInitials(formData.name.en)
    };

    onSave(newMember);
  };

  const generateInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Team Member</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Hidden File Input */}
        <input
          id="image-upload-add-member"
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Name (English) *</label>
            <input
              type="text"
              value={formData.name.en}
              onChange={(e) => {
                const newName = e.target.value;
                setFormData(prev => ({
                  ...prev,
                  name: { ...prev.name, en: newName },
                  initials: generateInitials(newName)
                }));
              }}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Name (Arabic)</label>
            <input
              type="text"
              value={formData.name.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                name: { ...prev.name, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Position (English) *</label>
            <input
              type="text"
              value={formData.position.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                position: { ...prev.position, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Position (Arabic)</label>
            <input
              type="text"
              value={formData.position.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                position: { ...prev.position, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Initials</label>
            <input
              type="text"
              value={formData.initials}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                initials: e.target.value.toUpperCase().slice(0, 3)
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              maxLength={3}
              placeholder="Auto-generated from name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Profile Image</label>
            <div 
              onClick={handleImageClick}
              className="w-full h-12 bg-gray-600 rounded cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
            >
              {previewUrl ? (
                <div className="relative w-full h-full">
                  <img 
                    src={previewUrl} 
                    alt="Preview"
                    className="w-full h-full object-cover rounded"
                    onError={() => setPreviewUrl('')}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                    <FiUpload className="text-white opacity-0 group-hover:opacity-100 h-5 w-5" />
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                  <div className="text-center">
                    <FiUpload className="h-5 w-5 mx-auto mb-1" />
                    <p className="text-xs">Click to upload</p>
                  </div>
                </div>
              )}
            </div>
            {selectedFile && (
              <p className="text-xs text-gray-400 mt-1">
                {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
              </p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            Add Member
          </button>
        </div>
      </form>
    </div>
  );
} 