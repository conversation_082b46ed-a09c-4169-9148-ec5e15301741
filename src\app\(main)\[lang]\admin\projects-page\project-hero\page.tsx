"use client";

import { useState } from 'react';
import { FiSave, FiEdit3, FiX, FiImage, FiUpload } from 'react-icons/fi';

interface ProjectHeroContent {
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
    description: {    en: string;    ar: string;  };  scrollText: {
    en: string;
    ar: string;
  };
  images: {
    topRight: string;
    bottomLeft: string;
    center: string;
  };
}

export default function ProjectsPageHeroManagement() {
  const [heroContent, setHeroContent] = useState<ProjectHeroContent>({
    badge: {
      en: "Premium Development Portfolio",
      ar: "محفظة التطوير المتميزة"
    },
    title: {
      en: "Our Projects",
      ar: "مشاريعنا"
    },
        description: {      en: "Explore our diverse portfolio of premium real estate developments across the UAE, setting new standards in modern living and investment value.",      ar: "استكشف محفظتنا المتنوعة من التطويرات العقارية المتميزة عبر دولة الإمارات العربية المتحدة، والتي تضع معايير جديدة في المعيشة العصرية وقيمة الاستثمار."    },    scrollText: {
      en: "Scroll to explore",
      ar: "انتقل لاستكشاف المزيد"
    },
    images: {
      topRight: "/images/project-1.jpg",
      bottomLeft: "/images/project-3.jpg",
      center: "/images/project-2.jpg"
    }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    // TODO: Implement save functionality
    console.log('Saving Projects Hero content:', heroContent);
    setTimeout(() => {
      setIsLoading(false);
      alert('Projects Hero content saved successfully!');
    }, 1000);
  };

  const handleImageUpload = (imageKey: 'topRight' | 'bottomLeft' | 'center', file: File | null) => {
    if (file) {
      // Create a temporary URL for preview
      const objectUrl = URL.createObjectURL(file);
      setHeroContent(prev => ({
        ...prev,
        images: {
          ...prev.images,
          [imageKey]: objectUrl
        }
      }));
    }
  };

  

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Projects Page Hero Management</h1>
          <p className="text-gray-400 mt-1">
            Manage the hero section content for the Projects page
          </p>
        </div>
        <button
          onClick={handleSave}
          disabled={isLoading}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-500 disabled:opacity-50 text-white rounded-lg font-medium transition-colors duration-200"
        >
          <FiSave className="mr-2 h-4 w-4" />
          {isLoading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Badge Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <span className="mr-2">🏷️</span>
            Badge Text
          </h2>
          <button
            onClick={() => setEditingSection(editingSection === 'badge' ? null : 'badge')}
            className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
          >
            {editingSection === 'badge' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
            {editingSection === 'badge' ? 'Cancel' : 'Edit'}
          </button>
        </div>

        {editingSection === 'badge' ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (English)</label>
                <input
                  type="text"
                  value={heroContent.badge.en}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    badge: { ...prev.badge, en: e.target.value }
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (Arabic)</label>
                <input
                  type="text"
                  value={heroContent.badge.ar}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    badge: { ...prev.badge, ar: e.target.value }
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-400">English:</span>
              <p className="text-white mt-1">{heroContent.badge.en}</p>
            </div>
            <div>
              <span className="text-sm text-gray-400">Arabic:</span>
              <p className="text-white mt-1">{heroContent.badge.ar}</p>
            </div>
          </div>
        )}
      </div>

      {/* Title Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <span className="mr-2">📝</span>
            Main Title
          </h2>
          <button
            onClick={() => setEditingSection(editingSection === 'title' ? null : 'title')}
            className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
          >
            {editingSection === 'title' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
            {editingSection === 'title' ? 'Cancel' : 'Edit'}
          </button>
        </div>

        {editingSection === 'title' ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                <input
                  type="text"
                  value={heroContent.title.en}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    title: { ...prev.title, en: e.target.value }
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                <input
                  type="text"
                  value={heroContent.title.ar}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    title: { ...prev.title, ar: e.target.value }
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-400">English:</span>
              <p className="text-white mt-1 text-2xl font-bold">{heroContent.title.en}</p>
            </div>
            <div>
              <span className="text-sm text-gray-400">Arabic:</span>
              <p className="text-white mt-1 text-2xl font-bold">{heroContent.title.ar}</p>
            </div>
          </div>
        )}
      </div>

      {/* Description Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <span className="mr-2">📄</span>
            Description
          </h2>
          <button
            onClick={() => setEditingSection(editingSection === 'description' ? null : 'description')}
            className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
          >
            {editingSection === 'description' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
            {editingSection === 'description' ? 'Cancel' : 'Edit'}
          </button>
        </div>

        {editingSection === 'description' ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                <textarea
                  value={heroContent.description.en}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    description: { ...prev.description, en: e.target.value }
                  }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                <textarea
                  value={heroContent.description.ar}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    description: { ...prev.description, ar: e.target.value }
                  }))}
                  rows={4}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-400">English:</span>
              <p className="text-gray-300 mt-1">{heroContent.description.en}</p>
            </div>
            <div>
              <span className="text-sm text-gray-400">Arabic:</span>
              <p className="text-gray-300 mt-1">{heroContent.description.ar}</p>
            </div>
          </div>
        )}
      </div>

      

      {/* Scroll Text Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <span className="mr-2">⬇️</span>
            Scroll Text
          </h2>
          <button
            onClick={() => setEditingSection(editingSection === 'scroll' ? null : 'scroll')}
            className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
          >
            {editingSection === 'scroll' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
            {editingSection === 'scroll' ? 'Cancel' : 'Edit'}
          </button>
        </div>

        {editingSection === 'scroll' ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Scroll Text (English)</label>
                <input
                  type="text"
                  value={heroContent.scrollText.en}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    scrollText: { ...prev.scrollText, en: e.target.value }
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Scroll Text (Arabic)</label>
                <input
                  type="text"
                  value={heroContent.scrollText.ar}
                  onChange={(e) => setHeroContent(prev => ({
                    ...prev,
                    scrollText: { ...prev.scrollText, ar: e.target.value }
                  }))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-400">English:</span>
              <p className="text-white mt-1">{heroContent.scrollText.en}</p>
            </div>
            <div>
              <span className="text-sm text-gray-400">Arabic:</span>
              <p className="text-white mt-1">{heroContent.scrollText.ar}</p>
            </div>
          </div>
        )}
      </div>

      {/* Images Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <FiImage className="mr-2 h-5 w-5" />
            Hero Images
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Top Right Image */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-400">Top Right Image</label>
            <div className="relative group">
              <img 
                src={heroContent.images.topRight} 
                alt="Top Right" 
                className="w-full h-32 object-cover rounded-lg border border-gray-600"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <label className="cursor-pointer text-white flex items-center space-x-2">
                  <FiUpload className="h-5 w-5" />
                  <span>Change Image</span>
                  <input 
                    type="file" 
                    accept="image/*" 
                    className="hidden"
                    onChange={(e) => handleImageUpload('topRight', e.target.files?.[0] || null)}
                  />
                </label>
              </div>
            </div>
          </div>

          {/* Center Image */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-400">Center Image</label>
            <div className="relative group">
              <img 
                src={heroContent.images.center} 
                alt="Center" 
                className="w-full h-32 object-cover rounded-lg border border-gray-600"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <label className="cursor-pointer text-white flex items-center space-x-2">
                  <FiUpload className="h-5 w-5" />
                  <span>Change Image</span>
                  <input 
                    type="file" 
                    accept="image/*" 
                    className="hidden"
                    onChange={(e) => handleImageUpload('center', e.target.files?.[0] || null)}
                  />
                </label>
              </div>
            </div>
          </div>

          {/* Bottom Left Image */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-400">Bottom Left Image</label>
            <div className="relative group">
              <img 
                src={heroContent.images.bottomLeft} 
                alt="Bottom Left" 
                className="w-full h-32 object-cover rounded-lg border border-gray-600"
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <label className="cursor-pointer text-white flex items-center space-x-2">
                  <FiUpload className="h-5 w-5" />
                  <span>Change Image</span>
                  <input 
                    type="file" 
                    accept="image/*" 
                    className="hidden"
                    onChange={(e) => handleImageUpload('bottomLeft', e.target.files?.[0] || null)}
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      
    </div>
  );
} 