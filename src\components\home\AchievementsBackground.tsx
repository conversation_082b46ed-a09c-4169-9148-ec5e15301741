"use client";

import { useEffect, useRef, useState } from "react";

const AchievementsBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Create intersection observer to detect when section is visible
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    // Only start the animation if the component is visible
    if (!isVisible) return;

    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    let animationFrameId: number;
    let pixelRatio = window.devicePixelRatio || 1;
    let time = 0;

    // Set canvas dimensions and handle high DPI displays
    const handleResize = () => {
      pixelRatio = window.devicePixelRatio || 1;
      
      // Get actual container dimensions
      const rect = container.getBoundingClientRect();
      const displayWidth = rect.width;
      const displayHeight = rect.height;
      
      // Set canvas size for high DPI displays
      canvas.width = displayWidth * pixelRatio;
      canvas.height = displayHeight * pixelRatio;
      
      // Scale all drawing operations
      ctx.scale(pixelRatio, pixelRatio);
      
      // Set canvas CSS size
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;
    };

    // Growing Building Class
    class GrowingBuilding {
      x: number;
      y: number;
      width: number;
      maxHeight: number;
      currentHeight: number;
      growthSpeed: number;
      color: string;
      windows: {x: number, y: number, width: number, height: number, lit: boolean}[];
      completed: boolean;
      hasConstruction: boolean;
      constructionY: number;

      constructor(x: number, displayHeight: number) {
        this.width = 40 + Math.random() * 60;
        this.x = x;
        this.maxHeight = 100 + Math.random() * 200;
        this.currentHeight = 0;
        this.y = displayHeight - this.maxHeight;
        this.growthSpeed = 0.2 + Math.random() * 0.4;
        this.color = `rgba(var(--color-text))`;
        this.windows = [];
        this.completed = false;
        this.hasConstruction = Math.random() > 0.3;
        this.constructionY = 0;
        
        // Create windows
        this.generateWindows();
      }
      
      generateWindows() {
        this.windows = [];
        const rows = Math.floor(this.maxHeight / 20);
        const cols = Math.floor(this.width / 15);
        
        for (let row = 0; row < rows; row++) {
          for (let col = 0; col < cols; col++) {
            if (Math.random() > 0.3) { // Not all positions have windows
              this.windows.push({
                x: this.x + 5 + (col * 15),
                y: this.y + 5 + (row * 20),
                width: 8,
                height: 12,
                lit: Math.random() > 0.5
              });
            }
          }
        }
      }

      update() {
        if (this.currentHeight < this.maxHeight) {
          this.currentHeight += this.growthSpeed;
          if (this.currentHeight > this.maxHeight) {
            this.currentHeight = this.maxHeight;
            this.completed = true;
          }
        }
        
        // Move construction element upward with the building
        if (this.hasConstruction && !this.completed) {
          this.constructionY = this.y + (this.maxHeight - this.currentHeight);
        }
      }

      draw(ctx: CanvasRenderingContext2D, displayHeight: number) {
        const visibleHeight = Math.min(this.currentHeight, this.maxHeight);
        const buildingY = displayHeight - visibleHeight;
        
        // Calculate construction progress (0 to 1)
        const progress = this.currentHeight / this.maxHeight;
        
        // Create a color transition from dark to light
        // Start with very dark color (almost black) and transition to final light color
        const startR = 10;  // Dark gray/near black R value
        const startG = 15;  // Dark gray/near black G value 
        const startB = 30;  // Dark gray/near black B value
        
        // End with lighter gray color
        const endR = 50;   // Lighter gray R value
        const endG = 60;   // Lighter gray G value
        const endB = 80;   // Lighter gray B value
        
        // Calculate current RGB based on progress
        const currentR = Math.floor(startR + progress * (endR - startR));
        const currentG = Math.floor(startG + progress * (endG - startG));
        const currentB = Math.floor(startB + progress * (endB - startB));
        
        // Apply the calculated color
        ctx.fillStyle = `rgb(${currentR}, ${currentG}, ${currentB})`;
        ctx.fillRect(this.x, buildingY, this.width, visibleHeight);
        
        // Draw windows only when the building is completed
        if (this.completed) {
          ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
          this.windows.forEach(window => {
            if (window.y + window.height <= buildingY + visibleHeight) {
              // Only draw lit windows
              if (window.lit) {
                ctx.fillRect(window.x, window.y, window.width, window.height);
              }
            }
          });
        }
        
        // Draw construction crane at the top if not completed
        if (this.hasConstruction && !this.completed) {
          this.drawConstruction(ctx, this.x + this.width/2, this.constructionY);
        }
        
        // Show construction lines while growing
        if (!this.completed) {
          // Draw scaffolding lines
          ctx.strokeStyle = 'rgba(var(--color-text), 0.2)';
          ctx.setLineDash([5, 5]);
          
          for (let y = 0; y < visibleHeight; y += 20) {
            ctx.beginPath();
            ctx.moveTo(this.x, buildingY + y);
            ctx.lineTo(this.x + this.width, buildingY + y);
            ctx.stroke();
          }
          
          ctx.setLineDash([]);
          
          // Draw progress percentage
          const percentage = Math.round((this.currentHeight / this.maxHeight) * 100);
          ctx.font = '10px Arial';
          ctx.fillStyle = 'rgba(var(--color-primary), 0.8)';
          ctx.fillText(`${percentage}%`, this.x + this.width/2 - 10, buildingY - 5);
        }
      }
      
      drawConstruction(ctx: CanvasRenderingContext2D, x: number, y: number) {
        ctx.strokeStyle = 'rgba(var(--color-text), 0.5)';
        ctx.lineWidth = 2;
        
        // Draw vertical pole
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x, y - 30);
        ctx.stroke();
        
        // Draw horizontal arm
        ctx.beginPath();
        ctx.moveTo(x, y - 25);
        ctx.lineTo(x + 30, y - 25);
        ctx.stroke();
        
        // Draw diagonal support
        ctx.beginPath();
        ctx.moveTo(x, y - 15);
        ctx.lineTo(x + 15, y - 25);
        ctx.stroke();
        
        // Draw hanging element
        ctx.beginPath();
        ctx.moveTo(x + 25, y - 25);
        ctx.lineTo(x + 25, y - 15);
        ctx.stroke();
        
        ctx.fillStyle = 'rgba(var(--color-primary), 0.5)';
        ctx.fillRect(x + 22, y - 15, 6, 6);
      }
    }

    // Initialize elements
    let buildings: GrowingBuilding[] = [];
    function initElements() {
      if (!canvas) return;
      const displayWidth = canvas.width / pixelRatio;
      const displayHeight = canvas.height / pixelRatio;
      
      // Create buildings
      buildings = [];
      let currentX = 0;
      
      while (currentX < displayWidth) {
        const building = new GrowingBuilding(currentX, displayHeight);
        buildings.push(building);
        currentX += building.width + 5;
      }
    }

    // Draw grid
    function drawGrid(ctx: CanvasRenderingContext2D, displayWidth: number, displayHeight: number) {
      ctx.strokeStyle = 'rgba(var(--color-text), 0.03)';
      ctx.lineWidth = 0.5;
      
      const gridSize = 30;
      
      // Draw vertical lines
      for (let x = 0; x < displayWidth; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, displayHeight);
        ctx.stroke();
      }
      
      // Draw horizontal lines
      for (let y = 0; y < displayHeight; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(displayWidth, y);
        ctx.stroke();
      }
    }

    // Handle window resizing
    window.addEventListener("resize", handleResize);
    handleResize();
    initElements();

    function animate() {
      if (!ctx || !canvas) return;
      
      const displayWidth = canvas.width / pixelRatio;
      const displayHeight = canvas.height / pixelRatio;
      
      // Clear the canvas
      ctx.clearRect(0, 0, displayWidth, displayHeight);
      
      // Update time
      time += 0.01;
      
      // Draw background grid
      drawGrid(ctx, displayWidth, displayHeight);
      
      // Update and draw buildings
      buildings.forEach(building => {
        building.update();
        building.draw(ctx, displayHeight);
      });
      
      // Continue animation
      animationFrameId = requestAnimationFrame(animate);
    }

    animate();

    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize);
      cancelAnimationFrame(animationFrameId);
    };
  }, [isVisible]); // Make the effect depend on isVisible state

  return (
    <div ref={containerRef} className="absolute inset-0 w-full h-full overflow-hidden">
      <canvas
        ref={canvasRef}
        className="pointer-events-none opacity-60"
      />
    </div>
  );
};

export default AchievementsBackground; 