"use client";

import { FiPhone, FiMail, FiMapPin, FiClock, FiGlobe, FiUser, FiInfo, FiEdit, FiChevronDown } from 'react-icons/fi';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';

interface FormField {
  id: string;
  name: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select';
  label: {
    english: string;
    arabic: string;
  };
  placeholder: {
    english: string;
    arabic: string;
  };
  required: boolean;
  enabled: boolean;
  options?: {
    english: string[];
    arabic: string[];
  };
  icon?: string;
}

interface ContactBodyData {
  formSection: {
    title: {
      english: string;
      arabic: string;
    };
    description: {
      english: string;
      arabic: string;
    };
    buttonText: {
      english: string;
      arabic: string;
    };
    privacyPolicy: {
      text: {
        english: string;
        arabic: string;
      };
      links: {
        privacyPolicyUrl: string;
        termsOfServiceUrl: string;
        privacyPolicyText: {
          english: string;
          arabic: string;
        };
        termsOfServiceText: {
          english: string;
          arabic: string;
        };
      };
    };
    fields: FormField[];
  };
  contactInfo: {
    title: {
      english: string;
      arabic: string;
    };
    sections: {
      phone: {
        title: {
          english: string;
          arabic: string;
        };
        numbers: {
          id: string;
          number: string;
          label: {
            english: string;
            arabic: string;
          };
        }[];
      };
      email: {
        title: {
          english: string;
          arabic: string;
        };
        addresses: {
          id: string;
          address: string;
          label: {
            english: string;
            arabic: string;
          };
        }[];
      };
      address: {
        title: {
          english: string;
          arabic: string;
        };
        company: {
          english: string;
          arabic: string;
        };
        lines: {
          id: string;
          line: {
            english: string;
            arabic: string;
          };
        }[];
      };
    };
  };
  businessHours: {
    title: {
      english: string;
      arabic: string;
    };
    schedule: {
      id: string;
      day: {
        english: string;
        arabic: string;
      };
      hours: {
        english: string;
        arabic: string;
      };
    }[];
    holidaySection: {
      title: {
        english: string;
        arabic: string;
      };
      note: {
        english: string;
        arabic: string;
      };
    };
  };
  socialLinks: {
    title: {
      english: string;
      arabic: string;
    };
    description: {
      english: string;
      arabic: string;
    };
    connectOnlineTitle: {
      english: string;
      arabic: string;
    };
    connectOnlineDescription: {
      english: string;
      arabic: string;
    };
    links: {
      id: string;
      platform: string;
      url: string;
      enabled: boolean;
      icon: string;
      ariaLabel: {
        english: string;
        arabic: string;
      };
    }[];
  };
}

interface ContactBodyPreviewProps {
  bodyData: ContactBodyData;
}

export default function ContactBodyPreview({ bodyData }: ContactBodyPreviewProps) {
  // Safety check: Ensure socialLinks.links is always an array
  const safeSocialLinks = Array.isArray(bodyData?.socialLinks?.links) ? bodyData.socialLinks.links : [];

  // Helper function to get icon component
  const getIcon = (iconName: string) => {
    const icons: { [key: string]: any } = {
      FiUser: FiUser,
      FiMail: FiMail, 
      FiPhone: FiPhone,
      FiInfo: FiInfo,
      FiEdit: FiEdit
    };
    const IconComponent = icons[iconName];
    return IconComponent ? <IconComponent className="h-6 w-6" /> : <FiInfo className="h-6 w-6" />;
  };

  // Get icon component from icon name for social media
  const getSocialIconComponent = (iconName: string): React.ReactElement | null => {
    if (!iconName) return null;
    
    if (iconName.startsWith('Fc')) {
      const IconFc = FcIcons[iconName as keyof typeof FcIcons];
      return IconFc ? <IconFc className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Fa6')) {
      const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
      return IconFa6 ? <IconFa6 className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Fa')) {
      const IconFa = FaIcons[iconName as keyof typeof FaIcons];
      return IconFa ? <IconFa className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Bs')) {
      const IconBs = BsIcons[iconName as keyof typeof BsIcons];
      return IconBs ? <IconBs className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Ri')) {
      const IconRi = RiIcons[iconName as keyof typeof RiIcons];
      return IconRi ? <IconRi className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Gi')) {
      const IconGi = GiIcons[iconName as keyof typeof GiIcons];
      return IconGi ? <IconGi className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Tb')) {
      const IconTb = TbIcons[iconName as keyof typeof TbIcons];
      return IconTb ? <IconTb className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Md')) {
      const IconMd = MdIcons[iconName as keyof typeof MdIcons];
      return IconMd ? <IconMd className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Hi')) {
      const IconHi = HiIcons[iconName as keyof typeof HiIcons];
      return IconHi ? <IconHi className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Ai')) {
      const IconAi = AiIcons[iconName as keyof typeof AiIcons];
      return IconAi ? <IconAi className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Io5')) {
      const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
      return IconIo5 ? <IconIo5 className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Io')) {
      const IconIo = IoIcons[iconName as keyof typeof IoIcons];
      return IconIo ? <IconIo className="h-6 w-6" /> : null;
    } else if (iconName.startsWith('Pi')) {
      const IconPi = PiIcons[iconName as keyof typeof PiIcons];
      return IconPi ? <IconPi className="h-6 w-6" /> : null;
    }
    
    return null;
  };

  return (
    <div className="mt-6 bg-gray-700 rounded-lg p-6 border border-gray-600">
      <h3 className="text-lg font-medium text-white mb-4">Live Preview</h3>
      <div className="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8 rounded-lg text-white relative overflow-hidden">
        {/* Background overlays to match real design */}
        <div className="absolute inset-0 bg-black/20"></div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 relative z-10">
          {/* Form Section Preview */}
          <div className="lg:col-span-2">
            <div className="bg-white/10 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 p-8 overflow-hidden relative">
              {/* Glass effect overlays */}
              <div className="absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 opacity-15 blur-3xl"></div>
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
              
              <h2 className="text-3xl font-bold mb-6 text-white relative z-10">{bodyData.formSection.title.english}</h2>
              <p className="text-white/80 mb-8 relative z-10">{bodyData.formSection.description.english}</p>
              
              {/* Form with Glass Effect */}
              <div className="bg-white/10 backdrop-blur-xl rounded-xl p-6 border border-white/15 shadow-lg relative">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
                
                <div className="relative">
                  <form className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                      {bodyData.formSection.fields.filter(f => f.enabled).map((field) => {
                        if (field.type === 'textarea') {
                          return (
                            <div key={field.id} className="md:col-span-2 relative">
                              <label className="block text-white font-medium mb-2 flex items-center">
                                {field.label.english}
                                {field.required && <span className="text-blue-400 ms-1">*</span>}
                              </label>
                              <div className="relative">
                                <textarea
                                  rows={6}
                                  className="w-full px-4 py-3 border border-white/20 rounded-lg focus:ring-blue-400 focus:border-blue-400 outline-none transition resize-none bg-white/10 text-white placeholder-white/50"
                                  placeholder={field.placeholder.english}
                                ></textarea>
                                <div className="absolute end-3 top-3 text-blue-400 opacity-50">
                                  {getIcon(field.icon || 'FiInfo')}
                                </div>
                              </div>
                            </div>
                          );
                        } else if (field.type === 'select') {
                          return (
                            <div key={field.id} className="relative">
                              <label className="block text-white font-medium mb-2 flex items-center">
                                {field.label.english}
                                {field.required && <span className="text-blue-400 ms-1">*</span>}
                              </label>
                              <div className="relative">
                                <div className="w-full px-4 py-3 border border-white/20 rounded-lg transition bg-white/10 text-white flex justify-between items-center cursor-pointer hover:border-white/40">
                                  <span className="text-white/70">{field.placeholder.english}</span>
                                  <FiChevronDown className="h-6 w-6 text-blue-400 opacity-50" />
                                </div>
                              </div>
                            </div>
                          );
                        } else {
                          return (
                            <div key={field.id} className="relative">
                              <label className="block text-white font-medium mb-2 flex items-center">
                                {field.label.english}
                                {field.required && <span className="text-blue-400 ms-1">*</span>}
                                {!field.required && <span className="text-white/60 text-sm ms-2">(Optional)</span>}
                              </label>
                              <div className="relative">
                                <input
                                  type={field.type}
                                  className="w-full px-4 py-3 border border-white/20 rounded-lg focus:ring-blue-400 focus:border-blue-400 outline-none transition bg-white/10 text-white placeholder-white/50 pe-10"
                                  placeholder={field.placeholder.english}
                                />
                                <div className="absolute end-3 top-3 text-blue-400 opacity-50">
                                  {getIcon(field.icon || 'FiInfo')}
                                </div>
                              </div>
                            </div>
                          );
                        }
                      })}
                    </div>

                    <div className="flex justify-center">
                      <span className="text-white/80 text-sm">
                        <span className="text-blue-400">*</span> Required fields
                      </span>
                    </div>

                    <div className="mt-4">
                      <button 
                        type="button"
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-4 px-6 rounded-lg transition shadow-xl overflow-hidden relative group"
                      >
                        <span className="absolute top-0 start-0 w-full h-full bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
                        <div className="flex items-center justify-center">
                          <span>{bodyData.formSection.buttonText.english}</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ms-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </svg>
                        </div>
                      </button>
                      <p className="text-xs text-white/60 text-center mt-4">
                        {bodyData.formSection.privacyPolicy.text.english} <a href={bodyData.formSection.privacyPolicy.links.privacyPolicyUrl} className="text-blue-400 hover:underline">{bodyData.formSection.privacyPolicy.links.privacyPolicyText.english}</a> and <a href={bodyData.formSection.privacyPolicy.links.termsOfServiceUrl} className="text-blue-400 hover:underline">{bodyData.formSection.privacyPolicy.links.termsOfServiceText.english}</a>.
                      </p>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
          
          {/* Sidebar Preview - Updated to match actual template */}
          <div className="space-y-8">
            {/* Contact Information - Match actual template structure */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 p-8 hover:border-white/30 transition-all duration-300 relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
              
              <div className="flex items-center mb-4 relative z-10">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white mr-3 shadow-lg">
                  <FiPhone className="h-4 w-4" />
                </div>
                <h2 className="text-2xl font-bold text-white">{bodyData.contactInfo.title.english}</h2>
              </div>
              
              <div className="space-y-6">
                {/* Phone */}
                <div className="flex items-start group cursor-pointer hover:translate-x-1 transition-transform duration-300">
                  <div className="flex-shrink-0 me-4">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full shadow-lg">
                      <FiPhone className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1 text-white">{bodyData.contactInfo.sections.phone.title.english}</h3>
                    {bodyData.contactInfo.sections.phone.numbers.map((phone) => (
                      <p key={phone.id} className="text-white/80 mb-1">{phone.label.english}: {phone.number}</p>
                    ))}
                  </div>
                </div>
                
                {/* Email */}
                <div className="flex items-start group cursor-pointer hover:translate-x-1 transition-transform duration-300">
                  <div className="flex-shrink-0 me-4">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full shadow-lg">
                      <FiMail className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1 text-white">{bodyData.contactInfo.sections.email.title.english}</h3>
                    {bodyData.contactInfo.sections.email.addresses.map((email) => (
                      <p key={email.id} className="text-white/80 mb-1 hover:text-blue-400 transition-colors duration-300">{email.address}</p>
                    ))}
                  </div>
                </div>
                
                {/* Address */}
                <div className="flex items-start group cursor-pointer hover:translate-x-1 transition-transform duration-300">
                  <div className="flex-shrink-0 me-4">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full shadow-lg">
                      <FiMapPin className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-1 text-white">{bodyData.contactInfo.sections.address.title.english}</h3>
                    <p className="text-white/80">
                      {bodyData.contactInfo.sections.address.company.english}<br />
                      {bodyData.contactInfo.sections.address.lines.map((line, index) => (
                        <span key={line.id}>
                          {line.line.english}
                          {index < bodyData.contactInfo.sections.address.lines.length - 1 && <br />}
                        </span>
                      ))}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Business Hours */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 p-8 hover:border-white/30 transition-all duration-300 relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
              
              <div className="flex items-center mb-4 relative z-10">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white mr-3 shadow-lg">
                  <FiClock className="h-4 w-4" />
                </div>
                <h2 className="text-2xl font-bold text-white">{bodyData.businessHours.title.english}</h2>
              </div>
              
              <div>
                <ul className="space-y-3">
                  {bodyData.businessHours.schedule.map((item) => (
                    <li key={item.id} className="flex justify-between items-center p-2 hover:bg-white/10 rounded-lg transition-colors duration-300">
                      <span className="text-white/90 font-medium">{item.day.english}</span>
                      <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full py-1 px-3">
                        {item.hours.english}
                      </span>
                    </li>
                  ))}
                </ul>
                
                <div className="mt-6 pt-4 border-t border-white/20">
                  <div className="flex items-center mb-2">
                    <FiClock className="h-5 w-5 text-blue-400 me-2" />
                    <h3 className="text-lg font-semibold text-white">{bodyData.businessHours.holidaySection.title.english}</h3>
                  </div>
                  <p className="text-white/80">{bodyData.businessHours.holidaySection.note.english}</p>
                </div>
              </div>
            </div>
            
            {/* Social Links */}
            <div className="bg-white/10 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 p-8 hover:border-white/30 transition-all duration-300 relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-xl pointer-events-none"></div>
              
              <div className="flex items-center mb-4 relative z-10">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white mr-3 shadow-lg">
                  <FiGlobe className="h-4 w-4" />
                </div>
                <h2 className="text-2xl font-bold text-white">{bodyData.socialLinks.title.english}</h2>
              </div>
              
              <div>
                <div className="grid grid-cols-5 gap-4">
                  {safeSocialLinks.filter(link => link.enabled).map((link) => (
                    <button key={link.id} className="flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 group relative overflow-hidden shadow-lg">
                      <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                      <span className="relative z-10 text-white">
                        {link.icon ? (
                          getSocialIconComponent(link.icon) || <span className="text-sm font-bold">{link.platform.charAt(0)}</span>
                        ) : (
                          <span className="text-sm font-bold">{link.platform.charAt(0)}</span>
                        )}
                      </span>
                    </button>
                  ))}
                </div>
                
                <div className="mt-6 pt-4 border-t border-white/20">
                  <h3 className="text-lg font-semibold mb-2 text-white">{bodyData.socialLinks.connectOnlineTitle.english}</h3>
                  <p className="text-white/80">{bodyData.socialLinks.connectOnlineDescription.english}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 