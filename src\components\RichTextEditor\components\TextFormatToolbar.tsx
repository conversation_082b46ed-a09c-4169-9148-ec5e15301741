import React, { useState } from 'react';
import { Editor } from 'slate';
import { useSlate } from 'slate-react';
import { FiType, FiSettings } from 'react-icons/fi';
import { HexColorPicker } from 'react-colorful';

interface TextFormatToolbarProps {
  icon: React.ReactNode;
}

// Font size options
const FONT_SIZES = [
  { value: 'xs', label: 'Extra Small' },
  { value: 'sm', label: 'Small' },
  { value: 'base', label: 'Normal' },
  { value: 'lg', label: 'Large' },
  { value: 'xl', label: 'Extra Large' },
  { value: '2xl', label: 'Huge' },
];

// Font family options
const FONT_FAMILIES = [
  { value: 'sans', label: 'Sans-serif' },
  { value: 'serif', label: 'Serif' },
  { value: 'mono', label: 'Monospace' },
];

const TextFormatToolbar = ({ icon }: TextFormatToolbarProps) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showBgColorPicker, setShowBgColorPicker] = useState(false);
  const [textColor, setTextColor] = useState('#ffffff');
  const [bgColor, setBgColor] = useState('#000000');

  const isMarkActive = (format: string) => {
    const marks = Editor.marks(editor);
    return marks ? marks[format] === true : false;
  };

  const toggleMark = (format: string, value: any = true) => {
    const isActive = isMarkActive(format);

    if (isActive) {
      Editor.removeMark(editor, format);
    } else {
      Editor.addMark(editor, format, value);
    }
  };

  const applyTextColor = (color: string) => {
    Editor.addMark(editor, 'color', color);
    setTextColor(color);
  };

  const applyBgColor = (color: string) => {
    Editor.addMark(editor, 'bgColor', color);
    setBgColor(color);
  };

  const applyFontSize = (size: string) => {
    Editor.addMark(editor, 'fontSize', size);
  };

  const applyFontFamily = (family: string) => {
    Editor.addMark(editor, 'fontFamily', family);
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      {showOptions && (
        <div className="absolute top-full left-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[200px]">
          {/* Text Color */}
          <div className="relative">
            <button
              className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
              onMouseDown={(e) => {
                e.preventDefault();
                setShowColorPicker(!showColorPicker);
                setShowBgColorPicker(false);
              }}
            >
              <div className="w-4 h-4 rounded-full" style={{ backgroundColor: textColor }}></div>
              <span>Text Color</span>
            </button>
            
            {showColorPicker && (
              <div className="absolute left-full top-0 ml-2 p-3 bg-[#141b35] border border-white/10 rounded shadow-lg">
                <HexColorPicker color={textColor} onChange={applyTextColor} />
              </div>
            )}
          </div>
          
          {/* Background Color */}
          <div className="relative">
            <button
              className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
              onMouseDown={(e) => {
                e.preventDefault();
                setShowBgColorPicker(!showBgColorPicker);
                setShowColorPicker(false);
              }}
            >
              <div className="w-4 h-4 rounded-full" style={{ backgroundColor: bgColor }}></div>
              <span>Background Color</span>
            </button>
            
            {showBgColorPicker && (
              <div className="absolute left-full top-0 ml-2 p-3 bg-[#141b35] border border-white/10 rounded shadow-lg">
                <HexColorPicker color={bgColor} onChange={applyBgColor} />
              </div>
            )}
          </div>
          
          {/* Divider */}
          <div className="border-t border-white/10 my-1"></div>
          
          {/* Font Size */}
          <div className="px-3 py-2 text-white/70 text-xs uppercase">Font Size</div>
          {FONT_SIZES.map((size) => (
            <button
              key={size.value}
              className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
              onMouseDown={(e) => {
                e.preventDefault();
                applyFontSize(size.value);
              }}
            >
              <span>{size.label}</span>
            </button>
          ))}
          
          {/* Divider */}
          <div className="border-t border-white/10 my-1"></div>
          
          {/* Font Family */}
          <div className="px-3 py-2 text-white/70 text-xs uppercase">Font Family</div>
          {FONT_FAMILIES.map((font) => (
            <button
              key={font.value}
              className={`flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left font-${font.value}`}
              onMouseDown={(e) => {
                e.preventDefault();
                applyFontFamily(font.value);
              }}
            >
              <span>{font.label}</span>
            </button>
          ))}
          
          {/* Divider */}
          <div className="border-t border-white/10 my-1"></div>
          
          {/* Additional formatting */}
          <button
            className={`flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left ${isMarkActive('strikethrough') ? 'bg-[#1a2349]' : ''}`}
            onMouseDown={(e) => {
              e.preventDefault();
              toggleMark('strikethrough');
            }}
          >
            <span className="line-through">Strikethrough</span>
          </button>
          
          <button
            className={`flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left ${isMarkActive('superscript') ? 'bg-[#1a2349]' : ''}`}
            onMouseDown={(e) => {
              e.preventDefault();
              toggleMark('superscript');
              if (isMarkActive('subscript')) toggleMark('subscript');
            }}
          >
            <span>Superscript</span>
          </button>
          
          <button
            className={`flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left ${isMarkActive('subscript') ? 'bg-[#1a2349]' : ''}`}
            onMouseDown={(e) => {
              e.preventDefault();
              toggleMark('subscript');
              if (isMarkActive('superscript')) toggleMark('superscript');
            }}
          >
            <span>Subscript</span>
          </button>
          
          <button
            className={`flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left ${isMarkActive('highlight') ? 'bg-[#1a2349]' : ''}`}
            onMouseDown={(e) => {
              e.preventDefault();
              toggleMark('highlight');
            }}
          >
            <span className="bg-yellow-300 text-black px-1">Highlight</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default TextFormatToolbar; 