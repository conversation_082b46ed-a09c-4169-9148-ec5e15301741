"use client";

import React, { useState } from 'react';
import { 
  FiSettings, FiSave, FiGlobe, FiShield, FiMail, 
  FiDatabase, FiImage, FiCode, FiMonitor, FiSearch
} from 'react-icons/fi';

// Import types
import { SettingsData, TabConfig } from './types';

// Import components
import GeneralSettings from './components/GeneralSettings';
import AppearanceSettings from './components/AppearanceSettings';
import SecuritySettings from './components/SecuritySettings';
import IntegrationsSettings from './components/IntegrationsSettings';
import SEOSettings from './components/SEOSettings';
import PerformanceSettings from './components/PerformanceSettings';
import BackupSettings from './components/BackupSettings';

export default function SettingsPage() {
  const [settingsData, setSettingsData] = useState<SettingsData>({
    general: {
      siteName: {
        en: "Mazaya Capital",
        ar: "مزايا كابيتال"
      },
      siteDescription: {
        en: "Premier real estate development company in Dubai",
        ar: "شركة رائدة في تطوير العقارات في دبي"
      },
      siteUrl: "https://mazayacapital.com",
      adminEmail: "<EMAIL>",
      timezone: "Asia/Dubai",
      language: {
        default: 'en',
        enabled: ['en', 'ar']
      },
      maintenance: {
        enabled: false,
        message: {
          en: "We are currently performing maintenance. Please check back soon.",
          ar: "نحن نقوم حاليًا بأعمال الصيانة. يرجى المراجعة قريبًا."
        }
      }
    },
    appearance: {
      logo: {
        favicon: "/favicon.ico",
        appleTouchIcon: "/apple-touch-icon.png"
      }
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireNumbers: true,
        requireSymbols: false
      },
      ipWhitelist: [],
      rateLimiting: {
        enabled: true,
        maxRequests: 100,
        timeWindow: 60
      },
      bruteForceProtection: {
        enabled: false,
        maxAttempts: 5,
        lockoutDuration: 15
      },
      sslSettings: {
        enforceHttps: true,
        hstsEnabled: true
      },
      auditLogging: {
        enabled: true,
        logFailedLogins: true,
        logAdminActions: true,
        retentionDays: 30
      }
    },
    integrations: {
      analytics: {
        googleAnalytics: {
          enabled: false,
          trackingId: ""
        },
        facebookPixel: {
          enabled: false,
          pixelId: ""
        },
        googleTagManager: {
          enabled: false,
          containerId: ""
        },
        hotjar: {
          enabled: false,
          siteId: ""
        }
      },
      maps: {
        googleMaps: {
          enabled: false,
          apiKey: ""
        },
        mapbox: {
          enabled: false,
          accessToken: ""
        }
      },
      chat: {
        whatsapp: {
          enabled: false,
          phoneNumber: "",
          welcomeMessage: {
            en: "Hello! How can we help you with your real estate needs?",
            ar: "مرحباً! كيف يمكننا مساعدتك في احتياجاتك العقارية؟"
          }
        },
        tawk: {
          enabled: false,
          propertyId: "",
          widgetId: ""
        },
        intercom: {
          enabled: false,
          appId: ""
        },
        zendesk: {
          enabled: false,
          key: ""
        }
      },
      marketing: {
        mailchimp: {
          enabled: false,
          apiKey: "",
          audienceId: ""
        },
        hubspot: {
          enabled: false,
          portalId: ""
        },
        salesforce: {
          enabled: false,
          organizationId: ""
        }
      },
      social: {
        facebook: "",
        twitter: "",
        instagram: "",
        linkedin: "",
        youtube: "",
        tiktok: "",
        snapchat: ""
      },
      communication: {
        calendly: {
          enabled: false,
          username: ""
        },
        zoom: {
          enabled: false,
          meetingId: ""
        },
        googleMeet: {
          enabled: false,
          meetingLink: ""
        }
      },
      seo: {
        googleSearchConsole: {
          enabled: false,
          verificationCode: ""
        },
        bingWebmaster: {
          enabled: false,
          verificationCode: ""
        },
        yandexWebmaster: {
          enabled: false,
          verificationCode: ""
        }
      },
      email: {
        provider: 'smtp',
        smtp: {
          host: "",
          port: 587,
          username: "",
          password: "",
          encryption: 'tls'
        },
        sendgrid: {
          apiKey: ""
        },
        mailgun: {
          domain: "",
          apiKey: ""
        },
        ses: {
          accessKeyId: "",
          secretAccessKey: "",
          region: "us-east-1"
        }
      }
    },
    seo: {
      general: {
        siteTitle: {
          en: "Mazaya Capital - Premier Real Estate in Dubai",
          ar: "مزايا كابيتال - العقارات الراقية في دبي"
        },
        siteDescription: {
          en: "Discover luxury properties in Dubai with Mazaya Capital. Expert real estate services, premium locations, and exceptional investment opportunities.",
          ar: "اكتشف العقارات الفاخرة في دبي مع مزايا كابيتال. خدمات عقارية متخصصة ومواقع مميزة وفرص استثمارية استثنائية."
        },
        keywords: {
          en: ["real estate dubai", "luxury properties", "dubai properties", "investment opportunities", "mazaya capital"],
          ar: ["عقارات دبي", "عقارات فاخرة", "استثمار عقاري", "مزايا كابيتال"]
        },
        canonicalUrl: "https://mazayacapital.com",
        hreflang: {
          enabled: true,
          defaultLanguage: 'en',
          alternateUrls: {
            en: "https://mazayacapital.com/en",
            ar: "https://mazayacapital.com/ar"
          }
        }
      },
      metaTags: {
        robots: {
          index: true,
          follow: true,
          archive: true,
          snippet: true,
          imageIndex: true,
          maxSnippet: 160,
          maxImagePreview: 'large',
          maxVideoPreview: 30
        },
        openGraph: {
          enabled: true,
          type: 'business.business',
          title: {
            en: "Mazaya Capital - Premier Real Estate in Dubai",
            ar: "مزايا كابيتال - العقارات الراقية في دبي"
          },
          description: {
            en: "Discover luxury properties in Dubai with Mazaya Capital. Expert real estate services and premium locations.",
            ar: "اكتشف العقارات الفاخرة في دبي مع مزايا كابيتال. خدمات عقارية متخصصة ومواقع مميزة."
          },
          image: "https://mazayacapital.com/og-image.jpg",
          imageAlt: {
            en: "Mazaya Capital - Dubai Real Estate",
            ar: "مزايا كابيتال - عقارات دبي"
          },
          url: "https://mazayacapital.com",
          siteName: {
            en: "Mazaya Capital",
            ar: "مزايا كابيتال"
          },
          locale: {
            en: "en_US",
            ar: "ar_AE"
          }
        },
        twitter: {
          enabled: true,
          card: 'summary_large_image',
          site: "@mazayacapital",
          creator: "@mazayacapital",
          title: {
            en: "Mazaya Capital - Premier Real Estate in Dubai",
            ar: "مزايا كابيتال - العقارات الراقية في دبي"
          },
          description: {
            en: "Discover luxury properties in Dubai with Mazaya Capital.",
            ar: "اكتشف العقارات الفاخرة في دبي مع مزايا كابيتال."
          },
          image: "https://mazayacapital.com/twitter-image.jpg",
          imageAlt: {
            en: "Mazaya Capital - Dubai Real Estate",
            ar: "مزايا كابيتال - عقارات دبي"
          }
        },
        customMeta: []
      },
      structuredData: {
        enabled: true,
        organization: {
          enabled: true,
          name: {
            en: "Mazaya Capital",
            ar: "مزايا كابيتال"
          },
          description: {
            en: "Premier real estate development company in Dubai",
            ar: "شركة رائدة في تطوير العقارات في دبي"
          },
          url: "https://mazayacapital.com",
          logo: "https://mazayacapital.com/logo.png",
          contactPoint: {
            telephone: "+971-4-123-4567",
            contactType: "customer service",
            areaServed: ["Dubai", "UAE"],
            availableLanguage: ["English", "Arabic"]
          },
          address: {
            streetAddress: {
              en: "Business Bay, Dubai",
              ar: "الخليج التجاري، دبي"
            },
            addressLocality: {
              en: "Dubai",
              ar: "دبي"
            },
            addressRegion: {
              en: "Dubai",
              ar: "دبي"
            },
            postalCode: "00000",
            addressCountry: "AE"
          },
          sameAs: [
            "https://facebook.com/mazayacapital",
            "https://instagram.com/mazayacapital",
            "https://linkedin.com/company/mazayacapital"
          ]
        },
        realEstate: {
          enabled: true,
          businessType: 'RealEstateBusiness',
          priceRange: "$$$$",
          paymentAccepted: ["Cash", "Credit Card", "Bank Transfer"],
          currenciesAccepted: ["AED", "USD"],
          openingHours: ["Mo-Fr 09:00-18:00", "Sa 09:00-15:00"]
        },
        breadcrumbs: {
          enabled: true,
          separator: " > ",
          showHome: true
        },
        faq: {
          enabled: false,
          questions: []
        }
      },
      sitemaps: {
        enabled: true,
        autoGenerate: true,
        includeImages: true,
        includeVideos: false,
        changeFrequency: 'weekly',
        priority: 0.8,
        excludePages: ["/admin", "/private"],
        customSitemaps: []
      },
      robotsTxt: {
        enabled: true,
        userAgent: "*",
        disallow: ["/admin", "/private"],
        allow: ["/"],
        crawlDelay: 1,
        sitemapUrl: "https://mazayacapital.com/sitemap.xml",
        customDirectives: []
      },
      analytics: {
        googleSearchConsole: {
          enabled: false,
          verificationCode: "",
          siteUrl: "https://mazayacapital.com"
        },
        bingWebmaster: {
          enabled: false,
          verificationCode: ""
        },
        yandexWebmaster: {
          enabled: false,
          verificationCode: ""
        },
        googleAnalytics: {
          enabled: false,
          trackingId: "",
          enhancedEcommerce: false
        }
      },
      localSEO: {
        enabled: true,
        businessName: {
          en: "Mazaya Capital",
          ar: "مزايا كابيتال"
        },
        businessType: "Real Estate Agency",
        address: {
          street: {
            en: "Business Bay",
            ar: "الخليج التجاري"
          },
          city: {
            en: "Dubai",
            ar: "دبي"
          },
          state: {
            en: "Dubai",
            ar: "دبي"
          },
          zipCode: "00000",
          country: {
            en: "United Arab Emirates",
            ar: "الإمارات العربية المتحدة"
          }
        },
        coordinates: {
          latitude: 25.2048,
          longitude: 55.2708
        },
        phone: "+971-4-123-4567",
        email: "<EMAIL>",
        website: "https://mazayacapital.com",
        hours: {
          monday: { open: "09:00", close: "18:00", closed: false },
          tuesday: { open: "09:00", close: "18:00", closed: false },
          wednesday: { open: "09:00", close: "18:00", closed: false },
          thursday: { open: "09:00", close: "18:00", closed: false },
          friday: { open: "09:00", close: "18:00", closed: false },
          saturday: { open: "09:00", close: "15:00", closed: false },
          sunday: { open: "00:00", close: "00:00", closed: true }
        },
        googleMyBusiness: {
          enabled: false,
          placeId: "",
          reviewsWidget: false
        }
      },
      realEstateSpecific: {
        propertyTypes: ["Apartment", "Villa", "Townhouse", "Penthouse", "Commercial"],
        serviceAreas: [
          {
            name: { en: "Downtown Dubai", ar: "وسط مدينة دبي" },
            coordinates: { latitude: 25.1972, longitude: 55.2744 },
            radius: 5
          },
          {
            name: { en: "Dubai Marina", ar: "مرسى دبي" },
            coordinates: { latitude: 25.0657, longitude: 55.1393 },
            radius: 3
          }
        ],
        specializations: {
          en: ["Luxury Properties", "Investment Properties", "Commercial Real Estate"],
          ar: ["العقارات الفاخرة", "العقارات الاستثمارية", "العقارات التجارية"]
        },
        certifications: ["RERA Licensed", "Dubai Real Estate Institute"],
        languages: ["English", "Arabic", "French", "Russian"]
      },
      monitoring: {
        enabled: false,
        keywordTracking: {
          enabled: false,
          keywords: []
        },
        competitorAnalysis: {
          enabled: false,
          competitors: []
        },
        rankingReports: {
          enabled: false,
          frequency: 'weekly',
          recipients: []
        }
      }
    },
    performance: {
      caching: {
        enabled: true,
        strategy: 'hybrid',
        duration: {
          pages: 3600,
          api: 300,
          static: 86400,
          database: 1800
        },
        redis: {
          host: 'localhost',
          port: 6379,
          password: '',
          database: 0
        },
        purgeRules: {
          autoOnUpdate: true,
          scheduledPurge: false,
          purgeFrequency: 'daily'
        }
      },
      compression: {
        enabled: true,
        algorithm: 'brotli',
        level: 6,
        minSize: 1024,
        types: ['text/html', 'text/css', 'application/javascript', 'application/json']
      },
      cdn: {
        enabled: false,
        provider: 'cloudflare',
        cloudflare: {
          zoneId: '',
          apiKey: '',
          email: ''
        },
        aws: {
          distributionId: '',
          accessKeyId: '',
          secretAccessKey: '',
          region: 'me-south-1'
        },
        azure: {
          profileName: '',
          endpointName: '',
          resourceGroup: '',
          subscriptionId: ''
        },
        google: {
          projectId: '',
          keyFile: ''
        },
        custom: {
          url: '',
          apiKey: ''
        },
        settings: {
          minify: {
            html: true,
            css: true,
            js: true
          },
          browserCache: 86400,
          edgeCache: 604800
        }
      },
      imageOptimization: {
        enabled: true,
        quality: {
          jpeg: 85,
          webp: 80,
          png: 90
        },
        formats: {
          webp: true,
          avif: false,
          jpeg: true,
          png: true,
          gif: true,
          svg: true
        },
        responsive: {
          enabled: true,
          breakpoints: [480, 768, 1024, 1440]
        },
        lazy: {
          enabled: true,
          threshold: 200
        },
        processing: {
          resize: true,
          crop: true,
          watermark: false,
          progressive: true
        }
      },
      database: {
        optimization: {
          enabled: true,
          indexOptimization: true,
          queryOptimization: true,
          connectionPooling: true
        },
        caching: {
          enabled: true,
          queryCache: true,
          resultCache: true,
          cacheDuration: 3600
        },
        cleanup: {
          enabled: true,
          oldLogs: true,
          tempFiles: true,
          frequency: 'weekly'
        }
      },
      realEstate: {
        propertyImages: {
          preload: true,
          thumbnailGeneration: true,
          virtualTourOptimization: true
        },
        maps: {
          tileCache: true,
          vectorOptimization: true,
          clusterMarkers: true
        },
        search: {
          indexing: true,
          facetedSearch: true,
          autoComplete: true
        }
      },
      monitoring: {
        enabled: false,
        metrics: {
          pageSpeed: true,
          coreWebVitals: true,
          serverResponse: true,
          databaseQueries: false
        },
        alerts: {
          enabled: false,
          thresholds: {
            pageLoadTime: 3000,
            serverResponseTime: 500,
            errorRate: 5
          },
          notifications: {
            email: [],
            webhook: ''
          }
        },
        reporting: {
          enabled: false,
          frequency: 'weekly',
          recipients: []
        }
      },
      advanced: {
        preloading: {
          enabled: true,
          criticalResources: true,
          nextPagePrediction: false
        },
        serviceWorker: {
          enabled: false,
          cacheStrategy: 'stale-while-revalidate',
          offlineSupport: false
        },
        http2: {
          enabled: true,
          serverPush: false
        },
        security: {
          contentSecurityPolicy: true,
          hsts: true,
          xssProtection: true
        }
      }
    },
    backup: {
      autoBackup: {
        enabled: false,
        frequency: 'weekly',
        time: '02:00',
        retention: 30,
        maxBackups: 10
      },
      backupTypes: {
        database: true,
        files: true,
        media: true,
        configurations: true,
        userUploads: true
      },
      storage: {
        local: {
          enabled: true,
          path: '/var/backups/mazaya',
          maxSize: 50
        },
        cloud: {
          primary: 'aws',
          aws: {
            enabled: false,
            bucket: '',
            region: 'me-south-1',
            accessKeyId: '',
            secretAccessKey: '',
            storageClass: 'STANDARD'
          },
          google: {
            enabled: false,
            bucket: '',
            projectId: '',
            keyFile: '',
            storageClass: 'STANDARD'
          },
          azure: {
            enabled: false,
            containerName: '',
            accountName: '',
            accountKey: '',
            tier: 'Hot'
          },
          dropbox: {
            enabled: false,
            accessToken: '',
            appKey: ''
          },
          onedrive: {
            enabled: false,
            clientId: '',
            clientSecret: '',
            tenantId: ''
          }
        }
      },
      compression: {
        enabled: true,
        level: 6,
        format: 'zip'
      },
      encryption: {
        enabled: false,
        algorithm: 'AES-256',
        password: ''
      },
      notifications: {
        email: {
          enabled: false,
          recipients: [],
          onSuccess: true,
          onFailure: true,
          onWarning: true
        },
        webhook: {
          enabled: false,
          url: '',
          secret: ''
        }
      },
      monitoring: {
        healthCheck: true,
        integrityCheck: true,
        performanceMetrics: false,
        alertThresholds: {
          backupSize: 10,
          duration: 60,
          failureCount: 3
        }
      },
      restore: {
        pointInTimeRecovery: false,
        incrementalBackups: false,
        testRestores: {
          enabled: false,
          frequency: 'monthly'
        }
      }
    }
  });

  const [activeTab, setActiveTab] = useState('general');

  const tabs: TabConfig[] = [
    { id: 'general', label: 'General', icon: FiGlobe },
    { id: 'appearance', label: 'Appearance', icon: FiImage },
    { id: 'security', label: 'Security', icon: FiShield },
    { id: 'integrations', label: 'Integrations', icon: FiCode },
    { id: 'seo', label: 'SEO', icon: FiSearch },
    { id: 'performance', label: 'Performance', icon: FiMonitor },
    { id: 'backup', label: 'Backup', icon: FiDatabase }
  ];

  const handleSave = () => {
    console.log('Saving settings:', settingsData);
    alert('Settings saved successfully!');
  };

  const updateGeneralSettings = (data: SettingsData['general']) => {
    setSettingsData(prev => ({ ...prev, general: data }));
  };

  const updateAppearanceSettings = (data: SettingsData['appearance']) => {
    setSettingsData(prev => ({ ...prev, appearance: data }));
  };

  const updateSecuritySettings = (data: SettingsData['security']) => {
    setSettingsData(prev => ({ ...prev, security: data }));
  };

  const updateIntegrationsSettings = (data: SettingsData['integrations']) => {
    setSettingsData(prev => ({ ...prev, integrations: data }));
  };

  const updateSEOSettings = (data: SettingsData['seo']) => {
    setSettingsData(prev => ({ ...prev, seo: data }));
  };

  const updatePerformanceSettings = (data: SettingsData['performance']) => {
    setSettingsData(prev => ({ ...prev, performance: data }));
  };

  const updateBackupSettings = (data: SettingsData['backup']) => {
    setSettingsData(prev => ({ ...prev, backup: data }));
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <GeneralSettings 
            data={settingsData.general} 
            onChange={updateGeneralSettings} 
          />
        );
      case 'appearance':
        return (
          <AppearanceSettings 
            data={settingsData.appearance} 
            onChange={updateAppearanceSettings} 
          />
        );
      case 'security':
        return (
          <SecuritySettings 
            data={settingsData.security} 
            onChange={updateSecuritySettings} 
          />
        );
      case 'integrations':
        return (
          <IntegrationsSettings 
            data={settingsData.integrations} 
            onChange={updateIntegrationsSettings} 
          />
        );
      case 'seo':
        return (
          <SEOSettings 
            data={settingsData.seo} 
            onChange={updateSEOSettings} 
          />
        );
      case 'performance':
        return (
          <PerformanceSettings 
            data={settingsData.performance} 
            onChange={updatePerformanceSettings} 
          />
        );
      case 'backup':
        return (
          <BackupSettings 
            data={settingsData.backup} 
            onChange={updateBackupSettings} 
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white flex items-center">
            <FiSettings className="mr-3 h-8 w-8 text-[#00C2FF]" />
            Website Settings
          </h1>
          <p className="text-gray-400 mt-1">Manage your website configuration and preferences</p>
        </div>
        <button
          onClick={handleSave}
          className="inline-flex items-center px-6 py-3 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors font-medium"
        >
          <FiSave className="mr-2 h-5 w-5" />
          Save All Settings
        </button>
      </div>

      {/* Tabs */}
      <div className="mt-6">
        <div className="border-b border-gray-700">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors ${
                    activeTab === tab.id
                      ? 'border-[#00C2FF] text-[#00C2FF]'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {renderTabContent()}
      </div>
    </div>
  );
} 