import React, { useState } from 'react';
import { Editor, Transforms, Element as SlateElement } from 'slate';
import { useSlate } from 'slate-react';
import { FiCode } from 'react-icons/fi';

interface CodeBlockToolbarProps {
  icon: React.ReactNode;
}

// Available languages for code highlighting
const LANGUAGES = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'html', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'python', label: 'Python' },
  { value: 'java', label: 'Java' },
  { value: 'csharp', label: 'C#' },
  { value: 'php', label: 'PHP' },
  { value: 'ruby', label: 'Ruby' },
  { value: 'go', label: 'Go' },
  { value: 'bash', label: 'Bash/Shell' },
  { value: 'sql', label: 'SQL' },
  { value: 'json', label: 'JSON' },
];

const CodeBlockToolbar = ({ icon }: CodeBlockToolbarProps) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);

  const insertCodeBlock = (language: string) => {
    // Create a code block with the selected language
    const codeBlock = {
      type: 'code-block',
      language,
      children: [{ text: '// Your code here' }]
    };
    
    Transforms.insertNodes(editor, codeBlock);
    
    // Select the content to make it easy to replace
    const path = [...editor.selection!.anchor.path.slice(0, -1), 0];
    Transforms.select(editor, {
      anchor: { path, offset: 0 },
      focus: { path, offset: '// Your code here'.length }
    });
  };

  const isCodeBlockActive = () => {
    const [match] = Editor.nodes(editor, {
      match: n => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'code-block',
    }) || [null];
    
    return !!match;
  };

  return (
    <div className="relative">
      <button
        type="button"
        className={`px-3 py-1.5 ${isCodeBlockActive() ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      {showOptions && (
        <div className="absolute top-full left-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[150px] max-h-[300px] overflow-y-auto">
          {LANGUAGES.map((lang) => (
            <button
              key={lang.value}
              className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
              onMouseDown={(e) => {
                e.preventDefault();
                insertCodeBlock(lang.value);
                setShowOptions(false);
              }}
            >
              <FiCode size={16} />
              <span>{lang.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CodeBlockToolbar; 