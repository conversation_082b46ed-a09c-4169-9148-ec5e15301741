import React, { useState, useCallback, useMemo } from 'react';
import isHotkey from 'is-hotkey';
import { Editable, withReact, useSlate, Slate, ReactEditor } from 'slate-react';
import { Editor, Transforms, Element as SlateElement, createEditor, Node, Text, BaseEditor } from 'slate';
import { withHistory } from 'slate-history';
import { FiBold, FiItalic, FiLink, FiList, FiAlignLeft, FiAlignCenter, FiAlignRight, FiType, FiCode, FiImage, FiFileText, FiAlignJustify } from 'react-icons/fi';

// Define typescript types
export type CustomElement = {
  type: 'paragraph' | 'heading-one' | 'heading-two' | 'heading-three' | 'block-quote' | 'bulleted-list' | 'numbered-list' | 'list-item';
  align?: 'left' | 'center' | 'right' | 'justify';
  children: CustomText[];
};

export type CustomText = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  code?: boolean;
  underline?: boolean;
};

declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

// Hotkeys for formatting shortcuts
const HOTKEYS = {
  'mod+b': 'bold',
  'mod+i': 'italic',
  'mod+u': 'underline',
  'mod+`': 'code',
};

// Define component props types
interface RichTextEditorProps {
  value?: CustomElement[];
  onChange: (value: CustomElement[]) => void;
  placeholder?: string;
  error?: string;
}

// Default initial value for the editor
const initialValue: CustomElement[] = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

// Define rich text editor component
const RichTextEditor = ({ value, onChange, placeholder, error }: RichTextEditorProps) => {
  const renderElement = useCallback(props => <Element {...props} />, []);
  const renderLeaf = useCallback(props => <Leaf {...props} />, []);
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  
  // Always ensure we have a valid array for Slate
  const editorValue = useMemo(() => {
    if (!value || !Array.isArray(value) || value.length === 0) {
      console.log('Using default editor value');
      return initialValue;
    }
    return value;
  }, [value]);
  
  const handleChange = (newValue: CustomElement[]) => {
    if (onChange && Array.isArray(newValue)) {
      onChange(newValue);
    }
  };

  return (
    <div className="rich-text-editor">
      <Slate editor={editor} initialValue={editorValue} onChange={handleChange}>
        <div className="mb-2 flex flex-wrap gap-2 bg-[#0A0F23] p-2 rounded-t-lg border-t border-l border-r border-white/10">
          <MarkButton format="bold" icon={<FiBold />} />
          <MarkButton format="italic" icon={<FiItalic />} />
          <MarkButton format="underline" icon={<span className="underline">U</span>} />
          <MarkButton format="code" icon={<FiCode />} />
          <BlockButton format="heading-one" icon={<span className="font-bold">H1</span>} />
          <BlockButton format="heading-two" icon={<span className="font-bold">H2</span>} />
          <BlockButton format="heading-three" icon={<span className="font-bold">H3</span>} />
          <BlockButton format="block-quote" icon={<FiFileText />} />
          <BlockButton format="numbered-list" icon={<span className="font-bold">1.</span>} />
          <BlockButton format="bulleted-list" icon={<FiList />} />
          <div className="border-l border-white/10 mx-1 h-8"></div>
          <AlignButton format="left" icon={<FiAlignLeft />} />
          <AlignButton format="center" icon={<FiAlignCenter />} />
          <AlignButton format="right" icon={<FiAlignRight />} />
          <AlignButton format="justify" icon={<FiAlignJustify />} />
          <InsertImageButton icon={<FiImage />} />
        </div>
        <div className={`p-3 bg-[#0A0F23] border ${error ? 'border-red-500' : 'border-white/10'} rounded-b-lg text-white focus-within:border-[rgb(var(--color-primary))]/50 focus-within:ring-1 focus-within:ring-[rgb(var(--color-primary))]/50 min-h-[300px]`}>
          <Editable
            renderElement={renderElement}
            renderLeaf={renderLeaf}
            placeholder={placeholder}
            spellCheck
            autoFocus
            onKeyDown={event => {
              for (const hotkey in HOTKEYS) {
                if (isHotkey(hotkey, event)) {
                  event.preventDefault();
                  const mark = HOTKEYS[hotkey];
                  toggleMark(editor, mark);
                }
              }
            }}
            className="outline-none min-h-[280px]"
          />
        </div>
      </Slate>
      {error && <p className="mt-1 text-red-500 text-sm">{error}</p>}
    </div>
  );
};

// Define props for the Element component
interface ElementProps {
  attributes: any;
  children: React.ReactNode;
  element: CustomElement;
}

// Element component to render different block types
const Element = ({ attributes, children, element }: ElementProps) => {
  const style = { textAlign: element.align };
  
  switch (element.type) {
    case 'block-quote':
      return (
        <blockquote
          style={style}
          className="border-l-4 border-[rgb(var(--color-primary))]/50 pl-4 italic text-white/80"
          {...attributes}
        >
          {children}
        </blockquote>
      );
    case 'bulleted-list':
      return (
        <ul style={style} className="list-disc pl-10" {...attributes}>
          {children}
        </ul>
      );
    case 'heading-one':
      return (
        <h1 style={style} className="text-3xl font-bold my-4" {...attributes}>
          {children}
        </h1>
      );
    case 'heading-two':
      return (
        <h2 style={style} className="text-2xl font-bold my-3" {...attributes}>
          {children}
        </h2>
      );
    case 'heading-three':
      return (
        <h3 style={style} className="text-xl font-bold my-2" {...attributes}>
          {children}
        </h3>
      );
    case 'list-item':
      return (
        <li style={style} {...attributes}>
          {children}
        </li>
      );
    case 'numbered-list':
      return (
        <ol style={style} className="list-decimal pl-10" {...attributes}>
          {children}
        </ol>
      );
    default:
      return (
        <p style={style} className="mb-3" {...attributes}>
          {children}
        </p>
      );
  }
};

// Define props for the Leaf component
interface LeafProps {
  attributes: any;
  children: React.ReactNode;
  leaf: CustomText;
}

// Leaf component to render text formatting
const Leaf = ({ attributes, children, leaf }: LeafProps) => {
  if (leaf.bold) {
    children = <strong>{children}</strong>;
  }

  if (leaf.italic) {
    children = <em>{children}</em>;
  }

  if (leaf.underline) {
    children = <u>{children}</u>;
  }

  if (leaf.code) {
    children = <code className="bg-white/10 px-1 py-0.5 rounded font-mono">{children}</code>;
  }

  return <span {...attributes}>{children}</span>;
};

// Define props for the button components
interface ButtonProps {
  format: string;
  icon: React.ReactNode;
}

// Button component for text formatting
const MarkButton = ({ format, icon }: ButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className={`px-3 py-1.5 ${isMarkActive(editor, format) ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
      onMouseDown={event => {
        event.preventDefault();
        toggleMark(editor, format);
      }}
    >
      {icon}
    </button>
  );
};

// Button component for block formatting
const BlockButton = ({ format, icon }: ButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className={`px-3 py-1.5 ${isBlockActive(editor, format) ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
      onMouseDown={event => {
        event.preventDefault();
        toggleBlock(editor, format);
      }}
    >
      {icon}
    </button>
  );
};

// Button component for text alignment
const AlignButton = ({ format, icon }: ButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className={`px-3 py-1.5 ${isAlignActive(editor, format) ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
      onMouseDown={event => {
        event.preventDefault();
        toggleAlign(editor, format);
      }}
    >
      {icon}
    </button>
  );
};

// Button component for inserting images
interface ImageButtonProps {
  icon: React.ReactNode;
}

const InsertImageButton = ({ icon }: ImageButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
      onMouseDown={event => {
        event.preventDefault();
        const url = window.prompt('Enter the URL of the image:');
        if (url) {
          insertImage(editor, url);
        }
      }}
    >
      {icon}
    </button>
  );
};

// Helper to check if a mark is active
const isMarkActive = (editor, format) => {
  const marks = Editor.marks(editor);
  return marks ? marks[format] === true : false;
};

// Helper to toggle a mark
const toggleMark = (editor, format) => {
  const isActive = isMarkActive(editor, format);

  if (isActive) {
    Editor.removeMark(editor, format);
  } else {
    Editor.addMark(editor, format, true);
  }
};

// Helper to check if a block type is active
const isBlockActive = (editor, format) => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n.type === format,
    })
  );

  return !!match;
};

// Helper to check if alignment is active
const isAlignActive = (editor, format) => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n.align === format,
    })
  );

  return !!match;
};

// Helper to toggle block type
const toggleBlock = (editor, format) => {
  const isActive = isBlockActive(editor, format);
  const isList = ['numbered-list', 'bulleted-list'].includes(format);

  Transforms.unwrapNodes(editor, {
    match: n =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      ['numbered-list', 'bulleted-list'].includes(n.type),
    split: true,
  });

  const newProperties: Partial<SlateElement> = {
    type: isActive ? 'paragraph' : isList ? 'list-item' : format,
  };
  
  Transforms.setNodes<SlateElement>(editor, newProperties);

  if (!isActive && isList) {
    const block = { type: format, children: [] };
    Transforms.wrapNodes(editor, block);
  }
};

// Helper to toggle alignment
const toggleAlign = (editor, format) => {
  const isActive = isAlignActive(editor, format);
  const newProperties: Partial<SlateElement> = {
    align: isActive ? undefined : format,
  };
  
  Transforms.setNodes<SlateElement>(editor, newProperties);
};

// Helper to insert an image
const insertImage = (editor, url) => {
  const text = { text: '' };
  const image = { type: 'image', url, children: [text] };
  Transforms.insertNodes(editor, image);
};

// Helper to serialize the Slate content to HTML
export const serializeToHtml = (nodes: CustomElement[]): string => {
  try {
    if (!Array.isArray(nodes)) {
      console.error('serializeToHtml received non-array:', nodes);
      return '';
    }
    
    return nodes.map(node => {
      if (Text.isText(node)) {
        let text = node.text;
        if (node.bold) {
          text = `<strong>${text}</strong>`;
        }
        if (node.italic) {
          text = `<em>${text}</em>`;
        }
        if (node.underline) {
          text = `<u>${text}</u>`;
        }
        if (node.code) {
          text = `<code>${text}</code>`;
        }
        return text;
      }

      const children = node.children.map(n => serializeToHtml([n as CustomElement])).join('');
      
      switch (node.type) {
        case 'block-quote':
          return `<blockquote style="text-align: ${node.align || 'left'}">${children}</blockquote>`;
        case 'paragraph':
          return `<p style="text-align: ${node.align || 'left'}">${children}</p>`;
        case 'heading-one':
          return `<h1 style="text-align: ${node.align || 'left'}">${children}</h1>`;
        case 'heading-two':
          return `<h2 style="text-align: ${node.align || 'left'}">${children}</h2>`;
        case 'heading-three':
          return `<h3 style="text-align: ${node.align || 'left'}">${children}</h3>`;
        case 'list-item':
          return `<li style="text-align: ${node.align || 'left'}">${children}</li>`;
        case 'numbered-list':
          return `<ol style="text-align: ${node.align || 'left'}">${children}</ol>`;
        case 'bulleted-list':
          return `<ul style="text-align: ${node.align || 'left'}">${children}</ul>`;
        default:
          return children;
      }
    }).join('');
  } catch (error) {
    console.error('Error serializing to HTML:', error);
    return '';
  }
};

// Helper to parse HTML to Slate content
export const deserializeHtml = (html: string): CustomElement[] => {
  try {
    // Ensure html is a string
    if (typeof html !== 'string') {
      console.error('deserializeHtml received non-string input:', html);
      return initialValue;
    }
    
    // Empty string check
    if (!html || html.trim() === '') {
      return initialValue;
    }
    
    // Very basic parsing to extract heading and paragraph content
    const h1Pattern = /<h1[^>]*>(.*?)<\/h1>/gs;
    const h2Pattern = /<h2[^>]*>(.*?)<\/h2>/gs;
    const h3Pattern = /<h3[^>]*>(.*?)<\/h3>/gs;
    const pPattern = /<p[^>]*>(.*?)<\/p>/gs;
    const quotePattern = /<blockquote[^>]*>(.*?)<\/blockquote>/gs;
    
    const nodes: CustomElement[] = [];
    
    // Extract h1 tags
    let match;
    while ((match = h1Pattern.exec(html)) !== null) {
      nodes.push({
        type: 'heading-one',
        children: [{ text: match[1].replace(/<[^>]*>/g, '') }]
      });
    }
    
    // Extract h2 tags
    while ((match = h2Pattern.exec(html)) !== null) {
      nodes.push({
        type: 'heading-two',
        children: [{ text: match[1].replace(/<[^>]*>/g, '') }]
      });
    }
    
    // Extract h3 tags
    while ((match = h3Pattern.exec(html)) !== null) {
      nodes.push({
        type: 'heading-three',
        children: [{ text: match[1].replace(/<[^>]*>/g, '') }]
      });
    }
    
    // Extract blockquote tags
    while ((match = quotePattern.exec(html)) !== null) {
      nodes.push({
        type: 'block-quote',
        children: [{ text: match[1].replace(/<[^>]*>/g, '') }]
      });
    }
    
    // Extract p tags
    while ((match = pPattern.exec(html)) !== null) {
      nodes.push({
        type: 'paragraph',
        children: [{ text: match[1].replace(/<[^>]*>/g, '') }]
      });
    }
    
    // If no nodes were created, create a default paragraph
    if (nodes.length === 0) {
      nodes.push({
        type: 'paragraph',
        children: [{ text: html.replace(/<[^>]*>/g, '') }]
      });
    }
    
    return nodes;
  } catch (error) {
    console.error('Error parsing HTML:', error);
    return initialValue;
  }
};

export default RichTextEditor;