"use client";

import React from 'react';
import { FiGlobe } from 'react-icons/fi';

interface GeneralSettingsData {
  siteName: {
    en: string;
    ar: string;
  };
  siteDescription: {
    en: string;
    ar: string;
  };
  siteUrl: string;
  adminEmail: string;
  timezone: string;
  language: {
    default: 'en' | 'ar';
    enabled: string[];
  };
  maintenance: {
    enabled: boolean;
    message: {
      en: string;
      ar: string;
    };
  };
}

interface GeneralSettingsProps {
  data: GeneralSettingsData;
  onChange: (data: GeneralSettingsData) => void;
}

export default function GeneralSettings({ data, onChange }: GeneralSettingsProps) {
  const updateData = (updates: Partial<GeneralSettingsData>) => {
    onChange({ ...data, ...updates });
  };

  return (
    <div className="space-y-6">
      {/* Site Information */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Site Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Site Name (English)</label>
            <input
              type="text"
              value={data.siteName.en}
              onChange={(e) => updateData({
                siteName: { ...data.siteName, en: e.target.value }
              })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Site Name (Arabic)</label>
            <input
              type="text"
              value={data.siteName.ar}
              onChange={(e) => updateData({
                siteName: { ...data.siteName, ar: e.target.value }
              })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Site URL</label>
            <input
              type="url"
              value={data.siteUrl}
              onChange={(e) => updateData({ siteUrl: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Admin Email</label>
            <input
              type="email"
              value={data.adminEmail}
              onChange={(e) => updateData({ adminEmail: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>
      </div>

      {/* Language & Localization */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Language & Localization</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Default Language</label>
            <select
              value={data.language.default}
              onChange={(e) => updateData({
                language: { ...data.language, default: e.target.value as 'en' | 'ar' }
              })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            >
              <option value="en">English</option>
              <option value="ar">Arabic</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Timezone</label>
            <select
              value={data.timezone}
              onChange={(e) => updateData({ timezone: e.target.value })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            >
              <option value="Asia/Dubai">Asia/Dubai (UTC+4)</option>
              <option value="UTC">UTC (UTC+0)</option>
              <option value="America/New_York">America/New_York (UTC-5)</option>
              <option value="Europe/London">Europe/London (UTC+0)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Maintenance Mode */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Maintenance Mode</h3>
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="text-white font-medium">Enable Maintenance Mode</p>
            <p className="text-gray-400 text-sm">Temporarily disable the website for maintenance</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={data.maintenance.enabled}
              onChange={(e) => updateData({
                maintenance: { ...data.maintenance, enabled: e.target.checked }
              })}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
          </label>
        </div>
        
        {data.maintenance.enabled && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Maintenance Message (English)</label>
              <textarea
                value={data.maintenance.message.en}
                onChange={(e) => updateData({
                  maintenance: {
                    ...data.maintenance,
                    message: { ...data.maintenance.message, en: e.target.value }
                  }
                })}
                rows={3}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Maintenance Message (Arabic)</label>
              <textarea
                value={data.maintenance.message.ar}
                onChange={(e) => updateData({
                  maintenance: {
                    ...data.maintenance,
                    message: { ...data.maintenance.message, ar: e.target.value }
                  }
                })}
                rows={3}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 