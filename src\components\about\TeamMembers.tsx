"use client";

import React, { useEffect, useRef } from "react";
import Image from "next/image";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

const TeamMembers = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const teamRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Add language context
  const { locale } = useLanguage();

  useEffect(() => {
    // Initialize the array with the correct number of refs
    teamRefs.current = teamRefs.current.slice(0, 4);
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in");
          }
        });
      },
      {
        threshold: 0.2,
        rootMargin: "0px 0px -100px 0px"
      }
    );
    
    // Observe the section header
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    // Observe each team member card
    teamRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });
    
    return () => {
      observer.disconnect();
    };
  }, []);

  const team = [
    {
      name: "<PERSON> Al-Mansour",
      title: "Chief Executive Officer",
      bio: "Ahmed brings over 20 years of experience in real estate development and investment. He has led the development of multiple award-winning projects across the region.",
      image: "/images/team/placeholder.jpg",
      initials: "AA",
    },
    {
      name: "Sara Khalid",
      title: "Chief Financial Officer",
      bio: "Sara oversees all financial operations with her extensive background in investment banking and financial management for major development projects.",
      image: "/images/team/placeholder.jpg",
      initials: "SK",
    },
    {
      name: "Omar Rashid",
      title: "Head of Development",
      bio: "Omar leads our development team with his extensive expertise in architectural design and project management for luxury residential and commercial properties.",
      image: "/images/team/placeholder.jpg",
      initials: "OR",
    },
    {
      name: "Layla Mahmoud",
      title: "Director of Investments",
      bio: "Layla's strategic vision guides our investment portfolio, leveraging her background in real estate finance and market analysis to maximize returns.",
      image: "/images/team/placeholder.jpg",
      initials: "LM",
    },
  ];

  return (
    <section className="relative py-20 overflow-hidden bg-[rgb(10,15,30)]">
      {/* Architectural Blueprint Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Blueprint Grid Base */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(var(--color-primary),0.07)_1px,transparent_1px),linear-gradient(90deg,rgba(var(--color-primary),0.07)_1px,transparent_1px)] bg-[size:40px_40px] opacity-10"></div>
        
        {/* Floor Plan Elements */}
        <div className="absolute top-10 end-20 opacity-[0.07]">
          <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
            <rect x="50" y="50" width="400" height="300" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="50" y1="150" x2="200" y2="150" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="200" y1="50" x2="200" y2="350" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <rect x="300" y="50" width="150" height="100" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <rect x="300" y="250" width="150" height="100" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <circle cx="125" cy="100" r="30" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <path d="M300,150 Q375,150 375,200 Q375,250 300,250" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
          </svg>
        </div>
        
        {/* Building Elevation */}
        <div className="absolute bottom-0 start-0 opacity-[0.07]">
          <svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
            <rect x="100" y="50" width="200" height="250" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <rect x="125" y="75" width="50" height="75" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <rect x="225" y="75" width="50" height="75" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <rect x="150" y="200" width="100" height="100" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="200" y1="50" x2="200" y2="300" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="5,5"/>
            <line x1="100" y1="180" x2="300" y2="180" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="5,5"/>
          </svg>
        </div>
        
        {/* Building Section Detail */}
        <div className="absolute top-40 start-20 opacity-[0.07]">
          <svg width="350" height="250" viewBox="0 0 350 250" xmlns="http://www.w3.org/2000/svg">
            <path d="M50,200 L300,200 L300,50 L175,20 L50,50 Z" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="50" y1="200" x2="50" y2="250" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="300" y1="200" x2="300" y2="250" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="30" y1="250" x2="320" y2="250" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <rect x="125" y="120" width="100" height="80" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="2"/>
            <line x1="175" y1="20" x2="175" y2="120" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="5,5"/>
          </svg>
        </div>
        
        {/* Compass Rose */}
        <div className="absolute bottom-10 end-10 opacity-[0.1]">
          <svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <circle cx="50" cy="50" r="45" fill="none" stroke="rgb(var(--color-primary))" strokeWidth="1"/>
            <line x1="50" y1="5" x2="50" y2="95" stroke="rgb(var(--color-primary))" strokeWidth="1"/>
            <line x1="5" y1="50" x2="95" y2="50" stroke="rgb(var(--color-primary))" strokeWidth="1"/>
            <path d="M50,5 L55,15 L45,15 Z" fill="rgb(var(--color-primary))"/>
            <text x="50" y="25" textAnchor="middle" fill="rgb(var(--color-primary))" fontSize="10">N</text>
            <text x="75" y="52" textAnchor="middle" fill="rgb(var(--color-primary))" fontSize="10">E</text>
            <text x="50" y="80" textAnchor="middle" fill="rgb(var(--color-primary))" fontSize="10">S</text>
            <text x="25" y="52" textAnchor="middle" fill="rgb(var(--color-primary))" fontSize="10">W</text>
          </svg>
        </div>
        
        {/* Dimension Lines */}
        <div className="absolute top-0 start-0 w-full h-full opacity-[0.05]">
          <svg width="100%" height="100%" viewBox="0 0 1000 600" preserveAspectRatio="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="200" y1="0" x2="200" y2="600" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
            <line x1="400" y1="0" x2="400" y2="600" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
            <line x1="600" y1="0" x2="600" y2="600" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
            <line x1="800" y1="0" x2="800" y2="600" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
            <line x1="0" y1="150" x2="1000" y2="150" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
            <line x1="0" y1="300" x2="1000" y2="300" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
            <line x1="0" y1="450" x2="1000" y2="450" stroke="rgb(var(--color-primary))" strokeWidth="1" strokeDasharray="10,10"/>
          </svg>
        </div>
        
        {/* Subtle Glow Effect */}
        <div className="absolute start-0 top-0 w-full h-full bg-gradient-to-br from-[rgb(var(--color-primary))]/5 to-transparent opacity-30"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section header */}
        <div 
          ref={sectionRef}
          className="max-w-3xl mx-auto text-center mb-16 opacity-0 transform translate-y-8 transition-all duration-1000 ease-out"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-[rgb(var(--color-text))] mb-4">
            {t('teamMembers.title', locale).includes('Leadership') || t('teamMembers.title', locale).includes('القيادة') ? (
              <>
                {t('teamMembers.title', locale).split(' ').slice(0, -1).join(' ')} <span className="text-[rgb(var(--color-primary))]">{t('teamMembers.title', locale).split(' ').slice(-1)[0]}</span>
              </>
            ) : (
              t('teamMembers.title', locale)
            )}
          </h2>
          <p className="text-lg text-[rgb(var(--color-text-secondary))]">
            {t('teamMembers.description', locale)}
          </p>
        </div>
        
        {/* Team grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {team.map((member, index) => (
            <div 
              key={index} 
              ref={(el) => {
                if (el) teamRefs.current[index] = el;
              }}
              className="opacity-0 transform translate-y-12 team-card bg-[rgb(var(--color-background))]/20 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden card-shape group"
              style={{ transitionDelay: `${index * 150}ms` }}
            >
              <div className="h-1.5 w-full bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] opacity-80 group-hover:opacity-100 transition-opacity"></div>
              <div className="px-6 pt-6 pb-5 text-center">
                {/* Initials Circle */}
                <div className="w-16 h-16 mx-auto relative mb-4">
                  <div className="w-full h-full rounded-full flex items-center justify-center bg-[rgb(var(--color-primary))]/8">
                    <span className="text-[rgb(var(--color-primary))] font-bold text-lg relative z-10">
                      {member.initials}
                    </span>
                  </div>
                </div>
                
                {/* Content */}
                <h3 className="text-lg font-bold text-[rgb(var(--color-text))] mb-1.5">
                  {member.name}
                </h3>
                <p className="text-[rgb(var(--color-primary))] font-medium text-sm tracking-wide mb-3">{member.title}</p>
                <p className="text-[rgb(var(--color-text-secondary))] text-sm leading-relaxed line-clamp-3">
                  {member.bio}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <style jsx>{`
        .animate-in {
          opacity: 1 !important;
          transform: translateY(0) !important;
        }
        
        .team-card {
          transition: all 400ms cubic-bezier(0.17, 0.67, 0.3, 0.96);
          backface-visibility: hidden;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .card-shape {
          border-radius: 2px 12px 2px 12px;
          border: 1px solid rgba(var(--color-primary), 0.1);
        }
        
        .card-shape:hover {
          transform: translateY(-2px);
          border: 1px solid rgba(var(--color-primary), 0.2);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </section>
  );
};

export default TeamMembers;