import Link from "next/link";

interface ProjectFooterCTAProps {
  project: any;
}

const ProjectFooterCTA = ({ project }: ProjectFooterCTAProps) => {
  return (
          <div className="bg-gradient-to-r from-[#0D1526] to-[#232F3E] py-16">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Secure Your Investment?</h2>
        <p className="text-white/80 max-w-2xl mx-auto mb-8 text-lg">
          Don't miss this exclusive opportunity to invest in one of Dubai's premier properties. Contact our team today.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link
            href={`/contact?project=${project.slug}`}
            className="bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white px-8 py-3 rounded-full font-medium hover:shadow-lg transition-all duration-300"
          >
            Contact Sales Team
          </Link>
          <Link
            href="/projects"
            className="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-medium hover:bg-white/20 transition-all duration-300"
          >
            View All Projects
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ProjectFooterCTA;