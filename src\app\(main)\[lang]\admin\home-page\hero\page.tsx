"use client";

import { useState, useEffect } from 'react';
import { FiSave, FiEdit3, FiX, FiEye, FiHome, FiArrowRight } from 'react-icons/fi';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/contexts/ToastContext';
import { createToast, toastMessages } from '@/utils/toast';
import { authenticatedApiCall } from '@/utils/api';

interface HeroContent {
  id?: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    line1: {
      en: string;
      ar: string;
    };
    line2: {
      en: string;
      ar: string;
    };
    line3: {
      en: string;
      ar: string;
    };
  };
  description: {
    en: string;
    ar: string;
  };
  buttons: {
    primary: {
      text: {
        en: string;
        ar: string;
      };
      link: string;
    };
    secondary: {
      text: {
        en: string;
        ar: string;
      };
      link: string;
    };
  };
  bottomText: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
  updated_by?: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export default function HomePageHeroManagement() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { showToast } = useToast();
  
  const [heroContent, setHeroContent] = useState<HeroContent>({
    badge: { en: "", ar: "" },
    title: {
      line1: { en: "", ar: "" },
      line2: { en: "", ar: "" },
      line3: { en: "", ar: "" }
    },
    description: { en: "", ar: "" },
    buttons: {
      primary: {
        text: { en: "", ar: "" },
        link: ""
      },
      secondary: {
        text: { en: "", ar: "" },
        link: ""
      }
    },
    bottomText: { en: "", ar: "" }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [originalContent, setOriginalContent] = useState<HeroContent | null>(null);

  // Fetch hero content when authentication is ready
  useEffect(() => {
    // Only fetch when auth is not loading and user is authenticated
    if (!authLoading && isAuthenticated) {
      fetchHeroContent();
    } else if (!authLoading && !isAuthenticated) {
      // Auth is ready but user is not authenticated
      setIsLoading(false);
      showToast(createToast.error(
        'Authentication required',
        'Please log in to access this page'
      ));
    }
  }, [authLoading, isAuthenticated]); // Dependencies: auth loading state and authentication status

  const fetchHeroContent = async () => {
    // Debug authentication state
    console.log('🔍 Auth Debug - User:', user);
    console.log('🔍 Auth Debug - Is Authenticated:', isAuthenticated);
    console.log('🔍 Auth Debug - Auth Loading:', authLoading);
    
    if (typeof window !== 'undefined') {
      const tokensData = localStorage.getItem('adminTokens');
      const userData = localStorage.getItem('adminUser');
      console.log('🔍 Auth Debug - Tokens in localStorage:', tokensData ? 'Present' : 'Missing');
      console.log('🔍 Auth Debug - User in localStorage:', userData ? 'Present' : 'Missing');
      
      if (tokensData) {
        try {
          const tokens = JSON.parse(tokensData);
          console.log('🔍 Auth Debug - Access token:', tokens.access ? 'Present' : 'Missing');
          console.log('🔍 Auth Debug - Access token preview:', tokens.access ? tokens.access.substring(0, 20) + '...' : 'None');
        } catch (e) {
          console.error('🔍 Auth Debug - Error parsing tokens:', e);
        }
      }
    }

    setIsLoading(true);
    try {
      console.log('🔄 Making authenticated API call to fetch hero content...');
      const response = await authenticatedApiCall('/api/admin/home-page/hero/', {
        method: 'GET'
      });

      console.log('📥 Hero content response:', response);

      if (response.success && response.data) {
        setHeroContent(response.data);
        setOriginalContent(response.data);
        setHasChanges(false);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || 'Failed to load hero content',
            'Please try refreshing the page'
          ));
        }
      }
    } catch (error) {
      console.error('Error fetching hero content:', error);
      showToast(createToast.error(
        'Failed to load hero content',
        'Please check your connection and try again'
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentChange = (newContent: HeroContent) => {
    setHeroContent(newContent);
    setHasChanges(JSON.stringify(newContent) !== JSON.stringify(originalContent));
  };

  const handleSaveAll = async () => {
    if (!hasChanges) {
      showToast(createToast.info('No changes to save'));
      return;
    }

    // Check authentication before saving
    if (!isAuthenticated) {
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    setIsSaving(true);
    try {
      const response = await authenticatedApiCall('/api/admin/home-page/hero/', {
        method: 'PUT',
        body: JSON.stringify({
          badge: heroContent.badge,
          title: heroContent.title,
          description: heroContent.description,
          buttons: heroContent.buttons,
          bottomText: heroContent.bottomText
        })
      });

      if (response.success) {
        showToast(createToast.success(
          response.message || 'Hero section updated successfully',
          'All changes have been saved'
        ));
        
        // Update the content with the response data
        if (response.data) {
          setHeroContent(response.data);
          setOriginalContent(response.data);
        }
        setHasChanges(false);
        setEditingSection(null);
      } else {
        // Handle validation errors
        if (response.errors) {
          const errorMessages = Object.entries(response.errors)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          
          showToast(createToast.error(
            'Validation failed',
            errorMessages
          ));
        } else {
          // Check if it's an authentication error
          if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
            showToast(createToast.error(
              'Authentication expired',
              'Please log in again to continue'
            ));
          } else {
            showToast(createToast.error(
              response.message || 'Failed to save changes',
              'Please check your input and try again'
            ));
          }
        }
      }
    } catch (error) {
      console.error('Error saving hero content:', error);
      showToast(createToast.error(
        'Failed to save changes',
        'Please check your connection and try again'
      ));
    } finally {
      setIsSaving(false);
    }
  };

  const handleSectionSave = async (section: string, data: any) => {
    // Check authentication before saving
    if (!isAuthenticated) {
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    try {
      const response = await authenticatedApiCall('/api/admin/home-page/hero/section/', {
        method: 'PATCH',
        body: JSON.stringify({
          section,
          data
        })
      });

      if (response.success) {
        showToast(createToast.success(
          response.message || `${section} section updated successfully`
        ));
        
        // Refresh the content to get the latest data
        await fetchHeroContent();
        setEditingSection(null);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || `Failed to update ${section} section`
          ));
        }
      }
    } catch (error) {
      console.error(`Error updating ${section} section:`, error);
      showToast(createToast.error(
        `Failed to update ${section} section`,
        'Please try again'
      ));
    }
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">
            {authLoading ? 'Checking authentication...' : 'Loading hero content...'}
          </p>
        </div>
      </div>
    );
  }

  // If auth is ready but user is not authenticated, the useEffect will handle the redirect
  if (!authLoading && !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <p className="text-gray-400">Authentication required. Please log in to continue.</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Hero Section</h1>
          <p className="text-gray-400 mt-1">Manage the hero section text content and call-to-action buttons</p>
          {heroContent.updated_at && (
            <p className="text-gray-500 text-sm mt-2">
              Last updated: {new Date(heroContent.updated_at).toLocaleString()}
              {heroContent.updated_by && (
                <span className="ml-2">
                  by {heroContent.updated_by.first_name} {heroContent.updated_by.last_name}
                </span>
              )}
            </p>
          )}
        </div>
        <button
          onClick={handleSaveAll}
          disabled={!hasChanges || isSaving}
          className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
            hasChanges && !isSaving
              ? 'bg-[#00C2FF] text-white hover:bg-[#00C2FF]/90'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
            </>
          )}
        </button>
      </div>

      {hasChanges && (
        <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
          <p className="text-yellow-400 text-sm">
            You have unsaved changes. Don't forget to save your work!
          </p>
        </div>
      )}

      <div className="mt-6 space-y-8">
        {/* Badge Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiHome className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Badge Text
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'badge' ? null : 'badge')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'badge' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'badge' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'badge' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (English)</label>
                  <input
                    type="text"
                    value={heroContent.badge.en}
                    onChange={(e) => handleContentChange({
                      ...heroContent,
                      badge: { ...heroContent.badge, en: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={200}
                  />
                  <p className="text-xs text-gray-500 mt-1">{heroContent.badge.en.length}/200 characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (Arabic)</label>
                  <input
                    type="text"
                    value={heroContent.badge.ar}
                    onChange={(e) => handleContentChange({
                      ...heroContent,
                      badge: { ...heroContent.badge, ar: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={200}
                  />
                  <p className="text-xs text-gray-500 mt-1">{heroContent.badge.ar.length}/200 characters</p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('badge', heroContent.badge)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-xs text-gray-500 block mb-1">English</span>
                <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20">
                  <span className="text-sm font-medium tracking-wide text-white">{heroContent.badge.en}</span>
                </div>
              </div>
              <div>
                <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 text-right" dir="rtl">
                  <span className="text-sm font-medium tracking-wide text-white">{heroContent.badge.ar}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Title Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiHome className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Main Title (3 Lines)
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'title' ? null : 'title')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'title' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'title' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'title' ? (
            <div className="space-y-6">
              {/* Line 1 */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Line 1 (White Text)</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">English</label>
                    <input
                      type="text"
                      value={heroContent.title.line1.en}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        title: { ...heroContent.title, line1: { ...heroContent.title.line1, en: e.target.value } }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={100}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.title.line1.en.length}/100 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Arabic</label>
                    <input
                      type="text"
                      value={heroContent.title.line1.ar}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        title: { ...heroContent.title, line1: { ...heroContent.title.line1, ar: e.target.value } }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={100}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.title.line1.ar.length}/100 characters</p>
                  </div>
                </div>
              </div>

              {/* Line 2 */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Line 2 (Gradient Text - Blue to Purple)</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">English</label>
                    <input
                      type="text"
                      value={heroContent.title.line2.en}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        title: { ...heroContent.title, line2: { ...heroContent.title.line2, en: e.target.value } }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={100}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.title.line2.en.length}/100 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Arabic</label>
                    <input
                      type="text"
                      value={heroContent.title.line2.ar}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        title: { ...heroContent.title, line2: { ...heroContent.title.line2, ar: e.target.value } }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={100}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.title.line2.ar.length}/100 characters</p>
                  </div>
                </div>
              </div>

              {/* Line 3 */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Line 3 (White Text)</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">English</label>
                    <input
                      type="text"
                      value={heroContent.title.line3.en}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        title: { ...heroContent.title, line3: { ...heroContent.title.line3, en: e.target.value } }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={100}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.title.line3.en.length}/100 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Arabic</label>
                    <input
                      type="text"
                      value={heroContent.title.line3.ar}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        title: { ...heroContent.title, line3: { ...heroContent.title.line3, ar: e.target.value } }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={100}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.title.line3.ar.length}/100 characters</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('title', heroContent.title)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <span className="text-xs text-gray-500 block mb-3">English</span>
                <h1 className="text-3xl md:text-4xl font-bold leading-tight tracking-tight">
                  <span className="block text-white">{heroContent.title.line1.en}</span>
                  <span className="block h-2"></span>
                  <span className="block bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">{heroContent.title.line2.en}</span>
                  <span className="block h-2"></span>
                  <span className="block text-white">{heroContent.title.line3.en}</span>
                </h1>
              </div>
              <div>
                <span className="text-xs text-gray-500 block mb-3">Arabic</span>
                <h1 className="text-3xl md:text-4xl font-bold leading-tight tracking-tight text-right" dir="rtl">
                  <span className="block text-white">{heroContent.title.line1.ar}</span>
                  <span className="block h-2"></span>
                  <span className="block bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">{heroContent.title.line2.ar}</span>
                  <span className="block h-2"></span>
                  <span className="block text-white">{heroContent.title.line3.ar}</span>
                </h1>
              </div>
            </div>
          )}
        </div>

        {/* Description Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiHome className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Description Text
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'description' ? null : 'description')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'description' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'description' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'description' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                  <textarea
                    value={heroContent.description.en}
                    onChange={(e) => handleContentChange({
                      ...heroContent,
                      description: { ...heroContent.description, en: e.target.value }
                    })}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-1">{heroContent.description.en.length}/500 characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                  <textarea
                    value={heroContent.description.ar}
                    onChange={(e) => handleContentChange({
                      ...heroContent,
                      description: { ...heroContent.description, ar: e.target.value }
                    })}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={500}
                  />
                  <p className="text-xs text-gray-500 mt-1">{heroContent.description.ar.length}/500 characters</p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('description', heroContent.description)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-xs text-gray-500 block mb-1">English</span>
                <p className="text-xl text-gray-300">{heroContent.description.en}</p>
              </div>
              <div>
                <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                <p className="text-xl text-gray-300 text-right" dir="rtl">{heroContent.description.ar}</p>
              </div>
            </div>
          )}
        </div>

        {/* Buttons Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiArrowRight className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Call-to-Action Buttons
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'buttons' ? null : 'buttons')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'buttons' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'buttons' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'buttons' ? (
            <div className="space-y-6">
              {/* Primary Button */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Primary Button (White Background)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Text (English)</label>
                    <input
                      type="text"
                      value={heroContent.buttons.primary.text.en}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        buttons: {
                          ...heroContent.buttons,
                          primary: {
                            ...heroContent.buttons.primary,
                            text: { ...heroContent.buttons.primary.text, en: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={50}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.buttons.primary.text.en.length}/50 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Text (Arabic)</label>
                    <input
                      type="text"
                      value={heroContent.buttons.primary.text.ar}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        buttons: {
                          ...heroContent.buttons,
                          primary: {
                            ...heroContent.buttons.primary,
                            text: { ...heroContent.buttons.primary.text, ar: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={50}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.buttons.primary.text.ar.length}/50 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Link</label>
                    <input
                      type="text"
                      value={heroContent.buttons.primary.link}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        buttons: {
                          ...heroContent.buttons,
                          primary: { ...heroContent.buttons.primary, link: e.target.value }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={200}
                      placeholder="/projects or https://example.com"
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.buttons.primary.link.length}/200 characters</p>
                  </div>
                </div>
              </div>

              {/* Secondary Button */}
              <div>
                <h3 className="text-lg font-medium text-white mb-3">Secondary Button (Transparent with Border)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Text (English)</label>
                    <input
                      type="text"
                      value={heroContent.buttons.secondary.text.en}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        buttons: {
                          ...heroContent.buttons,
                          secondary: {
                            ...heroContent.buttons.secondary,
                            text: { ...heroContent.buttons.secondary.text, en: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={50}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.buttons.secondary.text.en.length}/50 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Text (Arabic)</label>
                    <input
                      type="text"
                      value={heroContent.buttons.secondary.text.ar}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        buttons: {
                          ...heroContent.buttons,
                          secondary: {
                            ...heroContent.buttons.secondary,
                            text: { ...heroContent.buttons.secondary.text, ar: e.target.value }
                          }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={50}
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.buttons.secondary.text.ar.length}/50 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Link</label>
                    <input
                      type="text"
                      value={heroContent.buttons.secondary.link}
                      onChange={(e) => handleContentChange({
                        ...heroContent,
                        buttons: {
                          ...heroContent.buttons,
                          secondary: { ...heroContent.buttons.secondary, link: e.target.value }
                        }
                      })}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      maxLength={200}
                      placeholder="/contact or https://example.com"
                    />
                    <p className="text-xs text-gray-500 mt-1">{heroContent.buttons.secondary.link.length}/200 characters</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('buttons', heroContent.buttons)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <span className="text-xs text-gray-500 block mb-3">Button Preview</span>
                <div className="flex flex-wrap items-center gap-4">
                  <button className="group relative inline-flex items-center justify-center overflow-hidden rounded-full border border-transparent bg-white px-6 py-3 text-base font-medium text-gray-900 transition hover:scale-105">
                    <span>{heroContent.buttons.primary.text.en}</span>
                    <FiArrowRight className="ms-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                  </button>
                  <button className="relative inline-flex items-center justify-center overflow-hidden rounded-full border border-white/30 bg-transparent px-6 py-3 text-base font-medium text-white transition hover:bg-white/10">
                    <span>{heroContent.buttons.secondary.text.en}</span>
                  </button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-xs text-gray-500 block mb-1">Primary Button</span>
                  <p className="text-white text-sm">EN: {heroContent.buttons.primary.text.en}</p>
                  <p className="text-white text-sm">AR: {heroContent.buttons.primary.text.ar}</p>
                  <p className="text-gray-400 text-xs">Link: {heroContent.buttons.primary.link}</p>
                </div>
                <div>
                  <span className="text-xs text-gray-500 block mb-1">Secondary Button</span>
                  <p className="text-white text-sm">EN: {heroContent.buttons.secondary.text.en}</p>
                  <p className="text-white text-sm">AR: {heroContent.buttons.secondary.text.ar}</p>
                  <p className="text-gray-400 text-xs">Link: {heroContent.buttons.secondary.link}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Text Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiHome className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Bottom Tagline
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'bottom' ? null : 'bottom')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'bottom' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'bottom' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'bottom' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Bottom Text (English)</label>
                  <input
                    type="text"
                    value={heroContent.bottomText.en}
                    onChange={(e) => handleContentChange({
                      ...heroContent,
                      bottomText: { ...heroContent.bottomText, en: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={100}
                  />
                  <p className="text-xs text-gray-500 mt-1">{heroContent.bottomText.en.length}/100 characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Bottom Text (Arabic)</label>
                  <input
                    type="text"
                    value={heroContent.bottomText.ar}
                    onChange={(e) => handleContentChange({
                      ...heroContent,
                      bottomText: { ...heroContent.bottomText, ar: e.target.value }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    maxLength={100}
                  />
                  <p className="text-xs text-gray-500 mt-1">{heroContent.bottomText.ar.length}/100 characters</p>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleSectionSave('bottomText', heroContent.bottomText)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-xs text-gray-500 block mb-1">English</span>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-[1px] bg-white/30"></div>
                  <p className="text-white/70 uppercase tracking-wider text-sm font-medium">{heroContent.bottomText.en}</p>
                </div>
              </div>
              <div>
                <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                <div className="flex items-center gap-4 justify-end" dir="rtl">
                  <div className="w-12 h-[1px] bg-white/30"></div>
                  <p className="text-white/70 uppercase tracking-wider text-sm font-medium">{heroContent.bottomText.ar}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Live Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            <div className="w-full text-white space-y-6">
              {/* Badge */}
              {heroContent.badge.en && (
              <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-4">
                <span className="text-sm font-medium tracking-wide">{heroContent.badge.en}</span>
              </div>
              )}
              
              {/* Title */}
              <h1 className="text-5xl md:text-7xl font-bold leading-tight tracking-tight">
                <span className="block text-white">{heroContent.title.line1.en}</span>
                <span className="block h-2"></span>
                <span className="block bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500">{heroContent.title.line2.en}</span>
                <span className="block h-2"></span>
                <span className="block text-white">{heroContent.title.line3.en}</span>
              </h1>
              
              {/* Description */}
              {heroContent.description.en && (
              <p className="text-xl text-gray-300 max-w-lg">{heroContent.description.en}</p>
              )}
              
              {/* Buttons */}
              <div className="flex flex-wrap items-center gap-4 pt-2">
                {heroContent.buttons.primary.text.en && (
                <button className="group relative inline-flex items-center justify-center overflow-hidden rounded-full border border-transparent bg-white px-6 py-3 text-base font-medium text-gray-900 transition hover:scale-105">
                  <span>{heroContent.buttons.primary.text.en}</span>
                  <FiArrowRight className="ms-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                </button>
                )}
                {heroContent.buttons.secondary.text.en && (
                <button className="relative inline-flex items-center justify-center overflow-hidden rounded-full border border-white/30 bg-transparent px-6 py-3 text-base font-medium text-white transition hover:bg-white/10">
                  <span>{heroContent.buttons.secondary.text.en}</span>
                </button>
                )}
              </div>
              
              {/* Bottom Text */}
              {heroContent.bottomText.en && (
              <div className="pt-8">
                <div className="flex items-center gap-6">
                  <div className="w-12 h-[1px] bg-white/30"></div>
                  <p className="text-white/70 uppercase tracking-wider text-sm font-medium">{heroContent.bottomText.en}</p>
                </div>
              </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 