"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { FiSave, FiArrowLeft, FiTrash2, FiPlus, FiX } from 'react-icons/fi';
import Link from 'next/link';

// Mock function to get project data - in a real app, this would be an API call
const getProject = (id: string) => {
  // Mock project data
  const project = {
    id: parseInt(id),
    title: "Mazaya Heights",
    slug: "mazaya-heights",
    location: "Downtown Dubai",
    description: "Luxury residential tower with panoramic city views",
    longDescription: "Mazaya Heights is our flagship luxury residential tower located in the heart of Downtown Dubai. The 45-story tower offers unparalleled views of the city skyline and Dubai Fountain. With premium finishes and smart home technology in every unit, Mazaya Heights represents the pinnacle of urban living.",
    shortSummary: "An exclusive collection of luxury residences in Downtown Dubai with panoramic views of the city skyline and iconic landmarks.",
    category: "Residential",
    status: "Completed",
    unitsAvailable: 15,
    features: [
      "Panoramic Views", 
      "Smart Home", 
      "Fitness Center", 
      "Swimming Pool", 
      "24/7 Security", 
      "Concierge Service",
      "Children's Play Area",
      "Rooftop Garden"
    ],
    amenities: [
      "Infinity Pool",
      "State-of-the-art Gym",
      "Sauna & Steam Room",
      "Business Center",
      "Indoor/Outdoor Lounges",
      "Landscaped Gardens",
      "BBQ Area",
      "Visitor Parking"
    ],
    keyPoints: [
      "Prime Location in Downtown Dubai",
      "Panoramic Views of City Skyline",
      "Premium Smart Home Technology",
      "World-Class Amenities",
      "High ROI Investment Opportunity"
    ],
    gallery: [
      "/images/project-1-1.jpg", 
      "/images/project-1-2.jpg", 
      "/images/project-1-3.jpg"
    ],
    projectHighlights: {
      stories: 45,
      luxuryUnits: 350,
      completionYear: 2021,
      avgROI: "8%"
    },
    investment: {
      priceRange: "$500,000 - $3,000,000",
      rentalYield: "7-9% annually",
      completionDate: "2021",
      paymentPlan: "30% down payment, 70% upon completion"
    },
    investmentBenefits: [
      {
        title: "Strong Rental Yield",
        description: "With an expected rental yield of 7-9% annually, this investment offers attractive returns compared to other real estate options in the area.",
        icon: "chart"
      },
      {
        title: "Capital Appreciation",
        description: "Downtown Dubai continues to be one of the most sought-after areas, ensuring strong potential for capital appreciation over time.",
        icon: "trending-up"
      },
      {
        title: "Flexible Payment Plan",
        description: "Our attractive payment plan (30% down payment, 70% upon completion) makes this investment accessible and manageable for investors.",
        icon: "dollar"
      },
      {
        title: "Low Risk Investment",
        description: "Mazaya Capital's proven track record and the prime location of this development minimize investment risk while maximizing potential returns.",
        icon: "shield"
      }
    ],
    availablePropertyTypes: [
      {
        type: "Studio",
        size: "450 - 550 sq.ft",
        startingPrice: "$500,000",
        availability: "Available"
      },
      {
        type: "1 Bedroom",
        size: "750 - 850 sq.ft",
        startingPrice: "$750,000",
        availability: "Available"
      },
      {
        type: "2 Bedroom",
        size: "1100 - 1300 sq.ft",
        startingPrice: "$1,200,000",
        availability: "Limited"
      },
      {
        type: "3 Bedroom",
        size: "1600 - 1800 sq.ft",
        startingPrice: "$1,800,000",
        availability: "Limited"
      },
      {
        type: "Penthouse",
        size: "3000+ sq.ft",
        startingPrice: "$3,000,000",
        availability: "On Request"
      }
    ],
    constructionProgress: {
      completionPercentage: 85,
      stages: [
        {
          title: "Foundation Completed",
          description: "The foundation work has been finished ahead of schedule with all structural integrity tests passed successfully.",
          date: "January 2021",
          status: "Completed"
        },
        {
          title: "Structural Framework",
          description: "All 45 floors have been structurally completed with reinforced concrete framework in place.",
          date: "June 2021",
          status: "Completed"
        },
        {
          title: "Exterior Facade",
          description: "Exterior glass facade installation complete with energy-efficient materials for optimal temperature control.",
          date: "October 2021",
          status: "Completed"
        },
        {
          title: "Interior Finishing",
          description: "Luxury interior finishes being installed across all residential units with premium materials and fixtures.",
          date: "Current Phase",
          status: "In Progress",
          progressPercentage: 85
        },
        {
          title: "Final Inspections & Handover",
          description: "Final quality checks, smart home system testing, and preparation for unit handover to owners.",
          date: "Estimated December 2022",
          status: "Upcoming"
        }
      ]
    },
    locationDetails: {
      address: "Sheikh Mohammed bin Rashid Blvd, Downtown Dubai, UAE",
      coordinates: { lat: 25.197197, lng: 55.274376 },
      description: "Strategically located in the heart of Dubai with easy access to key landmarks and amenities.",
      nearbyAttractions: [
        {
          name: "Dubai Mall",
          distance: "5 minutes"
        },
        {
          name: "Burj Khalifa",
          distance: "7 minutes"
        },
        {
          name: "Dubai Opera",
          distance: "10 minutes"
        },
        {
          name: "Dubai International Airport",
          distance: "20 minutes"
        }
      ],
      transportation: [
        "Easy access to Sheikh Zayed Road and Al Khail Road",
        "5-minute walk to the Dubai Metro station",
        "Regular bus services to key destinations",
        "20 minutes from Dubai International Airport"
      ]
    },
    constructionPhotos: [
      {
        image: "/images/construction/foundation.jpg",
        title: "Foundation Work"
      },
      {
        image: "/images/construction/structural.jpg",
        title: "Structural Framework"
      },
      {
        image: "/images/construction/facade.jpg",
        title: "Exterior Facade"
      },
      {
        image: "/images/construction/interior.jpg",
        title: "Interior Finishing"
      },
      {
        image: "/images/construction/amenities.jpg",
        title: "Amenities Installation"
      },
      {
        image: "/images/construction/overview.jpg",
        title: "Project Overview"
      }
    ],
    themeColor: "#00C2FF",
    // Add styling property
    styling: {
      themeColor: '#00C2FF',
      heroSection: {
        textColor: '#FFFFFF',
        overlayColor: '#000000',
        overlayOpacity: 0.4,
        backgroundPosition: 'center'
      },
      overviewSection: {
        backgroundColor: '#F5F9FC',
        textColor: '#333333',
        headingColor: '#00C2FF',
        backgroundImage: ''
      },
      gallerySection: {
        backgroundColor: '#FFFFFF',
        textColor: '#333333',
        headingColor: '#00C2FF'
      },
      amenitiesSection: {
        backgroundColor: '#F5F9FC',
        textColor: '#333333',
        headingColor: '#00C2FF',
        itemBackgroundColor: '#FFFFFF',
        itemTextColor: '#333333',
        backgroundImage: ''
      },
      locationSection: {
        backgroundColor: '#FFFFFF',
        textColor: '#333333',
        headingColor: '#00C2FF'
      },
      investmentSection: {
        backgroundColor: '#F5F9FC',
        textColor: '#333333',
        headingColor: '#00C2FF',
        backgroundImage: ''
      },
      progressSection: {
        backgroundColor: '#FFFFFF',
        textColor: '#333333',
        headingColor: '#00C2FF',
        stageBackgroundColor: '#F5F9FC',
        stageTextColor: '#333333'
      },
      inquirySection: {
        backgroundColor: '#00C2FF',
        textColor: '#FFFFFF',
        headingColor: '#FFFFFF',
        inputBackgroundColor: '#FFFFFF',
        inputTextColor: '#333333',
        buttonColor: '#333333',
        buttonTextColor: '#FFFFFF',
        backgroundImage: ''
      },
      footerCTA: {
        backgroundColor: '#333333',
        textColor: '#FFFFFF',
        buttonColor: '#00C2FF',
        buttonTextColor: '#FFFFFF',
        backgroundImage: ''
      },
      navigationColors: {
        backgroundColor: 'rgba(255,255,255,0.9)',
        textColor: '#333333',
        activeTextColor: '#00C2FF'
      },
      fontFamily: 'Inter, sans-serif'
    },
    developer: {
      name: "Mazaya Capital",
      description: "Leading real estate developer with over 15 years of experience in creating luxury properties",
      projectsCompleted: 12,
      totalUnitsDelivered: 3500
    }
  };
  
  return project;
};

export default function ProjectEditPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const isNewProject = params.id === 'new';
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [project, setProject] = useState<any>({
    title: '',
    slug: '',
    location: '',
    description: '',
    longDescription: '',
    shortSummary: '',
    category: 'Residential',
    status: 'Planned',
    unitsAvailable: 0,
    features: [],
    amenities: [],
    keyPoints: [],
    gallery: [],
    projectHighlights: {
      stories: 0,
      luxuryUnits: 0,
      completionYear: 0,
      avgROI: ""
    },
    investment: {
      priceRange: '',
      rentalYield: '',
      completionDate: '',
      paymentPlan: ''
    },
    investmentBenefits: [],
    availablePropertyTypes: [],
    constructionProgress: {
      completionPercentage: 0,
      stages: []
    },
    locationDetails: {
      address: '',
      coordinates: { lat: 0, lng: 0 },
      description: '',
      nearbyAttractions: [],
      transportation: []
    },
    constructionPhotos: [],
    themeColor: "#00C2FF",
    // Add styling initialization
    styling: {
      themeColor: '#00C2FF',
      heroSection: {
        textColor: '#FFFFFF',
        overlayColor: '#000000',
        overlayOpacity: 0.4,
        backgroundPosition: 'center'
      },
      overviewSection: {
        backgroundColor: '#F5F9FC',
        textColor: '#333333',
        headingColor: '#00C2FF',
        backgroundImage: ''
      },
      gallerySection: {
        backgroundColor: '#FFFFFF',
        textColor: '#333333',
        headingColor: '#00C2FF'
      },
      amenitiesSection: {
        backgroundColor: '#F5F9FC',
        textColor: '#333333',
        headingColor: '#00C2FF',
        itemBackgroundColor: '#FFFFFF',
        itemTextColor: '#333333',
        backgroundImage: ''
      },
      locationSection: {
        backgroundColor: '#FFFFFF',
        textColor: '#333333',
        headingColor: '#00C2FF'
      },
      investmentSection: {
        backgroundColor: '#F5F9FC',
        textColor: '#333333',
        headingColor: '#00C2FF',
        backgroundImage: ''
      },
      progressSection: {
        backgroundColor: '#FFFFFF',
        textColor: '#333333',
        headingColor: '#00C2FF',
        stageBackgroundColor: '#F5F9FC',
        stageTextColor: '#333333'
      },
      inquirySection: {
        backgroundColor: '#00C2FF',
        textColor: '#FFFFFF',
        headingColor: '#FFFFFF',
        inputBackgroundColor: '#FFFFFF',
        inputTextColor: '#333333',
        buttonColor: '#333333',
        buttonTextColor: '#FFFFFF',
        backgroundImage: ''
      },
      footerCTA: {
        backgroundColor: '#333333',
        textColor: '#FFFFFF',
        buttonColor: '#00C2FF',
        buttonTextColor: '#FFFFFF',
        backgroundImage: ''
      },
      navigationColors: {
        backgroundColor: 'rgba(255,255,255,0.9)',
        textColor: '#333333',
        activeTextColor: '#00C2FF'
      },
      fontFamily: 'Inter, sans-serif'
    },
    developer: {
      name: "",
      description: "",
      projectsCompleted: 0,
      totalUnitsDelivered: 0
    }
  });
  
  // State variables for new items
  const [newFeature, setNewFeature] = useState('');
  const [newAmenity, setNewAmenity] = useState('');
  const [newKeyPoint, setNewKeyPoint] = useState('');
  const [newGalleryImage, setNewGalleryImage] = useState('');
  const [newNearbyAttraction, setNewNearbyAttraction] = useState({ name: '', distance: '' });
  const [newTransportation, setNewTransportation] = useState('');
  const [newInvestmentBenefit, setNewInvestmentBenefit] = useState({ title: '', description: '', icon: '' });
  const [newPropertyType, setNewPropertyType] = useState({ type: '', size: '', startingPrice: '', availability: 'Available' });
  const [newConstructionStage, setNewConstructionStage] = useState({ 
    title: '', 
    description: '', 
    date: '', 
    status: 'Upcoming',
    progressPercentage: 0 
  });
  const [newConstructionPhoto, setNewConstructionPhoto] = useState({ image: '', title: '' });
  
  useEffect(() => {
    if (!isNewProject) {
      // In a real app, this would be an API call
      const projectData = getProject(params.id);
      setProject(projectData);
    }
    setIsLoading(false);
  }, [params.id, isNewProject]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle nested objects with deeper nesting (e.g., locationDetails.coordinates.lat)
    if (name.includes('.')) {
      const parts = name.split('.');
      if (parts.length === 2) {
        const [parent, child] = parts;
        setProject({
          ...project,
          [parent]: {
            ...project[parent],
            [child]: value
          }
        });
      } else if (parts.length === 3) {
        const [parent, child, grandchild] = parts;
        setProject({
          ...project,
          [parent]: {
            ...project[parent],
            [child]: {
              ...project[parent][child],
              [grandchild]: value
            }
          }
        });
      }
    } else {
      setProject({
        ...project,
        [name]: value
      });
    }
  };
  
  // Arrays handlers (features, amenities, keyPoints, gallery)
  const addItem = (array: string, value: string) => {
    if (!value.trim()) return;
    
    // Handle nested arrays (e.g., locationDetails.nearbyAttractions)
    if (array.includes('.')) {
      const [parent, child] = array.split('.');
      
      // For complex objects stored as strings
      if (array === 'constructionProgress.stages' || 
          array === 'locationDetails.nearbyAttractions' || 
          array === 'investmentBenefits' || 
          array === 'availablePropertyTypes') {
        try {
          const parsedValue = JSON.parse(value);
          setProject({
            ...project,
            [parent]: {
              ...project[parent],
              [child]: [...project[parent][child], parsedValue]
            }
          });
        } catch (e) {
          console.error('Failed to parse JSON value:', e);
        }
      } else {
        // For simple string values
        setProject({
          ...project,
          [parent]: {
            ...project[parent],
            [child]: [...project[parent][child], value]
          }
        });
      }
    } else {
      // For top-level arrays with complex objects
      if (array === 'investmentBenefits' || array === 'availablePropertyTypes' || array === 'constructionPhotos') {
        try {
          const parsedValue = JSON.parse(value);
          setProject({
            ...project,
            [array]: [...project[array], parsedValue]
          });
        } catch (e) {
          console.error('Failed to parse JSON value:', e);
        }
      } else {
        // For simple string values in top-level arrays
        setProject({
          ...project,
          [array]: [...project[array], value]
        });
      }
    }
    
    // Reset the input values
    switch (array) {
      case 'features':
        setNewFeature('');
        break;
      case 'amenities':
        setNewAmenity('');
        break;
      case 'keyPoints':
        setNewKeyPoint('');
        break;
      case 'gallery':
        setNewGalleryImage('');
        break;
      case 'locationDetails.nearbyAttractions':
        setNewNearbyAttraction({ name: '', distance: '' });
        break;
      case 'locationDetails.transportation':
        setNewTransportation('');
        break;
      case 'investmentBenefits':
        setNewInvestmentBenefit({ title: '', description: '', icon: '' });
        break;
      case 'availablePropertyTypes':
        setNewPropertyType({ type: '', size: '', startingPrice: '', availability: 'Available' });
        break;
      case 'constructionProgress.stages':
        setNewConstructionStage({ 
          title: '', 
          description: '', 
          date: '', 
          status: 'Upcoming',
          progressPercentage: 0 
        });
        break;
      case 'constructionPhotos':
        setNewConstructionPhoto({ image: '', title: '' });
        break;
    }
  };
  
  const removeItem = (array: string, index: number) => {
    console.log('Removing item:', array, index);
    try {
      // Handle nested arrays
      if (array.includes('.')) {
        const [parent, child] = array.split('.');
        
        // Check if the parent and child exist
        if (!project[parent] || !project[parent][child]) {
          console.error(`Cannot remove item: ${parent}.${child} does not exist`);
          return;
        }
        
        // Check if the index is valid
        if (index < 0 || index >= project[parent][child].length) {
          console.error(`Cannot remove item: Index ${index} is out of bounds for ${parent}.${child}`);
          return;
        }
        
        const newArray = [...project[parent][child]];
        newArray.splice(index, 1);
        
        setProject({
          ...project,
          [parent]: {
            ...project[parent],
            [child]: newArray
          }
        });
      } else {
        // For top-level arrays
        // Check if the array exists
        if (!project[array]) {
          console.error(`Cannot remove item: ${array} does not exist`);
          return;
        }
        
        // Check if the index is valid
        if (index < 0 || index >= project[array].length) {
          console.error(`Cannot remove item: Index ${index} is out of bounds for ${array}`);
          return;
        }
        
        const newArray = [...project[array]];
        newArray.splice(index, 1);
        
        setProject({
          ...project,
          [array]: newArray
        });
      }
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };
  
  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // In a real app, this would be an API call to save the project
      console.log('Saving project:', project);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      router.push('/admin/projects');
    } catch (error) {
      console.error('Error saving project:', error);
      setIsSaving(false);
    }
  };
  
  if (isLoading) {
    return <div>Loading...</div>;
  }
  
  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">
            {isNewProject ? 'Add New Project' : `Edit: ${project.title}`}
          </h1>
          <p className="mt-2 text-sm text-gray-400">
            {isNewProject 
              ? 'Create a new project with all details and publish it to the website'
              : 'Update project information and settings'
            }
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Link
            href="/admin/projects"
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiArrowLeft className="mr-2 -ml-1 h-5 w-5 text-gray-400" />
            Back
          </Link>
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
            onClick={handleSave}
            disabled={isSaving}
          >
            <FiSave className="mr-2 -ml-1 h-5 w-5" />
            Save Project
          </button>
        </div>
      </div>
      
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="title" className="block text-sm font-medium text-gray-300">
              Project Title *
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="title"
                id="title"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.title}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
          
          <div className="sm:col-span-3">
            <label htmlFor="slug" className="block text-sm font-medium text-gray-300">
              URL Slug *
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="slug"
                id="slug"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.slug}
                onChange={handleInputChange}
                required
              />
            </div>
            <p className="mt-1 text-xs text-gray-500">Used in the URL: /projects/{project.slug}</p>
          </div>
          
          <div className="sm:col-span-3">
            <label htmlFor="location" className="block text-sm font-medium text-gray-300">
              Location *
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="location"
                id="location"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.location}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
          
          <div className="sm:col-span-3">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-300">
                  Category *
                </label>
                <div className="mt-1">
                  <select
                    id="category"
                    name="category"
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                    value={project.category}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="Residential">Residential</option>
                    <option value="Commercial">Commercial</option>
                    <option value="Mixed-Use">Mixed-Use</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-300">
                  Status *
                </label>
                <div className="mt-1">
                  <select
                    id="status"
                    name="status"
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                    value={project.status}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="Planned">Planned</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          
          <div className="sm:col-span-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-300">
              Short Description *
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="description"
                id="description"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.description}
                onChange={handleInputChange}
                required
              />
            </div>
            <p className="mt-1 text-xs text-gray-400">Brief description used in listings (max 100 chars)</p>
          </div>
          
          <div className="sm:col-span-6">
            <label htmlFor="shortSummary" className="block text-sm font-medium text-gray-300">
              Summary
            </label>
            <div className="mt-1">
              <textarea
                id="shortSummary"
                name="shortSummary"
                rows={2}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.shortSummary}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div className="sm:col-span-6">
            <label htmlFor="longDescription" className="block text-sm font-medium text-gray-300">
              Full Description
            </label>
            <div className="mt-1">
              <textarea
                id="longDescription"
                name="longDescription"
                rows={4}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.longDescription}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Features Section */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Features</h2>
        
        <div className="mb-4">
          <div className="flex space-x-2">
            <input
              type="text"
              value={newFeature}
              onChange={(e) => setNewFeature(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
              placeholder="Add a feature e.g. Smart Home, Swimming Pool"
            />
            <button
              type="button"
              onClick={() => addItem('features', newFeature)}
              className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
            >
              <FiPlus className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {project.features.length === 0 ? (
          <p className="text-sm text-gray-400 italic">No features added yet. Add some key features that make this project special.</p>
        ) : (
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 lg:grid-cols-4">
            {project.features.map((feature: string, index: number) => (
              <div key={index} className="flex justify-between items-center bg-gray-700 px-3 py-2 rounded-md">
                <span className="text-sm text-gray-300">{feature}</span>
                <button
                  type="button"
                  onClick={() => removeItem('features', index)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <FiX className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Amenities Section */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Amenities</h2>
        
        <div className="mb-4">
          <div className="flex space-x-2">
            <input
              type="text"
              value={newAmenity}
              onChange={(e) => setNewAmenity(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
              placeholder="Add an amenity e.g. Infinity Pool, Business Center"
            />
            <button
              type="button"
              onClick={() => addItem('amenities', newAmenity)}
              className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
            >
              <FiPlus className="h-4 w-4" />
            </button>
          </div>
        </div>
        
        {project.amenities.length === 0 ? (
          <p className="text-sm text-gray-400 italic">No amenities added yet. Add amenities that will enhance the residents' lifestyle.</p>
        ) : (
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-3 lg:grid-cols-4">
            {project.amenities.map((amenity: string, index: number) => (
              <div key={index} className="flex justify-between items-center bg-gray-700 px-3 py-2 rounded-md">
                <span className="text-sm text-gray-300">{amenity}</span>
                <button
                  type="button"
                  onClick={() => removeItem('amenities', index)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <FiX className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Investment Details */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Investment Details</h2>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
          <div>
            <label htmlFor="investment.priceRange" className="block text-sm font-medium text-gray-300">
              Price Range
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="investment.priceRange"
                id="investment.priceRange"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.investment.priceRange}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="investment.rentalYield" className="block text-sm font-medium text-gray-300">
              Rental Yield
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="investment.rentalYield"
                id="investment.rentalYield"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.investment.rentalYield}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="investment.completionDate" className="block text-sm font-medium text-gray-300">
              Completion Date
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="investment.completionDate"
                id="investment.completionDate"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.investment.completionDate}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="investment.paymentPlan" className="block text-sm font-medium text-gray-300">
              Payment Plan
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="investment.paymentPlan"
                id="investment.paymentPlan"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.investment.paymentPlan}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Investment Benefits Section */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Investment Benefits</h2>
        
        <div className="mb-4 grid grid-cols-1 gap-y-4">
          <div>
            <label htmlFor="benefit-title" className="block text-sm font-medium text-gray-300">
              Benefit Title
            </label>
            <div className="mt-1">
              <input
                type="text"
                id="benefit-title"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newInvestmentBenefit.title}
                onChange={(e) => setNewInvestmentBenefit({ ...newInvestmentBenefit, title: e.target.value })}
                placeholder="e.g. Strong Rental Yield"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="benefit-description" className="block text-sm font-medium text-gray-300">
              Description
            </label>
            <div className="mt-1">
              <textarea
                id="benefit-description"
                rows={2}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newInvestmentBenefit.description}
                onChange={(e) => setNewInvestmentBenefit({ ...newInvestmentBenefit, description: e.target.value })}
                placeholder="Describe the benefit in detail"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="benefit-icon" className="block text-sm font-medium text-gray-300">
              Icon
            </label>
            <div className="mt-1">
              <select
                id="benefit-icon"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newInvestmentBenefit.icon}
                onChange={(e) => setNewInvestmentBenefit({ ...newInvestmentBenefit, icon: e.target.value })}
              >
                <option value="">Select an icon</option>
                <option value="chart">Chart</option>
                <option value="trending-up">Trending Up</option>
                <option value="dollar">Dollar</option>
                <option value="shield">Shield</option>
                <option value="star">Star</option>
                <option value="check">Check</option>
              </select>
            </div>
          </div>
          
          <div>
            <button
              type="button"
              onClick={() => {
                if (newInvestmentBenefit.title && newInvestmentBenefit.description) {
                  addItem('investmentBenefits', JSON.stringify(newInvestmentBenefit));
                }
              }}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] w-full"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Investment Benefit
            </button>
          </div>
        </div>
        
        {project.investmentBenefits.length === 0 ? (
          <p className="text-sm text-gray-400 italic">No investment benefits added yet. Highlight the key benefits of this investment.</p>
        ) : (
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            {project.investmentBenefits.map((benefit: any, index: number) => {
              const benefitData = typeof benefit === 'string' ? JSON.parse(benefit) : benefit;
              return (
                <div key={index} className="bg-gray-700 p-4 rounded-md flex justify-between">
                  <div>
                    <h3 className="text-white font-medium">{benefitData.title}</h3>
                    <p className="text-sm text-gray-300 mt-1">{benefitData.description}</p>
                    <p className="text-xs text-gray-400 mt-1">Icon: {benefitData.icon}</p>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeItem('investmentBenefits', index)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <FiX className="h-4 w-4" />
                  </button>
                </div>
              );
            })}
          </div>
        )}
      </div>
      
      {/* Available Property Types */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Available Property Types</h2>
        
        <div className="mb-4 grid grid-cols-1 gap-y-4 sm:grid-cols-2 gap-x-4">
          <div>
            <label htmlFor="property-type" className="block text-sm font-medium text-gray-300">
              Property Type
            </label>
            <div className="mt-1">
              <input
                type="text"
                id="property-type"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newPropertyType.type}
                onChange={(e) => setNewPropertyType({ ...newPropertyType, type: e.target.value })}
                placeholder="e.g. Studio, 1 Bedroom, etc."
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="property-size" className="block text-sm font-medium text-gray-300">
              Size Range
            </label>
            <div className="mt-1">
              <input
                type="text"
                id="property-size"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newPropertyType.size}
                onChange={(e) => setNewPropertyType({ ...newPropertyType, size: e.target.value })}
                placeholder="e.g. 450 - 550 sq.ft"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="property-price" className="block text-sm font-medium text-gray-300">
              Starting Price
            </label>
            <div className="mt-1">
              <input
                type="text"
                id="property-price"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newPropertyType.startingPrice}
                onChange={(e) => setNewPropertyType({ ...newPropertyType, startingPrice: e.target.value })}
                placeholder="e.g. $500,000"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="property-availability" className="block text-sm font-medium text-gray-300">
              Availability
            </label>
            <div className="mt-1">
              <select
                id="property-availability"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={newPropertyType.availability}
                onChange={(e) => setNewPropertyType({ ...newPropertyType, availability: e.target.value })}
              >
                <option value="Available">Available</option>
                <option value="Limited">Limited</option>
                <option value="Sold Out">Sold Out</option>
                <option value="On Request">On Request</option>
              </select>
            </div>
          </div>
          
          <div className="sm:col-span-2">
            <button
              type="button"
              onClick={() => {
                if (newPropertyType.type && newPropertyType.size && newPropertyType.startingPrice) {
                  addItem('availablePropertyTypes', JSON.stringify(newPropertyType));
                }
              }}
              className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] w-full"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Property Type
            </button>
          </div>
        </div>
        
        {project.availablePropertyTypes.length === 0 ? (
          <p className="text-sm text-gray-400 italic">No property types added yet. List all available property types.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-600">
              <thead>
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Type</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Size</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Starting Price</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Availability</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-gray-700 divide-y divide-gray-600">
                {project.availablePropertyTypes.map((propertyType: any, index: number) => {
                  const propertyData = typeof propertyType === 'string' ? JSON.parse(propertyType) : propertyType;
                  return (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{propertyData.type}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{propertyData.size}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{propertyData.startingPrice}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{propertyData.availability}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          type="button"
                          onClick={() => removeItem('availablePropertyTypes', index)}
                          className="text-red-400 hover:text-red-500"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Construction Progress */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Construction Progress</h2>
        
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
          <div>
            <label htmlFor="projectHighlights.stories" className="block text-sm font-medium text-gray-300">
              Stories
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="projectHighlights.stories"
                id="projectHighlights.stories"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.projectHighlights.stories}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="projectHighlights.luxuryUnits" className="block text-sm font-medium text-gray-300">
              Luxury Units
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="projectHighlights.luxuryUnits"
                id="projectHighlights.luxuryUnits"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.projectHighlights.luxuryUnits}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="projectHighlights.completionYear" className="block text-sm font-medium text-gray-300">
              Completion Year
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="projectHighlights.completionYear"
                id="projectHighlights.completionYear"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.projectHighlights?.completionYear || ''}
                onChange={handleInputChange}
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="projectHighlights.avgROI" className="block text-sm font-medium text-gray-300">
              Average ROI
            </label>
            <div className="mt-1">
              <input
                type="text"
                name="projectHighlights.avgROI"
                id="projectHighlights.avgROI"
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                value={project.projectHighlights?.avgROI || ''}
                onChange={handleInputChange}
                placeholder="e.g. 8%"
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Units Available */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Units Availability</h2>
        
        <div>
          <label htmlFor="unitsAvailable" className="block text-sm font-medium text-gray-300">
            Number of Units Available
          </label>
          <div className="mt-1">
            <input
              type="number"
              name="unitsAvailable"
              id="unitsAvailable"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
              value={project.unitsAvailable || ''}
              onChange={handleInputChange}
            />
          </div>
        </div>
      </div>

      {/* Theme Settings */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Theme Settings</h2>
        
        <div>
          <label htmlFor="themeColor" className="block text-sm font-medium text-gray-300">
            Primary Theme Color
          </label>
          <div className="mt-1 flex items-center space-x-3">
            <input
              type="color"
              name="themeColor"
              id="themeColor"
              className="h-10 w-20 border-0 p-0 bg-transparent"
              value={project.themeColor || '#00C2FF'}
              onChange={handleInputChange}
            />
            <input
              type="text"
              name="themeColor"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
              value={project.themeColor || '#00C2FF'}
              onChange={handleInputChange}
            />
          </div>
          <p className="mt-1 text-xs text-gray-400">Choose a primary color for this project's branding</p>
        </div>
      </div>

      {/* Visual Styling Controls */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h2 className="text-lg font-medium text-gray-300 mb-4">Visual Styling Controls</h2>
        <p className="text-sm text-gray-400 mb-6">Customize the appearance of each section on the project page</p>
        
        {/* Global Styling */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-300 border-b border-gray-700 pb-2 mb-4">Global Styling</h3>
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
            <div>
              <label htmlFor="styling.themeColor" className="block text-sm font-medium text-gray-300">
                Theme Color
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="color"
                  name="styling.themeColor"
                  id="styling.themeColor"
                  className="h-8 w-16 border-0 p-0 bg-transparent"
                  value={project.styling?.themeColor || '#00C2FF'}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  name="styling.themeColor"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.themeColor || '#00C2FF'}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="styling.fontFamily" className="block text-sm font-medium text-gray-300">
                Font Family
              </label>
              <div className="mt-1">
                <select
                  id="styling.fontFamily"
                  name="styling.fontFamily"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.fontFamily || 'Inter, sans-serif'}
                  onChange={handleInputChange}
                >
                  <option value="Inter, sans-serif">Inter</option>
                  <option value="Roboto, sans-serif">Roboto</option>
                  <option value="Poppins, sans-serif">Poppins</option>
                  <option value="Montserrat, sans-serif">Montserrat</option>
                  <option value="Raleway, sans-serif">Raleway</option>
                  <option value="Playfair Display, serif">Playfair Display</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        
        {/* Navigation Styling */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-300 border-b border-gray-700 pb-2 mb-4">Navigation Styling</h3>
          
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-3">
            <div>
              <label htmlFor="styling.navigationColors.backgroundColor" className="block text-sm font-medium text-gray-300">
                Background Color
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="color"
                  name="styling.navigationColors.backgroundColor"
                  id="styling.navigationColors.backgroundColor"
                  className="h-8 w-16 border-0 p-0 bg-transparent"
                  value={project.styling?.navigationColors?.backgroundColor?.replace('rgba(255,255,255,0.9)', '#FFFFFF') || '#FFFFFF'}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  name="styling.navigationColors.backgroundColor"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.navigationColors?.backgroundColor || 'rgba(255,255,255,0.9)'}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="styling.navigationColors.textColor" className="block text-sm font-medium text-gray-300">
                Text Color
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="color"
                  name="styling.navigationColors.textColor"
                  id="styling.navigationColors.textColor"
                  className="h-8 w-16 border-0 p-0 bg-transparent"
                  value={project.styling?.navigationColors?.textColor || '#333333'}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  name="styling.navigationColors.textColor"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.navigationColors?.textColor || '#333333'}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="styling.navigationColors.activeTextColor" className="block text-sm font-medium text-gray-300">
                Active Text Color
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="color"
                  name="styling.navigationColors.activeTextColor"
                  id="styling.navigationColors.activeTextColor"
                  className="h-8 w-16 border-0 p-0 bg-transparent"
                  value={project.styling?.navigationColors?.activeTextColor || '#00C2FF'}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  name="styling.navigationColors.activeTextColor"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.navigationColors?.activeTextColor || '#00C2FF'}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Hero Section Styling */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-300 border-b border-gray-700 pb-2 mb-4">Hero Section Styling</h3>
          
          <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
            <div>
              <label htmlFor="styling.heroSection.textColor" className="block text-sm font-medium text-gray-300">
                Text Color
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="color"
                  name="styling.heroSection.textColor"
                  id="styling.heroSection.textColor"
                  className="h-8 w-16 border-0 p-0 bg-transparent"
                  value={project.styling?.heroSection?.textColor || '#FFFFFF'}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  name="styling.heroSection.textColor"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.heroSection?.textColor || '#FFFFFF'}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="styling.heroSection.overlayColor" className="block text-sm font-medium text-gray-300">
                Overlay Color
              </label>
              <div className="mt-1 flex items-center space-x-3">
                <input
                  type="color"
                  name="styling.heroSection.overlayColor"
                  id="styling.heroSection.overlayColor"
                  className="h-8 w-16 border-0 p-0 bg-transparent"
                  value={project.styling?.heroSection?.overlayColor || '#000000'}
                  onChange={handleInputChange}
                />
                <input
                  type="text"
                  name="styling.heroSection.overlayColor"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.heroSection?.overlayColor || '#000000'}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="styling.heroSection.overlayOpacity" className="block text-sm font-medium text-gray-300">
                Overlay Opacity
              </label>
              <div className="mt-1">
                <input
                  type="range"
                  name="styling.heroSection.overlayOpacity"
                  id="styling.heroSection.overlayOpacity"
                  min="0"
                  max="1"
                  step="0.1"
                  className="w-full"
                  value={project.styling?.heroSection?.overlayOpacity || 0.5}
                  onChange={handleInputChange}
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>Transparent</span>
                  <span>Solid</span>
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="styling.heroSection.backgroundPosition" className="block text-sm font-medium text-gray-300">
                Background Position
              </label>
              <div className="mt-1">
                <select
                  name="styling.heroSection.backgroundPosition"
                  id="styling.heroSection.backgroundPosition"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                  value={project.styling?.heroSection?.backgroundPosition || 'center'}
                  onChange={handleInputChange}
                >
                  <option value="center">Center</option>
                  <option value="top">Top</option>
                  <option value="bottom">Bottom</option>
                  <option value="left">Left</option>
                  <option value="right">Right</option>
                  <option value="top left">Top Left</option>
                  <option value="top right">Top Right</option>
                  <option value="bottom left">Bottom Left</option>
                  <option value="bottom right">Bottom Right</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Advanced Styling - Collapsed by Default */}
        <div className="mt-8">
          <details className="border border-gray-700 rounded-md">
            <summary className="px-4 py-3 cursor-pointer font-medium text-gray-300 hover:bg-gray-700">
              Advanced Section Styling (Click to expand)
            </summary>
            <div className="px-4 py-3 border-t border-gray-700">
              <p className="text-sm text-gray-400 mb-4">Customize each section's appearance with specific colors and backgrounds</p>
              
              {/* We'll add the per-section styling controls in separate edits */}
              
              {/* Overview Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Overview Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.overviewSection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.overviewSection.backgroundColor"
                        id="styling.overviewSection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.overviewSection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.overviewSection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.overviewSection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.overviewSection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.overviewSection.textColor"
                        id="styling.overviewSection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.overviewSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.overviewSection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.overviewSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.overviewSection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.overviewSection.headingColor"
                        id="styling.overviewSection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.overviewSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.overviewSection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.overviewSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.overviewSection.backgroundImage" className="block text-sm font-medium text-gray-300">
                      Background Image URL
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="styling.overviewSection.backgroundImage"
                        id="styling.overviewSection.backgroundImage"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.overviewSection?.backgroundImage || ''}
                        onChange={handleInputChange}
                        placeholder="/images/background.jpg"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Gallery Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Gallery Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.gallerySection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.gallerySection.backgroundColor"
                        id="styling.gallerySection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.gallerySection?.backgroundColor || '#F9FAFB'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.gallerySection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.gallerySection?.backgroundColor || '#F9FAFB'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.gallerySection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.gallerySection.textColor"
                        id="styling.gallerySection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.gallerySection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.gallerySection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.gallerySection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.gallerySection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.gallerySection.headingColor"
                        id="styling.gallerySection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.gallerySection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.gallerySection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.gallerySection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Amenities Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Amenities Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.amenitiesSection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.amenitiesSection.backgroundColor"
                        id="styling.amenitiesSection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.amenitiesSection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.amenitiesSection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.amenitiesSection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.amenitiesSection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.amenitiesSection.textColor"
                        id="styling.amenitiesSection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.amenitiesSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.amenitiesSection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.amenitiesSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.amenitiesSection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.amenitiesSection.headingColor"
                        id="styling.amenitiesSection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.amenitiesSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.amenitiesSection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.amenitiesSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.amenitiesSection.itemBackgroundColor" className="block text-sm font-medium text-gray-300">
                      Item Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.amenitiesSection.itemBackgroundColor"
                        id="styling.amenitiesSection.itemBackgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.amenitiesSection?.itemBackgroundColor || '#F3F4F6'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.amenitiesSection.itemBackgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.amenitiesSection?.itemBackgroundColor || '#F3F4F6'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.amenitiesSection.itemTextColor" className="block text-sm font-medium text-gray-300">
                      Item Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.amenitiesSection.itemTextColor"
                        id="styling.amenitiesSection.itemTextColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.amenitiesSection?.itemTextColor || '#111827'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.amenitiesSection.itemTextColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.amenitiesSection?.itemTextColor || '#111827'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label htmlFor="styling.amenitiesSection.backgroundImage" className="block text-sm font-medium text-gray-300">
                      Background Image URL
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="styling.amenitiesSection.backgroundImage"
                        id="styling.amenitiesSection.backgroundImage"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.amenitiesSection?.backgroundImage || ''}
                        onChange={handleInputChange}
                        placeholder="/images/amenities-background.jpg"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Location Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Location Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.locationSection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.locationSection.backgroundColor"
                        id="styling.locationSection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.locationSection?.backgroundColor || '#F9FAFB'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.locationSection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.locationSection?.backgroundColor || '#F9FAFB'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.locationSection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.locationSection.textColor"
                        id="styling.locationSection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.locationSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.locationSection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.locationSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.locationSection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.locationSection.headingColor"
                        id="styling.locationSection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.locationSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.locationSection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.locationSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Investment Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Investment Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.investmentSection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.investmentSection.backgroundColor"
                        id="styling.investmentSection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.investmentSection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.investmentSection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.investmentSection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.investmentSection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.investmentSection.textColor"
                        id="styling.investmentSection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.investmentSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.investmentSection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.investmentSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.investmentSection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.investmentSection.headingColor"
                        id="styling.investmentSection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.investmentSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.investmentSection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.investmentSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label htmlFor="styling.investmentSection.backgroundImage" className="block text-sm font-medium text-gray-300">
                      Background Image URL
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="styling.investmentSection.backgroundImage"
                        id="styling.investmentSection.backgroundImage"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.investmentSection?.backgroundImage || ''}
                        onChange={handleInputChange}
                        placeholder="/images/investment-background.jpg"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Progress Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Progress Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.progressSection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.progressSection.backgroundColor"
                        id="styling.progressSection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.progressSection?.backgroundColor || '#F9FAFB'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.progressSection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.progressSection?.backgroundColor || '#F9FAFB'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.progressSection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.progressSection.textColor"
                        id="styling.progressSection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.progressSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.progressSection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.progressSection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.progressSection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.progressSection.headingColor"
                        id="styling.progressSection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.progressSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.progressSection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.progressSection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.progressSection.stageBackgroundColor" className="block text-sm font-medium text-gray-300">
                      Stage Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.progressSection.stageBackgroundColor"
                        id="styling.progressSection.stageBackgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.progressSection?.stageBackgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.progressSection.stageBackgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.progressSection?.stageBackgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.progressSection.stageTextColor" className="block text-sm font-medium text-gray-300">
                      Stage Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.progressSection.stageTextColor"
                        id="styling.progressSection.stageTextColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.progressSection?.stageTextColor || '#111827'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.progressSection.stageTextColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.progressSection?.stageTextColor || '#111827'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Inquiry Section Styling */}
              <div className="p-4 border border-gray-700 rounded-md mb-6">
                <h4 className="text-md font-medium text-gray-300 mb-4">Inquiry Section Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.inquirySection.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.backgroundColor"
                        id="styling.inquirySection.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.backgroundColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.inquirySection.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.textColor"
                        id="styling.inquirySection.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.textColor || '#333333'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.inquirySection.headingColor" className="block text-sm font-medium text-gray-300">
                      Heading Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.headingColor"
                        id="styling.inquirySection.headingColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.headingColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.headingColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.inquirySection.inputBackgroundColor" className="block text-sm font-medium text-gray-300">
                      Input Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.inputBackgroundColor"
                        id="styling.inquirySection.inputBackgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.inputBackgroundColor || '#F3F4F6'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.inputBackgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.inputBackgroundColor || '#F3F4F6'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.inquirySection.inputTextColor" className="block text-sm font-medium text-gray-300">
                      Input Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.inputTextColor"
                        id="styling.inquirySection.inputTextColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.inputTextColor || '#111827'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.inputTextColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.inputTextColor || '#111827'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.inquirySection.buttonColor" className="block text-sm font-medium text-gray-300">
                      Button Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.buttonColor"
                        id="styling.inquirySection.buttonColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.buttonColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.buttonColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.buttonColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.inquirySection.buttonTextColor" className="block text-sm font-medium text-gray-300">
                      Button Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.inquirySection.buttonTextColor"
                        id="styling.inquirySection.buttonTextColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.inquirySection?.buttonTextColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.inquirySection.buttonTextColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.buttonTextColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label htmlFor="styling.inquirySection.backgroundImage" className="block text-sm font-medium text-gray-300">
                      Background Image URL
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="styling.inquirySection.backgroundImage"
                        id="styling.inquirySection.backgroundImage"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.inquirySection?.backgroundImage || ''}
                        onChange={handleInputChange}
                        placeholder="/images/inquiry-background.jpg"
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Footer CTA Styling */}
              <div className="p-4 border border-gray-700 rounded-md">
                <h4 className="text-md font-medium text-gray-300 mb-4">Footer CTA Styling</h4>
                
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                  <div>
                    <label htmlFor="styling.footerCTA.backgroundColor" className="block text-sm font-medium text-gray-300">
                      Background Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.footerCTA.backgroundColor"
                        id="styling.footerCTA.backgroundColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.footerCTA?.backgroundColor || '#0D1526'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.footerCTA.backgroundColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.footerCTA?.backgroundColor || '#0D1526'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.footerCTA.textColor" className="block text-sm font-medium text-gray-300">
                      Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.footerCTA.textColor"
                        id="styling.footerCTA.textColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.footerCTA?.textColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.footerCTA.textColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.footerCTA?.textColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.footerCTA.buttonColor" className="block text-sm font-medium text-gray-300">
                      Button Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.footerCTA.buttonColor"
                        id="styling.footerCTA.buttonColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.footerCTA?.buttonColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.footerCTA.buttonColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.footerCTA?.buttonColor || '#00C2FF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="styling.footerCTA.buttonTextColor" className="block text-sm font-medium text-gray-300">
                      Button Text Color
                    </label>
                    <div className="mt-1 flex items-center space-x-3">
                      <input
                        type="color"
                        name="styling.footerCTA.buttonTextColor"
                        id="styling.footerCTA.buttonTextColor"
                        className="h-8 w-16 border-0 p-0 bg-transparent"
                        value={project.styling?.footerCTA?.buttonTextColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                      <input
                        type="text"
                        name="styling.footerCTA.buttonTextColor"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.footerCTA?.buttonTextColor || '#FFFFFF'}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  
                  <div className="sm:col-span-2">
                    <label htmlFor="styling.footerCTA.backgroundImage" className="block text-sm font-medium text-gray-300">
                      Background Image URL
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="styling.footerCTA.backgroundImage"
                        id="styling.footerCTA.backgroundImage"
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white"
                        value={project.styling?.footerCTA?.backgroundImage || ''}
                        onChange={handleInputChange}
                        placeholder="/images/cta-background.jpg"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </details>
        </div>
      </div>

      {/* Save Button */}
      <div className="mt-6 flex justify-end">
        <button
          type="button"
          className="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          onClick={handleSave}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Project'}
        </button>
      </div>
    </div>
  );
} 