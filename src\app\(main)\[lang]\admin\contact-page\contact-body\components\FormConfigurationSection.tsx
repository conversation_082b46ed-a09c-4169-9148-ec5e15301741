"use client";

import { FiSave } from 'react-icons/fi';

interface FormField {
  id: string;
  name: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select';
  label: {
    english: string;
    arabic: string;
  };
  placeholder: {
    english: string;
    arabic: string;
  };
  required: boolean;
  enabled: boolean;
  options?: {
    english: string[];
    arabic: string[];
  };
  icon?: string;
}

interface FormSection {
  title: {
    english: string;
    arabic: string;
  };
  description: {
    english: string;
    arabic: string;
  };
  buttonText: {
    english: string;
    arabic: string;
  };
  privacyPolicy: {
    text: {
      english: string;
      arabic: string;
    };
    links: {
      privacyPolicyUrl: string;
      termsOfServiceUrl: string;
      privacyPolicyText: {
        english: string;
        arabic: string;
      };
      termsOfServiceText: {
        english: string;
        arabic: string;
      };
    };
  };
  fields: FormField[];
}

interface FormConfigurationSectionProps {
  formSection: FormSection;
  onUpdate: (section: 'formSection', subsection: string, field: string, value: string | boolean, language?: 'english' | 'arabic', subfield?: string) => void;
}

export default function FormConfigurationSection({ formSection, onUpdate }: FormConfigurationSectionProps) {
  return (
    <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
      <h3 className="text-lg font-medium text-white mb-4 flex items-center">
        <div className="w-6 h-6 bg-blue-500 rounded mr-2 flex items-center justify-center">
          <span className="text-white text-xs font-bold">1</span>
        </div>
        Contact Form Configuration
      </h3>
      <p className="text-gray-400 text-sm mb-6">Configure the main contact form section including title, description, and form behavior</p>
      
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* English Form Section */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-blue-400">English Content</h4>
          <div>
            <label className="block text-sm font-medium text-gray-300">Form Title</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.title.english}
              onChange={e => onUpdate('formSection', 'title', 'english', e.target.value, 'english')}
              placeholder="Get in Touch"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Form Description</label>
            <textarea
              rows={3}
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.description.english}
              onChange={e => onUpdate('formSection', 'description', 'english', e.target.value, 'english')}
              placeholder="Form description..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Submit Button Text</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.buttonText.english}
              onChange={e => onUpdate('formSection', 'buttonText', 'english', e.target.value, 'english')}
              placeholder="Send Message"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Privacy Policy Text (before links)</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.privacyPolicy.text.english}
              onChange={e => onUpdate('formSection', 'privacyPolicy', 'text', e.target.value, 'english')}
              placeholder="By submitting this form, you agree to our"
            />
          </div>
        </div>

        {/* Arabic Form Section */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-blue-400">المحتوى العربي</h4>
          <div>
            <label className="block text-sm font-medium text-gray-300">عنوان النموذج</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.title.arabic}
              onChange={e => onUpdate('formSection', 'title', 'arabic', e.target.value, 'arabic')}
              placeholder="تواصل معنا"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">وصف النموذج</label>
            <textarea
              rows={3}
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.description.arabic}
              onChange={e => onUpdate('formSection', 'description', 'arabic', e.target.value, 'arabic')}
              placeholder="وصف النموذج..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">نص زر الإرسال</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.buttonText.arabic}
              onChange={e => onUpdate('formSection', 'buttonText', 'arabic', e.target.value, 'arabic')}
              placeholder="إرسال الرسالة"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">نص سياسة الخصوصية (قبل الروابط)</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.privacyPolicy.text.arabic}
              onChange={e => onUpdate('formSection', 'privacyPolicy', 'text', e.target.value, 'arabic')}
              placeholder="بإرسال هذا النموذج، فإنك توافق على"
            />
          </div>
        </div>
      </div>

      {/* Privacy Policy Links */}
      <div className="mt-6 pt-6 border-t border-gray-600">
        <h4 className="text-md font-medium text-gray-300 mb-4">Privacy Policy & Terms Links</h4>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <label className="block text-sm font-medium text-gray-300">Privacy Policy URL</label>
            <input
              type="url"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.privacyPolicy.links.privacyPolicyUrl}
              onChange={e => onUpdate('formSection', 'privacyPolicy', 'links', e.target.value, undefined, 'privacyPolicyUrl')}
              placeholder="/privacy-policy"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Terms of Service URL</label>
            <input
              type="url"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={formSection.privacyPolicy.links.termsOfServiceUrl}
              onChange={e => onUpdate('formSection', 'privacyPolicy', 'links', e.target.value, undefined, 'termsOfServiceUrl')}
              placeholder="/terms-of-service"
            />
          </div>
        </div>
        
        {/* Link Text Controls */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 mt-6">
          <div className="space-y-4">
            <h5 className="text-sm font-medium text-blue-400">English Link Text</h5>
            <div>
              <label className="block text-sm font-medium text-gray-300">Privacy Policy Link Text</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={formSection.privacyPolicy.links.privacyPolicyText.english}
                onChange={e => onUpdate('formSection', 'privacyPolicy', 'links', e.target.value, 'english', 'privacyPolicyText')}
                placeholder="Privacy Policy"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Terms of Service Link Text</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={formSection.privacyPolicy.links.termsOfServiceText.english}
                onChange={e => onUpdate('formSection', 'privacyPolicy', 'links', e.target.value, 'english', 'termsOfServiceText')}
                placeholder="Terms of Service"
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <h5 className="text-sm font-medium text-blue-400">نص الروابط العربية</h5>
            <div>
              <label className="block text-sm font-medium text-gray-300">نص رابط سياسة الخصوصية</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={formSection.privacyPolicy.links.privacyPolicyText.arabic}
                onChange={e => onUpdate('formSection', 'privacyPolicy', 'links', e.target.value, 'arabic', 'privacyPolicyText')}
                placeholder="سياسة الخصوصية"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">نص رابط شروط الخدمة</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={formSection.privacyPolicy.links.termsOfServiceText.arabic}
                onChange={e => onUpdate('formSection', 'privacyPolicy', 'links', e.target.value, 'arabic', 'termsOfServiceText')}
                placeholder="شروط الخدمة"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 