import { forwardRef, useRef, useEffect, useState } from "react";

interface ProjectInvestmentProps {
  project: any;
}

const ProjectInvestment = forwardRef<HTMLElement, ProjectInvestmentProps>(({ project }, ref) => {
  const tableScrollRef = useRef<HTMLDivElement>(null);
  const [isTableScrollable, setIsTableScrollable] = useState(false);
  const [hasTableScrolled, setHasTableScrolled] = useState(false);

  // Check if the property types table is scrollable
  useEffect(() => {
    const checkScroll = () => {
      const container = tableScrollRef.current;
      if (container) {
        setIsTableScrollable(container.scrollWidth > container.clientWidth);
      }
    };

    // Check on initial load and window resize
    checkScroll();
    window.addEventListener('resize', checkScroll);
    
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  // Add scroll listener to hide indicator after user scrolls
  useEffect(() => {
    const container = tableScrollRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (!hasTableScrolled) {
        setHasTableScrolled(true);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasTableScrolled]);

  return (
    <section ref={ref} id="investment" className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Investment Opportunity</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">A premium investment with strong potential for capital appreciation and rental yields.</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        <div className="lg:col-span-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Invest in {project.title}?</h3>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-8 h-8 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-4 flex-shrink-0">
                  <svg className="h-4 w-4 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">Strong Rental Yield</h4>
                  <p className="text-gray-700">With an expected rental yield of {project.investment.rentalYield}, this investment offers attractive returns compared to other real estate options in the area.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-4 flex-shrink-0">
                  <svg className="h-4 w-4 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">Capital Appreciation</h4>
                  <p className="text-gray-700">Downtown Dubai continues to be one of the most sought-after areas, ensuring strong potential for capital appreciation over time.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-4 flex-shrink-0">
                  <svg className="h-4 w-4 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">Flexible Payment Plan</h4>
                  <p className="text-gray-700">Our attractive payment plan ({project.investment.paymentPlan}) makes this investment accessible and manageable for investors.</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-8 h-8 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-4 flex-shrink-0">
                  <svg className="h-4 w-4 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">Low Risk Investment</h4>
                  <p className="text-gray-700">Mazaya Capital's proven track record and the prime location of this development minimize investment risk while maximizing potential returns.</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Property Types Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Available Property Types</h3>
            <div className="relative">
              <div 
                ref={tableScrollRef}
                className="overflow-x-auto"
                style={{ scrollbarWidth: 'thin' }}
              >
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-4 py-3 text-start text-gray-600 font-medium whitespace-nowrap">Type</th>
                      <th className="px-4 py-3 text-start text-gray-600 font-medium whitespace-nowrap">Size</th>
                      <th className="px-4 py-3 text-start text-gray-600 font-medium whitespace-nowrap">Starting Price</th>
                      <th className="px-4 py-3 text-start text-gray-600 font-medium whitespace-nowrap">Availability</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    <tr>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">Studio</td>
                      <td className="px-4 py-4 text-gray-700 whitespace-nowrap">450 - 550 sq.ft</td>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">{project.investment.startingPrices["Studio"]}</td>
                      <td className="px-4 py-4 whitespace-nowrap"><span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Available</span></td>
                    </tr>
                    <tr>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">1 Bedroom</td>
                      <td className="px-4 py-4 text-gray-700 whitespace-nowrap">750 - 850 sq.ft</td>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">{project.investment.startingPrices["1 Bedroom"]}</td>
                      <td className="px-4 py-4 whitespace-nowrap"><span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Available</span></td>
                    </tr>
                    <tr>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">2 Bedroom</td>
                      <td className="px-4 py-4 text-gray-700 whitespace-nowrap">1,100 - 1,300 sq.ft</td>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">{project.investment.startingPrices["2 Bedroom"]}</td>
                      <td className="px-4 py-4 whitespace-nowrap"><span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Limited</span></td>
                    </tr>
                    <tr>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">3 Bedroom</td>
                      <td className="px-4 py-4 text-gray-700 whitespace-nowrap">1,600 - 1,900 sq.ft</td>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">{project.investment.startingPrices["3 Bedroom"]}</td>
                      <td className="px-4 py-4 whitespace-nowrap"><span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Limited</span></td>
                    </tr>
                    <tr>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">Penthouse</td>
                      <td className="px-4 py-4 text-gray-700 whitespace-nowrap">3,000+ sq.ft</td>
                      <td className="px-4 py-4 text-gray-900 font-medium whitespace-nowrap">{project.investment.startingPrices["Penthouse"]}</td>
                      <td className="px-4 py-4 whitespace-nowrap"><span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">On Request</span></td>
                    </tr>
                  </tbody>
                </table>
              </div>
              
              {/* Scroll indicator - only shows when scrollable and user hasn't scrolled yet */}
              {isTableScrollable && !hasTableScrolled && (
                <div className="absolute top-0 end-0 bottom-0 pointer-events-none flex items-center">
                  <div className="h-full w-12 bg-gradient-to-s from-[#f9fafb] to-transparent"></div>
                  <div className="me-2 w-6 h-6 rounded-full bg-[#00C2FF]/10 flex items-center justify-center animate-pulse">
                    <svg className="h-4 w-4 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="lg:col-span-4">
          {/* Sidebar Cards */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Investment Overview</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Price Range</span>
                <span className="text-gray-900 font-medium">{project.investment.priceRange}</span>
              </div>
              <div className="h-px bg-gray-100"></div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Expected Rental Yield</span>
                <span className="text-gray-900 font-medium">{project.investment.rentalYield}</span>
              </div>
              <div className="h-px bg-gray-100"></div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Completion Date</span>
                <span className="text-gray-900 font-medium">{project.investment.completionDate}</span>
              </div>
              <div className="h-px bg-gray-100"></div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Plan</span>
                <span className="text-gray-900 font-medium">{project.investment.paymentPlan.split(',')[0]}</span>
              </div>
            </div>
          </div>
          
          {/* Request Information */}
          <div className="bg-gradient-to-br from-[#0D1526] to-[#232F3E] rounded-xl shadow-md p-6 text-white">
            <h3 className="text-xl font-bold mb-4">Interested in {project.title}?</h3>
            <p className="text-white/80 mb-6">Contact our sales team for more information about this exclusive investment opportunity.</p>
            
            <form className="space-y-4">
              <div>
                <input type="text" placeholder="Full Name" className="w-full bg-white/10 rounded-lg border border-white/20 px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#00C2FF]/50" />
              </div>
              <div>
                <input type="email" placeholder="Email Address" className="w-full bg-white/10 rounded-lg border border-white/20 px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#00C2FF]/50" />
              </div>
              <div>
                <input type="tel" placeholder="Phone Number" className="w-full bg-white/10 rounded-lg border border-white/20 px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#00C2FF]/50" />
              </div>
              <button className="w-full bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                Request Information
              </button>
            </form>
            
            <div className="mt-6 text-center text-white/50 text-sm">
              Or call us directly at <span className="text-white font-medium">+971 4 123 4567</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

ProjectInvestment.displayName = "ProjectInvestment";

export default ProjectInvestment; 