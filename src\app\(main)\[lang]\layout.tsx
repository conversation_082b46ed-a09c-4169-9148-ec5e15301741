"use client";

import { useEffect } from "react";
import { use } from "react";
import { LangParams } from "./params";
import { useLanguage } from "@/contexts/LanguageContext";

export default function LangLayout({ 
  children, 
  params
}: {
  children: React.ReactNode;
  params: LangParams["params"];
}) {
  // Unwrap the params Promise
  const resolvedParams = use(params as unknown as Promise<LangParams["params"]>);
  const { changeLanguage } = useLanguage();
  
  // Update the language when the route changes
  useEffect(() => {
    if (resolvedParams.lang) {
      changeLanguage(resolvedParams.lang);
    }
  }, [resolvedParams.lang, changeLanguage]);

  return <>{children}</>;
} 