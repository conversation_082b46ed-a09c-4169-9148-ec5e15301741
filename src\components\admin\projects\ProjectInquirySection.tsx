import React, { useState, useEffect } from 'react';
import { FiPlus, FiX, FiMove } from 'react-icons/fi';

interface ProjectInquirySectionProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectInquirySection: React.FC<ProjectInquirySectionProps> = ({
  project,
  language,
  setProject
}) => {
  // Get current section info based on selected language
  const currentInquirySection = project.localizedContent?.[language]?.inquirySection || {
    title: '',
    description: '',
    contactInfo: {
      phone: '',
      phoneNote: '',
      email: '',
      emailNote: '',
      address: '',
      addressNote: ''
    },
    whyChoosePoints: []
  };
  
  const currentContactPhone = project.inquirySection?.contactPhone || '';
  const currentContactEmail = project.inquirySection?.contactEmail || '';
  
  // State for the current language's section info
  const [sectionTitle, setSectionTitle] = useState(currentInquirySection.title);
  const [sectionDescription, setSectionDescription] = useState(currentInquirySection.description);
  
  // State for contact information
  const [contactInfo, setContactInfo] = useState(currentInquirySection.contactInfo);
  const [contactPhone, setContactPhone] = useState(currentContactPhone);
  const [contactEmail, setContactEmail] = useState(currentContactEmail);
  
  // State for "Why Choose" points
  const [whyChoosePoints, setWhyChoosePoints] = useState(currentInquirySection.whyChoosePoints || []);
  const [newPoint, setNewPoint] = useState('');
  const [draggedPointIndex, setDraggedPointIndex] = useState<number | null>(null);
  
  // Update the form values when language changes
  useEffect(() => {
    const inquirySection = project.localizedContent?.[language]?.inquirySection || {
      title: '',
      description: '',
      contactInfo: {
        phone: '',
        phoneNote: '',
        email: '',
        emailNote: '',
        address: '',
        addressNote: ''
      },
      whyChoosePoints: []
    };
    
    setSectionTitle(inquirySection.title);
    setSectionDescription(inquirySection.description);
    setContactInfo(inquirySection.contactInfo);
    setWhyChoosePoints(inquirySection.whyChoosePoints || []);
    setContactPhone(project.inquirySection?.contactPhone || '');
    setContactEmail(project.inquirySection?.contactEmail || '');
  }, [language, project.localizedContent, project.inquirySection]);
  
  // Auto-save section info when values change
  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      setProject({
        ...project,
        localizedContent: {
          ...project.localizedContent,
          [language]: {
            ...project.localizedContent?.[language],
            inquirySection: {
              title: sectionTitle,
              description: sectionDescription,
              contactInfo: contactInfo,
              whyChoosePoints: whyChoosePoints
            }
          }
        },
        inquirySection: {
          ...project.inquirySection,
          contactPhone: contactPhone,
          contactEmail: contactEmail
        }
      });
    }, 1000);
    
    return () => clearTimeout(debounceTimeout);
  }, [
    sectionTitle,
    sectionDescription,
    contactInfo,
    whyChoosePoints,
    contactPhone,
    contactEmail,
    language,
    project,
    setProject
  ]);
  
  // Function to add a new "Why Choose" point
  const addWhyChoosePoint = () => {
    if (!newPoint.trim()) return;
    
    setWhyChoosePoints([...whyChoosePoints, newPoint]);
    setNewPoint('');
  };
  
  // Function to remove a "Why Choose" point
  const removeWhyChoosePoint = (index: number) => {
    const updatedPoints = [...whyChoosePoints];
    updatedPoints.splice(index, 1);
    setWhyChoosePoints(updatedPoints);
  };
  
  // Functions for drag and drop reordering
  const handlePointDragStart = (index: number) => {
    setDraggedPointIndex(index);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };
  
  const handlePointDrop = (dropIndex: number) => {
    if (draggedPointIndex === null || draggedPointIndex === dropIndex) return;
    
    const updatedPoints = [...whyChoosePoints];
    const draggedItem = updatedPoints[draggedPointIndex];
    
    // Remove the dragged item
    updatedPoints.splice(draggedPointIndex, 1);
    
    // Add it at the new position
    updatedPoints.splice(dropIndex, 0, draggedItem);
    
    // Update state
    setWhyChoosePoints(updatedPoints);
    setDraggedPointIndex(null);
  };
  
  // Function to update contact info
  const updateContactInfo = (field: string, value: string) => {
    setContactInfo({
      ...contactInfo,
      [field]: value
    });
  };

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Inquiry Section' : 'قسم الاستفسار'}
      </h2>
      
      {/* Section Title and Description */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Title' : 'عنوان القسم'}
            </label>
            <input
              type="text"
              value={sectionTitle}
              onChange={(e) => setSectionTitle(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Inquire About Mazaya Heights" : "استفسر عن مزايا هايتس"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Description' : 'وصف القسم'}
            </label>
            <textarea
              value={sectionDescription}
              onChange={(e) => setSectionDescription(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={3}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "Interested in this property? Complete the form below, and our dedicated sales team will contact you shortly." 
                : "مهتم بهذا العقار؟ أكمل النموذج أدناه، وسيتصل بك فريق المبيعات المخصص لدينا قريباً."}
            />
          </div>
        </div>
      </div>
      
      {/* Contact Information */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Contact Information' : 'معلومات الاتصال'}
        </h3>
        
        <div className="space-y-6">
          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Phone Number' : 'رقم الهاتف'}
            </label>
            <input
              type="text"
              value={contactPhone}
              onChange={(e) => setContactPhone(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="+971 4 123 4567"
            />
            <p className="mt-1 text-sm text-gray-400">
              {language === 'en' 
                ? 'This phone number will be displayed in both English and Arabic interfaces' 
                : 'سيتم عرض رقم الهاتف هذا في واجهات اللغة الإنجليزية والعربية'}
            </p>
            
            <div className="mt-2">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Phone Note' : 'ملاحظة الهاتف'}
              </label>
              <input
                type="text"
                value={contactInfo.phoneNote}
                onChange={(e) => updateContactInfo('phoneNote', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={language === 'en' ? "Available 7 days a week, 9am - 8pm" : "متاح 7 أيام في الأسبوع، 9 صباحًا - 8 مساءً"}
              />
            </div>
          </div>
          
          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Email Address' : 'البريد الإلكتروني'}
            </label>
            <input
              type="text"
              value={contactEmail}
              onChange={(e) => setContactEmail(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="<EMAIL>"
            />
            <p className="mt-1 text-sm text-gray-400">
              {language === 'en' 
                ? 'This email will be displayed in both English and Arabic interfaces' 
                : 'سيتم عرض هذا البريد الإلكتروني في واجهات اللغة الإنجليزية والعربية'}
            </p>
            
            <div className="mt-2">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Email Note' : 'ملاحظة البريد الإلكتروني'}
              </label>
              <input
                type="text"
                value={contactInfo.emailNote}
                onChange={(e) => updateContactInfo('emailNote', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={language === 'en' ? "We'll respond within 24 hours" : "سنرد خلال 24 ساعة"}
              />
            </div>
          </div>
          
          {/* Address */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Sales Center Address' : 'عنوان مركز المبيعات'}
            </label>
            <input
              type="text"
              value={contactInfo.address}
              onChange={(e) => updateContactInfo('address', e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Sheikh Mohammed bin Rashid Blvd, Downtown Dubai" : "شارع الشيخ محمد بن راشد، وسط مدينة دبي"}
            />
            
            <div className="mt-2">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Address Note' : 'ملاحظة العنوان'}
              </label>
              <input
                type="text"
                value={contactInfo.addressNote}
                onChange={(e) => updateContactInfo('addressNote', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={language === 'en' ? "Open daily from 10am - 7pm" : "مفتوح يوميًا من 10 صباحًا - 7 مساءً"}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Why Choose Points */}
      <div className="mb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Why Choose Points' : 'نقاط لماذا تختار'}
        </h3>
        
        <div className="space-y-4 mb-4">
          <div className="flex items-center space-x-2 gap-4">
            <input
              type="text"
              value={newPoint}
              onChange={(e) => setNewPoint(e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] flex-grow sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Add a reason..." : "أضف سببًا..."}
            />
            <button
              type="button"
              onClick={addWhyChoosePoint}
              disabled={!newPoint.trim()}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                newPoint.trim() ? 'bg-[#00C2FF] hover:bg-[#009DB5]' : 'bg-gray-600 cursor-not-allowed'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]`}
            >
              <FiPlus className="-ms-1 me-2 h-4 w-4" />
              {language === 'en' ? 'Add' : 'إضافة'}
            </button>
          </div>
          
          {whyChoosePoints.length > 0 ? (
            <div className="bg-gray-700 rounded-md p-1">
              <ul className="divide-y divide-gray-600">
                {whyChoosePoints.map((point: string, index: number) => (
                  <li 
                    key={index}
                    className={`p-3 rounded-md ${draggedPointIndex === index ? 'opacity-50 bg-gray-600' : 'bg-gray-700'}`}
                    draggable={true}
                    onDragStart={() => handlePointDragStart(index)}
                    onDragOver={handleDragOver}
                    onDrop={() => handlePointDrop(index)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <span className="text-[#00C2FF] mr-3">
                          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </span>
                        <span className="text-white">{point}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {whyChoosePoints.length > 1 && (
                          <button className="text-gray-400 cursor-move">
                            <FiMove className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => removeWhyChoosePoint(index)}
                          className="text-gray-400 hover:text-red-500"
                        >
                          <FiX className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p className="text-sm text-gray-400 italic">
              {language === 'en' 
                ? 'No selling points added yet. Add points to highlight why buyers should choose this property.'
                : 'لم تتم إضافة نقاط بيع بعد. أضف نقاطًا لإبراز سبب اختيار المشترين لهذا العقار.'}
            </p>
          )}
          
          {whyChoosePoints.length > 1 && (
            <p className="mt-2 text-xs text-gray-400">
              {language === 'en' ? 'Drag items to reorder them.' : 'اسحب العناصر لإعادة ترتيبها.'}
            </p>
          )}
        </div>
      </div>
      
      {/* Form Preview Section */}
      <div className="mb-6 border-t border-gray-700 pt-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Inquiry Form Preview' : 'معاينة نموذج الاستفسار'}
        </h3>
        
        <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600">
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-300 mb-1">
                {language === 'en' ? 'The inquiry form includes the following fields:' : 'يتضمن نموذج الاستفسار الحقول التالية:'}
              </p>
              <ul className="text-sm text-gray-400 list-disc list-inside space-y-1 ml-2">
                <li>{language === 'en' ? 'Full Name' : 'الاسم الكامل'}</li>
                <li>{language === 'en' ? 'Email Address' : 'البريد الإلكتروني'}</li>
                <li>{language === 'en' ? 'Phone Number' : 'رقم الهاتف'}</li>
                <li>{language === 'en' ? 'Nationality' : 'الجنسية'}</li>
                <li>{language === 'en' ? 'Interested In (Property Type)' : 'مهتم بـ (نوع العقار)'}</li>
                <li>{language === 'en' ? 'Investment Budget' : 'ميزانية الاستثمار'}</li>
                <li>{language === 'en' ? 'Message' : 'الرسالة'}</li>
                <li>{language === 'en' ? 'Newsletter Subscription' : 'الاشتراك في النشرة الإخبارية'}</li>
              </ul>
            </div>
            
            <div className="bg-gray-700/50 p-4 rounded-md">
              <p className="text-xs text-gray-400">
                {language === 'en' 
                  ? 'Note: The form will be fully functional on the live website. Form submissions will be sent to your configured email address.' 
                  : 'ملاحظة: سيكون النموذج فعالاً بالكامل على الموقع المباشر. سيتم إرسال طلبات النموذج إلى عنوان البريد الإلكتروني الذي قمت بتكوينه.'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectInquirySection; 