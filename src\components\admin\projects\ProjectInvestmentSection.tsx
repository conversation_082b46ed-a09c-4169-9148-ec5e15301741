import React, { useState, useEffect } from 'react';
import { FiPlus, FiX, FiMove, FiSearch, FiEdit } from 'react-icons/fi';
import * as FcIcons from 'react-icons/fc';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';

interface ProjectInvestmentSectionProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectInvestmentSection: React.FC<ProjectInvestmentSectionProps> = ({
  project,
  language,
  setProject
}) => {
  // Get current section info based on selected language
  const currentSectionTitle = project.localizedContent?.[language]?.investmentSection?.title || '';
  const currentSectionDescription = project.localizedContent?.[language]?.investmentSection?.description || '';
  
  // Why Invest Points
  const currentInvestmentPoints = project.localizedContent?.[language]?.investmentSection?.whyInvestPoints || [];
  
  // Property Types
  const currentPropertyTypes = project.localizedContent?.[language]?.investmentSection?.propertyTypes || [];
  
  // Investment Overview
  const currentInvestmentOverview = project.localizedContent?.[language]?.investmentSection?.overview || {
    priceRange: '',
    rentalYield: '',
    completionDate: '',
    paymentPlan: ''
  };
  
  // Contact Form Section
  const currentContactFormTitle = project.localizedContent?.[language]?.investmentSection?.contactForm?.title || '';
  const currentContactFormDescription = project.localizedContent?.[language]?.investmentSection?.contactForm?.description || '';
  const currentContactFormPhone = project.investmentSection?.contactFormPhone || '';
  
  // State for the current language's section info
  const [sectionTitle, setSectionTitle] = useState(currentSectionTitle);
  const [sectionDescription, setSectionDescription] = useState(currentSectionDescription);
  
  // State for why invest points
  const [newInvestmentPoint, setNewInvestmentPoint] = useState({ title: '', description: '', icon: 'FcAddressBook' });
  const [investmentPoints, setInvestmentPoints] = useState(currentInvestmentPoints);
  const [draggedPoint, setDraggedPoint] = useState<number | null>(null);
  const [editingPointIndex, setEditingPointIndex] = useState<number | null>(null);
  const [editingPoint, setEditingPoint] = useState({ title: '', description: '', icon: '' });
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [showAmenityForm, setShowAmenityForm] = useState(false);
  const [selectedIconSet, setSelectedIconSet] = useState<
    'fc' | 'fa' | 'fa6' | 'bs' | 'ri' | 'gi' | 'tb' | 'md' | 'hi' | 'ai' | 'io' | 'io5' | 'pi'
  >('fc');
  
  // State for property types
  const [newPropertyType, setNewPropertyType] = useState({ 
    type: '',
    size: '',
    price: '',
    availability: 'Available'
  });
  const [propertyTypes, setPropertyTypes] = useState(currentPropertyTypes);
  const [draggedPropertyType, setDraggedPropertyType] = useState<number | null>(null);
  
  // State for investment overview
  const [overview, setOverview] = useState(currentInvestmentOverview);
  
  // State for contact form
  const [contactFormTitle, setContactFormTitle] = useState(currentContactFormTitle);
  const [contactFormDescription, setContactFormDescription] = useState(currentContactFormDescription);
  const [contactFormPhone, setContactFormPhone] = useState(currentContactFormPhone);
  
  
  
  // Update the form values when language changes
  useEffect(() => {
    setSectionTitle(project.localizedContent?.[language]?.investmentSection?.title || '');
    setSectionDescription(project.localizedContent?.[language]?.investmentSection?.description || '');
    setInvestmentPoints(project.localizedContent?.[language]?.investmentSection?.whyInvestPoints || []);
    setPropertyTypes(project.localizedContent?.[language]?.investmentSection?.propertyTypes || []);
    setOverview(project.localizedContent?.[language]?.investmentSection?.overview || {
      priceRange: '',
      rentalYield: '',
      completionDate: '',
      paymentPlan: ''
    });
    setContactFormTitle(project.localizedContent?.[language]?.investmentSection?.contactForm?.title || '');
    setContactFormDescription(project.localizedContent?.[language]?.investmentSection?.contactForm?.description || '');
    setContactFormPhone(project.investmentSection?.contactFormPhone || '');
  }, [language, project.localizedContent]);
  
  // Direct handlers for input fields
  const handleSectionTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setSectionTitle(newTitle);
    
    // Immediately update project state
    updateProjectState({
      title: newTitle,
      description: sectionDescription
    });
  };
  
  const handleSectionDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setSectionDescription(newDescription);
    
    // Immediately update project state
    updateProjectState({
      title: sectionTitle,
      description: newDescription
    });
  };
  
  const handleContactFormTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setContactFormTitle(newTitle);
    
    // Immediately update project state
    updateProjectState({
      contactForm: {
        title: newTitle,
        description: contactFormDescription
      }
    });
  };
  
  const handleContactFormDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setContactFormDescription(newDescription);
    
    // Immediately update project state
    updateProjectState({
      contactForm: {
        title: contactFormTitle,
        description: newDescription
      }
    });
  };
  
  const handleContactFormPhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPhone = e.target.value;
    setContactFormPhone(newPhone);
    
    // Immediately update project state
    setProject({
      ...project,
      investmentSection: {
        ...(project.investmentSection || {}),
        contactFormPhone: newPhone
      }
    });
  };
  
  const handleOverviewChange = (field: string, value: string) => {
    const updatedOverview = { ...overview, [field]: value };
    setOverview(updatedOverview);
    
    // Immediately update project state
    updateProjectState({ overview: updatedOverview });
  };
  
  // Helper to update the project state with new investment section values
  const updateProjectState = (newValues: any) => {
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          investmentSection: {
            ...(project.localizedContent?.[language]?.investmentSection || {}),
            ...newValues,
            whyInvestPoints: investmentPoints,
            propertyTypes: propertyTypes,
            ...(newValues.overview ? {} : { overview: overview }),
            ...(newValues.contactForm ? {} : { 
              contactForm: {
                ...(project.localizedContent?.[language]?.investmentSection?.contactForm || {}),
                title: contactFormTitle,
                description: contactFormDescription
              }
            }),
          }
        }
      }
    });
  };
  
  // All available icon libraries
  const iconSets = [
    { id: 'fc', label: 'Flat Color', total: Object.keys(FcIcons).length },
    { id: 'fa', label: 'Font Awesome', total: Object.keys(FaIcons).length },
    { id: 'fa6', label: 'Font Awesome 6', total: Object.keys(FaIconsSolid).length },
    { id: 'bs', label: 'Bootstrap', total: Object.keys(BsIcons).length },
    { id: 'ri', label: 'Remix', total: Object.keys(RiIcons).length },
    { id: 'gi', label: 'Game Icons', total: Object.keys(GiIcons).length },
    { id: 'tb', label: 'Tabler', total: Object.keys(TbIcons).length },
    { id: 'md', label: 'Material Design', total: Object.keys(MdIcons).length },
    { id: 'hi', label: 'Heroicons', total: Object.keys(HiIcons).length },
    { id: 'ai', label: 'Ant Design', total: Object.keys(AiIcons).length },
    { id: 'io', label: 'Ionicons 4', total: Object.keys(IoIcons).length },
    { id: 'io5', label: 'Ionicons 5', total: Object.keys(Io5Icons).length },
    { id: 'pi', label: 'Phosphor', total: Object.keys(PiIcons).length },
  ];
  
  // Function to check if a library has matching icons based on search term
  const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
    if (!searchTerm.trim()) return true;
    
    let library;
    switch (libraryId) {
      case 'fc': library = FcIcons; break;
      case 'fa': library = FaIcons; break;
      case 'fa6': library = FaIconsSolid; break;
      case 'bs': library = BsIcons; break;
      case 'ri': library = RiIcons; break;
      case 'gi': library = GiIcons; break;
      case 'tb': library = TbIcons; break;
      case 'md': library = MdIcons; break;
      case 'hi': library = HiIcons; break;
      case 'ai': library = AiIcons; break;
      case 'io': library = IoIcons; break;
      case 'io5': library = Io5Icons; break;
      case 'pi': library = PiIcons; break;
      default: return false;
    }
    
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).some(iconName =>
      iconName.toLowerCase().includes(normalizedSearch)
    );
  };
  
  // Get icons for the current selected library filtered by search term
  const getFilteredIcons = () => {
    let library;
    switch (selectedIconSet) {
      case 'fc': library = FcIcons; break;
      case 'fa': library = FaIcons; break;
      case 'fa6': library = FaIconsSolid; break;
      case 'bs': library = BsIcons; break;
      case 'ri': library = RiIcons; break;
      case 'gi': library = GiIcons; break;
      case 'tb': library = TbIcons; break;
      case 'md': library = MdIcons; break;
      case 'hi': library = HiIcons; break;
      case 'ai': library = AiIcons; break;
      case 'io': library = IoIcons; break;
      case 'io5': library = Io5Icons; break;
      case 'pi': library = PiIcons; break;
      default: library = FcIcons;
    }
    
    // Filter out non-component entries (avoid empty icons)
    const icons = Object.keys(library).filter(key => {
      const Icon = library[key as keyof typeof library];
      return typeof Icon === 'function';
    });
    
    const trimmedSearchTerm = iconSearchTerm.trim();
    if (!trimmedSearchTerm) {
      return icons.slice(0, 120); // Show more icons by default
    }
    
    const filteredIcons = icons
      .filter(name => name.toLowerCase().includes(trimmedSearchTerm.toLowerCase()))
      .slice(0, 120); // Show more search results
    
    // If we have a search term but no results in the current library,
    // auto-switch to the first library that has results
    if (trimmedSearchTerm && filteredIcons.length === 0) {
      // Find the first library with matching results
      const firstLibWithResults = iconSets.find(set =>
        set.id !== selectedIconSet && hasMatchingIcons(set.id, trimmedSearchTerm)
      );
      
      if (firstLibWithResults) {
        // Use setTimeout to avoid state update during render
        setTimeout(() => {
          setSelectedIconSet(firstLibWithResults.id as any);
        }, 0);
      }
    }
    
    return filteredIcons;
  };
  
  // Render icon component
  const renderIcon = (iconName: string) => {
    if (!iconName) return null;
    
    let library;
    let iconKey = iconName;
    
    if (iconName.startsWith('Fc')) {
      library = FcIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Fa6')) {
      library = FaIconsSolid;
      iconKey = iconName.substring(3);
    }
    else if (iconName.startsWith('Fa')) {
      library = FaIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Bs')) {
      library = BsIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Ri')) {
      library = RiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Gi')) {
      library = GiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Tb')) {
      library = TbIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Md')) {
      library = MdIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Hi')) {
      library = HiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Ai')) {
      library = AiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Io5')) {
      library = Io5Icons;
      iconKey = iconName.substring(3);
    }
    else if (iconName.startsWith('Io')) {
      library = IoIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Pi')) {
      library = PiIcons;
      iconKey = iconName.substring(2);
    }
    else return null;
    
    // Use proper type checking for the icon component
    const IconComponent = library[iconKey as keyof typeof library] as React.ComponentType<any>;
    return IconComponent ? <IconComponent className="text-xl" style={{ display: 'block', fontSize: '1.5rem' }} /> : null;
  };
  
  // Handle selecting an icon
  const selectIcon = (iconName: string) => {
    if (editingPointIndex !== null) {
      setEditingPoint({ ...editingPoint, icon: iconName });
    } else {
      setNewInvestmentPoint({ ...newInvestmentPoint, icon: iconName });
    }
    setShowIconSelector(false);
  };
  
  // Functions for investment points editing
  const startEditPoint = (index: number) => {
    setEditingPointIndex(index);
    setEditingPoint({
      icon: investmentPoints[index].icon,
      title: investmentPoints[index].title,
      description: investmentPoints[index].description
    });
  };

  const savePointEdit = () => {
    if (editingPointIndex !== null) {
      const updatedPoints = [...investmentPoints];
      updatedPoints[editingPointIndex] = {
        ...updatedPoints[editingPointIndex],
        icon: editingPoint.icon,
        title: editingPoint.title,
        description: editingPoint.description
      };
      
      // Update local state
      setInvestmentPoints(updatedPoints);
      
      // Immediately update project state
      updateProjectState({ whyInvestPoints: updatedPoints });
      
      // Reset editing state
      setEditingPointIndex(null);
      setEditingPoint({ icon: '', title: '', description: '' });
      setShowIconSelector(false);
    }
  };
  
  // Add a new investment point
  const addInvestmentPoint = () => {
    if (!newInvestmentPoint.title.trim() || !newInvestmentPoint.icon) return;
    
    // Create the new point
    const newPoint = {
      icon: newInvestmentPoint.icon,
      title: newInvestmentPoint.title.trim(),
      description: newInvestmentPoint.description.trim()
    };
    
    // Update local state first
    const updatedPoints = [...investmentPoints, newPoint];
    setInvestmentPoints(updatedPoints);
    
    // Then update project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          investmentSection: {
            ...(project.localizedContent?.[language]?.investmentSection || {}),
            title: sectionTitle,
            description: sectionDescription,
            whyInvestPoints: updatedPoints,
            propertyTypes: propertyTypes,
            overview: overview,
            contactForm: {
              ...(project.localizedContent?.[language]?.investmentSection?.contactForm || {}),
              title: contactFormTitle,
              description: contactFormDescription
            }
          }
        }
      }
    });
    
    // Reset form
    setNewInvestmentPoint({ title: '', description: '', icon: 'FcAddressBook' });
    setShowIconSelector(false);
    setShowAmenityForm(false);
  };
  
  // Remove an investment point
  const removeInvestmentPoint = (index: number) => {
    console.log('Removing investment point:', index);
    try {
      // Validate the index
      if (index < 0 || index >= investmentPoints.length) {
        console.error(`Cannot remove item: Index ${index} is out of bounds for investmentPoints array`);
        return;
      }

      // Create a new array by filtering out the item at the specified index
      const updatedPoints = investmentPoints.filter((_: any, i: number) => i !== index);
      
      // Update local state
      setInvestmentPoints(updatedPoints);
      
      // Immediately update project state with complete data
      setProject((prevProject: any) => ({
        ...prevProject,
        localizedContent: {
          ...prevProject.localizedContent,
          [language]: {
            ...prevProject.localizedContent?.[language],
            investmentSection: {
              ...(prevProject.localizedContent?.[language]?.investmentSection || {}),
              title: sectionTitle,
              description: sectionDescription,
              whyInvestPoints: updatedPoints,
              propertyTypes: propertyTypes,
              overview: overview,
              contactForm: {
                ...(prevProject.localizedContent?.[language]?.investmentSection?.contactForm || {}),
                title: contactFormTitle,
                description: contactFormDescription
              }
            }
          }
        }
      }));
      
      console.log('Investment point removed successfully');
    } catch (error) {
      console.error('Error removing investment point:', error);
    }
  };
  
  // Functions for investment points drag and drop
  const handlePointDragStart = (index: number) => {
    setDraggedPoint(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };

  const handlePointDrop = (dropIndex: number) => {
    if (draggedPoint === null || draggedPoint === dropIndex) return;
    
    const updatedPoints = [...investmentPoints];
    const draggedItem = updatedPoints[draggedPoint];
    
    // Remove the dragged item
    updatedPoints.splice(draggedPoint, 1);
    
    // Add it at the new position
    updatedPoints.splice(dropIndex, 0, draggedItem);
    
    // Update the local state
    setInvestmentPoints(updatedPoints);
    
    // Update the project state directly with the complete structure
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          investmentSection: {
            ...(project.localizedContent?.[language]?.investmentSection || {}),
            title: sectionTitle,
            description: sectionDescription,
            whyInvestPoints: updatedPoints,
            propertyTypes: propertyTypes,
            overview: overview,
            contactForm: {
              ...(project.localizedContent?.[language]?.investmentSection?.contactForm || {}),
              title: contactFormTitle,
              description: contactFormDescription
            }
          }
        }
      }
    });
    
    setDraggedPoint(null);
  };
  
  // Add a new property type
  const addPropertyType = () => {
    if (!newPropertyType.type.trim() || !newPropertyType.size.trim() || !newPropertyType.price.trim()) return;
    
    // Create the new property type
    const newType = {
      type: newPropertyType.type.trim(),
      size: newPropertyType.size.trim(),
      price: newPropertyType.price.trim(),
      availability: newPropertyType.availability
    };
    
    // Update local state first
    const updatedTypes = [...propertyTypes, newType];
    setPropertyTypes(updatedTypes);
    
    // Then update project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          investmentSection: {
            ...(project.localizedContent?.[language]?.investmentSection || {}),
            title: sectionTitle,
            description: sectionDescription,
            whyInvestPoints: investmentPoints,
            propertyTypes: updatedTypes,
            overview: overview,
            contactForm: {
              ...(project.localizedContent?.[language]?.investmentSection?.contactForm || {}),
              title: contactFormTitle,
              description: contactFormDescription
            }
          }
        }
      }
    });
    
    // Reset form
    setNewPropertyType({ type: '', size: '', price: '', availability: 'Available' });
  };
  
  // Remove a property type
  const removePropertyType = (index: number) => {
    console.log('Removing property type:', index);
    try {
      // Validate the index
      if (index < 0 || index >= propertyTypes.length) {
        console.error(`Cannot remove item: Index ${index} is out of bounds for propertyTypes array`);
        return;
      }

      // Create a new array by filtering out the item at the specified index
      const updatedTypes = propertyTypes.filter((_: any, i: number) => i !== index);
      
      // Update local state
      setPropertyTypes(updatedTypes);
      
      // Immediately update project state with complete data
      setProject((prevProject: any) => ({
        ...prevProject,
        localizedContent: {
          ...prevProject.localizedContent,
          [language]: {
            ...prevProject.localizedContent?.[language],
            investmentSection: {
              ...(prevProject.localizedContent?.[language]?.investmentSection || {}),
              title: sectionTitle,
              description: sectionDescription,
              whyInvestPoints: investmentPoints,
              propertyTypes: updatedTypes,
              overview: overview,
              contactForm: {
                ...(prevProject.localizedContent?.[language]?.investmentSection?.contactForm || {}),
                title: contactFormTitle,
                description: contactFormDescription
              }
            }
          }
        }
      }));
      
      console.log('Property type removed successfully');
    } catch (error) {
      console.error('Error removing property type:', error);
    }
  };
  
  // Functions for property types drag and drop
  const handlePropertyTypeDragStart = (index: number) => {
    setDraggedPropertyType(index);
  };

  const handlePropertyTypeDrop = (dropIndex: number) => {
    if (draggedPropertyType === null || draggedPropertyType === dropIndex) return;
    
    const updatedTypes = [...propertyTypes];
    const draggedItem = updatedTypes[draggedPropertyType];
    
    // Remove the dragged item
    updatedTypes.splice(draggedPropertyType, 1);
    
    // Add it at the new position
    updatedTypes.splice(dropIndex, 0, draggedItem);
    
    // Update the local state
    setPropertyTypes(updatedTypes);
    
    // Update the project state directly with the complete structure
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          investmentSection: {
            ...(project.localizedContent?.[language]?.investmentSection || {}),
            title: sectionTitle,
            description: sectionDescription,
            whyInvestPoints: investmentPoints,
            propertyTypes: updatedTypes,
            overview: overview,
            contactForm: {
              ...(project.localizedContent?.[language]?.investmentSection?.contactForm || {}),
              title: contactFormTitle,
              description: contactFormDescription
            }
          }
        }
      }
    });
    
    setDraggedPropertyType(null);
  };
  
  // Direct handlers for property type input fields
  const handlePropertyTypeChange = (field: string, value: string) => {
    setNewPropertyType({
      ...newPropertyType,
      [field]: value
    });
  };

  // Direct handlers for investment point input fields
  const handleInvestmentPointChange = (field: string, value: string) => {
    setNewInvestmentPoint({
      ...newInvestmentPoint,
      [field]: value
    });
  };

  const handleEditingPointChange = (field: string, value: string) => {
    setEditingPoint({
      ...editingPoint,
      [field]: value
    });
  };

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Investment Section' : 'قسم الاستثمار'}
      </h2>
      
      {/* Section Title and Description */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Title' : 'عنوان القسم'}
            </label>
            <input
              type="text"
              value={sectionTitle}
              onChange={handleSectionTitleChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Investment Opportunity" : "فرصة استثمارية"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Description' : 'وصف القسم'}
            </label>
            <textarea
              value={sectionDescription}
              onChange={handleSectionDescriptionChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={3}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "A premium investment with strong potential for capital appreciation and rental yields." 
                : "استثمار متميز مع إمكانات قوية لزيادة رأس المال وعوائد الإيجار."}
            />
          </div>
        </div>
      </div>
      
      {/* Why Invest Points */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Investment FAQ' : 'الأسئلة الشائعة حول الاستثمار'}
        </h3>
        
        {/* Grid of Investment Points and Add Card */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 mt-4">
          {/* Existing Investment Points */}
          {investmentPoints.map((point: any, index: number) => (
            <div key={index}>
              {editingPointIndex === index ? (
                // Edit Point Form
                <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600 shadow-lg">
                  <h3 className="text-md font-medium text-white mb-4">
                    {language === 'en' ? 'Edit Investment Point' : 'تعديل نقطة الاستثمار'}
                  </h3>
                  
                  <div className="flex flex-col items-center mb-4">
                    <div className="relative mb-4 flex flex-col items-center">
                      <button
                        type="button"
                        onClick={() => setShowIconSelector(!showIconSelector)}
                        className="w-16 h-16 rounded-full bg-[#00C2FF]/10 flex items-center justify-center text-[#00C2FF] hover:bg-[#00C2FF]/20"
                      >
                        {renderIcon(editingPoint.icon)}
                      </button>
                      <p className="text-xs text-gray-400 text-center mt-1">
                        {language === 'en' ? 'Click to change icon' : 'انقر لتغيير الأيقونة'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="mb-5 mt-4">
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      {language === 'en' ? 'Question' : 'السؤال'}
                    </label>
                    <input
                      type="text"
                      value={editingPoint.title}
                      onChange={(e) => handleEditingPointChange('title', e.target.value)}
                      className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                      dir={language === 'ar' ? 'rtl' : 'ltr'}
                    />
                  </div>
                  
                  <div className="mb-5">
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      {language === 'en' ? 'Answer' : 'الإجابة'}
                    </label>
                    <textarea
                      value={editingPoint.description}
                      onChange={(e) => handleEditingPointChange('description', e.target.value)}
                      className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                      rows={3}
                      dir={language === 'ar' ? 'rtl' : 'ltr'}
                    />
                  </div>
                  
                  <div className="flex justify-end space-x-2 mt-5 gap-2">
                    <button
                      type="button"
                      onClick={() => {
                        setEditingPointIndex(null);
                        setEditingPoint({ icon: '', title: '', description: '' });
                        setShowIconSelector(false);
                      }}
                      className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
                    >
                      {language === 'en' ? 'Cancel' : 'إلغاء'}
                    </button>
                    <button
                      type="button"
                      onClick={savePointEdit}
                      disabled={!editingPoint.title.trim() || !editingPoint.description.trim()}
                      className={`px-4 py-2 text-sm font-medium text-white rounded ${
                        editingPoint.title.trim() && editingPoint.description.trim()
                          ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
                          : 'bg-gray-600 cursor-not-allowed'
                      }`}
                    >
                      {language === 'en' ? 'Save Changes' : 'حفظ التغييرات'}
                    </button>
                  </div>
                </div>
              ) : (
                // Normal Point Card
                <div
                  className={`bg-[#151b29] rounded-lg overflow-hidden border border-gray-700 p-5 relative ${draggedPoint === index ? 'opacity-50' : 'opacity-100'}`}
                  draggable={true}
                  onDragStart={() => handlePointDragStart(index)}
                  onDragOver={handleDragOver}
                  onDrop={() => handlePointDrop(index)}
                >
                  {/* Drag indicator */}
                  {investmentPoints.length > 1 && (
                    <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1.5 z-10 text-gray-300 cursor-move">
                      <FiMove className="h-3 w-3" />
                    </div>
                  )}
                  
                  <div className="flex flex-col items-center">
                    <div className="w-14 h-14 rounded-full bg-[#182035] flex items-center justify-center text-[#00C2FF] mb-5">
                      {renderIcon(point.icon)}
                    </div>
                    
                    <h3 className="text-white font-medium text-lg text-center mb-2">{point.title}</h3>
                    <p className="text-gray-400 text-sm text-center">{point.description}</p>
                  </div>
                  
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-70 rounded-lg">
                    <div className="flex space-x-3 gap-2">
                      <button
                        type="button"
                        onClick={() => startEditPoint(index)}
                        className="p-2 bg-blue-600 rounded-full text-white"
                        title={language === 'en' ? 'Edit point' : 'تعديل النقطة'}
                      >
                        <FiEdit className="h-5 w-5" />
                      </button>
                      <button
                        type="button"
                        onClick={() => removeInvestmentPoint(index)}
                        className="p-2 bg-red-600 rounded-full text-white"
                        title={language === 'en' ? 'Delete point' : 'حذف النقطة'}
                      >
                        <FiX className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
          
          {/* Add New Point Card - only show when no form is visible and we're not editing */}
          {!showAmenityForm && editingPointIndex === null && (
            <div 
              className="bg-[#151b29] rounded-lg overflow-hidden border border-gray-700 border-dashed p-5 flex flex-col items-center justify-center cursor-pointer hover:bg-[#182035] transition-colors"
              onClick={() => setShowAmenityForm(true)}
            >
              <div className="w-14 h-14 bg-[#182035] rounded-full flex items-center justify-center mb-3 text-[#00C2FF]">
                <FiPlus className="h-6 w-6" />
              </div>
              <span className="text-sm text-gray-300 text-center">
                {language === 'en' ? 'Add New Investment Point' : 'إضافة نقطة استثمار جديدة'}
              </span>
            </div>
          )}
          
          {/* New Point Form */}
          {showAmenityForm && editingPointIndex === null && (
            <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600 shadow-lg">
              <h3 className="text-md font-medium text-white mb-4">
                {language === 'en' ? 'Add New Investment Point' : 'إضافة نقطة استثمار جديدة'}
              </h3>
              
              <div className="flex flex-col items-center mb-4">
                <div className="relative mb-4 flex flex-col items-center">
                  <button
                    type="button"
                    onClick={() => setShowIconSelector(!showIconSelector)}
                    className="w-16 h-16 rounded-full bg-[#00C2FF]/10 flex items-center justify-center text-[#00C2FF] hover:bg-[#00C2FF]/20"
                  >
                    {renderIcon(newInvestmentPoint.icon)}
                  </button>
                  <p className="text-xs text-gray-400 text-center mt-1">
                    {language === 'en' ? 'Click to select icon' : 'انقر لاختيار الأيقونة'}
                  </p>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Question' : 'السؤال'}
                </label>
                <input
                  type="text"
                  value={newInvestmentPoint.title}
                  onChange={(e) => handleInvestmentPointChange('title', e.target.value)}
                  placeholder={language === 'en' ? "e.g. Why invest in this property?" : "مثال: لماذا تستثمر في هذا العقار؟"}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Answer' : 'الإجابة'}
                </label>
                <textarea
                  value={newInvestmentPoint.description}
                  onChange={(e) => handleInvestmentPointChange('description', e.target.value)}
                  placeholder={language === 'en' ? "Answer the question" : "أجب عن السؤال"}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                  rows={3}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
              </div>
              
              <div className="flex justify-end space-x-2 mt-5 gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setNewInvestmentPoint({ title: '', description: '', icon: 'FcAddressBook' });
                    setShowAmenityForm(false);
                    setShowIconSelector(false);
                  }}
                  className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
                >
                  {language === 'en' ? 'Cancel' : 'إلغاء'}
                </button>
                <button
                  type="button"
                  onClick={addInvestmentPoint}
                  disabled={!newInvestmentPoint.title.trim() || !newInvestmentPoint.description.trim()}
                  className={`px-4 py-2 text-sm font-medium text-white rounded ${
                    newInvestmentPoint.title.trim() && newInvestmentPoint.description.trim() 
                      ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
                      : 'bg-gray-600 cursor-not-allowed'
                  }`}
                >
                  {language === 'en' ? 'Add Point' : 'إضافة نقطة'}
                </button>
              </div>
            </div>
          )}
        </div>
        
        {investmentPoints.length > 1 && !editingPointIndex && !showIconSelector && (
          <p className="mt-4 text-xs text-gray-400">
            {language === 'en' ? 'Drag point cards to reorder them.' : 'اسحب بطاقات النقاط لإعادة ترتيبها.'}
          </p>
        )}
        
        {investmentPoints.length === 0 && !showIconSelector && (
          <p className="text-sm text-gray-400 italic">
            {language === 'en' 
              ? 'No investment points added yet. Add points to highlight the investment benefits.'
              : 'لم تتم إضافة نقاط استثمار بعد. أضف نقاطًا لإبراز فوائد الاستثمار.'}
          </p>
        )}
        
        {/* Icon Selector Dropdown */}
        {showIconSelector && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
            <div className="bg-[#1a2234] rounded-lg shadow-xl p-3 w-full max-w-md max-h-[70vh] flex flex-col border border-gray-700">
              <div className="flex justify-between items-center mb-2 pb-2 border-b border-gray-700">
                <h3 className="text-base font-medium text-white flex items-center">
                  <span className="text-[#00C2FF] mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-14a2 2 0 10-.001 0H10zm-.001 0a2 2 0 100 0H10zM16 4a2 2 0 00-2-2h-1a2 2 0 100 4h1a2 2 0 002-2zm0 11a3 3 0 10-6 0 3 3 0 006 0z" clipRule="evenodd" />
                    </svg>
                  </span>
                  Select an Icon
                </h3>
                <button 
                  className="text-gray-400 hover:text-white hover:bg-gray-700 p-1 rounded-full transition-colors"
                  onClick={() => setShowIconSelector(false)}
                >
                  <FiX className="h-4 w-4" />
                </button>
              </div>
              
              {/* Search input */}
              <div className="relative mb-2">
                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                  <FiSearch className="h-3.5 w-3.5 text-[#00C2FF]" />
                </div>
                <input
                  type="text"
                  value={iconSearchTerm}
                  onChange={(e) => setIconSearchTerm(e.target.value)}
                  className="bg-gray-800 border border-gray-700 rounded-md py-1.5 pl-7 pr-3 w-full text-xs text-white placeholder-gray-400 focus:ring-[#00C2FF] focus:border-[#00C2FF] transition-colors"
                  placeholder="Search icons..."
                />
              </div>
              
              {/* Library selector */}
              <div className="mb-2 pb-2 border-b border-gray-700 overflow-hidden">
                <div className="flex gap-1 overflow-x-auto pb-1" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                  {iconSets
                    .filter(set => !iconSearchTerm.trim() || hasMatchingIcons(set.id, iconSearchTerm.trim()))
                    .map(set => (
                      <button
                        key={set.id}
                        onClick={() => {
                          setSelectedIconSet(set.id as any);
                        }}
                        className={`px-2 py-0.5 rounded-md text-xs whitespace-nowrap flex-shrink-0 transition-colors ${
                          selectedIconSet === set.id 
                            ? 'bg-[#00C2FF] text-white shadow-md' 
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        }`}
                      >
                        {set.label} ({set.total})
                      </button>
                    ))}
                </div>
              </div>
              
              {/* Icons container with scroll */}
              <div 
                className="h-[300px] overflow-y-auto border border-gray-700 rounded mb-2"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#6b7280 #1a2234',
                }}
              >
                <div className="grid grid-cols-5 gap-0.5 p-0.5">
                  {getFilteredIcons().length > 0 ? (
                    getFilteredIcons().map(iconName => {
                      // Directly render icons from the appropriate library
                      let Icon;
                      switch (selectedIconSet) {
                        case 'fc': Icon = FcIcons[iconName as keyof typeof FcIcons]; break;
                        case 'fa': Icon = FaIcons[iconName as keyof typeof FaIcons]; break;
                        case 'fa6': Icon = FaIconsSolid[iconName as keyof typeof FaIconsSolid]; break;
                        case 'bs': Icon = BsIcons[iconName as keyof typeof BsIcons]; break;
                        case 'ri': Icon = RiIcons[iconName as keyof typeof RiIcons]; break;
                        case 'gi': Icon = GiIcons[iconName as keyof typeof GiIcons]; break;
                        case 'tb': Icon = TbIcons[iconName as keyof typeof TbIcons]; break;
                        case 'md': Icon = MdIcons[iconName as keyof typeof MdIcons]; break;
                        case 'hi': Icon = HiIcons[iconName as keyof typeof HiIcons]; break;
                        case 'ai': Icon = AiIcons[iconName as keyof typeof AiIcons]; break;
                        case 'io': Icon = IoIcons[iconName as keyof typeof IoIcons]; break;
                        case 'io5': Icon = Io5Icons[iconName as keyof typeof Io5Icons]; break;
                        case 'pi': Icon = PiIcons[iconName as keyof typeof PiIcons]; break;
                        default: Icon = null;
                      }
                      
                      // Create the full icon name with library prefix
                      let prefix = '';
                      switch (selectedIconSet) {
                        case 'fc': prefix = 'Fc'; break;
                        case 'fa': prefix = 'Fa'; break;
                        case 'fa6': prefix = 'Fa6'; break;
                        case 'bs': prefix = 'Bs'; break;
                        case 'ri': prefix = 'Ri'; break;
                        case 'gi': prefix = 'Gi'; break;
                        case 'tb': prefix = 'Tb'; break;
                        case 'md': prefix = 'Md'; break;
                        case 'hi': prefix = 'Hi'; break;
                        case 'ai': prefix = 'Ai'; break;
                        case 'io': prefix = 'Io'; break;
                        case 'io5': prefix = 'Io5'; break;
                        case 'pi': prefix = 'Pi'; break;
                      }
                      
                      const fullIconName = `${prefix}${iconName}`;
                      
                      return (
                        <button
                          key={iconName}
                          onClick={() => selectIcon(fullIconName)}
                          className="aspect-square p-1 flex items-center justify-center bg-[#151b29] hover:bg-[#00C2FF]/10 rounded transition-colors border border-gray-800 hover:border-[#00C2FF]/50"
                          title={iconName}
                        >
                          {Icon ? <Icon className="text-xl" style={{ display: 'block' }} /> : <span className="text-xs text-gray-400">?</span>}
                        </button>
                      );
                    })
                  ) : (
                    <div className="col-span-5 flex flex-col items-center justify-center py-10 text-center bg-[#151b29]/50 rounded-md">
                      <div className="w-16 h-16 mb-3 flex items-center justify-center rounded-full bg-[#1a2234] text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                      <h3 className="text-base font-medium text-gray-300 mb-1">No icons found</h3>
                      <p className="text-sm text-gray-400 mb-3 max-w-[220px]">
                        Try a different search term or browse another icon library
                      </p>
                      {iconSearchTerm.trim().length > 0 && (
                        <button
                          onClick={() => setIconSearchTerm('')}
                          className="px-3 py-1.5 bg-[#00C2FF]/10 hover:bg-[#00C2FF]/20 text-[#00C2FF] text-xs rounded-md transition-colors flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                          Clear search
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              {/* Footer with info */}
              <div className="mt-0 pt-1 border-t border-gray-700 flex justify-between items-center text-xs text-gray-400">
                <div>
                  {getFilteredIcons().length} icons shown
                </div>
                <button 
                  onClick={() => setIconSearchTerm('')}
                  className={`text-xs px-2 py-0.5 rounded hover:bg-gray-700 transition-colors ${
                    iconSearchTerm ? 'text-[#00C2FF]' : 'text-gray-500 cursor-default'
                  }`}
                  disabled={!iconSearchTerm}
                >
                  Clear search
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Property Types */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Available Property Types' : 'أنواع العقارات المتاحة'}
        </h3>
        
        <div className="space-y-4 mb-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Property Type' : 'نوع العقار'}
              </label>
              <input
                type="text"
                value={newPropertyType.type}
                onChange={(e) => handlePropertyTypeChange('type', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={language === 'en' ? "e.g. Studio" : "مثال: استوديو"}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Size' : 'المساحة'}
              </label>
              <input
                type="text"
                value={newPropertyType.size}
                onChange={(e) => handlePropertyTypeChange('size', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                placeholder={language === 'en' ? "450 - 550 sq.ft" : "450 - 550 قدم مربع"}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Starting Price' : 'السعر الأولي'}
              </label>
              <input
                type="text"
                value={newPropertyType.price}
                onChange={(e) => handlePropertyTypeChange('price', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                placeholder="$500,000"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Availability' : 'التوفر'}
              </label>
              <select
                value={newPropertyType.availability}
                onChange={(e) => handlePropertyTypeChange('availability', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              >
                <option value="Available">{language === 'en' ? 'Available' : 'متاح'}</option>
                <option value="Limited">{language === 'en' ? 'Limited' : 'محدود'}</option>
                <option value="On Request">{language === 'en' ? 'On Request' : 'عند الطلب'}</option>
                <option value="Sold Out">{language === 'en' ? 'Sold Out' : 'نفذت الكمية'}</option>
              </select>
            </div>
          </div>
          
          <button
            type="button"
            onClick={addPropertyType}
            disabled={!newPropertyType.type.trim() || !newPropertyType.size.trim() || !newPropertyType.price.trim()}
            className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              newPropertyType.type.trim() && newPropertyType.size.trim() && newPropertyType.price.trim() 
              ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
              : 'bg-gray-600 cursor-not-allowed'
            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]`}
          >
            <FiPlus className="-ml-1 mr-2 h-4 w-4" />
            {language === 'en' ? 'Add Property Type' : 'إضافة نوع عقار'}
          </button>
        </div>
        
        {propertyTypes.length === 0 ? (
          <p className="text-sm text-gray-400 italic">
            {language === 'en' 
              ? 'No property types added yet. Add property types to show available options.'
              : 'لم تتم إضافة أنواع عقارات بعد. أضف أنواع العقارات لإظهار الخيارات المتاحة.'}
          </p>
        ) : (
          <div className="overflow-x-auto bg-gray-700 rounded-md">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-800">
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {language === 'en' ? 'Type' : 'النوع'}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {language === 'en' ? 'Size' : 'المساحة'}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {language === 'en' ? 'Price' : 'السعر'}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {language === 'en' ? 'Availability' : 'التوفر'}
                  </th>
                  <th className="px-4 py-3 text-left text-gray-300 font-medium">
                    {language === 'en' ? 'Actions' : 'الإجراءات'}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-600">
                {propertyTypes.map((type: any, index: number) => (
                  <tr 
                    key={index}
                    className={draggedPropertyType === index ? 'opacity-50 bg-gray-600' : 'bg-gray-700'}
                    draggable={true}
                    onDragStart={() => handlePropertyTypeDragStart(index)}
                    onDragOver={handleDragOver}
                    onDrop={() => handlePropertyTypeDrop(index)}
                  >
                    <td className="px-4 py-3 text-white">{type.type}</td>
                    <td className="px-4 py-3 text-gray-300">{type.size}</td>
                    <td className="px-4 py-3 text-white">{type.price}</td>
                    <td className="px-4 py-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        type.availability === 'Available' ? 'bg-green-900/30 text-green-300' :
                        type.availability === 'Limited' ? 'bg-blue-900/30 text-blue-300' :
                        type.availability === 'On Request' ? 'bg-yellow-900/30 text-yellow-300' :
                        'bg-red-900/30 text-red-300'
                      }`}>
                        {type.availability}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center space-x-2">
                        {propertyTypes.length > 1 && (
                          <button className="text-gray-400 cursor-move">
                            <FiMove className="h-4 w-4" />
                          </button>
                        )}
                        <button 
                          onClick={() => removePropertyType(index)}
                          className="text-gray-400 hover:text-red-500"
                        >
                          <FiX className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Investment Overview */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Investment Overview' : 'نظرة عامة على الاستثمار'}
        </h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Price Range' : 'نطاق السعر'}
            </label>
            <input
              type="text"
              value={overview.priceRange}
              onChange={(e) => handleOverviewChange('priceRange', e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="$500,000 - $3,000,000"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Expected Rental Yield' : 'العائد الإيجاري المتوقع'}
            </label>
            <input
              type="text"
              value={overview.rentalYield}
              onChange={(e) => handleOverviewChange('rentalYield', e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="7-9% annually"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Completion Date' : 'تاريخ الإنجاز'}
            </label>
            <input
              type="text"
              value={overview.completionDate}
              onChange={(e) => handleOverviewChange('completionDate', e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="2025"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Payment Plan' : 'خطة الدفع'}
            </label>
            <input
              type="text"
              value={overview.paymentPlan}
              onChange={(e) => handleOverviewChange('paymentPlan', e.target.value)}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="30% down payment"
            />
          </div>
        </div>
      </div>
      
      {/* Contact Form Section */}
      <div>
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Contact Form Section' : 'قسم نموذج الاتصال'}
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Form Title' : 'عنوان النموذج'}
            </label>
            <input
              type="text"
              value={contactFormTitle}
              onChange={handleContactFormTitleChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Interested in Mazaya Heights?" : "مهتم بمزايا هايتس؟"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Form Description' : 'وصف النموذج'}
            </label>
            <textarea
              value={contactFormDescription}
              onChange={handleContactFormDescriptionChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={2}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "Contact our sales team for more information about this exclusive investment opportunity." 
                : "اتصل بفريق المبيعات لدينا للحصول على مزيد من المعلومات حول فرصة الاستثمار الحصرية هذه."}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Contact Phone Number' : 'رقم هاتف الاتصال'}
            </label>
            <input
              type="text"
              value={contactFormPhone}
              onChange={handleContactFormPhoneChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              placeholder="+971 4 123 4567"
            />
            <p className="mt-1 text-sm text-gray-400">
              {language === 'en' 
                ? 'This phone number will be displayed in both English and Arabic interfaces' 
                : 'سيتم عرض رقم الهاتف هذا في واجهات اللغة الإنجليزية والعربية'}
            </p>
          </div>
        </div>
      </div>
      
      
    </div>
  );
};

export default ProjectInvestmentSection; 