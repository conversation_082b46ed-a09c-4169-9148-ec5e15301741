"use client";

import React, { useState } from 'react';
import { FiSave, FiEdit3, FiX, FiSearch, FiGlobe, FiTag, FiLink, FiImage, FiFileText, FiCode, FiEye } from 'react-icons/fi';

interface SEOData {
  basic: {
    pageTitle: {
      en: string;
      ar: string;
    };
    metaDescription: {
      en: string;
      ar: string;
    };
    metaKeywords: {
      en: string[];
      ar: string[];
    };
    canonicalUrl: string;
    pageSlug: {
      en: string;
      ar: string;
    };
  };
  openGraph: {
    title: {
      en: string;
      ar: string;
    };
    description: {
      en: string;
      ar: string;
    };
    image: string;
    imageAlt: {
      en: string;
      ar: string;
    };
    type: string;
    locale: {
      en: string;
      ar: string;
    };
  };
  twitter: {
    card: string;
    title: {
      en: string;
      ar: string;
    };
    description: {
      en: string;
      ar: string;
    };
    image: string;
    creator: string;
  };
  structured: {
    organizationName: {
      en: string;
      ar: string;
    };
    organizationType: string;
    logo: string;
    description: {
      en: string;
      ar: string;
    };
    foundingDate: string;
    address: {
      streetAddress: string;
      city: string;
      country: string;
      postalCode: string;
    };
    contactInfo: {
      phone: string;
      email: string;
      website: string;
    };
    socialMediaLinks: string[];
  };
  technical: {
    robots: string;
    hreflang: {
      en: string;
      ar: string;
    };
    sitemap: string;
    favicon: string;
    appleTouchIcon: string;
  };
}

// Utility function to create a stable fallback image
const createFallbackImage = (width: number, height: number, text: string) => {
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="${width}" height="${height}" fill="#1f2937"/>
      <text x="${width/2}" y="${height/2}" fill="#00C2FF" text-anchor="middle" font-family="Arial" font-size="24">${text}</text>
    </svg>
  `)}`;
};

// Custom hook for handling image loading with error state
function useImageWithFallback(initialUrl: string, fallbackUrl: string) {
  const [imageUrl, setImageUrl] = useState(initialUrl);
  const [hasError, setHasError] = useState(false);

  const handleError = () => {
    if (!hasError) {
      setHasError(true);
      setImageUrl(fallbackUrl);
    }
  };

  const handleLoad = () => {
    setHasError(false);
  };

  const updateUrl = (newUrl: string) => {
    setHasError(false);
    setImageUrl(newUrl);
  };

  return { imageUrl, handleError, handleLoad, updateUrl, hasError };
}

export default function ContactPageSEOPage() {
    const [seoData, setSeoData] = useState<SEOData>({    basic: {      pageTitle: {        en: "Contact Mazaya Capital - Get in Touch with Our Real Estate Experts",        ar: "تواصل مع مزايا كابيتال - تواصل مع خبراء العقارات لدينا"      },      metaDescription: {        en: "Contact Mazaya Capital for inquiries about luxury real estate projects in Dubai. Reach out to our expert team for property investment opportunities and development information.",        ar: "تواصل مع مزايا كابيتال للاستفسارات حول مشاريع العقارات الفاخرة في دبي. تواصل مع فريق الخبراء لدينا لفرص الاستثمار العقاري ومعلومات التطوير."      },      metaKeywords: {        en: ["contact Mazaya Capital", "real estate inquiries Dubai", "property consultation", "real estate contact", "Dubai property experts", "real estate support", "property investment contact", "Mazaya Capital office"],        ar: ["تواصل مزايا كابيتال", "استفسارات عقارية دبي", "استشارة عقارية", "تواصل عقاري", "خبراء عقارات دبي", "دعم عقاري", "تواصل استثمار عقاري", "مكتب مزايا كابيتال"]      },      canonicalUrl: "https://mazayacapital.com/contact",      pageSlug: {        en: "contact",        ar: "تواصل-معنا"      }
    },
        openGraph: {      title: {        en: "Contact Mazaya Capital - Real Estate Experts in Dubai",        ar: "تواصل مع مزايا كابيتال - خبراء العقارات في دبي"      },      description: {        en: "Get in touch with Mazaya Capital's expert team for luxury real estate inquiries, property investment opportunities, and development information in Dubai.",        ar: "تواصل مع فريق خبراء مزايا كابيتال للاستفسارات حول العقارات الفاخرة وفرص الاستثمار العقاري ومعلومات التطوير في دبي."      },      image: "https://mazayacapital.com/images/og-contact.jpg",      imageAlt: {        en: "Contact Mazaya Capital - Real estate consultation and support",        ar: "تواصل مع مزايا كابيتال - استشارة ودعم عقاري"      },
      type: "website",
      locale: {
        en: "en_US",
        ar: "ar_AE"
      }
    },
        twitter: {      card: "summary_large_image",      title: {        en: "Contact Mazaya Capital - Real Estate Consultation",        ar: "تواصل مع مزايا كابيتال - استشارة عقارية"      },      description: {        en: "Reach out to Mazaya Capital for expert real estate consultation and support. Contact our team for luxury property inquiries in Dubai.",        ar: "تواصل مع مزايا كابيتال للحصول على استشارة ودعم عقاري متخصص. اتصل بفريقنا للاستفسارات حول العقارات الفاخرة في دبي."      },      image: "https://mazayacapital.com/images/twitter-contact.jpg",      creator: "@MazayaCapital"    },
    structured: {
      organizationName: {
        en: "Mazaya Capital",
        ar: "مزايا كابيتال"
      },
      organizationType: "Corporation",
      logo: "https://mazayacapital.com/images/logo/mazaya-logo.png",
            description: {        en: "Contact Mazaya Capital, a premier real estate development company in Dubai. Get in touch with our expert team for luxury residential and commercial property inquiries, investment opportunities, and development information.",        ar: "تواصل مع مزايا كابيتال، شركة رائدة في تطوير العقارات في دبي. تواصل مع فريق الخبراء لدينا للاستفسارات حول العقارات السكنية والتجارية الفاخرة وفرص الاستثمار ومعلومات التطوير."      },
      foundingDate: "2005",
      address: {
        streetAddress: "Sheikh Zayed Road",
        city: "Dubai",
        country: "United Arab Emirates",
        postalCode: "00000"
      },
      contactInfo: {
        phone: "+971-4-XXX-XXXX",
        email: "<EMAIL>",
        website: "https://mazayacapital.com"
      },
      socialMediaLinks: [
        "https://linkedin.com/company/mazaya-capital",
        "https://twitter.com/mazayacapital",
        "https://instagram.com/mazayacapital",
        "https://facebook.com/mazayacapital"
      ]
    },
    technical: {
      robots: "index, follow",
      hreflang: {
        en: "en-US",
        ar: "ar-AE"
      },
      sitemap: "https://mazayacapital.com/sitemap.xml",
      favicon: "/favicon.ico",
      appleTouchIcon: "/apple-touch-icon.png"
    }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);

  const handleSectionSave = (section: string, data: any) => {
    setSeoData(prev => ({
      ...prev,
      [section]: data
    }));
    setEditingSection(null);
  };

  const handleSaveAll = () => {
    console.log('Saving SEO data:', seoData);
    alert('SEO settings saved successfully!');
  };

  const addKeyword = (lang: 'en' | 'ar', keyword: string) => {
    if (keyword.trim()) {
      setSeoData(prev => ({
        ...prev,
        basic: {
          ...prev.basic,
          metaKeywords: {
            ...prev.basic.metaKeywords,
            [lang]: [...prev.basic.metaKeywords[lang], keyword.trim()]
          }
        }
      }));
    }
  };

  const removeKeyword = (lang: 'en' | 'ar', index: number) => {
    setSeoData(prev => ({
      ...prev,
      basic: {
        ...prev.basic,
        metaKeywords: {
          ...prev.basic.metaKeywords,
          [lang]: prev.basic.metaKeywords[lang].filter((_, i) => i !== index)
        }
      }
    }));
  };

  const addSocialLink = (url: string) => {
    if (url.trim()) {
      setSeoData(prev => ({
        ...prev,
        structured: {
          ...prev.structured,
          socialMediaLinks: [...prev.structured.socialMediaLinks, url.trim()]
        }
      }));
    }
  };

  const removeSocialLink = (index: number) => {
    setSeoData(prev => ({
      ...prev,
      structured: {
        ...prev.structured,
        socialMediaLinks: prev.structured.socialMediaLinks.filter((_, i) => i !== index)
      }
    }));
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
                <div>          <h1 className="text-3xl font-bold leading-tight text-white">Contact Page - SEO Settings</h1>          <p className="text-gray-400 mt-1">Manage SEO optimization for the Contact page</p>        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save SEO Settings
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Basic SEO */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiSearch className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Basic SEO
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'basic' ? null : 'basic')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'basic' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'basic' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'basic' ? (
            <BasicSEOForm 
              data={seoData.basic}
              onSave={(data) => handleSectionSave('basic', data)}
              onCancel={() => setEditingSection(null)}
              addKeyword={addKeyword}
              removeKeyword={removeKeyword}
            />
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Page Title (English)</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.basic.pageTitle.en}</p>
                  <p className="text-xs text-gray-500 mt-1">Length: {seoData.basic.pageTitle.en.length} characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Page Title (Arabic)</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.basic.pageTitle.ar}</p>
                  <p className="text-xs text-gray-500 mt-1">Length: {seoData.basic.pageTitle.ar.length} characters</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Meta Description (English)</label>
                  <p className="text-gray-300 bg-gray-700 p-2 rounded text-sm">{seoData.basic.metaDescription.en}</p>
                  <p className="text-xs text-gray-500 mt-1">Length: {seoData.basic.metaDescription.en.length} characters</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Meta Description (Arabic)</label>
                  <p className="text-gray-300 bg-gray-700 p-2 rounded text-sm">{seoData.basic.metaDescription.ar}</p>
                  <p className="text-xs text-gray-500 mt-1">Length: {seoData.basic.metaDescription.ar.length} characters</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Keywords (English)</label>
                  <div className="flex flex-wrap gap-2">
                    {seoData.basic.metaKeywords.en.map((keyword, index) => (
                      <span key={index} className="px-2 py-1 bg-[#00C2FF]/20 text-[#00C2FF] rounded-full text-xs">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Keywords (Arabic)</label>
                  <div className="flex flex-wrap gap-2">
                    {seoData.basic.metaKeywords.ar.map((keyword, index) => (
                      <span key={index} className="px-2 py-1 bg-[#00C2FF]/20 text-[#00C2FF] rounded-full text-xs">
                        {keyword}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Open Graph */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiGlobe className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Open Graph (Social Media)
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'openGraph' ? null : 'openGraph')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'openGraph' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'openGraph' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'openGraph' ? (
            <OpenGraphForm 
              data={seoData.openGraph}
              onSave={(data: SEOData['openGraph']) => handleSectionSave('openGraph', data)}
              onCancel={() => setEditingSection(null)}
            />
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">OG Title (English)</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.openGraph.title.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">OG Title (Arabic)</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.openGraph.title.ar}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">OG Image</label>
                <div className="flex items-center space-x-4">
                  <img src={seoData.openGraph.image} alt="OG Preview" className="w-24 h-12 object-cover rounded" />
                  <p className="text-gray-300">{seoData.openGraph.image}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Twitter Cards */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiTag className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Twitter Cards
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'twitter' ? null : 'twitter')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'twitter' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'twitter' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'twitter' ? (
            <TwitterForm 
              data={seoData.twitter}
              onSave={(data: SEOData['twitter']) => handleSectionSave('twitter', data)}
              onCancel={() => setEditingSection(null)}
            />
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Card Type</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.twitter.card}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Creator</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.twitter.creator}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Image</label>
                  <img src={seoData.twitter.image} alt="Twitter Preview" className="w-full h-16 object-cover rounded" />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Structured Data */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiCode className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Structured Data (Schema.org)
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'structured' ? null : 'structured')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'structured' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'structured' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'structured' ? (
            <StructuredDataForm 
              data={seoData.structured}
              onSave={(data: SEOData['structured']) => handleSectionSave('structured', data)}
              onCancel={() => setEditingSection(null)}
              addSocialLink={addSocialLink}
              removeSocialLink={removeSocialLink}
            />
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Organization Name</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.structured.organizationName.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Founded</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{seoData.structured.foundingDate}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Social Media Links</label>
                <div className="space-y-1">
                  {seoData.structured.socialMediaLinks.map((link, index) => (
                    <p key={index} className="text-[#00C2FF] bg-gray-700 p-2 rounded text-sm">{link}</p>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Technical SEO */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiFileText className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Technical SEO
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'technical' ? null : 'technical')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'technical' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'technical' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'technical' ? (
            <TechnicalSEOForm 
              data={seoData.technical}
              onSave={(data: SEOData['technical']) => handleSectionSave('technical', data)}
              onCancel={() => setEditingSection(null)}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Robots</label>
                <p className="text-white bg-gray-700 p-2 rounded">{seoData.technical.robots}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Sitemap URL</label>
                <p className="text-[#00C2FF] bg-gray-700 p-2 rounded">{seoData.technical.sitemap}</p>
              </div>
            </div>
          )}
        </div>

        {/* SEO Preview */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            SEO Preview
          </h2>
          
          <div className="space-y-6">
            {/* Google Search Preview */}
            <div>
              <h3 className="text-sm font-medium text-gray-400 mb-3">Google Search Results Preview</h3>
              <div className="bg-white p-4 rounded border">
                <div className="text-blue-600 text-lg hover:underline cursor-pointer">
                  {seoData.basic.pageTitle.en}
                </div>
                <div className="text-green-700 text-sm">
                  {seoData.basic.canonicalUrl}
                </div>
                <div className="text-gray-600 text-sm mt-1">
                  {seoData.basic.metaDescription.en}
                </div>
              </div>
            </div>
            {/* Social Media Preview */}
            <div>
              <h3 className="text-sm font-medium text-gray-400 mb-3">Social Media Share Preview (Facebook/LinkedIn)</h3>
              <div className="bg-white border rounded overflow-hidden max-w-md">
                <img 
                  src={seoData.openGraph.image} 
                  alt="Social preview" 
                  className="w-full h-32 object-cover" 
                />
                <div className="p-3">
                  <div className="font-medium text-gray-900 text-sm">{seoData.openGraph.title.en}</div>
                  <div className="text-gray-600 text-xs mt-1">{seoData.openGraph.description.en}</div>
                  <div className="text-gray-500 text-xs mt-1">{seoData.basic.canonicalUrl}</div>
                </div>
              </div>
            </div>

            {/* Twitter Card Preview */}
            <div>
              <h3 className="text-sm font-medium text-gray-400 mb-3">Twitter Card Preview</h3>
              <div className="bg-white border rounded overflow-hidden max-w-sm">
                {seoData.twitter.card === 'summary_large_image' ? (
                  // Large Image Card
                  <>
                    <img
                      src={seoData.twitter.image}
                      alt="Twitter card preview"
                      className="w-full h-48 object-cover"
                      onError={(e) => {
                        e.currentTarget.src = createFallbackImage(400, 200, 'Twitter Image');
                      }}
                    />
                    <div className="p-3">
                      <div className="text-gray-500 text-xs mb-1">{seoData.basic.canonicalUrl}</div>
                      <div className="font-medium text-gray-900 text-sm leading-tight">{seoData.twitter.title.en}</div>
                      <div className="text-gray-600 text-xs mt-1 line-clamp-2">{seoData.twitter.description.en}</div>
                      <div className="text-gray-500 text-xs mt-2">{seoData.twitter.creator}</div>
                    </div>
                  </>
                ) : (
                  // Summary Card
                  <div className="flex">
                    <div className="flex-1 p-3">
                      <div className="text-gray-500 text-xs mb-1">{seoData.basic.canonicalUrl}</div>
                      <div className="font-medium text-gray-900 text-sm leading-tight">{seoData.twitter.title.en}</div>
                      <div className="text-gray-600 text-xs mt-1 line-clamp-2">{seoData.twitter.description.en}</div>
                      <div className="text-gray-500 text-xs mt-2">{seoData.twitter.creator}</div>
                    </div>
                    <div className="w-24 h-24 flex-shrink-0">
                      <img
                        src={seoData.twitter.image}
                        alt="Twitter card preview"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = createFallbackImage(96, 96, 'Twitter');
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Basic SEO Form Component
function BasicSEOForm({ 
  data, 
  onSave, 
  onCancel, 
  addKeyword, 
  removeKeyword 
}: { 
  data: SEOData['basic'];
  onSave: (data: SEOData['basic']) => void;
  onCancel: () => void;
  addKeyword: (lang: 'en' | 'ar', keyword: string) => void;
  removeKeyword: (lang: 'en' | 'ar', index: number) => void;
}) {
  const [formData, setFormData] = useState(data);
  const [newKeywordEn, setNewKeywordEn] = useState('');
  const [newKeywordAr, setNewKeywordAr] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Page Title (English)</label>
          <input
            type="text"
            value={formData.pageTitle.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              pageTitle: { ...prev.pageTitle, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={60}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.pageTitle.en.length}/60 characters</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Page Title (Arabic)</label>
          <input
            type="text"
            value={formData.pageTitle.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              pageTitle: { ...prev.pageTitle, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={60}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.pageTitle.ar.length}/60 characters</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Meta Description (English)</label>
          <textarea
            value={formData.metaDescription.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              metaDescription: { ...prev.metaDescription, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={160}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.metaDescription.en.length}/160 characters</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Meta Description (Arabic)</label>
          <textarea
            value={formData.metaDescription.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              metaDescription: { ...prev.metaDescription, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={160}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.metaDescription.ar.length}/160 characters</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Keywords (English)</label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.metaKeywords.en.map((keyword, index) => (
              <span key={index} className="px-2 py-1 bg-[#00C2FF]/20 text-[#00C2FF] rounded-full text-xs flex items-center">
                {keyword}
                <button
                  type="button"
                  onClick={() => {
                    const newKeywords = formData.metaKeywords.en.filter((_, i) => i !== index);
                    setFormData(prev => ({
                      ...prev,
                      metaKeywords: { ...prev.metaKeywords, en: newKeywords }
                    }));
                  }}
                  className="ml-1 text-[#00C2FF] hover:text-red-400"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex space-x-2">
            <input
              type="text"
              value={newKeywordEn}
              onChange={(e) => setNewKeywordEn(e.target.value)}
              placeholder="Add keyword"
              className="flex-1 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
            />
            <button
              type="button"
              onClick={() => {
                if (newKeywordEn.trim()) {
                  setFormData(prev => ({
                    ...prev,
                    metaKeywords: {
                      ...prev.metaKeywords,
                      en: [...prev.metaKeywords.en, newKeywordEn.trim()]
                    }
                  }));
                  setNewKeywordEn('');
                }
              }}
              className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm"
            >
              Add
            </button>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Keywords (Arabic)</label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.metaKeywords.ar.map((keyword, index) => (
              <span key={index} className="px-2 py-1 bg-[#00C2FF]/20 text-[#00C2FF] rounded-full text-xs flex items-center">
                {keyword}
                <button
                  type="button"
                  onClick={() => {
                    const newKeywords = formData.metaKeywords.ar.filter((_, i) => i !== index);
                    setFormData(prev => ({
                      ...prev,
                      metaKeywords: { ...prev.metaKeywords, ar: newKeywords }
                    }));
                  }}
                  className="ml-1 text-[#00C2FF] hover:text-red-400"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex space-x-2">
            <input
              type="text"
              value={newKeywordAr}
              onChange={(e) => setNewKeywordAr(e.target.value)}
              placeholder="إضافة كلمة مفتاحية"
              className="flex-1 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
            />
            <button
              type="button"
              onClick={() => {
                if (newKeywordAr.trim()) {
                  setFormData(prev => ({
                    ...prev,
                    metaKeywords: {
                      ...prev.metaKeywords,
                      ar: [...prev.metaKeywords.ar, newKeywordAr.trim()]
                    }
                  }));
                  setNewKeywordAr('');
                }
              }}
              className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm"
            >
              Add
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Canonical URL</label>
          <input
            type="url"
            value={formData.canonicalUrl}
            onChange={(e) => setFormData(prev => ({ ...prev, canonicalUrl: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Page Slug (English)</label>
          <input
            type="text"
            value={formData.pageSlug.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              pageSlug: { ...prev.pageSlug, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Open Graph Form Component with Image Upload
function OpenGraphForm({ data, onSave, onCancel }: any) {
  const [formData, setFormData] = useState(data);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(data.image);
  const [uploadMethod, setUploadMethod] = useState<'url' | 'upload'>('url');
  const [imageError, setImageError] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Update form data with the new image URL
      setFormData((prev: any) => ({ ...prev, image: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('og-image-upload')?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Hidden File Input */}
      <input
        id="og-image-upload"
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Title Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">OG Title (English)</label>
        <input
          type="text"
          placeholder="OG Title (English)"
          value={formData.title.en}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, title: { ...prev.title, en: e.target.value } }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={60}
        />
          <p className="text-xs text-gray-500 mt-1">{formData.title.en.length}/60 characters</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">OG Title (Arabic)</label>
        <input
          type="text"
          placeholder="OG Title (Arabic)"
          value={formData.title.ar}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, title: { ...prev.title, ar: e.target.value } }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={60}
        />
          <p className="text-xs text-gray-500 mt-1">{formData.title.ar.length}/60 characters</p>
        </div>
      </div>
      
      {/* Description Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">OG Description (English)</label>
          <textarea
            placeholder="OG Description (English)"
            value={formData.description.en}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, description: { ...prev.description, en: e.target.value } }))}
            rows={3}
        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={155}
      />
          <p className="text-xs text-gray-500 mt-1">{formData.description.en.length}/155 characters</p>
      </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">OG Description (Arabic)</label>
          <textarea
            placeholder="OG Description (Arabic)"
            value={formData.description.ar}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, description: { ...prev.description, ar: e.target.value } }))}
            rows={3}
        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={155}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.description.ar.length}/155 characters</p>
      </div>
      </div>

      {/* Image Alt Text */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Image Alt Text (English)</label>
        <input
          type="text"
            placeholder="Describe the image"
            value={formData.imageAlt.en}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, imageAlt: { ...prev.imageAlt, en: e.target.value } }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Image Alt Text (Arabic)</label>
        <input
          type="text"
            placeholder="وصف الصورة"
            value={formData.imageAlt.ar}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, imageAlt: { ...prev.imageAlt, ar: e.target.value } }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
        </div>
      </div>
      
      {/* Image Upload Section */}
      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Open Graph Image</label>
        
        {/* Upload Method Toggle */}
        <div className="flex space-x-2 mb-4">
              <button
                type="button"
            onClick={() => setUploadMethod('upload')}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              uploadMethod === 'upload' 
                ? 'bg-[#00C2FF] text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            <FiImage className="inline mr-1 h-3 w-3" />
            Upload Image
              </button>
          <button
            type="button"
            onClick={() => setUploadMethod('url')}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              uploadMethod === 'url' 
                ? 'bg-[#00C2FF] text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            <FiLink className="inline mr-1 h-3 w-3" />
            Image URL
          </button>
      </div>
      
        {uploadMethod === 'upload' ? (
          // Image Upload Area
          <div 
            onClick={handleImageClick}
            className="w-full h-48 bg-gray-600 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
          >
            {previewUrl ? (
              <div className="relative w-full h-full">
                <img 
                  src={previewUrl} 
                  alt="OG Preview"
                                    className="w-full h-full object-cover"                  onError={() => {                    if (!imageError) {                      setImageError(true);                      setPreviewUrl(createFallbackImage(1200, 630, 'OG Image'));                    }                  }}                  onLoad={() => setImageError(false)}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                  <div className="text-center text-white opacity-0 group-hover:opacity-100">
                    <FiImage className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Click to change image</p>
                    <p className="text-xs text-gray-300 mt-1">Recommended: 1200x630px</p>
      </div>
      </div>
        </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                <div className="text-center">
                  <FiImage className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium">Click to upload OG image</p>
                  <p className="text-sm">Recommended size: 1200x630px</p>
                  <p className="text-xs text-gray-500 mt-2">JPG, PNG, GIF up to 10MB</p>
              </div>
            </div>
          )}
        </div>
        ) : (
          // URL Input
          <input
            type="url"
            placeholder="https://example.com/og-image.jpg"
            value={formData.image}
            onChange={(e) => {
              setFormData((prev: any) => ({ ...prev, image: e.target.value }));
              setPreviewUrl(e.target.value);
            }}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        )}

        {/* Upload Info */}
        {selectedFile && uploadMethod === 'upload' && (
          <div className="mt-2 p-3 bg-gray-700 rounded border border-gray-600">
            <div className="flex items-center text-green-400 text-sm">
              <FiImage className="mr-2 h-4 w-4" />
              <span>Selected: {selectedFile.name}</span>
              <span className="ml-2 text-gray-400">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
              </div>
            </div>
          )}

        {/* Image Preview for URL */}
        {uploadMethod === 'url' && previewUrl && (
          <div className="mt-3">
            <p className="text-xs text-gray-400 mb-2">Preview:</p>
            <img 
              src={previewUrl} 
              alt="OG Preview" 
                            className="w-full max-w-md h-32 object-cover rounded border border-gray-600"              onError={() => {                if (!imageError) {                  setImageError(true);                  setPreviewUrl(createFallbackImage(1200, 630, 'Invalid URL'));                }              }}
            />
            </div>
          )}
        </div>

      {/* Additional OG Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">OG Type</label>
          <select
            value={formData.type}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, type: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          >
            <option value="website">Website</option>
            <option value="article">Article</option>
            <option value="business.business">Business</option>
            <option value="profile">Profile</option>
          </select>
          </div>
              <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Locale</label>
          <div className="grid grid-cols-2 gap-2">
            <input
              type="text"
              placeholder="en_US"
              value={formData.locale.en}
              onChange={(e) => setFormData((prev: any) => ({ ...prev, locale: { ...prev.locale, en: e.target.value } }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-sm"
            />
            <input
              type="text"
              placeholder="ar_AE"
              value={formData.locale.ar}
              onChange={(e) => setFormData((prev: any) => ({ ...prev, locale: { ...prev.locale, ar: e.target.value } }))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-sm"
            />
              </div>
              </div>
        </div>

      <div className="flex justify-end space-x-3">
        <button type="button" onClick={onCancel} className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors">Cancel</button>
        <button type="submit" className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors">Save Changes</button>
                </div>
    </form>
  );
}

function TwitterForm({ data, onSave, onCancel }: any) {
  const [formData, setFormData] = useState(data);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(data.image);
  const [uploadMethod, setUploadMethod] = useState<'url' | 'upload'>('url');
  const [imageError, setImageError] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Update form data with the new image URL
      setFormData((prev: any) => ({ ...prev, image: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('twitter-image-upload')?.click();
  };

  return (
    <form onSubmit={(e) => { e.preventDefault(); onSave(formData); }} className="space-y-4">
      {/* Hidden File Input */}
      <input
        id="twitter-image-upload"
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Title Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Title (English)</label>
          <input
            type="text"
            placeholder="Twitter Title (English)"
            value={formData.title.en}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, title: { ...prev.title, en: e.target.value } }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={70}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.title.en.length}/70 characters</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Title (Arabic)</label>
          <input
            type="text"
            placeholder="Twitter Title (Arabic)"
            value={formData.title.ar}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, title: { ...prev.title, ar: e.target.value } }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={70}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.title.ar.length}/70 characters</p>
        </div>
      </div>

      {/* Description Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Description (English)</label>
          <textarea
            placeholder="Twitter Description (English)"
            value={formData.description.en}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, description: { ...prev.description, en: e.target.value } }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={200}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.description.en.length}/200 characters</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Description (Arabic)</label>
          <textarea
            placeholder="Twitter Description (Arabic)"
            value={formData.description.ar}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, description: { ...prev.description, ar: e.target.value } }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            maxLength={200}
          />
          <p className="text-xs text-gray-500 mt-1">{formData.description.ar.length}/200 characters</p>
        </div>
      </div>

      {/* Card Type and Creator */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Card Type</label>
          <select
            value={formData.card}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, card: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          >
            <option value="summary">Summary</option>
            <option value="summary_large_image">Summary Large Image</option>
          </select>
          </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Handle</label>
            <input
              type="text"
            placeholder="@MazayaCapital"
            value={formData.creator}
            onChange={(e) => setFormData((prev: any) => ({ ...prev, creator: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
          </div>
        </div>

      {/* Twitter Image Upload */}
        <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Twitter Card Image</label>
        
        {/* Upload Method Toggle */}
        <div className="flex space-x-2 mb-4">
                <button
                  type="button"
            onClick={() => setUploadMethod('upload')}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              uploadMethod === 'upload' 
                ? 'bg-[#00C2FF] text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            <FiImage className="inline mr-1 h-3 w-3" />
            Upload Image
                </button>
            <button
              type="button"
            onClick={() => setUploadMethod('url')}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              uploadMethod === 'url' 
                ? 'bg-[#00C2FF] text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            <FiLink className="inline mr-1 h-3 w-3" />
            Image URL
            </button>
          </div>

        {uploadMethod === 'upload' ? (
          // Image Upload Area
          <div 
            onClick={handleImageClick}
            className="w-full h-40 bg-gray-600 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
          >
            {previewUrl ? (
              <div className="relative w-full h-full">
                <img                  src={previewUrl}                  alt="Twitter Preview"                  className="w-full h-full object-cover"                  onError={() => {                    if (!imageError) {                      setImageError(true);                      setPreviewUrl(createFallbackImage(1200, 600, 'Twitter Image'));                    }                  }}                  onLoad={() => setImageError(false)}                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                  <div className="text-center text-white opacity-0 group-hover:opacity-100">
                    <FiImage className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Click to change image</p>
                    <p className="text-xs text-gray-300 mt-1">Recommended: 1200x600px</p>
        </div>
      </div>
        </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                <div className="text-center">
                  <FiImage className="h-10 w-10 mx-auto mb-3" />
                  <p className="text-lg font-medium">Click to upload Twitter image</p>
                  <p className="text-sm">Recommended size: 1200x600px</p>
                  <p className="text-xs text-gray-500 mt-2">JPG, PNG, GIF up to 5MB</p>
        </div>
      </div>
            )}
      </div>
        ) : (
          // URL Input
      <input
        type="url"
            placeholder="https://example.com/twitter-image.jpg"
        value={formData.image}
            onChange={(e) => {
              setFormData((prev: any) => ({ ...prev, image: e.target.value }));
              setPreviewUrl(e.target.value);
            }}
        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
      />
        )}

        {/* Upload Info */}
        {selectedFile && uploadMethod === 'upload' && (
          <div className="mt-2 p-3 bg-gray-700 rounded border border-gray-600">
            <div className="flex items-center text-green-400 text-sm">
              <FiImage className="mr-2 h-4 w-4" />
              <span>Selected: {selectedFile.name}</span>
              <span className="ml-2 text-gray-400">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
      </div>
          </div>
        )}

        {/* Image Preview for URL */}
        {uploadMethod === 'url' && previewUrl && (
          <div className="mt-3">
            <p className="text-xs text-gray-400 mb-2">Preview:</p>
            <img              src={previewUrl}              alt="Twitter Preview"              className="w-full max-w-md h-24 object-cover rounded border border-gray-600"              onError={() => {                if (!imageError) {                  setImageError(true);                  setPreviewUrl(createFallbackImage(1200, 600, 'Invalid URL'));                }              }}            />
          </div>
        )}
      </div>
      
      <div className="flex justify-end space-x-3">
        <button type="button" onClick={onCancel} className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors">Cancel</button>
        <button type="submit" className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors">Save</button>
      </div>
    </form>
  );
}

function StructuredDataForm({ data, onSave, onCancel, addSocialLink, removeSocialLink }: any) {
  const [formData, setFormData] = useState(data);
  const [newSocialLink, setNewSocialLink] = useState('');

  return (
    <form onSubmit={(e) => { e.preventDefault(); onSave(formData); }} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <input
          type="text"
          placeholder="Organization Name (English)"
          value={formData.organizationName.en}
          onChange={(e) => setFormData((prev: any) => ({ ...prev, organizationName: { ...prev.organizationName, en: e.target.value } }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Founding Date"
          value={formData.foundingDate}
          onChange={(e) => setFormData((prev: any) => ({ ...prev, foundingDate: e.target.value }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Social Media Links</label>
        <div className="space-y-2 mb-2">
          {formData.socialMediaLinks.map((link: string, index: number) => (
            <div key={index} className="flex items-center space-x-2">
              <input
                type="url"
                value={link}
                onChange={(e) => {
                  const newLinks = [...formData.socialMediaLinks];
                  newLinks[index] = e.target.value;
                  setFormData((prev: any) => ({ ...prev, socialMediaLinks: newLinks }));
                }}
                className="flex-1 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
              />
              <button
                type="button"
                onClick={() => {
                  const newLinks = formData.socialMediaLinks.filter((_: string, i: number) => i !== index);
                  setFormData((prev: any) => ({ ...prev, socialMediaLinks: newLinks }));
                }}
                className="px-2 py-1 bg-red-600 text-white rounded text-sm"
              >
                Remove
              </button>
            </div>
          ))}
        </div>
        <div className="flex space-x-2">
          <input
            type="url"
            value={newSocialLink}
            onChange={(e) => setNewSocialLink(e.target.value)}
            placeholder="Add social media URL"
            className="flex-1 px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
          />
          <button
            type="button"
            onClick={() => {
              if (newSocialLink.trim()) {
                setFormData((prev: any) => ({
                  ...prev,
                  socialMediaLinks: [...prev.socialMediaLinks, newSocialLink.trim()]
                }));
                setNewSocialLink('');
              }
            }}
            className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm"
          >
            Add Link
          </button>
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        <button type="button" onClick={onCancel} className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors">Cancel</button>
        <button type="submit" className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors">Save</button>
      </div>
    </form>
  );
}

function TechnicalSEOForm({ data, onSave, onCancel }: any) {
  const [formData, setFormData] = useState(data);

  return (
    <form onSubmit={(e) => { e.preventDefault(); onSave(formData); }} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <select
          value={formData.robots}
          onChange={(e) => setFormData((prev: any) => ({ ...prev, robots: e.target.value }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        >
          <option value="index, follow">index, follow</option>
          <option value="noindex, follow">noindex, follow</option>
          <option value="index, nofollow">index, nofollow</option>
          <option value="noindex, nofollow">noindex, nofollow</option>
        </select>
        
        <input
          type="url"
          placeholder="Sitemap URL"
          value={formData.sitemap}
          onChange={(e) => setFormData((prev: any) => ({ ...prev, sitemap: e.target.value }))}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>
      
      <div className="flex justify-end space-x-3">
        <button type="button" onClick={onCancel} className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors">Cancel</button>
        <button type="submit" className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors">Save</button>
      </div>
    </form>
  );
} 