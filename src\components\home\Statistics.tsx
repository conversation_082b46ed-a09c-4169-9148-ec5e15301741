"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from "framer-motion";
import AchievementsBackground from "./AchievementsBackground";
import CountUp from 'react-countup';
import { useLanguage } from '@/contexts/LanguageContext';
import { getIconComponent } from '@/components/admin/IconPicker';

// Custom hook to track element visibility using Intersection Observer
const useIntersectionObserver = (options = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [options]);

  return [ref, isVisible] as [React.RefObject<HTMLDivElement>, boolean];
};

// Achievement Item Component to avoid hooks in map
const AchievementItem = ({ achievement, index, parseValue, iconMapping }: {
  achievement: AchievementItem;
  index: number;
  parseValue: (value: string) => { prefix: string; numeric: number; suffix: string };
  iconMapping: { [key: string]: JSX.Element };
}) => {
  // Use custom hook for each stat
  const [counterRef, counterVisible] = useIntersectionObserver({
    threshold: 0.3,
    triggerOnce: true
  });

  const parsedValue = parseValue(achievement.achievement_value);

  // Try to get icon from new icon system first, then fallback to old mapping
  const iconFromNewSystem = getIconComponent(achievement.icon_name);
  const iconFromOldSystem = iconMapping[achievement.icon_name] || iconMapping.target;

  // If we have a new system icon, wrap it with proper sizing
  const icon = iconFromNewSystem ? (
    <div className="text-5xl">
      {iconFromNewSystem}
    </div>
  ) : iconFromOldSystem;

  return (
    <motion.div
      key={achievement.id}
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: 0.1 * index + 0.3 }}
      className="group"
    >
      <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 hover:bg-white/15 hover:border-white/30 hover:translate-y-[-5px]">
        <div className="relative z-10">
          <div className="flex justify-center mb-6 text-[rgb(var(--color-primary))] *">
              {icon}
          </div>
          <div ref={counterRef} className="text-5xl font-bold mb-3 text-white text-center">
            {parsedValue.prefix && <span>{parsedValue.prefix}</span>}
            <span style={{ display: 'inline-block' }}>
              {counterVisible ? (
                <CountUp
                  end={parsedValue.numeric}
                  duration={2.5}
                  separator=","
                />
              ) : 0}
            </span>
            {parsedValue.suffix && <span>{parsedValue.suffix}</span>}
          </div>
          <div className="text-white font-medium text-center">{achievement.title}</div>
        </div>

        {/* Subtle background glow */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
      </div>
    </motion.div>
  );
};

// Split text animation function
const SplitText = ({ text, className }: { text: string, className?: string }) => {
  // Check if text contains Arabic characters
  const isArabic = /[\u0600-\u06FF]/.test(text);

  if (isArabic) {
    // For Arabic text, split by words to preserve letter connections
    const words = text.split(" ");
    return (
      <span className={className}>
        {words.map((word, wordIndex) => (
          <motion.span
            key={wordIndex}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              delay: 0.1 * wordIndex,
              ease: [0.215, 0.61, 0.355, 1]
            }}
            className="inline-block"
          >
            {word}
            {wordIndex < words.length - 1 && "\u00A0"}
          </motion.span>
        ))}
      </span>
    );
  } else {
    // For English text, split by characters for the original effect
    return (
      <span className={className}>
        {text.split("").map((char, index) => (
          <motion.span
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              duration: 0.6,
              delay: 0.05 * index,
              ease: [0.215, 0.61, 0.355, 1]
            }}
            className="inline-block"
          >
            {char === " " ? "\u00A0" : char}
          </motion.span>
        ))}
      </span>
    );
  }
};

// Icon mapping for achievements - matching API icon names
const iconMapping: { [key: string]: JSX.Element } = {
  // Common achievement icons from API documentation
  trophy: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
    </svg>
  ),
  users: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
    </svg>
  ),
  star: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
    </svg>
  ),
  calendar: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
  ),
  "check-circle": (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  heart: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
    </svg>
  ),
  globe: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  shield: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  ),
  // Legacy icon names for backward compatibility
  clock: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  home: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
    </svg>
  ),
  smile: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  dollar: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  target: (
    <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
    </svg>
  )
};

// Achievements content interface - matching API structure
interface AchievementsContent {
  id: number;
  badge: string;
  title: string;
  supporting_text: string;
  created_at: string;
  updated_at: string;
}

interface AchievementItem {
  id: number;
  icon_name: string;
  achievement_value: string;
  title: string;
  created_at: string;
  updated_at: string;
}

const Statistics = () => {
  const { locale } = useLanguage();
  const sectionRef = useRef<HTMLDivElement>(null);

  // API state management - following Hero.tsx pattern
  const [achievementsContent, setAchievementsContent] = useState<AchievementsContent | null>(null);
  const [achievementItems, setAchievementItems] = useState<AchievementItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Add parallax scrolling effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const contentY = useTransform(scrollYProgress, [0, 1], [50, -50]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  // Fetch achievements content and items from API
  useEffect(() => {
    const fetchAchievementsData = async () => {
      try {
        setIsLoading(true);
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

        // Fetch section content
        const sectionEndpoint = `/api/home-page/achievements/${locale}/`;
        console.log('🔄 Fetching achievements section from:', `${apiBaseUrl}${sectionEndpoint}`);

        const sectionResponse = await fetch(`${apiBaseUrl}${sectionEndpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Fetch achievement items
        const itemsEndpoint = `/api/home-page/achievements/items/${locale}/`;
        console.log('🔄 Fetching achievement items from:', `${apiBaseUrl}${itemsEndpoint}`);

        const itemsResponse = await fetch(`${apiBaseUrl}${itemsEndpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const [sectionData, itemsData] = await Promise.all([
          sectionResponse.json(),
          itemsResponse.json()
        ]);

        console.log('📥 Achievements section response:', sectionData);
        console.log('📥 Achievement items response:', itemsData);

        if (sectionData.success && sectionData.data) {
          setAchievementsContent(sectionData.data);
        } else {
          console.error('Failed to fetch achievements section:', sectionData.message);
        }

        if (itemsData.success && itemsData.data) {
          setAchievementItems(itemsData.data);
        } else {
          console.error('Failed to fetch achievement items:', itemsData.message);
        }
      } catch (error) {
        console.error('Error fetching achievements data:', error);
        // Keep state as null/empty to fall back to static content
      } finally {
        setIsLoading(false);
      }
    };

    fetchAchievementsData();
  }, [locale]);

  // Get content with fallback to static content - following Hero.tsx pattern
  const getContent = () => {
    // Fallback to static content
    const fallbackAchievements = [
      {
        id: 1,
        icon_name: "clock",
        achievement_value: "15",
        title: locale === 'ar' ? "سنة من الخبرة" : "Years of Experience",
        created_at: "",
        updated_at: ""
      },
      {
        id: 2,
        icon_name: "home",
        achievement_value: "50",
        title: locale === 'ar' ? "مشروع مكتمل" : "Completed Projects",
        created_at: "",
        updated_at: ""
      },
      {
        id: 3,
        icon_name: "smile",
        achievement_value: "500+",
        title: locale === 'ar' ? "عميل راضي" : "Satisfied Clients",
        created_at: "",
        updated_at: ""
      },
      {
        id: 4,
        icon_name: "dollar",
        achievement_value: "$2B",
        title: locale === 'ar' ? "قيمة المشاريع" : "Project Value",
        created_at: "",
        updated_at: ""
      }
    ];

    const fallbackContent = {
      badge: locale === 'ar' ? "سجلنا المثبت" : "Our Track Record",
      title: locale === 'ar' ? "إنجازاتنا" : "Our Achievements",
      description: locale === 'ar'
        ? "مزايا كابيتال أنشأت سجلاً قوٍ من التطويرات الناجحة والعملاء الراضين على مر السنين"
        : "Mazaya Capital has established a strong track record of successful developments and satisfied clients over the years",
      achievements: fallbackAchievements
    };

    // Use API data if available, otherwise fallback
    const sectionData = achievementsContent ? {
      badge: achievementsContent.badge || fallbackContent.badge,
      title: achievementsContent.title || fallbackContent.title,
      description: achievementsContent.supporting_text || fallbackContent.description,
    } : {
      badge: fallbackContent.badge,
      title: fallbackContent.title,
      description: fallbackContent.description,
    };

    const itemsData = achievementItems && achievementItems.length > 0
      ? achievementItems
      : fallbackAchievements;

    return {
      ...sectionData,
      achievements: itemsData
    };
  };

  const content = getContent();

  // Parse value to extract numeric part, prefix, and suffix
  const parseValue = (value: string) => {
    // Handle different patterns: +70, $100, 50%, 2B, etc.
    const patterns = [
      // Pattern 1: +number (e.g., +70)
      /^(\+)(\d+(?:\.\d+)?)([A-Za-z%]*)$/,
      // Pattern 2: $number (e.g., $100)
      /^(\$)(\d+(?:\.\d+)?)([A-Za-z+%]*)$/,
      // Pattern 3: number+ (e.g., 500+)
      /^()(\d+(?:\.\d+)?)(\+)$/,
      // Pattern 4: numberSuffix (e.g., 2B, 50%)
      /^()(\d+(?:\.\d+)?)([A-Za-z%]+)$/,
      // Pattern 5: plain number (e.g., 70)
      /^()(\d+(?:\.\d+)?)()$/
    ];

    for (const pattern of patterns) {
      const match = value.match(pattern);
      if (match) {
        return {
          prefix: match[1] || '',
          numeric: parseFloat(match[2]),
          suffix: match[3] || ''
        };
      }
    }

    // If no pattern matches, treat the whole value as suffix with 0 numeric
    return { prefix: '', numeric: 0, suffix: value };
  };

  const [stars, setStars] = useState<{ id: number; size: number; top: number; left: number; delay: number; duration: number }[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);

  // Generate stars on client-side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const newStars = Array.from({ length: 50 }).map((_, index) => ({
        id: index,
        size: Math.random() * 2 + 0.5,
        top: Math.random() * 100,
        left: Math.random() * 100,
        delay: Math.random() * 5,
        duration: Math.floor(Math.random() * 4) + 3
      }));

      setStars(newStars);
      setIsHydrated(true);
    }
  }, []);

  return (
    <section
      ref={sectionRef}
      className="relative py-28 md:py-32 overflow-hidden bg-[#0A1429]"
    >
      {/* Architectural background */}
      <div className="absolute inset-0 z-0">
        <AchievementsBackground />
      </div>

      {/* Architectural grid overlay */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <div className="h-full w-full grid grid-cols-6 lg:grid-cols-12">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="border-s border-white/5 h-full">
              {i === 0 && <div className="border-e border-white/5 h-full w-full"></div>}
            </div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="col-span-full border-t border-white/5 h-0"></div>
          ))}
        </div>
      </div>

      {/* Subtle background patterns */}
      <div className="absolute top-0 start-0 w-full h-full opacity-10 z-5">
        <div className="absolute top-0 end-0 w-[600px] h-[600px] bg-gradient-to-b from-blue-500/30 to-transparent rounded-full filter blur-[120px] -translate-y-1/2 translate-x-1/3"></div>
        <div className="absolute bottom-0 start-0 w-[500px] h-[500px] bg-gradient-to-t from-purple-600/20 to-transparent rounded-full filter blur-[100px] translate-y-1/3 -translate-x-1/4"></div>
      </div>

      {/* Stars effect - client-side only rendering */}
      {isHydrated && (
        <div className="star-field">
          {stars.map((star) => (
            <div
              key={`star-${star.id}`}
              className="star"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                top: `${star.top}%`,
                left: `${star.left}%`,
                animationDuration: `${star.duration}s`,
                animationDelay: `${star.delay}s`
              }}
            />
          ))}
        </div>
      )}

      <div className="container relative z-20 mx-auto px-4">
        <motion.div style={{ opacity }} className="flex flex-col">
          {/* Section Header */}
          <motion.div
            className="text-center mb-16"
            style={{ y: contentY }}
          >
            {/* Loading state for badge - following Hero.tsx pattern */}
            {isLoading ? (
              <div className="inline-block h-8 w-32 bg-white/20 rounded-full animate-pulse mb-6"></div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6"
              >
                <span
                  className={`text-sm font-medium text-white ${
                    locale === 'ar' ? 'font-arabic' : 'tracking-wide'
                  }`}
                  dir={locale === 'ar' ? 'rtl' : 'ltr'}
                >
                  {content.badge}
                </span>
              </motion.div>
            )}

            {/* Loading state for title - following Hero.tsx pattern */}
            {isLoading ? (
              <div className="h-12 w-80 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded mx-auto animate-pulse mb-6"></div>
            ) : (
              <h2 className={`text-4xl md:text-5xl font-bold mb-6 leading-tight ${locale === 'ar' ? 'text-center' : ''}`}>
                <SplitText
                  text={content.title}
                  className={`block text-white ${locale === 'ar' ? 'font-arabic' : ''}`}
                />
              </h2>
            )}

            {/* Loading state for description - following Hero.tsx pattern */}
            {isLoading ? (
              <div className="max-w-3xl mx-auto space-y-2">
                <div className="h-4 w-full bg-white/20 rounded animate-pulse"></div>
                <div className="h-4 w-3/4 bg-white/20 rounded animate-pulse mx-auto"></div>
              </div>
            ) : (
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className={`text-xl text-gray-300 max-w-3xl mx-auto ${
                  locale === 'ar' ? 'text-center font-arabic leading-relaxed' : ''
                }`}
                dir={locale === 'ar' ? 'rtl' : 'ltr'}
              >
                {content.description}
              </motion.p>
            )}
          </motion.div>

          {/* Loading state for achievements grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="h-48 bg-white/10 rounded-xl animate-pulse"></div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8"
            >
              {content.achievements && content.achievements.map((achievement, index) => (
                <AchievementItem
                  key={achievement.id}
                  achievement={achievement}
                  index={index}
                  parseValue={parseValue}
                  iconMapping={iconMapping}
                />
              ))}
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default Statistics;

