"use client";

import translations from './translations';

interface Translations {
  [locale: string]: {
    [namespace: string]: {
      [key: string]: any; // Changed from string to any to support nested objects
    };
  };
}

/**
 * Simple translation function that retrieves translations from our translation files
 * 
 * @param key The translation key to lookup (supports nested keys like 'missionVision.mission')
 * @param locale The current locale
 * @param namespace The namespace to use (defaults to 'common')
 * @returns The translated string or the key if not found
 */
export function t(key: string, locale: string = 'en', namespace: string = 'common'): string {
  const allTranslations = translations as Translations;
  
  try {
    // Get the namespace translations
    const namespaceTranslations = allTranslations?.[locale]?.[namespace];
    if (!namespaceTranslations) {
      console.warn(`Translation namespace "${namespace}" not found for locale "${locale}"`);
      return key;
    }

    // Handle nested keys (e.g., 'missionVision.mission')
    const keyParts = key.split('.');
    let result: any = namespaceTranslations;
    
    for (const part of keyParts) {
      if (result && typeof result === 'object' && part in result) {
        result = result[part];
      } else {
        console.warn(`Translation key "${key}" not found for locale "${locale}"`);
        return key; // Return the key if not found
      }
    }
    
    // Ensure we return a string
    return typeof result === 'string' ? result : key;
  } catch (error) {
    console.error(`Translation error for key "${key}"`, error);
    return key;
  }
}

/**
 * Function to load translations for a specific locale and namespace
 * 
 * @param locale The locale to load
 * @param namespace The namespace to load
 * @returns The translations object or null if not found
 */
export async function loadTranslations(locale: string = 'en', namespace: string = 'common') {
  try {
    const translations = await import(`../../public/locales/${locale}/${namespace}.json`);
    return translations.default || translations;
  } catch (error) {
    console.error(`Failed to load translations for ${locale}/${namespace}`, error);
    return null;
  }
}