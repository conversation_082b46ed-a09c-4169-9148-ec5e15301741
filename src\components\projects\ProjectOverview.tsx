import { forwardRef } from "react";

interface ProjectOverviewProps {
  project: any;
}

const ProjectOverview = forwardRef<HTMLElement, ProjectOverviewProps>(({ project }, ref) => {
  return (
    <section ref={ref} id="overview" className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Premier Luxury Living at {project.title}</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Experience the epitome of luxury living in one of Dubai's most prestigious addresses.</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        <div>
          <div className="prose prose-lg max-w-none">
            <p className="text-gray-700 mb-6">
              {project.longDescription} Designed by world-renowned architects, each residence is meticulously crafted to provide an unparalleled living experience. Floor-to-ceiling windows capture breathtaking views, while premium finishes and thoughtful layouts create a sense of sophisticated elegance.
            </p>
          </div>
          
          {/* Key Features */}
          <div className="mt-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
              {project.features.map((feature: string, index: number) => (
                <div key={index} className="flex items-start">
                  <div className="mt-1">
                    <div className="w-6 h-6 rounded-full bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] flex items-center justify-center">
                      <svg className="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                  <span className="text-gray-700 ms-3">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div>
          {/* Project Highlights */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Project Highlights</h3>
            
            <div className="grid grid-cols-2 gap-6">
              <div className="flex items-start">
                <div className="w-12 h-12 rounded-lg bg-[#00C2FF]/10 flex items-center justify-center me-4">
                  <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-[#00C2FF]">45</div>
                  <div className="text-sm text-gray-500">Stories</div>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-12 h-12 rounded-lg bg-[#00C2FF]/10 flex items-center justify-center me-4">
                  <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-[#00C2FF]">350</div>
                  <div className="text-sm text-gray-500">Luxury Units</div>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-12 h-12 rounded-lg bg-[#00C2FF]/10 flex items-center justify-center me-4">
                  <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-[#00C2FF]">2021</div>
                  <div className="text-sm text-gray-500">Completion Year</div>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="w-12 h-12 rounded-lg bg-[#00C2FF]/10 flex items-center justify-center me-4">
                  <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <div className="text-3xl font-bold text-[#00C2FF]">8%</div>
                  <div className="text-sm text-gray-500">Avg. ROI</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

ProjectOverview.displayName = "ProjectOverview";

export default ProjectOverview;