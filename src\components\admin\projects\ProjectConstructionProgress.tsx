import React, { useState, useEffect } from 'react';
import { FiPlus, FiX, FiMove, FiEdit } from 'react-icons/fi';

interface ProjectConstructionProgressProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectConstructionProgress: React.FC<ProjectConstructionProgressProps> = ({
  project,
  language,
  setProject
}) => {
  // Get current section info based on selected language
  const currentConstructionProgress = project.localizedContent?.[language]?.constructionProgress || {
    title: '',
    description: '',
    percentComplete: 85,
    timeline: []
  };
  
  // State for the current language's section info
  const [constructionTitle, setConstructionTitle] = useState(currentConstructionProgress.title);
  const [constructionDescription, setConstructionDescription] = useState(currentConstructionProgress.description);
  const [percentComplete, setPercentComplete] = useState(currentConstructionProgress.percentComplete || 85);
  
  // State for timeline items
  const [newTimelineItem, setNewTimelineItem] = useState({
    title: '',
    status: 'In Progress',
    description: '',
    date: ''
  });
  const [constructionTimeline, setConstructionTimeline] = useState(currentConstructionProgress.timeline || []);
  const [draggedTimelineItem, setDraggedTimelineItem] = useState<number | null>(null);
  const [editingTimelineIndex, setEditingTimelineIndex] = useState<number | null>(null);
  const [editingTimelineItem, setEditingTimelineItem] = useState({
    title: '',
    status: 'In Progress',
    description: '',
    date: ''
  });
  
  // Update the form values when language changes
  useEffect(() => {
    const progress = project.localizedContent?.[language]?.constructionProgress || {
      title: '',
      description: '',
      percentComplete: 85,
      timeline: []
    };
    
    setConstructionTitle(progress.title || '');
    setConstructionDescription(progress.description || '');
    setPercentComplete(progress.percentComplete || 85);
    setConstructionTimeline(progress.timeline || []);
  }, [language, project.localizedContent]);
  
  // Helper to update the project state with new construction progress values
  const updateProjectState = (newValues: any) => {
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          constructionProgress: {
            ...(project.localizedContent?.[language]?.constructionProgress || {}),
            ...newValues,
            timeline: constructionTimeline
          }
        }
      }
    });
  };
  
  // Direct handlers for input fields
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setConstructionTitle(newTitle);
    
    // Immediately update project state
    updateProjectState({
      title: newTitle,
      description: constructionDescription,
      percentComplete: percentComplete
    });
  };
  
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setConstructionDescription(newDescription);
    
    // Immediately update project state
    updateProjectState({
      title: constructionTitle,
      description: newDescription,
      percentComplete: percentComplete
    });
  };
  
  const handlePercentCompleteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPercent = Number(e.target.value);
    setPercentComplete(newPercent);
    
    // Immediately update project state
    updateProjectState({
      title: constructionTitle,
      description: constructionDescription,
      percentComplete: newPercent
    });
  };
  
  // Handle timeline item changes
  const handleNewTimelineItemChange = (field: string, value: string) => {
    setNewTimelineItem(prev => {
      const updated = { ...prev, [field]: value };
      return updated;
    });
  };
  
  // Function to add a new timeline item
  const addTimelineItem = () => {
    if (!newTimelineItem.title.trim() || !newTimelineItem.description.trim() || !newTimelineItem.date.trim()) return;
    
    const updatedTimeline = [...constructionTimeline, newTimelineItem];
    setConstructionTimeline(updatedTimeline);
    
    // Update project state with the new timeline
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          constructionProgress: {
            ...(project.localizedContent?.[language]?.constructionProgress || {}),
            title: constructionTitle,
            description: constructionDescription,
            percentComplete: percentComplete,
            timeline: updatedTimeline
          }
        }
      }
    });
    
    // Reset new timeline item form
    setNewTimelineItem({
      title: '',
      status: 'In Progress',
      description: '',
      date: ''
    });
  };
  
  // Edit timeline item functions
  const startEditTimelineItem = (index: number) => {
    setEditingTimelineIndex(index);
    setEditingTimelineItem({...constructionTimeline[index]});
  };
  
  const handleEditingTimelineItemChange = (field: string, value: string) => {
    setEditingTimelineItem(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const saveTimelineItemEdit = () => {
    if (editingTimelineIndex === null) return;
    
    const updatedTimeline = [...constructionTimeline];
    updatedTimeline[editingTimelineIndex] = editingTimelineItem;
    
    setConstructionTimeline(updatedTimeline);
    
    // Update project state with the updated timeline
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          constructionProgress: {
            ...(project.localizedContent?.[language]?.constructionProgress || {}),
            title: constructionTitle,
            description: constructionDescription,
            percentComplete: percentComplete,
            timeline: updatedTimeline
          }
        }
      }
    });
    
    // Reset editing state
    setEditingTimelineIndex(null);
    setEditingTimelineItem({
      title: '',
      status: 'In Progress',
      description: '',
      date: ''
    });
  };
  
  // Function to remove a timeline item
  const removeTimelineItem = (index: number) => {
    const updatedTimeline = [...constructionTimeline];
    updatedTimeline.splice(index, 1);
    
    setConstructionTimeline(updatedTimeline);
    
    // Update project state with the timeline minus the removed item
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          constructionProgress: {
            ...(project.localizedContent?.[language]?.constructionProgress || {}),
            title: constructionTitle,
            description: constructionDescription,
            percentComplete: percentComplete,
            timeline: updatedTimeline
          }
        }
      }
    });
  };
  
  // Functions for timeline items drag and drop
  const handleTimelineItemDragStart = (index: number) => {
    setDraggedTimelineItem(index);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };
  
  const handleTimelineItemDrop = (dropIndex: number) => {
    if (draggedTimelineItem === null || draggedTimelineItem === dropIndex) return;
    
    const updatedTimeline = [...constructionTimeline];
    const draggedItem = updatedTimeline[draggedTimelineItem];
    
    // Remove the dragged item
    updatedTimeline.splice(draggedTimelineItem, 1);
    
    // Add it at the new position
    updatedTimeline.splice(dropIndex, 0, draggedItem);
    
    // Update state
    setConstructionTimeline(updatedTimeline);
    
    // Update project state with the reordered timeline
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent?.[language],
          constructionProgress: {
            ...(project.localizedContent?.[language]?.constructionProgress || {}),
            title: constructionTitle,
            description: constructionDescription,
            percentComplete: percentComplete,
            timeline: updatedTimeline
          }
        }
      }
    });
    
    setDraggedTimelineItem(null);
  };

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Construction Progress' : 'تقدم البناء'}
      </h2>
      
      {/* Construction Progress Section */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Progress Overview' : 'نظرة عامة على التقدم'}
        </h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Title' : 'عنوان القسم'}
            </label>
            <input
              type="text"
              value={constructionTitle}
              onChange={handleTitleChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "Construction Progress" : "تقدم البناء"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Section Description' : 'وصف القسم'}
            </label>
            <textarea
              value={constructionDescription}
              onChange={handleDescriptionChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={2}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "Track the development journey from foundation to completion." 
                : "تتبع رحلة التطوير من الأساس إلى الانتهاء."}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Percent Complete' : 'النسبة المكتملة'}
            </label>
            <div className="flex items-center">
              <input
                type="number"
                min="0"
                max="100"
                value={percentComplete}
                onChange={handlePercentCompleteChange}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                placeholder="85"
              />
              <span className="ml-2 text-white">%</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Construction Timeline */}
      <div className="mb-6">
        <h3 className="text-md font-medium text-gray-300 mb-4">
          {language === 'en' ? 'Construction Timeline' : 'جدول البناء'}
        </h3>
        
        <div className="space-y-4 mb-4">
          {/* New Timeline Item Form */}
          <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600">
            <h4 className="text-sm font-medium text-white mb-3">
              {language === 'en' ? 'Add New Timeline Item' : 'إضافة عنصر جديد للجدول الزمني'}
            </h4>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Milestone Title' : 'عنوان المرحلة'}
                </label>
                <input
                  type="text"
                  value={newTimelineItem.title}
                  onChange={(e) => handleNewTimelineItemChange('title', e.target.value)}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={language === 'en' ? "e.g. Foundation Completed" : "مثال: اكتمل الأساس"}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Status' : 'الحالة'}
                </label>
                <select
                  value={newTimelineItem.status}
                  onChange={(e) => handleNewTimelineItemChange('status', e.target.value)}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                >
                  <option value="Completed">{language === 'en' ? 'Completed' : 'مكتمل'}</option>
                  <option value="In Progress">{language === 'en' ? 'In Progress' : 'جاري العمل'}</option>
                  <option value="Upcoming">{language === 'en' ? 'Upcoming' : 'قادم'}</option>
                  <option value="Available">{language === 'en' ? 'Available' : 'متاح'}</option>
                </select>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Description' : 'الوصف'}
              </label>
              <textarea
                value={newTimelineItem.description}
                onChange={(e) => handleNewTimelineItemChange('description', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                rows={2}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={language === 'en' ? "Describe this milestone" : "وصف هذه المرحلة"}
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Date' : 'التاريخ'}
              </label>
              <input
                type="text"
                value={newTimelineItem.date}
                onChange={(e) => handleNewTimelineItemChange('date', e.target.value)}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                placeholder={language === 'en' ? "e.g. January 2023" : "مثال: يناير 2023"}
              />
            </div>
            
            <button
              type="button"
              onClick={addTimelineItem}
              disabled={!newTimelineItem.title.trim() || !newTimelineItem.description.trim() || !newTimelineItem.date.trim()}
              className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                newTimelineItem.title.trim() && newTimelineItem.description.trim() && newTimelineItem.date.trim()
                ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
                : 'bg-gray-600 cursor-not-allowed'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]`}
            >
              <FiPlus className="-ml-1 mr-2 h-4 w-4" />
              {language === 'en' ? 'Add Timeline Item' : 'إضافة عنصر'}
            </button>
          </div>
          
          {/* Timeline Items List */}
          {editingTimelineIndex !== null ? (
            // Editing form for timeline item
            <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600 mb-4">
              <h4 className="text-sm font-medium text-white mb-3">
                {language === 'en' ? 'Edit Timeline Item' : 'تعديل عنصر الجدول الزمني'}
              </h4>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    {language === 'en' ? 'Milestone Title' : 'عنوان المرحلة'}
                  </label>
                  <input
                    type="text"
                    value={editingTimelineItem.title}
                    onChange={(e) => handleEditingTimelineItemChange('title', e.target.value)}
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                    placeholder={language === 'en' ? "e.g. Foundation Completed" : "مثال: اكتمل الأساس"}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    {language === 'en' ? 'Status' : 'الحالة'}
                  </label>
                  <select
                    value={editingTimelineItem.status}
                    onChange={(e) => handleEditingTimelineItemChange('status', e.target.value)}
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  >
                    <option value="Completed">{language === 'en' ? 'Completed' : 'مكتمل'}</option>
                    <option value="In Progress">{language === 'en' ? 'In Progress' : 'جاري العمل'}</option>
                    <option value="Upcoming">{language === 'en' ? 'Upcoming' : 'قادم'}</option>
                    <option value="Available">{language === 'en' ? 'Available' : 'متاح'}</option>
                  </select>
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Description' : 'الوصف'}
                </label>
                <textarea
                  value={editingTimelineItem.description}
                  onChange={(e) => handleEditingTimelineItemChange('description', e.target.value)}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                  rows={2}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={language === 'en' ? "Describe this milestone" : "وصف هذه المرحلة"}
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Date' : 'التاريخ'}
                </label>
                <input
                  type="text"
                  value={editingTimelineItem.date}
                  onChange={(e) => handleEditingTimelineItemChange('date', e.target.value)}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  placeholder={language === 'en' ? "e.g. January 2023" : "مثال: يناير 2023"}
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setEditingTimelineIndex(null);
                    setEditingTimelineItem({
                      title: '',
                      status: 'In Progress',
                      description: '',
                      date: ''
                    });
                  }}
                  className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
                >
                  {language === 'en' ? 'Cancel' : 'إلغاء'}
                </button>
                <button
                  type="button"
                  onClick={saveTimelineItemEdit}
                  disabled={!editingTimelineItem.title.trim() || !editingTimelineItem.description.trim() || !editingTimelineItem.date.trim()}
                  className={`px-4 py-2 text-sm font-medium text-white rounded ${
                    editingTimelineItem.title.trim() && editingTimelineItem.description.trim() && editingTimelineItem.date.trim()
                    ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
                    : 'bg-gray-600 cursor-not-allowed'
                  }`}
                >
                  {language === 'en' ? 'Save Changes' : 'حفظ التغييرات'}
                </button>
              </div>
            </div>
          ) : null}
          
          {constructionTimeline.length > 0 ? (
            <div className="bg-gray-700 rounded-md overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-800">
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">
                      {language === 'en' ? 'Milestone' : 'المرحلة'}
                    </th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">
                      {language === 'en' ? 'Status' : 'الحالة'}
                    </th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">
                      {language === 'en' ? 'Date' : 'التاريخ'}
                    </th>
                    <th className="px-4 py-3 text-left text-gray-300 font-medium">
                      {language === 'en' ? 'Actions' : 'الإجراءات'}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-600">
                  {constructionTimeline.map((item: any, index: number) => (
                    <tr 
                      key={index}
                      className={draggedTimelineItem === index ? 'opacity-50 bg-gray-600' : 'bg-gray-700'}
                      draggable={true}
                      onDragStart={() => handleTimelineItemDragStart(index)}
                      onDragOver={handleDragOver}
                      onDrop={() => handleTimelineItemDrop(index)}
                    >
                      <td className="px-4 py-3 text-white">
                        <div className="font-medium">{item.title}</div>
                        <div className="text-xs text-gray-300 truncate max-w-[200px]">{item.description}</div>
                      </td>
                      <td className="px-4 py-3">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.status === 'Completed' ? 'bg-green-900/30 text-green-300' :
                          item.status === 'In Progress' ? 'bg-blue-900/30 text-blue-300' :
                          'bg-yellow-900/30 text-yellow-300'
                        }`}>
                          {language === 'ar' ? 
                            (item.status === 'Completed' ? 'مكتمل' :
                             item.status === 'In Progress' ? 'جاري العمل' :
                             item.status === 'Upcoming' ? 'قادم' :
                             item.status === 'Available' ? 'متاح' : item.status)
                           : item.status
                          }
                        </span>
                      </td>
                      <td className="px-4 py-3 text-gray-300">{item.date}</td>
                      <td className="px-4 py-3">
                        <div className="flex items-center space-x-2">
                          {constructionTimeline.length > 1 && (
                            <button className="text-gray-400 cursor-move">
                              <FiMove className="h-4 w-4" />
                            </button>
                          )}
                          <button 
                            onClick={() => startEditTimelineItem(index)}
                            className="text-gray-400 hover:text-blue-500"
                          >
                            <FiEdit className="h-4 w-4" />
                          </button>
                          <button 
                            onClick={() => removeTimelineItem(index)}
                            className="text-gray-400 hover:text-red-500"
                          >
                            <FiX className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-sm text-gray-400 italic">
              {language === 'en' 
                ? 'No timeline items added yet. Add milestones to show construction progress.'
                : 'لم تتم إضافة عناصر الجدول الزمني بعد. أضف مراحل لإظهار تقدم البناء.'}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectConstructionProgress; 