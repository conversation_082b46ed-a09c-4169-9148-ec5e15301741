import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        white: "rgb(var(--color-background) / <alpha-value>)",
        gray: {
          100: "rgb(var(--color-text-secondary) / <alpha-value>)",
          200: "rgb(var(--color-text-secondary) / <alpha-value>)",
          300: "rgb(var(--color-text-secondary) / <alpha-value>)",
          400: "rgb(var(--color-text-secondary) / <alpha-value>)",
          500: "rgb(var(--color-text-secondary) / <alpha-value>)",
          600: "rgb(var(--color-text-secondary) / <alpha-value>)",
          700: "rgb(var(--color-text-secondary) / <alpha-value>)",
          800: "rgb(var(--color-text-secondary) / <alpha-value>)",
          900: "rgb(var(--color-text-secondary) / <alpha-value>)",
        },
        primary: {
          DEFAULT: "rgb(var(--color-primary) / <alpha-value>)",
          hover: "rgb(var(--color-primary-hover) / <alpha-value>)",
        },
        secondary: "rgb(var(--color-secondary) / <alpha-value>)",
        background: "rgb(var(--color-background) / <alpha-value>)",
        text: {
          DEFAULT: "rgb(var(--color-text) / <alpha-value>)",
          secondary: "rgb(var(--color-text-secondary) / <alpha-value>)",
        },
      },
      backgroundImage: {
        'brand-gradient': 'linear-gradient(90deg, rgb(var(--color-primary)) 0%, rgb(var(--color-secondary)) 100%)',
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
      },
      animation: {
        'border-pulse': 'border-pulse 3s ease-in-out infinite',
        'pulse-slow': 'pulse-slow 4s ease-in-out infinite',
        'gradient-x': 'gradient-x 3s ease infinite',
      },
      keyframes: {
        'border-pulse': {
          '0%': { opacity: '0.3' },
          '50%': { opacity: '0.8' },
          '100%': { opacity: '0.3' },
        },
        'pulse-slow': {
          '0%': { opacity: '0', transform: 'scale(0.95)' },
          '50%': { opacity: '0.6', transform: 'scale(1.05)' },
          '100%': { opacity: '0', transform: 'scale(0.95)' },
        },
        'gradient-x': {
          '0%, 100%': {
            'background-position': '0% 50%',
            'background-size': '200% 200%'
          },
          '50%': {
            'background-position': '100% 50%',
            'background-size': '200% 200%'
          },
        },
      },
      textShadow: {
        sm: '0 0 2px var(--tw-shadow-color)',
        DEFAULT: '0 0 4px var(--tw-shadow-color)',
        lg: '0 0 8px var(--tw-shadow-color)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/line-clamp'),
    function ({ matchUtilities, theme }: any) {
      matchUtilities(
        {
          'text-shadow': (value: string) => ({
            textShadow: value,
          }),
        },
        { values: theme('textShadow') }
      );
    },
  ],
};

export default config;
export default config;