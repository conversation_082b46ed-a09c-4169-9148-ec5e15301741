"use client";

import React, { useState } from 'react';
import { 
  FiZap, FiDatabase, FiImage, FiGlobe, FiMonitor, 
  FiSettings, FiTrendingUp, FiClock, FiHardDrive,
  FiWifi, FiEye, FiEyeOff, FiAlertCircle, FiCheckCircle
} from 'react-icons/fi';

interface PerformanceSettingsData {
  caching: {
    enabled: boolean;
    strategy: 'memory' | 'redis' | 'file' | 'hybrid';
    duration: {
      pages: number;
      api: number;
      static: number;
      database: number;
    };
    redis: {
      host: string;
      port: number;
      password: string;
      database: number;
    };
    purgeRules: {
      autoOnUpdate: boolean;
      scheduledPurge: boolean;
      purgeFrequency: 'hourly' | 'daily' | 'weekly';
    };
  };
  compression: {
    enabled: boolean;
    algorithm: 'gzip' | 'brotli' | 'deflate';
    level: number;
    minSize: number; // in bytes
    types: string[];
  };
  cdn: {
    enabled: boolean;
    provider: 'cloudflare' | 'aws' | 'azure' | 'google' | 'custom';
    cloudflare: {
      zoneId: string;
      apiKey: string;
      email: string;
    };
    aws: {
      distributionId: string;
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
    };
    azure: {
      profileName: string;
      endpointName: string;
      resourceGroup: string;
      subscriptionId: string;
    };
    google: {
      projectId: string;
      keyFile: string;
    };
    custom: {
      url: string;
      apiKey: string;
    };
    settings: {
      minify: {
        html: boolean;
        css: boolean;
        js: boolean;
      };
      browserCache: number; // in seconds
      edgeCache: number; // in seconds
    };
  };
  imageOptimization: {
    enabled: boolean;
    quality: {
      jpeg: number;
      webp: number;
      png: number;
    };
    formats: {
      webp: boolean;
      avif: boolean;
      jpeg: boolean;
      png: boolean;
      gif: boolean;
      svg: boolean;
    };
    responsive: {
      enabled: boolean;
      breakpoints: number[];
    };
    lazy: {
      enabled: boolean;
      threshold: number; // pixels
    };
    processing: {
      resize: boolean;
      crop: boolean;
      watermark: boolean;
      progressive: boolean;
    };
  };
  database: {
    optimization: {
      enabled: boolean;
      indexOptimization: boolean;
      queryOptimization: boolean;
      connectionPooling: boolean;
    };
    caching: {
      enabled: boolean;
      queryCache: boolean;
      resultCache: boolean;
      cacheDuration: number;
    };
    cleanup: {
      enabled: boolean;
      oldLogs: boolean;
      tempFiles: boolean;
      frequency: 'daily' | 'weekly' | 'monthly';
    };
  };
  realEstate: {
    propertyImages: {
      preload: boolean;
      thumbnailGeneration: boolean;
      virtualTourOptimization: boolean;
    };
    maps: {
      tileCache: boolean;
      vectorOptimization: boolean;
      clusterMarkers: boolean;
    };
    search: {
      indexing: boolean;
      facetedSearch: boolean;
      autoComplete: boolean;
    };
  };
  monitoring: {
    enabled: boolean;
    metrics: {
      pageSpeed: boolean;
      coreWebVitals: boolean;
      serverResponse: boolean;
      databaseQueries: boolean;
    };
    alerts: {
      enabled: boolean;
      thresholds: {
        pageLoadTime: number; // in ms
        serverResponseTime: number; // in ms
        errorRate: number; // percentage
      };
      notifications: {
        email: string[];
        webhook: string;
      };
    };
    reporting: {
      enabled: boolean;
      frequency: 'daily' | 'weekly' | 'monthly';
      recipients: string[];
    };
  };
  advanced: {
    preloading: {
      enabled: boolean;
      criticalResources: boolean;
      nextPagePrediction: boolean;
    };
    serviceWorker: {
      enabled: boolean;
      cacheStrategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
      offlineSupport: boolean;
    };
    http2: {
      enabled: boolean;
      serverPush: boolean;
    };
    security: {
      contentSecurityPolicy: boolean;
      hsts: boolean;
      xssProtection: boolean;
    };
  };
}

interface PerformanceSettingsProps {
  data: PerformanceSettingsData;
  onChange: (data: PerformanceSettingsData) => void;
}

export default function PerformanceSettings({ data, onChange }: PerformanceSettingsProps) {
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({});
  const [newEmail, setNewEmail] = useState('');

  const updateData = (updates: Partial<PerformanceSettingsData>) => {
    onChange({ ...data, ...updates });
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const addEmailRecipient = (type: 'alerts' | 'reporting') => {
    if (newEmail.trim()) {
      if (type === 'alerts') {
        updateData({
          monitoring: {
            ...data.monitoring,
            alerts: {
              ...data.monitoring.alerts,
              notifications: {
                ...data.monitoring.alerts.notifications,
                email: [...data.monitoring.alerts.notifications.email, newEmail.trim()]
              }
            }
          }
        });
      } else {
        updateData({
          monitoring: {
            ...data.monitoring,
            reporting: {
              ...data.monitoring.reporting,
              recipients: [...data.monitoring.reporting.recipients, newEmail.trim()]
            }
          }
        });
      }
      setNewEmail('');
    }
  };

  const removeEmailRecipient = (type: 'alerts' | 'reporting', index: number) => {
    if (type === 'alerts') {
      updateData({
        monitoring: {
          ...data.monitoring,
          alerts: {
            ...data.monitoring.alerts,
            notifications: {
              ...data.monitoring.alerts.notifications,
              email: data.monitoring.alerts.notifications.email.filter((_, i) => i !== index)
            }
          }
        }
      });
    } else {
      updateData({
        monitoring: {
          ...data.monitoring,
          reporting: {
            ...data.monitoring.reporting,
            recipients: data.monitoring.reporting.recipients.filter((_, i) => i !== index)
          }
        }
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Advanced Caching */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiZap className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Advanced Caching System
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Caching</p>
              <p className="text-gray-400 text-sm">Accelerate website performance with intelligent caching</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.caching.enabled}
                onChange={(e) => updateData({
                  caching: { ...data.caching, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.caching.enabled && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Caching Strategy</label>
                <select
                  value={data.caching.strategy}
                  onChange={(e) => updateData({
                    caching: { ...data.caching, strategy: e.target.value as 'memory' | 'redis' | 'file' | 'hybrid' }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="memory">Memory Cache (Fast)</option>
                  <option value="redis">Redis Cache (Scalable)</option>
                  <option value="file">File Cache (Persistent)</option>
                  <option value="hybrid">Hybrid (Best Performance)</option>
                </select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Pages Cache (seconds)</label>
                  <input
                    type="number"
                    value={data.caching.duration.pages}
                    onChange={(e) => updateData({
                      caching: {
                        ...data.caching,
                        duration: { ...data.caching.duration, pages: parseInt(e.target.value) }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    min="60"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">API Cache (seconds)</label>
                  <input
                    type="number"
                    value={data.caching.duration.api}
                    onChange={(e) => updateData({
                      caching: {
                        ...data.caching,
                        duration: { ...data.caching.duration, api: parseInt(e.target.value) }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    min="30"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Static Files (seconds)</label>
                  <input
                    type="number"
                    value={data.caching.duration.static}
                    onChange={(e) => updateData({
                      caching: {
                        ...data.caching,
                        duration: { ...data.caching.duration, static: parseInt(e.target.value) }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    min="3600"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Database (seconds)</label>
                  <input
                    type="number"
                    value={data.caching.duration.database}
                    onChange={(e) => updateData({
                      caching: {
                        ...data.caching,
                        duration: { ...data.caching.duration, database: parseInt(e.target.value) }
                      }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    min="300"
                  />
                </div>
              </div>

              {data.caching.strategy === 'redis' && (
                <div className="p-4 bg-gray-700/30 rounded-lg">
                  <h4 className="text-white font-medium mb-3">Redis Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Host</label>
                      <input
                        type="text"
                        value={data.caching.redis.host}
                        onChange={(e) => updateData({
                          caching: {
                            ...data.caching,
                            redis: { ...data.caching.redis, host: e.target.value }
                          }
                        })}
                        placeholder="localhost"
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Port</label>
                      <input
                        type="number"
                        value={data.caching.redis.port}
                        onChange={(e) => updateData({
                          caching: {
                            ...data.caching,
                            redis: { ...data.caching.redis, port: parseInt(e.target.value) }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Password</label>
                      <div className="relative">
                        <input
                          type={showPasswords['redis'] ? 'text' : 'password'}
                          value={data.caching.redis.password}
                          onChange={(e) => updateData({
                            caching: {
                              ...data.caching,
                              redis: { ...data.caching.redis, password: e.target.value }
                            }
                          })}
                          className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('redis')}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                        >
                          {showPasswords['redis'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Database</label>
                      <input
                        type="number"
                        value={data.caching.redis.database}
                        onChange={(e) => updateData({
                          caching: {
                            ...data.caching,
                            redis: { ...data.caching.redis, database: parseInt(e.target.value) }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        min="0"
                        max="15"
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="p-4 bg-gray-700/30 rounded-lg">
                <h4 className="text-white font-medium mb-3">Cache Purge Rules</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Auto-purge on content update</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.caching.purgeRules.autoOnUpdate}
                        onChange={(e) => updateData({
                          caching: {
                            ...data.caching,
                            purgeRules: { ...data.caching.purgeRules, autoOnUpdate: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Scheduled purge</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.caching.purgeRules.scheduledPurge}
                        onChange={(e) => updateData({
                          caching: {
                            ...data.caching,
                            purgeRules: { ...data.caching.purgeRules, scheduledPurge: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  {data.caching.purgeRules.scheduledPurge && (
                    <div>
                      <select
                        value={data.caching.purgeRules.purgeFrequency}
                        onChange={(e) => updateData({
                          caching: {
                            ...data.caching,
                            purgeRules: { ...data.caching.purgeRules, purgeFrequency: e.target.value as 'hourly' | 'daily' | 'weekly' }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      >
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                      </select>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Advanced Compression */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiHardDrive className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Advanced Compression
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Compression</p>
              <p className="text-gray-400 text-sm">Compress files for faster transfer and reduced bandwidth</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.compression.enabled}
                onChange={(e) => updateData({
                  compression: { ...data.compression, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.compression.enabled && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Compression Algorithm</label>
                  <select
                    value={data.compression.algorithm}
                    onChange={(e) => updateData({
                      compression: { ...data.compression, algorithm: e.target.value as 'gzip' | 'brotli' | 'deflate' }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  >
                    <option value="brotli">Brotli (Best Compression)</option>
                    <option value="gzip">Gzip (Universal)</option>
                    <option value="deflate">Deflate (Legacy)</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Compression Level (1-9)</label>
                  <input
                    type="range"
                    min="1"
                    max="9"
                    value={data.compression.level}
                    onChange={(e) => updateData({
                      compression: { ...data.compression, level: parseInt(e.target.value) }
                    })}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>Fast (1)</span>
                    <span>Current: {data.compression.level}</span>
                    <span>Best (9)</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Min File Size (bytes)</label>
                  <input
                    type="number"
                    value={data.compression.minSize}
                    onChange={(e) => updateData({
                      compression: { ...data.compression, minSize: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    min="100"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">File Types to Compress</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {['text/html', 'text/css', 'application/javascript', 'application/json', 'text/xml', 'image/svg+xml', 'text/plain', 'application/xml'].map((type) => (
                    <label key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={data.compression.types.includes(type)}
                        onChange={(e) => {
                          const types = e.target.checked
                            ? [...data.compression.types, type]
                            : data.compression.types.filter(t => t !== type);
                          updateData({
                            compression: { ...data.compression, types }
                          });
                        }}
                        className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-0"
                      />
                      <span className="text-white text-xs">{type.split('/')[1]?.toUpperCase() || type}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* CDN & Edge Optimization */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiGlobe className="mr-2 h-5 w-5 text-[#00C2FF]" />
          CDN & Edge Optimization
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable CDN</p>
              <p className="text-gray-400 text-sm">Serve content from global edge locations</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.cdn.enabled}
                onChange={(e) => updateData({
                  cdn: { ...data.cdn, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.cdn.enabled && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">CDN Provider</label>
                <select
                  value={data.cdn.provider}
                  onChange={(e) => updateData({
                    cdn: { ...data.cdn, provider: e.target.value as 'cloudflare' | 'aws' | 'azure' | 'google' | 'custom' }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="cloudflare">Cloudflare (Recommended)</option>
                  <option value="aws">AWS CloudFront</option>
                  <option value="azure">Azure CDN</option>
                  <option value="google">Google Cloud CDN</option>
                  <option value="custom">Custom CDN</option>
                </select>
              </div>

              {/* Cloudflare Configuration */}
              {data.cdn.provider === 'cloudflare' && (
                <div className="p-4 bg-gray-700/30 rounded-lg">
                  <h4 className="text-white font-medium mb-3">Cloudflare Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Zone ID</label>
                      <input
                        type="text"
                        value={data.cdn.cloudflare.zoneId}
                        onChange={(e) => updateData({
                          cdn: {
                            ...data.cdn,
                            cloudflare: { ...data.cdn.cloudflare, zoneId: e.target.value }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">API Key</label>
                      <div className="relative">
                        <input
                          type={showPasswords['cloudflare'] ? 'text' : 'password'}
                          value={data.cdn.cloudflare.apiKey}
                          onChange={(e) => updateData({
                            cdn: {
                              ...data.cdn,
                              cloudflare: { ...data.cdn.cloudflare, apiKey: e.target.value }
                            }
                          })}
                          className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('cloudflare')}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                        >
                          {showPasswords['cloudflare'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Email</label>
                      <input
                        type="email"
                        value={data.cdn.cloudflare.email}
                        onChange={(e) => updateData({
                          cdn: {
                            ...data.cdn,
                            cloudflare: { ...data.cdn.cloudflare, email: e.target.value }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* CDN Settings */}
              <div className="p-4 bg-gray-700/30 rounded-lg">
                <h4 className="text-white font-medium mb-3">CDN Settings</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-white text-sm font-medium mb-2">Minification</h5>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="flex items-center justify-between">
                        <span className="text-white text-sm">HTML</span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={data.cdn.settings.minify.html}
                            onChange={(e) => updateData({
                              cdn: {
                                ...data.cdn,
                                settings: {
                                  ...data.cdn.settings,
                                  minify: { ...data.cdn.settings.minify, html: e.target.checked }
                                }
                              }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-white text-sm">CSS</span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={data.cdn.settings.minify.css}
                            onChange={(e) => updateData({
                              cdn: {
                                ...data.cdn,
                                settings: {
                                  ...data.cdn.settings,
                                  minify: { ...data.cdn.settings.minify, css: e.target.checked }
                                }
                              }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                        </label>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-white text-sm">JavaScript</span>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={data.cdn.settings.minify.js}
                            onChange={(e) => updateData({
                              cdn: {
                                ...data.cdn,
                                settings: {
                                  ...data.cdn.settings,
                                  minify: { ...data.cdn.settings.minify, js: e.target.checked }
                                }
                              }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Browser Cache (seconds)</label>
                      <input
                        type="number"
                        value={data.cdn.settings.browserCache}
                        onChange={(e) => updateData({
                          cdn: {
                            ...data.cdn,
                            settings: { ...data.cdn.settings, browserCache: parseInt(e.target.value) }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        min="3600"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Edge Cache (seconds)</label>
                      <input
                        type="number"
                        value={data.cdn.settings.edgeCache}
                        onChange={(e) => updateData({
                          cdn: {
                            ...data.cdn,
                            settings: { ...data.cdn.settings, edgeCache: parseInt(e.target.value) }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        min="7200"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Advanced Image Optimization */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiImage className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Advanced Image Optimization
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Image Optimization</p>
              <p className="text-gray-400 text-sm">Optimize property images for faster loading</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.imageOptimization.enabled}
                onChange={(e) => updateData({
                  imageOptimization: { ...data.imageOptimization, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.imageOptimization.enabled && (
            <div className="space-y-4">
              <div>
                <h4 className="text-white font-medium mb-3">Quality Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">JPEG Quality (1-100)</label>
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={data.imageOptimization.quality.jpeg}
                      onChange={(e) => updateData({
                        imageOptimization: {
                          ...data.imageOptimization,
                          quality: { ...data.imageOptimization.quality, jpeg: parseInt(e.target.value) }
                        }
                      })}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="text-center text-xs text-gray-400 mt-1">
                      Current: {data.imageOptimization.quality.jpeg}%
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">WebP Quality (1-100)</label>
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={data.imageOptimization.quality.webp}
                      onChange={(e) => updateData({
                        imageOptimization: {
                          ...data.imageOptimization,
                          quality: { ...data.imageOptimization.quality, webp: parseInt(e.target.value) }
                        }
                      })}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="text-center text-xs text-gray-400 mt-1">
                      Current: {data.imageOptimization.quality.webp}%
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">PNG Quality (1-100)</label>
                    <input
                      type="range"
                      min="1"
                      max="100"
                      value={data.imageOptimization.quality.png}
                      onChange={(e) => updateData({
                        imageOptimization: {
                          ...data.imageOptimization,
                          quality: { ...data.imageOptimization.quality, png: parseInt(e.target.value) }
                        }
                      })}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="text-center text-xs text-gray-400 mt-1">
                      Current: {data.imageOptimization.quality.png}%
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-3">Supported Formats</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {Object.entries(data.imageOptimization.formats).map(([format, enabled]) => (
                    <label key={format} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={enabled}
                        onChange={(e) => updateData({
                          imageOptimization: {
                            ...data.imageOptimization,
                            formats: { ...data.imageOptimization.formats, [format]: e.target.checked }
                          }
                        })}
                        className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-0"
                      />
                      <span className="text-white text-sm uppercase">{format}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-3">Responsive Images</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Enable Responsive Images</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.imageOptimization.responsive.enabled}
                        onChange={(e) => updateData({
                          imageOptimization: {
                            ...data.imageOptimization,
                            responsive: { ...data.imageOptimization.responsive, enabled: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  {data.imageOptimization.responsive.enabled && (
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Breakpoints (px)</label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {data.imageOptimization.responsive.breakpoints.map((breakpoint, index) => (
                          <input
                            key={index}
                            type="number"
                            value={breakpoint}
                            onChange={(e) => {
                              const newBreakpoints = [...data.imageOptimization.responsive.breakpoints];
                              newBreakpoints[index] = parseInt(e.target.value);
                              updateData({
                                imageOptimization: {
                                  ...data.imageOptimization,
                                  responsive: { ...data.imageOptimization.responsive, breakpoints: newBreakpoints }
                                }
                              });
                            }}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-3">Lazy Loading</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Enable Lazy Loading</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.imageOptimization.lazy.enabled}
                        onChange={(e) => updateData({
                          imageOptimization: {
                            ...data.imageOptimization,
                            lazy: { ...data.imageOptimization.lazy, enabled: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  {data.imageOptimization.lazy.enabled && (
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">Load Threshold (pixels)</label>
                      <input
                        type="number"
                        value={data.imageOptimization.lazy.threshold}
                        onChange={(e) => updateData({
                          imageOptimization: {
                            ...data.imageOptimization,
                            lazy: { ...data.imageOptimization.lazy, threshold: parseInt(e.target.value) }
                          }
                        })}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                        min="0"
                      />
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-3">Image Processing</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(data.imageOptimization.processing).map(([feature, enabled]) => (
                    <div key={feature} className="flex items-center justify-between">
                      <span className="text-white text-sm capitalize">{feature}</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={enabled}
                          onChange={(e) => updateData({
                            imageOptimization: {
                              ...data.imageOptimization,
                              processing: { ...data.imageOptimization.processing, [feature]: e.target.checked }
                            }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Real Estate Specific Optimizations */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiSettings className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Real Estate Optimizations
        </h3>
        <p className="text-gray-400 text-sm mb-4">Specialized optimizations for real estate websites</p>
        <div className="space-y-4">
          <div>
            <h4 className="text-white font-medium mb-3">Property Images</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Preload Featured Images</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.propertyImages.preload}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        propertyImages: { ...data.realEstate.propertyImages, preload: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Auto Thumbnail Generation</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.propertyImages.thumbnailGeneration}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        propertyImages: { ...data.realEstate.propertyImages, thumbnailGeneration: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Virtual Tour Optimization</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.propertyImages.virtualTourOptimization}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        propertyImages: { ...data.realEstate.propertyImages, virtualTourOptimization: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-white font-medium mb-3">Maps & Location</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Tile Caching</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.maps.tileCache}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        maps: { ...data.realEstate.maps, tileCache: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Vector Optimization</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.maps.vectorOptimization}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        maps: { ...data.realEstate.maps, vectorOptimization: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Cluster Markers</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.maps.clusterMarkers}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        maps: { ...data.realEstate.maps, clusterMarkers: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-white font-medium mb-3">Search & Discovery</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Search Indexing</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.search.indexing}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        search: { ...data.realEstate.search, indexing: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Faceted Search</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.search.facetedSearch}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        search: { ...data.realEstate.search, facetedSearch: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Auto Complete</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={data.realEstate.search.autoComplete}
                    onChange={(e) => updateData({
                      realEstate: {
                        ...data.realEstate,
                        search: { ...data.realEstate.search, autoComplete: e.target.checked }
                      }
                    })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Monitoring */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiMonitor className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Performance Monitoring
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white font-medium">Enable Performance Monitoring</p>
              <p className="text-gray-400 text-sm">Track and analyze website performance metrics</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={data.monitoring.enabled}
                onChange={(e) => updateData({
                  monitoring: { ...data.monitoring, enabled: e.target.checked }
                })}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
            </label>
          </div>
          
          {data.monitoring.enabled && (
            <div className="space-y-4">
              <div>
                <h4 className="text-white font-medium mb-3">Metrics to Track</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Object.entries(data.monitoring.metrics).map(([metric, enabled]) => (
                    <div key={metric} className="flex items-center justify-between">
                      <span className="text-white text-sm capitalize">{metric.replace(/([A-Z])/g, ' $1')}</span>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={enabled}
                          onChange={(e) => updateData({
                            monitoring: {
                              ...data.monitoring,
                              metrics: { ...data.monitoring.metrics, [metric]: e.target.checked }
                            }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-3">Performance Alerts</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white text-sm">Enable Alerts</span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={data.monitoring.alerts.enabled}
                        onChange={(e) => updateData({
                          monitoring: {
                            ...data.monitoring,
                            alerts: { ...data.monitoring.alerts, enabled: e.target.checked }
                          }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
                    </label>
                  </div>
                  
                  {data.monitoring.alerts.enabled && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-2">Page Load Time (ms)</label>
                          <input
                            type="number"
                            value={data.monitoring.alerts.thresholds.pageLoadTime}
                            onChange={(e) => updateData({
                              monitoring: {
                                ...data.monitoring,
                                alerts: {
                                  ...data.monitoring.alerts,
                                  thresholds: { ...data.monitoring.alerts.thresholds, pageLoadTime: parseInt(e.target.value) }
                                }
                              }
                            })}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            min="100"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-2">Server Response (ms)</label>
                          <input
                            type="number"
                            value={data.monitoring.alerts.thresholds.serverResponseTime}
                            onChange={(e) => updateData({
                              monitoring: {
                                ...data.monitoring,
                                alerts: {
                                  ...data.monitoring.alerts,
                                  thresholds: { ...data.monitoring.alerts.thresholds, serverResponseTime: parseInt(e.target.value) }
                                }
                              }
                            })}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            min="50"
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-400 mb-2">Error Rate (%)</label>
                          <input
                            type="number"
                            value={data.monitoring.alerts.thresholds.errorRate}
                            onChange={(e) => updateData({
                              monitoring: {
                                ...data.monitoring,
                                alerts: {
                                  ...data.monitoring.alerts,
                                  thresholds: { ...data.monitoring.alerts.thresholds, errorRate: parseInt(e.target.value) }
                                }
                              }
                            })}
                            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                            min="0"
                            max="100"
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-2">Alert Recipients</label>
                        <div className="flex space-x-2 mb-2">
                          <input
                            type="email"
                            value={newEmail}
                            onChange={(e) => setNewEmail(e.target.value)}
                            placeholder="<EMAIL>"
                            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                          />
                          <button
                            onClick={() => addEmailRecipient('alerts')}
                            className="px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#00C2FF]/90 transition-colors"
                          >
                            Add
                          </button>
                        </div>
                        
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {data.monitoring.alerts.notifications.email.map((email, index) => (
                            <div key={index} className="flex items-center justify-between bg-gray-700 p-2 rounded-md">
                              <span className="text-white text-sm">{email}</span>
                              <button
                                onClick={() => removeEmailRecipient('alerts', index)}
                                className="text-red-400 hover:text-red-300 transition-colors"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Performance Tips */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Performance Optimization Tips
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <FiCheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Enable Hybrid Caching</h4>
                <p className="text-gray-400 text-xs">Combine memory and Redis for optimal performance</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiCheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Use WebP Images</h4>
                <p className="text-gray-400 text-xs">Reduce image sizes by up to 30% without quality loss</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiCheckCircle className="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Enable Brotli Compression</h4>
                <p className="text-gray-400 text-xs">Better compression than Gzip for text files</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <FiAlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Monitor Core Web Vitals</h4>
                <p className="text-gray-400 text-xs">Track LCP, FID, and CLS for better SEO rankings</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiAlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">Optimize Property Images</h4>
                <p className="text-gray-400 text-xs">Use responsive images and lazy loading for galleries</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <FiAlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-white font-medium text-sm">CDN for Global Reach</h4>
                <p className="text-gray-400 text-xs">Serve content from edge locations worldwide</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 