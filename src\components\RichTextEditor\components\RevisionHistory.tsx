import React, { useState, useEffect } from 'react';
import { useSlate } from 'slate-react';
import { <PERSON>Clock, FiCheckCircle, FiRefreshCw, FiSave, FiEye } from 'react-icons/fi';
import { Revision } from '../types';

// Mock revision history (in a real app, this would be stored in a database)
const mockRevisions: Revision[] = [
  {
    id: 'rev1',
    author: '<PERSON>',
    authorId: 'user1',
    date: '2023-06-15T10:30:00Z',
    content: [], // Would contain actual content
    description: 'Initial draft'
  },
  {
    id: 'rev2',
    author: '<PERSON>',
    authorId: 'user1',
    date: '2023-06-15T14:45:00Z',
    content: [], // Would contain actual content
    description: 'Added introduction section'
  },
  {
    id: 'rev3',
    author: '<PERSON>',
    authorId: 'user2',
    date: '2023-06-16T09:15:00Z',
    content: [], // Would contain actual content
    description: 'Revised conclusion'
  }
];

interface RevisionHistoryProps {
  isOpen: boolean;
  togglePanel: () => void;
  value: any;
  onChange: (value: any) => void;
}

const RevisionHistory: React.FC<RevisionHistoryProps> = ({ 
  isOpen, 
  togglePanel, 
  value, 
  onChange 
}) => {
  const editor = useSlate();
  const [revisions, setRevisions] = useState<Revision[]>(mockRevisions);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaved, setLastSaved] = useState<string | null>(null);
  const [viewingRevision, setViewingRevision] = useState<string | null>(null);
  const [savingStatus, setSavingStatus] = useState<'idle' | 'saving' | 'saved'>('idle');

  // Auto-save functionality
  useEffect(() => {
    if (!autoSaveEnabled || !value) return;

    const timer = setTimeout(() => {
      saveRevision('Auto-saved');
    }, 30000); // Auto-save every 30 seconds

    return () => clearTimeout(timer);
  }, [value, autoSaveEnabled]);

  // Mock function to save a revision
  const saveRevision = (description: string = 'Manual save') => {
    setSavingStatus('saving');
    
    // In a real app, you would send this to your API
    setTimeout(() => {
      const newRevision: Revision = {
        id: `rev-${Date.now()}`,
        author: 'Current User', // Get from auth in real app
        authorId: 'currentUser',
        date: new Date().toISOString(),
        content: JSON.parse(JSON.stringify(value)), // Deep clone the content
        description
      };
      
      setRevisions([newRevision, ...revisions]);
      setLastSaved(new Date().toLocaleTimeString());
      setSavingStatus('saved');
      
      // Reset to idle after a moment
      setTimeout(() => setSavingStatus('idle'), 2000);
    }, 700); // Simulate network delay
  };

  // Load a specific revision
  const loadRevision = (revisionId: string) => {
    const revision = revisions.find(rev => rev.id === revisionId);
    if (!revision) return;
    
    if (viewingRevision === revisionId) {
      // If already viewing this revision, apply it
      if (confirm('Are you sure you want to restore this revision? This will overwrite your current changes.')) {
        onChange(revision.content);
        setViewingRevision(null);
      }
    } else {
      // Otherwise, just preview it
      setViewingRevision(revisionId);
      // In a real app, you would show a preview of the revision
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Status indicator component
  const StatusIndicator = () => {
    if (savingStatus === 'saving') {
      return (
        <div className="flex items-center text-yellow-300 text-xs gap-1 animate-pulse">
          <FiRefreshCw size={12} className="animate-spin" />
          <span>Saving...</span>
        </div>
      );
    } else if (savingStatus === 'saved') {
      return (
        <div className="flex items-center text-green-400 text-xs gap-1">
          <FiCheckCircle size={12} />
          <span>Saved at {lastSaved}</span>
        </div>
      );
    } else if (lastSaved) {
      return (
        <div className="flex items-center text-white/50 text-xs gap-1">
          <FiClock size={12} />
          <span>Last saved: {lastSaved}</span>
        </div>
      );
    }
    
    return null;
  };

  if (!isOpen) {
    return (
      <div className="flex items-center gap-2">
        <button
          className="p-2 bg-[#1a2349] rounded-full hover:bg-[#141b35] text-white"
          onClick={togglePanel}
          title="Revision History"
        >
          <FiClock size={18} />
        </button>
        <StatusIndicator />
      </div>
    );
  }

  return (
    <div className="absolute right-0 top-0 w-72 bg-[#141b35] border border-white/10 rounded-lg shadow-lg z-50 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-medium">Revision History</h3>
        <button 
          className="text-white/70 hover:text-white"
          onClick={togglePanel}
        >
          ×
        </button>
      </div>
      
      {/* Auto-save toggle and manual save */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <span className="text-white/70 text-sm">Auto-save</span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input 
              type="checkbox" 
              className="sr-only peer" 
              checked={autoSaveEnabled}
              onChange={() => setAutoSaveEnabled(!autoSaveEnabled)}
            />
            <div className="w-9 h-5 bg-[#0A0F23] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-[rgb(var(--color-primary))]"></div>
          </label>
        </div>
        
        <button
          className="text-sm px-3 py-1 bg-[#0A0F23] hover:bg-[#1a2349] text-white rounded-lg flex items-center gap-1"
          onClick={() => saveRevision('Manual save')}
        >
          <FiSave size={14} />
          <span>Save Now</span>
        </button>
      </div>
      
      <StatusIndicator />
      
      {/* Revision List */}
      <div className="mt-4 max-h-96 overflow-y-auto">
        <div className="text-white/70 text-xs uppercase mb-2">Revision History</div>
        
        {revisions.length === 0 ? (
          <p className="text-white/50 text-center text-sm py-4">No revisions yet</p>
        ) : (
          revisions.map(revision => (
            <div 
              key={revision.id} 
              className={`mb-2 p-2 rounded-lg cursor-pointer ${
                viewingRevision === revision.id 
                  ? 'bg-[rgb(var(--color-primary))]/20 border border-[rgb(var(--color-primary))]/50' 
                  : 'bg-[#0A0F23] hover:bg-[#1a2349]'
              }`}
              onClick={() => loadRevision(revision.id)}
            >
              <div className="flex justify-between items-start mb-1">
                <span className="text-white text-sm">{revision.description}</span>
                {viewingRevision === revision.id && (
                  <button 
                    className="text-xs bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white px-2 py-0.5 rounded-lg flex items-center gap-1"
                  >
                    <FiRefreshCw size={10} />
                    <span>Restore</span>
                  </button>
                )}
              </div>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-xs">{formatDate(revision.date)}</span>
                <span className="text-white/50 text-xs">{revision.author}</span>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default RevisionHistory; 