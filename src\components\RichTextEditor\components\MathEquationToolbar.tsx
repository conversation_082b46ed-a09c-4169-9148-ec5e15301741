import React, { useState } from 'react';
import { Transforms } from 'slate';
import { useSlate } from 'slate-react';
import { FiFunction } from 'react-icons/fi';

interface MathEquationToolbarProps {
  icon: React.ReactNode;
}

const MathEquationToolbar: React.FC<MathEquationToolbarProps> = ({ icon }) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);
  const [equation, setEquation] = useState('');
  const [showEditor, setShowEditor] = useState(false);
  const [equationType, setEquationType] = useState<'inline' | 'block'>('inline');

  const mathEquationTemplates = [
    { name: 'Quadratic Formula', equation: 'x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}' },
    { name: 'Pythagorean Theorem', equation: 'a^2 + b^2 = c^2' },
    { name: 'Integral', equation: '\\int_{a}^{b} f(x) \\, dx' },
    { name: 'Sum', equation: '\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}' },
    { name: 'Matrix', equation: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}' },
  ];

  const insertMathEquation = (equationText: string, type: 'inline' | 'block' = 'inline') => {
    const mathNode = {
      type: 'math-equation',
      equation: equationText,
      children: [{ text: '' }],
    };
    
    Transforms.insertNodes(editor, mathNode);
    setShowEditor(false);
    setEquation('');
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      {showOptions && (
        <div className="absolute top-full left-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[200px]">
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              setEquationType('inline');
              setShowEditor(true);
              setShowOptions(false);
            }}
          >
            <FiFunction size={16} />
            <span>Inline Math</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              setEquationType('block');
              setShowEditor(true);
              setShowOptions(false);
            }}
          >
            <FiFunction size={16} />
            <span>Block Math</span>
          </button>
          
          <div className="border-t border-white/10 my-1"></div>
          <div className="px-3 py-1 text-white/70 text-xs">Templates</div>
          
          {mathEquationTemplates.map((template, index) => (
            <button
              key={index}
              className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
              onMouseDown={(e) => {
                e.preventDefault();
                insertMathEquation(template.equation);
                setShowOptions(false);
              }}
            >
              <span className="text-sm">{template.name}</span>
            </button>
          ))}
        </div>
      )}
      
      {showEditor && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-[#141b35] p-6 rounded-lg max-w-2xl w-full">
            <h3 className="text-white text-lg font-medium mb-4">
              {equationType === 'inline' ? 'Insert Inline Math Equation' : 'Insert Block Math Equation'}
            </h3>
            
            <div className="mb-4">
              <label className="block text-white/70 mb-2">LaTeX Equation</label>
              <textarea
                className="w-full p-3 bg-[#0A0F23] border border-white/10 rounded-lg text-white font-mono"
                rows={4}
                value={equation}
                onChange={(e) => setEquation(e.target.value)}
                placeholder="Enter LaTeX code for your equation..."
              />
              <p className="text-white/50 text-sm mt-1">
                Example: x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}
              </p>
            </div>
            
            <div className="mb-4">
              <div className="text-white/70 mb-2">Preview</div>
              <div className="p-4 bg-[#0A0F23] border border-white/10 rounded-lg min-h-[60px] flex items-center justify-center">
                {equation ? (
                  <div className="text-white">
                    {/* In a real implementation, this would render the LaTeX equation */}
                    LaTeX: {equation}
                  </div>
                ) : (
                  <div className="text-white/30">Equation preview will appear here</div>
                )}
              </div>
            </div>
            
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg"
                onClick={() => {
                  setShowEditor(false);
                  setEquation('');
                }}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white rounded-lg"
                onClick={() => {
                  if (equation.trim()) {
                    insertMathEquation(equation, equationType);
                  } else {
                    alert('Please enter an equation');
                  }
                }}
              >
                Insert
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MathEquationToolbar; 