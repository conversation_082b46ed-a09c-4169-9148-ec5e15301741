import { jsx } from 'slate-hyperscript';
import { CustomElement, CustomText } from '@/components/RichTextEditor/types';

// Default initial value for the editor
const initialValue: CustomElement[] = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

// Helper to parse HTML to Slate content
export const deserializeHtml = (html: string): CustomElement[] => {
  // Create a temporary DOM element to parse the HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Return an array of nodes
  return Array.from(doc.body.children).map(el => deserializeElement(el));
};

const deserializeElement = (el: Element): CustomElement => {
  // Handle various HTML elements and convert them to appropriate Slate nodes
  
  // Get element attributes
  const align = el.style.textAlign || undefined;
  
  // Process element based on its tag
  switch (el.nodeName.toLowerCase()) {
    case 'p':
      return {
        type: 'paragraph',
        align,
        children: deserializeChildren(el),
      };
    
    case 'h1':
      return {
        type: 'heading-one',
        align,
        children: deserializeChildren(el),
      };
    
    case 'h2':
      return {
        type: 'heading-two',
        align,
        children: deserializeChildren(el),
      };
    
    case 'h3':
      return {
        type: 'heading-three',
        align,
        children: deserializeChildren(el),
      };
    
    case 'blockquote':
      return {
        type: 'block-quote',
        align,
        children: deserializeChildren(el),
      };
    
    case 'ul':
      return {
        type: 'bulleted-list',
        align,
        children: Array.from(el.children).map(li => deserializeElement(li)),
      };
    
    case 'ol':
      return {
        type: 'numbered-list',
        align,
        children: Array.from(el.children).map(li => deserializeElement(li)),
      };
    
    case 'li':
      return {
        type: 'list-item',
        align,
        children: deserializeChildren(el),
      };
    
    case 'pre':
      // Look for code block
      const code = el.querySelector('code');
      const language = code?.className ? 
        code?.className.replace('language-', '') : 
        'plaintext';
      
      return {
        type: 'code-block',
        language,
        children: [{ text: el.textContent || '' }],
      };
    
    case 'figure':
      // Check if it contains an image or video
      const img = el.querySelector('img');
      const video = el.querySelector('video');
      const figcaption = el.querySelector('figcaption');
      const caption = figcaption ? figcaption.textContent || '' : '';
      
      if (img) {
        return {
          type: 'image',
          url: img.getAttribute('src') || '',
          alt: img.getAttribute('alt') || '',
          caption,
          align,
          children: [{ text: '' }],
        };
      } else if (video) {
        return {
          type: 'video',
          url: video.getAttribute('src') || '',
          caption,
          align,
          children: [{ text: '' }],
        };
      }
      
      // If not a recognized figure, treat as paragraph
      return {
        type: 'paragraph',
        align,
        children: deserializeChildren(el),
      };
    
    case 'table':
      return {
        type: 'table',
        align,
        children: Array.from(el.children).map(row => deserializeElement(row)),
      };
    
    case 'tr':
      return {
        type: 'table-row',
        children: Array.from(el.children).map(cell => deserializeElement(cell)),
      };
    
    case 'td':
    case 'th':
      const colspan = el.getAttribute('colspan') ? 
        parseInt(el.getAttribute('colspan') || '1') : undefined;
      const rowspan = el.getAttribute('rowspan') ? 
        parseInt(el.getAttribute('rowspan') || '1') : undefined;
      
      return {
        type: 'table-cell',
        colspan,
        rowspan,
        children: deserializeChildren(el),
      };
    
    case 'div':
      // Check for special div types
      if (el.classList.contains('math-equation')) {
        return {
          type: 'math-equation',
          equation: el.getAttribute('data-equation') || el.textContent || '',
          children: [{ text: '' }],
        };
      } else if (el.classList.contains('table-of-contents')) {
        return {
          type: 'toc',
          children: [{ text: 'Table of Contents' }],
        };
      } else if (el.classList.contains('footnote')) {
        const footnoteId = el.id ? el.id.replace('footnote-', '') : '';
        return {
          type: 'endnote',
          footnoteId,
          children: deserializeChildren(el),
        };
      } else if (el.style.columnCount || el.classList.contains('column-container')) {
        const columns = el.style.columnCount ? 
          parseInt(el.style.columnCount) : 2;
        
        return {
          type: 'column-layout',
          columns,
          children: deserializeChildren(el),
        };
      }
      
      // Default div handling
      return {
        type: 'paragraph',
        align,
        children: deserializeChildren(el),
      };
    
    case 'hr':
      if (el.classList.contains('page-break')) {
        return {
          type: 'page-break',
          children: [{ text: '' }],
        };
      }
      
      // Default hr
      return {
        type: 'paragraph',
        children: [{ text: '' }],
      };
    
    case 'a':
      // Check if it's a file download
      const isDownload = el.hasAttribute('download');
      
      if (isDownload) {
        return {
          type: 'file-embed',
          url: el.getAttribute('href') || '',
          filename: el.textContent || 'Download',
          children: [{ text: el.textContent || 'Download' }],
        };
      }
      
      // Regular link is handled in deserializeLeaf
      return {
        type: 'paragraph',
        children: deserializeChildren(el),
      };
    
    default:
      // For any other elements, deserialize as paragraph
      return {
        type: 'paragraph',
        align,
        children: deserializeChildren(el),
      };
  }
};

const deserializeChildren = (el: Element): (CustomText | CustomElement)[] => {
  const children: (CustomText | CustomElement)[] = [];
  
  // Process all child nodes
  for (const node of Array.from(el.childNodes)) {
    if (node.nodeType === Node.TEXT_NODE) {
      // It's a text node
      children.push({ text: node.textContent || '' });
    } else if (node.nodeType === Node.ELEMENT_NODE && node instanceof Element) {
      // It's an element - check if it's a leaf mark or another block
      const leaf = deserializeLeaf(node);
      
      if (leaf) {
        children.push(leaf);
      } else {
        // It might be another block element
        children.push(deserializeElement(node));
      }
    }
  }
  
  return children;
};

const deserializeLeaf = (el: Element): CustomText | null => {
  // Handle leaf nodes (formatting marks)
  switch (el.nodeName.toLowerCase()) {
    case 'strong':
    case 'b':
      return {
        text: el.textContent || '',
        bold: true,
      };
    
    case 'em':
    case 'i':
      return {
        text: el.textContent || '',
        italic: true,
      };
    
    case 'u':
      return {
        text: el.textContent || '',
        underline: true,
      };
    
    case 'code':
      return {
        text: el.textContent || '',
        code: true,
      };
    
    case 's':
    case 'del':
      return {
        text: el.textContent || '',
        strikethrough: true,
      };
    
    case 'sup':
      // Check if it's a footnote
      if (el.id && el.id.startsWith('footnote-ref-')) {
        const footnoteId = el.id.replace('footnote-ref-', '');
        return {
          text: el.textContent || '',
          superscript: true,
          footnoteId,
        };
      }
      
      return {
        text: el.textContent || '',
        superscript: true,
      };
    
    case 'sub':
      return {
        text: el.textContent || '',
        subscript: true,
      };
    
    case 'mark':
      return {
        text: el.textContent || '',
        highlight: true,
      };
    
    case 'span':
      // Check for inline styles
      const style = el.style;
      const text = el.textContent || '';
      const leaf: CustomText = { text };
      
      if (style.color) {
        leaf.color = style.color;
      }
      
      if (style.backgroundColor) {
        leaf.bgColor = style.backgroundColor;
      }
      
      if (style.fontSize) {
        leaf.fontSize = style.fontSize;
      }
      
      if (style.fontFamily) {
        leaf.fontFamily = style.fontFamily;
      }
      
      return leaf;
    
    case 'a':
      // Handle links - in real implementation, we would handle links
      // For now, just return the text
      return {
        text: el.textContent || '',
      };
    
    default:
      // If not a recognized leaf, return null to be handled as a child element
      return null;
  }
}; 