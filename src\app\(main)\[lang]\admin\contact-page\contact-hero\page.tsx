"use client";

import React, { useState, useEffect, useRef } from 'react';
import { FiSave, FiRefreshCw, FiEye, FiEdit, FiPlus, FiX, FiPhone, FiMapPin, FiInfo, FiChevronLeft, FiChevronRight, FiSearch } from 'react-icons/fi';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';

interface ContactHeroData {
  badge: {
    english: string;
    arabic: string;
  };
  title: {
    beforeHighlight: {
      english: string;
      arabic: string;
    };
    highlightedWord: {
      english: string;
      arabic: string;
    };
    afterHighlight: {
      english: string;
      arabic: string;
    };
  };
  description: {
    english: string;
    arabic: string;
  };
  features: {
    id: string;
    icon: string;
    text: {
      english: string;
      arabic: string;
    };
  }[];
}

export default function ContactHeroPage() {
  const [heroData, setHeroData] = useState<ContactHeroData>({
    badge: {
      english: "GET IN TOUCH",
      arabic: "تواصل معنا"
    },
    title: {
      beforeHighlight: {
        english: "Contact",
        arabic: "تواصل مع"
      },
      highlightedWord: {
        english: "Mazaya",
        arabic: "مزايا"
      },
      afterHighlight: {
        english: "Capital",
        arabic: "كابيتال"
      }
    },
    description: {
      english: "We're here to answer your questions about real estate investment opportunities and our development projects.",
      arabic: "نحن هنا للإجابة على أسئلتكم حول فرص الاستثمار العقاري ومشاريعنا التنموية."
    },
    features: [
      {
        id: "1",
        icon: "FaPhone",
        text: {
          english: "Quick Response",
          arabic: "رد سريع"
        }
      },
      {
        id: "2", 
        icon: "FaMapMarkerAlt",
        text: {
          english: "Multiple Locations",
          arabic: "مواقع متعددة"
        }
      },
      {
        id: "3",
        icon: "FaUserTie",
        text: {
          english: "Expert Advisors", 
          arabic: "مستشارون خبراء"
        }
      }
    ]
  });

  const [showPreview, setShowPreview] = useState(false);
  const [newFeature, setNewFeature] = useState({
    icon: "FaStar",
    english: "",
    arabic: ""
  });

  // Icon system state - separate for each feature
  const [showIconSelector, setShowIconSelector] = useState<string | null>(null);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconSet, setSelectedIconSet] = useState<
    'fc' | 'fa' | 'fa6' | 'bs' | 'ri' | 'gi' | 'tb' | 'md' | 'hi' | 'ai' | 'io' | 'io5' | 'pi'
  >('fa');

  // Icon library carousel refs and state
  const iconLibsCarouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLibsLeft, setCanScrollLibsLeft] = useState(false);
  const [canScrollLibsRight, setCanScrollLibsRight] = useState(true);

  // Add custom CSS for hiding scrollbars
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .icon-selector-container {
        position: relative;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Icon library carousel scroll functions
  const scrollLibsLeft = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: -150,
        behavior: 'smooth'
      });
    }
  };

  const scrollLibsRight = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: 150,
        behavior: 'smooth'
      });
    }
  };

  const checkLibsScrollPosition = () => {
    if (iconLibsCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = iconLibsCarouselRef.current;
      setCanScrollLibsLeft(scrollLeft > 0);
      setCanScrollLibsRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    const carousel = iconLibsCarouselRef.current;
    if (carousel) {
      carousel.addEventListener('scroll', checkLibsScrollPosition);
      checkLibsScrollPosition();
      return () => {
        carousel.removeEventListener('scroll', checkLibsScrollPosition);
      };
    }
  }, []);

  // Function to determine if a library has any icons matching the search term
  const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
    if (!searchTerm.trim()) return true;
    
    let library;
    switch (libraryId) {
      case 'fc': library = FcIcons; break;
      case 'fa': library = FaIcons; break;
      case 'fa6': library = FaIconsSolid; break;
      case 'bs': library = BsIcons; break;
      case 'ri': library = RiIcons; break;
      case 'gi': library = GiIcons; break;
      case 'tb': library = TbIcons; break;
      case 'md': library = MdIcons; break;
      case 'hi': library = HiIcons; break;
      case 'ai': library = AiIcons; break;
      case 'io': library = IoIcons; break;
      case 'io5': library = Io5Icons; break;
      case 'pi': library = PiIcons; break;
      default: return false;
    }
    
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).some(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    );
  };
  
  // Function to get the icon component based on the icon name
  const getIconComponent = (iconName: string): React.ReactElement | null => {
    if (!iconName) return null;
    
    // Determine which library the icon belongs to based on prefix
    if (iconName.startsWith('Fc')) {
        const IconFc = FcIcons[iconName as keyof typeof FcIcons];
        return IconFc ? React.createElement(IconFc, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Fa6')) {
        const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
        return IconFa6 ? React.createElement(IconFa6, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Fa')) {
      const IconFa = FaIcons[iconName as keyof typeof FaIcons];
      return IconFa ? React.createElement(IconFa, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Bs')) {
        const IconBs = BsIcons[iconName as keyof typeof BsIcons];
        return IconBs ? React.createElement(IconBs, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Ri')) {
        const IconRi = RiIcons[iconName as keyof typeof RiIcons];
        return IconRi ? React.createElement(IconRi, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Gi')) {
        const IconGi = GiIcons[iconName as keyof typeof GiIcons];
        return IconGi ? React.createElement(IconGi, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Tb')) {
        const IconTb = TbIcons[iconName as keyof typeof TbIcons];
        return IconTb ? React.createElement(IconTb, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Md')) {
        const IconMd = MdIcons[iconName as keyof typeof MdIcons];
        return IconMd ? React.createElement(IconMd, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Hi')) {
        const IconHi = HiIcons[iconName as keyof typeof HiIcons];
        return IconHi ? React.createElement(IconHi, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Ai')) {
        const IconAi = AiIcons[iconName as keyof typeof AiIcons];
        return IconAi ? React.createElement(IconAi, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Io5')) {
        const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
        return IconIo5 ? React.createElement(IconIo5, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Io')) {
      const IconIo = IoIcons[iconName as keyof typeof IoIcons];
      return IconIo ? React.createElement(IconIo, { className: "text-lg" }) : null;
    } else if (iconName.startsWith('Pi')) {
        const IconPi = PiIcons[iconName as keyof typeof PiIcons];
        return IconPi ? React.createElement(IconPi, { className: "text-lg" }) : null;
      }
    
        return null;
  };

  const iconLibraries = [
    { id: 'fc', name: 'Flat Color', library: FcIcons },
    { id: 'fa', name: 'Font Awesome', library: FaIcons },
    { id: 'fa6', name: 'Font Awesome 6', library: FaIconsSolid },
    { id: 'bs', name: 'Bootstrap', library: BsIcons },
    { id: 'ri', name: 'Remix', library: RiIcons },
    { id: 'gi', name: 'Game Icons', library: GiIcons },
    { id: 'tb', name: 'Tabler', library: TbIcons },
    { id: 'md', name: 'Material Design', library: MdIcons },
    { id: 'hi', name: 'Heroicons', library: HiIcons },
    { id: 'ai', name: 'Ant Design', library: AiIcons },
    { id: 'io', name: 'Ionicons 4', library: IoIcons },
    { id: 'io5', name: 'Ionicons 5', library: Io5Icons },
    { id: 'pi', name: 'Phosphor', library: PiIcons },
  ];

  const getFilteredIcons = (library: any, searchTerm: string) => {
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).filter(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    ).slice(0, 50); // Limit to 50 icons for performance
  };

  // Handle input changes
  const handleInputChange = (section: keyof ContactHeroData, field: string, value: string, language?: 'english' | 'arabic', subfield?: string) => {
    setHeroData(prev => {
      const newData = { ...prev };
      if (language && subfield) {
        (newData[section] as any)[field][subfield][language] = value;
      } else if (language) {
        (newData[section] as any)[field][language] = value;
      } else {
        (newData[section] as any)[field] = value;
      }
      return newData;
    });
  };

  // Handle feature changes
  const handleFeatureChange = (featureId: string, field: string, value: string, language?: 'english' | 'arabic') => {
    setHeroData(prev => ({
      ...prev,
      features: prev.features.map(feature => {
        if (feature.id === featureId) {
          if (language) {
            return {
              ...feature,
              text: {
                ...feature.text,
                [language]: value
              }
            };
          } else {
            return {
              ...feature,
              [field]: value
            };
          }
        }
        return feature;
      })
    }));
  };

  // Add new feature
  const addFeature = () => {
    if (newFeature.english.trim() && newFeature.arabic.trim()) {
      const feature = {
        id: Date.now().toString(),
        icon: newFeature.icon,
        text: {
          english: newFeature.english.trim(),
          arabic: newFeature.arabic.trim()
        }
      };
      
      setHeroData(prev => ({
        ...prev,
        features: [...prev.features, feature]
      }));
      
      setNewFeature({
        icon: "FaStar",
        english: "",
        arabic: ""
      });
    }
  };

  // Remove feature
  const removeFeature = (featureId: string) => {
    setHeroData(prev => ({
      ...prev,
      features: prev.features.filter(f => f.id !== featureId)
    }));
  };

  // Handle save
  const handleSave = () => {
    console.log('Saving contact hero data:', heroData);
    alert('Contact hero content saved successfully!');
  };

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Contact Hero Section</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage the hero section content for the contact page - badge, title, description, and feature cards
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Preview Section */}
      {showPreview && (
        <div className="mt-6 bg-gray-700 rounded-lg p-6 border border-gray-600">
          <h3 className="text-lg font-medium text-white mb-4">Live Preview</h3>
          <div className="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-8 rounded-lg text-white relative overflow-hidden">
            {/* Background overlay effect */}
            <div className="absolute inset-0 bg-black/20"></div>
            
            <div className="max-w-4xl relative z-10">
              {/* Badge */}
              <div className="mb-4" style={{ opacity: 1, transform: 'none' }}>
                <span className="bg-white/10 backdrop-blur-sm text-white text-sm uppercase tracking-wider px-4 py-1 rounded-full border border-white/10">
                  {heroData.badge.english}
                </span>
              </div>
              
              {/* Main Title */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-5 leading-tight" style={{ opacity: 1, transform: 'none' }}>
                {heroData.title.beforeHighlight.english}{' '}
                <span className="relative inline-block">
                  {heroData.title.highlightedWord.english}
                  <span className="absolute -bottom-2 start-0 w-full h-1 bg-white" style={{ width: '100%' }}></span>
                </span>
                {heroData.title.afterHighlight.english && ` ${heroData.title.afterHighlight.english}`}
              </h1>
              
              {/* Description */}
              <p className="text-xl text-white/90 max-w-2xl" style={{ opacity: 1, transform: 'none' }}>
                {heroData.description.english}
              </p>
              
              {/* Feature Cards */}
              <div className="flex flex-wrap gap-6 mt-10" style={{ opacity: 1, transform: 'none' }}>
                {heroData.features.map((feature) => (
                  <div key={feature.id} className="flex items-center space-x-3 bg-white/10 backdrop-blur-sm px-4 py-3 rounded-lg border border-white/10">
                    <div className="text-white">
                      {getIconComponent(feature.icon)}
                    </div>
                    <span className="text-white/90">{feature.text.english}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 space-y-6">
        {/* Badge Section */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <div className="w-6 h-6 bg-blue-500 rounded mr-2 flex items-center justify-center">
              <span className="text-white text-xs font-bold">1</span>
            </div>
            Top Badge
          </h3>
          <p className="text-gray-400 text-sm mb-4">The small badge that appears above the main title</p>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-300">Badge Text (English)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.badge.english}
                onChange={e => handleInputChange('badge', 'english', e.target.value, 'english')}
                placeholder="GET IN TOUCH"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Badge Text (Arabic)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.badge.arabic}
                onChange={e => handleInputChange('badge', 'arabic', e.target.value, 'arabic')}
                placeholder="تواصل معنا"
              />
            </div>
          </div>
        </div>

        {/* Title Section */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <div className="w-6 h-6 bg-green-500 rounded mr-2 flex items-center justify-center">
              <span className="text-white text-xs font-bold">2</span>
            </div>
            Main Title
          </h3>
          <p className="text-gray-400 text-sm mb-4">The large headline with highlighted word. Format: "Contact [Mazaya] Capital"</p>
          
          <div className="space-y-6">
            {/* English Title */}
            <div>
              <h4 className="text-md font-medium text-blue-400 mb-3">English Title</h4>
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                <div>
                  <label className="block text-sm font-medium text-gray-300">Before Highlight</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={heroData.title.beforeHighlight.english}
                    onChange={e => handleInputChange('title', 'beforeHighlight', e.target.value, 'english')}
                    placeholder="Contact"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Highlighted Word</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={heroData.title.highlightedWord.english}
                    onChange={e => handleInputChange('title', 'highlightedWord', e.target.value, 'english')}
                    placeholder="Mazaya"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">After Highlight</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={heroData.title.afterHighlight.english}
                    onChange={e => handleInputChange('title', 'afterHighlight', e.target.value, 'english')}
                    placeholder="Capital"
                  />
                </div>
              </div>
            </div>

            {/* Arabic Title */}
            <div>
              <h4 className="text-md font-medium text-blue-400 mb-3">Arabic Title</h4>
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                <div>
                  <label className="block text-sm font-medium text-gray-300">قبل التمييز</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={heroData.title.beforeHighlight.arabic}
                    onChange={e => handleInputChange('title', 'beforeHighlight', e.target.value, 'arabic')}
                    placeholder="تواصل مع"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">الكلمة المميزة</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={heroData.title.highlightedWord.arabic}
                    onChange={e => handleInputChange('title', 'highlightedWord', e.target.value, 'arabic')}
                    placeholder="مزايا"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">بعد التمييز</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={heroData.title.afterHighlight.arabic}
                    onChange={e => handleInputChange('title', 'afterHighlight', e.target.value, 'arabic')}
                    placeholder="كابيتال"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Description Section */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <div className="w-6 h-6 bg-purple-500 rounded mr-2 flex items-center justify-center">
              <span className="text-white text-xs font-bold">3</span>
            </div>
            Description
          </h3>
          <p className="text-gray-400 text-sm mb-4">The description text that appears below the main title</p>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-300">Description (English)</label>
              <textarea
                rows={4}
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.description.english}
                onChange={e => handleInputChange('description', 'english', e.target.value, 'english')}
                placeholder="We're here to answer your questions about real estate investment opportunities and our development projects."
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Description (Arabic)</label>
              <textarea
                rows={4}
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={heroData.description.arabic}
                onChange={e => handleInputChange('description', 'arabic', e.target.value, 'arabic')}
                placeholder="نحن هنا للإجابة على أسئلتكم حول فرص الاستثمار العقاري ومشاريعنا التنموية."
              />
            </div>
          </div>
        </div>

        {/* Feature Cards Section */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <div className="w-6 h-6 bg-orange-500 rounded mr-2 flex items-center justify-center">
              <span className="text-white text-xs font-bold">4</span>
            </div>
            Feature Cards
          </h3>
          <p className="text-gray-400 text-sm mb-6">The small cards at the bottom with icons and text (Quick Response, Multiple Locations, Expert Advisors)</p>
          
          {/* Existing Features */}
          <div className="space-y-4 mb-6">
            {heroData.features.map((feature, index) => (
              <div key={feature.id} className="bg-gray-700 rounded-lg p-4 border-l-4 border-blue-500">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-md font-medium text-gray-300 flex items-center">
                    {getIconComponent(feature.icon)}
                    <span className="ml-2">Feature {index + 1}</span>
                  </h4>
                  <button
                    onClick={() => removeFeature(feature.id)}
                    className="text-red-400 hover:text-red-300 p-1"
                  >
                    <FiX className="h-4 w-4" />
                  </button>
                </div>
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Icon</label>
                    {/* Icon Selector */}
                    <div className="relative icon-selector-container">
                      <button
                        type="button"
                        onClick={() => setShowIconSelector(showIconSelector === `feature-${feature.id}` ? null : `feature-${feature.id}`)}
                        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF] flex items-center justify-between"
                      >
                        <span className="flex items-center min-w-0 flex-1">
                          <span key={feature.icon} className="flex-shrink-0">
                            {getIconComponent(feature.icon) || React.createElement(FaIcons.FaBuilding, { className: "text-lg" })}
                          </span>
                          <span className="ml-2 text-xs truncate min-w-0">{feature.icon || 'Select Icon'}</span>
                        </span>
                        <FiSearch className="h-3 w-3" />
                      </button>

                      {showIconSelector === `feature-${feature.id}` && (
                        <div 
                          className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg max-h-80 overflow-y-auto min-w-[400px]" 
                          style={{
                            scrollbarWidth: 'thin',
                            scrollbarColor: '#6b7280 transparent'
                          }}
                        >
                          {/* Icon Library Tabs with Carousel */}
                          <div className="relative border-b border-gray-600">
                            {/* Navigation Buttons */}
                            <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                              <button
                                type="button"
                                onClick={scrollLibsLeft}
                                disabled={!canScrollLibsLeft}
                                className={`p-1 rounded-r transition-colors ${
                                  canScrollLibsLeft 
                                    ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                                    : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                                }`}
                                aria-label="Scroll library tabs left"
                              >
                                <FiChevronLeft className="h-3 w-3" />
                              </button>
                            </div>
                            
                            <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                              <button
                                type="button"
                                onClick={scrollLibsRight}
                                disabled={!canScrollLibsRight}
                                className={`p-1 rounded-l transition-colors ${
                                  canScrollLibsRight 
                                    ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                                    : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                                }`}
                                aria-label="Scroll library tabs right"
                              >
                                <FiChevronRight className="h-3 w-3" />
                              </button>
                            </div>

                            {/* Scrollable Tabs Container */}
                            <div 
                              ref={iconLibsCarouselRef}
                              className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
                              style={{
                                scrollbarWidth: 'none',
                                msOverflowStyle: 'none',
                              }}
                            >
                              {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                                <button
                                  key={lib.id}
                                  type="button"
                                  onClick={() => setSelectedIconSet(lib.id as any)}
                                  className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                                    selectedIconSet === lib.id
                                      ? 'bg-[#00C2FF] text-white'
                                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                                  }`}
                                >
                                  {lib.name}
                                </button>
                              ))}
                            </div>
                          </div>

                          {/* Search Input */}
                          <div className="p-2 border-b border-gray-600">
                            <input
                              type="text"
                              placeholder="Search icons..."
                              value={iconSearchTerm}
                              onChange={(e) => setIconSearchTerm(e.target.value)}
                              className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                            />
                          </div>

                          {/* Icon Grid */}
                          <div className="p-2">
                            <div className="grid grid-cols-6 gap-1">
                              {(() => {
                                const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                                if (!currentLibrary) return null;

                                return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                                  const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary];
                                  
                                  const fullIconName = iconName;
                                  return (
                                    <button
                                      key={iconName}
                                      type="button"
                                      onClick={() => {
                                        handleFeatureChange(feature.id, 'icon', fullIconName);
                                        setShowIconSelector(null);
                                      }}
                                      className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors"
                                      title={iconName}
                                    >
                                      {IconComponent && React.createElement(IconComponent, { className: "text-lg text-white hover:text-[#00C2FF] transition-colors" })}
                                    </button>
                                  );
                                });
                              })()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Text (English)</label>
                    <input
                      type="text"
                      className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={feature.text.english}
                      onChange={e => handleFeatureChange(feature.id, 'text', e.target.value, 'english')}
                      placeholder="Feature text in English"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Text (Arabic)</label>
                    <input
                      type="text"
                      dir="rtl"
                      className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={feature.text.arabic}
                      onChange={e => handleFeatureChange(feature.id, 'text', e.target.value, 'arabic')}
                      placeholder="نص الميزة بالعربية"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Add New Feature */}
          <div className="border-t border-gray-600 pt-6">
            <h4 className="text-md font-medium text-gray-300 mb-3 flex items-center">
              <FiPlus className="mr-2 h-4 w-4" />
              Add New Feature Card
            </h4>
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Icon</label>
                {/* Icon Selector for New Feature */}
                <div className="relative icon-selector-container">
                  <button
                    type="button"
                    onClick={() => setShowIconSelector(showIconSelector === 'new-feature' ? null : 'new-feature')}
                    className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF] flex items-center justify-between"
                  >
                    <span className="flex items-center min-w-0 flex-1">
                      <span key={newFeature.icon} className="flex-shrink-0">
                        {getIconComponent(newFeature.icon) || React.createElement(FaIcons.FaBuilding, { className: "text-lg" })}
                      </span>
                      <span className="ml-2 text-xs truncate min-w-0">{newFeature.icon || 'Select Icon'}</span>
                    </span>
                    <FiSearch className="h-3 w-3" />
                  </button>

                  {showIconSelector === 'new-feature' && (
                    <div 
                      className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg max-h-80 overflow-y-auto min-w-[400px]" 
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#6b7280 transparent'
                      }}
                    >
                      {/* Icon Library Tabs with Carousel */}
                      <div className="relative border-b border-gray-600">
                        {/* Navigation Buttons */}
                        <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                          <button
                            type="button"
                            onClick={scrollLibsLeft}
                            disabled={!canScrollLibsLeft}
                            className={`p-1 rounded-r transition-colors ${
                              canScrollLibsLeft 
                                ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                            }`}
                            aria-label="Scroll library tabs left"
                          >
                            <FiChevronLeft className="h-3 w-3" />
                          </button>
                        </div>
                        
                        <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                          <button
                            type="button"
                            onClick={scrollLibsRight}
                            disabled={!canScrollLibsRight}
                            className={`p-1 rounded-l transition-colors ${
                              canScrollLibsRight 
                                ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                            }`}
                            aria-label="Scroll library tabs right"
                          >
                            <FiChevronRight className="h-3 w-3" />
                          </button>
                        </div>

                        {/* Scrollable Tabs Container */}
                        <div 
                          ref={iconLibsCarouselRef}
                          className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
                          style={{
                            scrollbarWidth: 'none',
                            msOverflowStyle: 'none',
                          }}
                        >
                          {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                            <button
                              key={lib.id}
                              type="button"
                              onClick={() => setSelectedIconSet(lib.id as any)}
                              className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                                selectedIconSet === lib.id
                                  ? 'bg-[#00C2FF] text-white'
                                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                              }`}
                            >
                              {lib.name}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Search Input */}
                      <div className="p-2 border-b border-gray-600">
                        <input
                          type="text"
                          placeholder="Search icons..."
                          value={iconSearchTerm}
                          onChange={(e) => setIconSearchTerm(e.target.value)}
                          className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                        />
                      </div>

                      {/* Icon Grid */}
                      <div className="p-2">
                        <div className="grid grid-cols-6 gap-1">
                          {(() => {
                            const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                            if (!currentLibrary) return null;

                            return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                              const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary];
                              
                              const fullIconName = iconName;
                              return (
                                <button
                                  key={iconName}
                                  type="button"
                                  onClick={() => {
                                    setNewFeature(prev => ({ ...prev, icon: fullIconName }));
                                    setShowIconSelector(null);
                                  }}
                                  className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors"
                                  title={iconName}
                                >
                                  {IconComponent && React.createElement(IconComponent, { className: "text-lg text-white hover:text-[#00C2FF] transition-colors" })}
                                </button>
                              );
                            });
                          })()}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">English Text</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={newFeature.english}
                  onChange={e => setNewFeature(prev => ({ ...prev, english: e.target.value }))}
                  placeholder="Feature text"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">Arabic Text</label>
                <input
                  type="text"
                  dir="rtl"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={newFeature.arabic}
                  onChange={e => setNewFeature(prev => ({ ...prev, arabic: e.target.value }))}
                  placeholder="نص الميزة"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={addFeature}
                  className="w-full px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] flex items-center justify-center"
                >
                  <FiPlus className="mr-1 h-4 w-4" />
                  Add Feature
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 