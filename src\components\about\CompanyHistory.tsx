"use client";

import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

const CompanyHistory = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

  const historyItems = [
    {
      year: 2005,
      title: "Foundation",
      description: "Mazaya Capital was founded with a vision to transform the real estate landscape through innovative development projects.",
      icon: "building",
      image: "https://via.placeholder.com/800x450/1f2937/00C2FF?text=Foundation+2005",
      color: "from-blue-500 to-cyan-500"
    },
    {
      year: 2008,
      title: "First Major Project",
      description: "Completed our first major residential development, establishing our reputation for quality and reliability.",
      icon: "home",
      image: "https://via.placeholder.com/800x450/374151/00C2FF?text=First+Project+2008",
      color: "from-emerald-500 to-teal-500"
    },
    {
      year: 2012,
      title: "Expansion into Commercial",
      description: "Expanded our portfolio to include commercial properties, diversifying our investment opportunities.",
      icon: "office-building",
      image: "https://via.placeholder.com/800x450/4b5563/00C2FF?text=Commercial+2012",
      color: "from-purple-500 to-violet-500"
    },
    {
      year: 2015,
      title: "International Recognition",
      description: "Received international recognition for architectural excellence and sustainable design practices.",
      icon: "globe",
      image: "https://via.placeholder.com/800x450/6b7280/00C2FF?text=Recognition+2015",
      color: "from-orange-500 to-amber-500"
    },
    {
      year: 2018,
      title: "Technological Innovation",
      description: "Pioneered the use of smart home technology in all residential developments, setting new industry standards.",
      icon: "chip",
      image: "https://via.placeholder.com/800x450/9ca3af/1f2937?text=Innovation+2018",
      color: "from-rose-500 to-pink-500"
    },
    {
      year: 2021,
      title: "Sustainability Focus",
      description: "Committed to sustainable development with implementation of green building practices across all projects.",
      icon: "leaf",
      image: "https://via.placeholder.com/800x450/d1d5db/1f2937?text=Sustainability+2021",
      color: "from-green-500 to-emerald-500"
    },
    {
      year: 2023,
      title: "Portfolio Expansion",
      description: "Expanded our investment portfolio to include hospitality and retail sectors, creating integrated lifestyle destinations.",
      icon: "chart-bar",
      image: "https://via.placeholder.com/800x450/e5e7eb/1f2937?text=Expansion+2023",
      color: "from-indigo-500 to-blue-500"
    },
  ];
  
  // Simple intersection observer for timeline items
  useEffect(() => {
    const observers: IntersectionObserver[] = [];
    
    itemRefs.current.forEach((ref, index) => {
      if (ref) {
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                setActiveIndex(index);
              }
            });
          },
          {
            threshold: 0.5,
            rootMargin: '-20% 0px -20% 0px'
          }
        );
      
      observer.observe(ref);
        observers.push(observer);
      }
    });
    
    return () => {
      observers.forEach((observer) => observer.disconnect());
    };
  }, []);

  const renderIcon = (iconName: string) => {
    const iconClass = "w-7 h-7";
    
    switch (iconName) {
      case "building":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path d="M11.584 2.376a.75.75 0 0 1 .832 0l9 6a.75.75 0 1 1-.832 1.248L12 3.901 3.416 9.624a.75.75 0 0 1-.832-1.248l9-6Z" />
            <path fillRule="evenodd" d="M20.25 10.332v9.918H21a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1 0-1.5h.75v-9.918a.75.75 0 0 1 .634-.74A49.109 49.109 0 0 1 12 9c2.59 0 5.134.175 7.616.518a.75.75 0 0 1 .634.74Zm-7.5 2.418a.75.75 0 0 0-1.5 0v6.75a.75.75 0 0 0 1.5 0v-6.75Zm3-.75a.75.75 0 0 1 .75.75v6.75a.75.75 0 0 1-1.5 0v-6.75a.75.75 0 0 1 .75-.75ZM9 12.75a.75.75 0 0 0-1.5 0v6.75a.75.75 0 0 0 1.5 0v-6.75Z" clipRule="evenodd" />
          </svg>
        );
      case "home":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path d="M19.006 3.705a.75.75 0 1 0-.512-1.41L6 6.838V3a.75.75 0 0 0-.75-.75h-1.5A.75.75 0 0 0 3 3v4.93l-1.006.365a.75.75 0 0 0 .512 1.41l16.5-6Z" />
            <path fillRule="evenodd" d="M3.019 11.115 18 5.667V9.09l4.006 1.456a.75.75 0 1 1-.512 1.41l-.494-.18v8.475h.75a.75.75 0 0 1 0 1.5H2.25a.75.75 0 0 1 0-1.5H3v-9.129l.019-.006ZM18 20.25v-9.565l1.5.545v9.02H18Zm-9-6a.75.75 0 0 0-.75.75v4.5c0 .414.336.75.75.75h3a.75.75 0 0 0 .75-.75V15a.75.75 0 0 0-.75-.75H9Z" clipRule="evenodd" />
          </svg>
        );
      case "office-building":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path fillRule="evenodd" d="M4.5 2.25a.75.75 0 0 0-.75.75v16.5c0 .414.336.75.75.75h15a.75.75 0 0 0 .75-.75V3a.75.75 0 0 0-.75-.75h-15ZM6 6v.75H5.25V6H6Zm-.75 2.25h.75v.75H5.25v-.75Zm.75 1.5v.75H5.25v-.75H6Zm-.75 2.25h.75v.75H5.25v-.75Zm.75 1.5v.75H5.25v-.75H6Zm6-7.5v.75h-.75V6h.75Zm-.75 2.25h.75v.75h-.75v-.75Zm.75 1.5v.75h-.75v-.75h.75Zm-.75 2.25h.75v.75h-.75v-.75Zm.75 1.5v.75h-.75v-.75h.75Zm6-7.5v.75h-.75V6h.75Zm-.75 2.25h.75v.75h-.75v-.75Zm.75 1.5v.75h-.75v-.75h.75Zm-.75 2.25h.75v.75h-.75v-.75Zm.75 1.5v.75h-.75v-.75h.75Z" clipRule="evenodd" />
          </svg>
        );
      case "globe":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path d="M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z" />
          </svg>
        );
      case "chip":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path d="M16.5 7.5h-9v9h9v-9Z" />
            <path fillRule="evenodd" d="M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z" clipRule="evenodd" />
          </svg>
        );
      case "leaf":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path fillRule="evenodd" d="M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z" clipRule="evenodd" />
          </svg>
        );
      case "chart-bar":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path d="M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75ZM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 0 1-1.875-1.875V8.625ZM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 0 1 3 19.875v-6.75Z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className={iconClass}>
            <path fillRule="evenodd" d="M3 2.25a.75.75 0 0 0 0 1.5v16.5h-.75a.75.75 0 0 0 0 1.5H15v-18a.75.75 0 0 0 0-1.5H3ZM6.75 19.5v-2.25a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h.75a.75.75 0 0 1 0 1.5h-.75A.75.75 0 0 1 6 6.75ZM6.75 9a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM6 12.75a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 6a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75Zm-.75 3.75A.75.75 0 0 1 10.5 9h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 12a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM16.5 6.75v15h5.25a.75.75 0 0 0 0-1.5H21v-12a.75.75 0 0 0 0-1.5h-4.5Zm1.5 4.5a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 2.25a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75v-.008a.75.75 0 0 0-.75-.75h-.008ZM18 17.25a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  return (
    <section className="relative py-16 md:py-24 bg-white dark:bg-gray-900">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-blue-50/30 dark:from-gray-800/30 dark:to-gray-900"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Professional Section Header */}
        <motion.div 
          className="text-center mb-16 md:mb-20"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center px-4 py-2 bg-[#00C2FF]/10 text-[#00C2FF] rounded-full text-sm font-medium mb-6">
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
            Company Timeline
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Our Journey of <span className="text-[#00C2FF]">Growth</span>
          </h2>
          
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Discover the key milestones that have shaped Mazaya Capital into a leading real estate development company.
          </p>
        </motion.div>
        
        {/* Clean Timeline */}
        <div className="relative max-w-6xl mx-auto">
          {/* Timeline Line */}
          <div className="absolute left-8 md:left-1/2 md:-ml-0.5 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
          
          {/* Progress Line */}
          <motion.div 
            className="absolute left-8 md:left-1/2 md:-ml-0.5 top-0 w-0.5 bg-[#00C2FF] z-10"
            initial={{ height: 0 }}
            animate={{ height: `${((activeIndex + 1) / historyItems.length) * 100}%` }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
          />

          {/* Timeline Items */}
          <div className="space-y-12 md:space-y-16">
            {historyItems.map((item, index) => (
              <motion.div
                key={index}
                ref={(el) => {itemRefs.current[index] = el}}
                className={`relative flex flex-col md:flex-row md:items-center ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                }`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, margin: "-10%" }}
              >
                {/* Timeline Marker */}
                <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 -translate-x-1/2 z-20">
        <motion.div 
                    className={`w-4 h-4 rounded-full border-4 bg-white dark:bg-gray-900 transition-all duration-500 ${
                      index <= activeIndex 
                        ? 'border-[#00C2FF] shadow-lg shadow-[#00C2FF]/25' 
                        : 'border-gray-300 dark:border-gray-600'
                    }`}
                    whileHover={{ scale: 1.2 }}
                  >
                    {index <= activeIndex && (
                      <motion.div
                        className="absolute inset-1 bg-[#00C2FF] rounded-full"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                      />
                    )}
        </motion.div>
        </div>
        
                {/* Content Card */}
                <div className={`flex-1 md:w-5/12 ml-16 md:ml-0 ${
                  index % 2 === 0 ? 'md:pr-12' : 'md:pl-12'
                }`}>
        <motion.div 
                    className="bg-white dark:bg-gray-800 rounded-xl p-6 md:p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
                    whileHover={{ y: -4 }}
                  >
                    {/* Year Badge */}
                    <div className="flex items-center gap-4 mb-4">
                      <div className="flex-shrink-0 w-12 h-12 bg-[#00C2FF] rounded-lg flex items-center justify-center">
                        <div className="text-white">
                          {renderIcon(item.icon)}
                        </div>
                      </div>
                      <div>
                        <div className="text-2xl md:text-3xl font-bold text-[#00C2FF]">
                          {item.year}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                          Milestone
                        </div>
                      </div>
                    </div>
                    
                    <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-3 leading-tight">
                      {item.title}
                    </h3>
                    
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {item.description}
                    </p>
        </motion.div>
                </div>
        
                {/* Image */}
                <div className={`flex-1 md:w-5/12 mt-6 md:mt-0 ml-16 md:ml-0 ${
                  index % 2 === 0 ? 'md:pl-12' : 'md:pr-12'
                }`}>
        <motion.div 
                    className="relative h-48 md:h-64 rounded-xl overflow-hidden shadow-lg group"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Image
                      src={item.image}
                      alt={item.title}
                      width={800}
                      height={450}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                      sizes="(max-width: 768px) 100vw, 50vw"
                    />
                    
                    {/* Simple overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                    
                    {/* Image info */}
                    <div className="absolute bottom-4 left-4 text-white">
                      <div className="text-lg font-semibold">{item.year}</div>
                      <div className="text-sm opacity-90">{item.title}</div>
                    </div>

                    {/* Icon badge */}
                    <div className="absolute top-4 right-4 w-10 h-10 bg-white/90 dark:bg-gray-800/90 rounded-lg flex items-center justify-center">
                      <div className="text-[#00C2FF] w-5 h-5">
                        {renderIcon(item.icon)}
                      </div>
                    </div>
        </motion.div>
                </div>
        </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CompanyHistory;