"use client";

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { FiEdit2, FiTrash2, FiPlus, FiSearch, FiX, FiSave, FiUpload, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import * as Icons from 'react-icons/fi';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';

// Icon libraries configuration
const iconLibraries = [
  { id: 'fi', name: '<PERSON>ather', library: Icons },
  { id: 'fc', name: 'Flat Color', library: FcIcons },
  { id: 'fa', name: 'Font Awesome', library: FaIcons },
  { id: 'fa6', name: 'Font Awesome 6', library: FaIconsSolid },
  { id: 'bs', name: 'Bootstrap', library: BsIcons },
  { id: 'ri', name: 'Remix', library: RiIcons },
  { id: 'gi', name: 'Game Icons', library: GiIcons },
  { id: 'tb', name: 'Tabler', library: TbIcons },
  { id: 'md', name: 'Material Design', library: MdIcons },
  { id: 'hi', name: 'Heroicons', library: HiIcons },
  { id: 'ai', name: 'Ant Design', library: AiIcons },
  { id: 'io', name: 'Ionicons 4', library: IoIcons },
  { id: 'io5', name: 'Ionicons 5', library: Io5Icons },
  { id: 'pi', name: 'Phosphor', library: PiIcons },
];

// Interface for category
interface Category {
  id: number;
  english: {
    name: string;
    slug: string;
  };
  arabic: {
    name: string;
    slug: string;
  };
  icon: string;
  status: 'active' | 'inactive';
  createdAt: string;
  projectCount: number;
}

// Mock categories data
const MOCK_CATEGORIES: Category[] = [
  {
    id: 1,
    english: { name: "Residential", slug: "residential" },
    arabic: { name: "سكني", slug: "سكني" },
    icon: "FiHome",
    status: "active",
    createdAt: "2023-05-15T09:00:00Z",
    projectCount: 12
  },
  {
    id: 2,
    english: { name: "Commercial", slug: "commercial" },
    arabic: { name: "تجاري", slug: "تجاري" },
    icon: "FiBuilding",
    status: "active",
    createdAt: "2023-04-12T14:30:00Z",
    projectCount: 8
  },
  {
    id: 3,
    english: { name: "Mixed-Use", slug: "mixed-use" },
    arabic: { name: "مختلط الاستخدام", slug: "مختلط-الاستخدام" },
    icon: "FiLayers",
    status: "active",
    createdAt: "2023-03-28T11:15:00Z",
    projectCount: 5
  },
  {
    id: 4,
    english: { name: "Retail", slug: "retail" },
    arabic: { name: "تجاري التجزئة", slug: "تجارة-التجزئة" },
    icon: "FiShoppingBag",
    status: "inactive",
    createdAt: "2023-02-10T16:45:00Z",
    projectCount: 3
  }
];

export default function AdminProjectCategories() {
  // Add custom CSS for hiding scrollbars
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const [categories, setCategories] = useState<Category[]>(MOCK_CATEGORIES);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<number | null>(null);

  // Icon selector state
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconSet, setSelectedIconSet] = useState<string>('fi');

  // Icon library carousel refs and state
  const iconLibsCarouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLibsLeft, setCanScrollLibsLeft] = useState(false);
  const [canScrollLibsRight, setCanScrollLibsRight] = useState(true);

  // Form state for add/edit modal
  const [formData, setFormData] = useState({
    english: { name: '', slug: '' },
    arabic: { name: '', slug: '' },
    icon: 'FiHome',
    status: 'active' as 'active' | 'inactive'
  });

  // Icon library carousel scroll functions
  const scrollLibsLeft = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({ left: -150, behavior: 'smooth' });
    }
  };

  const scrollLibsRight = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({ left: 150, behavior: 'smooth' });
    }
  };

  const checkLibsScrollPosition = () => {
    if (iconLibsCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = iconLibsCarouselRef.current;
      setCanScrollLibsLeft(scrollLeft > 0);
      setCanScrollLibsRight(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  // Check scroll position on mount and when content changes
  useEffect(() => {
    checkLibsScrollPosition();
    const carousel = iconLibsCarouselRef.current;
    if (carousel) {
      carousel.addEventListener('scroll', checkLibsScrollPosition);
      const resizeObserver = new ResizeObserver(checkLibsScrollPosition);
      resizeObserver.observe(carousel);
      
      return () => {
        carousel.removeEventListener('scroll', checkLibsScrollPosition);
        resizeObserver.disconnect();
      };
    }
  }, [iconLibraries, iconSearchTerm]);

  // Function to determine if a library has any icons matching the search term
  const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
    if (!searchTerm.trim()) return true;
    
    const library = iconLibraries.find(lib => lib.id === libraryId)?.library;
    if (!library) return false;
    
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).some(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    );
  };
  
  // Function to get the icon component based on the icon name
  const getIconComponent = (iconName: string): React.ReactElement => {
    if (!iconName) return <Icons.FiHome className="h-5 w-5" />;
    
    // Determine which library the icon belongs to based on prefix
    if (iconName.startsWith('Fc')) {
        const IconFc = FcIcons[iconName as keyof typeof FcIcons];
        return IconFc ? <IconFc className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Fa6')) {
        const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
        return IconFa6 ? <IconFa6 className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Fa')) {
      const IconFa = FaIcons[iconName as keyof typeof FaIcons];
      return IconFa ? <IconFa className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Bs')) {
        const IconBs = BsIcons[iconName as keyof typeof BsIcons];
        return IconBs ? <IconBs className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Ri')) {
        const IconRi = RiIcons[iconName as keyof typeof RiIcons];
        return IconRi ? <IconRi className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Gi')) {
        const IconGi = GiIcons[iconName as keyof typeof GiIcons];
        return IconGi ? <IconGi className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Tb')) {
        const IconTb = TbIcons[iconName as keyof typeof TbIcons];
        return IconTb ? <IconTb className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Md')) {
        const IconMd = MdIcons[iconName as keyof typeof MdIcons];
        return IconMd ? <IconMd className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Hi')) {
        const IconHi = HiIcons[iconName as keyof typeof HiIcons];
        return IconHi ? <IconHi className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Ai')) {
        const IconAi = AiIcons[iconName as keyof typeof AiIcons];
        return IconAi ? <IconAi className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Io5')) {
        const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
        return IconIo5 ? <IconIo5 className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Io')) {
      const IconIo = IoIcons[iconName as keyof typeof IoIcons];
      return IconIo ? <IconIo className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Pi')) {
        const IconPi = PiIcons[iconName as keyof typeof PiIcons];
        return IconPi ? <IconPi className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    } else if (iconName.startsWith('Fi')) {
        const IconFi = Icons[iconName as keyof typeof Icons];
        return IconFi ? <IconFi className="h-5 w-5" /> : <Icons.FiHome className="h-5 w-5" />;
    }
    
    // Default fallback
    return <Icons.FiHome className="h-5 w-5" />;
  };

  const getFilteredIcons = (library: any, searchTerm: string) => {
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).filter(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    ).slice(0, 50); // Limit to 50 icons for performance
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter categories based on search and status
  const filteredCategories = categories.filter(category => {
    const matchesSearch = 
      category.english.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.arabic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.english.slug.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || category.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Generate slug from name
  const generateSlug = (name: string, isArabic: boolean = false) => {
    if (isArabic) {
      return name.toLowerCase().replace(/\s+/g, '-');
    }
    return name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string, language?: 'english' | 'arabic') => {
    if (language) {
      setFormData(prev => ({
        ...prev,
        [language]: {
          ...prev[language],
          [field]: value,
          ...(field === 'name' && { slug: generateSlug(value, language === 'arabic') })
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  // Open add modal
  const handleAddNew = () => {
    setEditingCategory(null);
    setFormData({
      english: { name: '', slug: '' },
      arabic: { name: '', slug: '' },
      icon: 'FiHome',
      status: 'active'
    });
    setShowModal(true);
  };

  // Open edit modal
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      english: category.english,
      arabic: category.arabic,
      icon: category.icon,
      status: category.status
    });
    setShowModal(true);
  };

  // Save category (add or edit)
  const handleSave = () => {
    if (!formData.english.name || !formData.arabic.name) {
      alert('Please fill in all required fields');
      return;
    }

    const categoryData = {
      english: formData.english,
      arabic: formData.arabic,
      icon: formData.icon,
      status: formData.status,
      projectCount: editingCategory?.projectCount || 0
    };

    if (editingCategory) {
      // Update existing category
      setCategories(prev => prev.map(cat => 
        cat.id === editingCategory.id 
          ? { ...cat, ...categoryData }
          : cat
      ));
    } else {
      // Add new category
      const newCategory: Category = {
        id: Date.now(),
        ...categoryData,
        createdAt: new Date().toISOString()
      };
      setCategories(prev => [newCategory, ...prev]);
    }

    setShowModal(false);
  };

  // Handle delete
  const handleDeleteClick = (categoryId: number) => {
    setCategoryToDelete(categoryId);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (categoryToDelete) {
      setCategories(prev => prev.filter(cat => cat.id !== categoryToDelete));
      setShowDeleteModal(false);
      setCategoryToDelete(null);
    }
  };

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Project Categories</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage project categories and their properties
          </p>
        </div>
        <button
          onClick={handleAddNew}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
        >
          <FiPlus className="-ml-1 mr-2 h-5 w-5" />
          New Category
        </button>
      </div>

      {/* Filters */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-4 sm:p-6 border border-gray-700">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-300">
              Search
            </label>
            <div className="relative rounded-md shadow-sm mt-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                name="search"
                id="search"
                className="bg-gray-700 focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full pl-10 py-2 sm:text-sm border-gray-600 rounded-md text-white h-10"
                placeholder="Search categories"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-300">
              Status
            </label>
            <select
              id="status"
              name="status"
              className="mt-1 block w-full pl-3 pr-10 py-2 sm:text-sm border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-[#00C2FF] focus:border-[#00C2FF] rounded-md h-10"
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
            >
              <option value="all">All</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Categories Table */}
      <div className="mt-6 flex flex-col">
        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div className="shadow overflow-hidden border-b border-gray-700 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      English Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Arabic Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Projects
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Created
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-900 divide-y divide-gray-700">
                  {filteredCategories.map(category => (
                    <tr key={category.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-gray-700 rounded-lg flex items-center justify-center text-[#00C2FF]">
                            {getIconComponent(category.icon)}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-white">{category.english.name}</div>
                            <div className="text-sm text-gray-400">{category.english.slug}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">{category.english.name}</div>
                        <div className="text-sm text-gray-400">{category.english.slug}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">{category.arabic.name}</div>
                        <div className="text-sm text-gray-400">{category.arabic.slug}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-gray-400">{category.projectCount} projects</span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            category.status === 'active' 
                              ? 'bg-green-900/50 text-green-400' 
                              : 'bg-red-900/50 text-red-400'
                          }`}
                        >
                          {category.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {formatDate(category.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleEdit(category)}
                            className="text-indigo-400 hover:text-indigo-300"
                            title="Edit"
                          >
                            <FiEdit2 className="h-5 w-5" />
                          </button>
                          <button
                            onClick={() => handleDeleteClick(category.id)}
                            className="text-red-400 hover:text-red-300"
                            title="Delete"
                          >
                            <FiTrash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Modal */}
      {showModal && (
        <div className="fixed z-50 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full border border-gray-700">
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4 overflow-visible">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg leading-6 font-medium text-white">
                    {editingCategory ? 'Edit Category' : 'Add New Category'}
                  </h3>
                  <button
                    onClick={() => setShowModal(false)}
                    className="text-gray-400 hover:text-white"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>

                <div className="space-y-6">
                  {/* English Fields */}
                  <div>
                    <h4 className="text-md font-medium text-white mb-3">English</h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-300">Name *</label>
                        <input
                          type="text"
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                          value={formData.english.name}
                          onChange={e => handleInputChange('name', e.target.value, 'english')}
                          placeholder="Enter English name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300">Slug</label>
                        <input
                          type="text"
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                          value={formData.english.slug}
                          onChange={e => handleInputChange('slug', e.target.value, 'english')}
                          placeholder="auto-generated"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Arabic Fields */}
                  <div>
                    <h4 className="text-md font-medium text-white mb-3">Arabic</h4>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-300">Name *</label>
                        <input
                          type="text"
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                          value={formData.arabic.name}
                          onChange={e => handleInputChange('name', e.target.value, 'arabic')}
                          placeholder="ادخل الاسم بالعربي"
                          dir="rtl"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300">Slug</label>
                        <input
                          type="text"
                          className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                          value={formData.arabic.slug}
                          onChange={e => handleInputChange('slug', e.target.value, 'arabic')}
                          placeholder="تلقائي"
                          dir="rtl"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Advanced Icon Selector and Status */}
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {/* Advanced Icon Selector */}
                    <div className="relative icon-selector-container">
                      <label className="block text-sm font-medium text-gray-300 mb-1">Icon</label>
                                            <button                        type="button"                        onClick={() => setShowIconSelector(!showIconSelector)}                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] flex items-center justify-between"                      >                        <span className="flex items-center min-w-0 flex-1">                          <span key={formData.icon} className="flex-shrink-0">                            {getIconComponent(formData.icon)}                          </span>                          <span className="ml-2 text-xs truncate min-w-0">{formData.icon || 'Select Icon'}</span>                        </span>                        <FiSearch className="h-3 w-3" />                      </button>                      {showIconSelector && (                        <div                           className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-xl max-h-80 overflow-y-auto min-w-[400px]"                           style={{                            scrollbarWidth: 'thin',                            scrollbarColor: '#6b7280 transparent'                          }}                        >
                          {/* Icon Library Tabs with Carousel */}
                          <div className="relative border-b border-gray-600">
                            {/* Navigation Buttons */}
                            <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                              <button
                                type="button"
                                onClick={scrollLibsLeft}
                                disabled={!canScrollLibsLeft}
                                className={`p-1 rounded-r transition-colors ${
                                  canScrollLibsLeft 
                                    ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                                    : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                                }`}
                                aria-label="Scroll library tabs left"
                              >
                                <FiChevronLeft className="h-3 w-3" />
                              </button>
                            </div>
                            
                            <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                              <button
                                type="button"
                                onClick={scrollLibsRight}
                                disabled={!canScrollLibsRight}
                                className={`p-1 rounded-l transition-colors ${
                                  canScrollLibsRight 
                                    ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                                    : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                                }`}
                                aria-label="Scroll library tabs right"
                              >
                                <FiChevronRight className="h-3 w-3" />
                              </button>
                            </div>

                            {/* Scrollable Tabs Container */}
                            <div 
                              ref={iconLibsCarouselRef}
                              className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
                              style={{
                                scrollbarWidth: 'none',
                                msOverflowStyle: 'none',
                              }}
                            >
                              {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                                <button
                                  key={lib.id}
                                  type="button"
                                  onClick={() => setSelectedIconSet(lib.id)}
                                  className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                                    selectedIconSet === lib.id
                                      ? 'bg-[#00C2FF] text-white'
                                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                                  }`}
                                >
                                  {lib.name}
                                </button>
                              ))}
                            </div>
                          </div>

                          {/* Search Input */}
                          <div className="p-2 border-b border-gray-600">
                            <input
                              type="text"
                              placeholder="Search icons..."
                              value={iconSearchTerm}
                              onChange={(e) => setIconSearchTerm(e.target.value)}
                              className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                            />
                          </div>

                          {/* Icon Grid */}
                          <div className="p-2">
                            <div className="grid grid-cols-3 gap-1">
                              {(() => {
                                const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                                if (!currentLibrary) return null;

                                return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                                  const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary] as React.ComponentType<{ className?: string }>;
                                  
                                  const fullIconName = iconName;
                                  return (
                                    <button
                                      key={iconName}
                                      type="button"
                                      onClick={() => {
                                        setFormData(prev => ({ ...prev, icon: fullIconName }));
                                        setShowIconSelector(false);
                                      }}
                                      className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors"
                                      title={iconName}
                                    >
                                      {IconComponent && <IconComponent className="text-lg text-white hover:text-[#00C2FF] transition-colors" />}
                                    </button>
                                  );
                                });
                              })()}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300">Status</label>
                      <select
                        className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={formData.status}
                        onChange={e => handleInputChange('status', e.target.value)}
                      >
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-700">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-[#00C2FF] text-base font-medium text-white hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleSave}
                >
                  <FiSave className="-ml-1 mr-2 h-5 w-5" />
                  {editingCategory ? 'Update' : 'Create'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-700">
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-white">Confirm Delete</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-300">
                        Are you sure you want to delete this category? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-700">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={confirmDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 