import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  FiX, FiInfo, FiEye, FiCheck, FiAlertTriangle, FiRefreshCw, 
  FiBarChart, FiSearch, FiTarget, FiTrendingUp, FiUpload,
  FiImage, FiLink, FiZap, FiSettings, FiCpu, FiEdit3,
  FiFileText, FiClock, FiTool, FiAward, FiChevronDown,
  FiChevronUp, FiCopy, FiDownload, FiShare2, FiBookOpen
} from 'react-icons/fi';

// Enhanced SEO Data Interface for Articles
interface ArticleSEOData {
  // Basic Meta
  title: {
    english: string;
    arabic: string;
  };
  description: {
    english: string;
    arabic: string;
  };
  keywords: {
    english: string[];
    arabic: string[];
  };
  
  // Focus Keywords for analysis
  focusKeywords: {
    primary: string;
    secondary: string[];
  };
  
  // Article Schema
  schema: {
    enabled: boolean;
    type: 'Article' | 'BlogPosting' | 'NewsArticle';
    customSchema: string;
  };
  
  // Images
  featuredImage: {
    url: string;
    alt: {
      english: string;
      arabic: string;
    };
    caption: {
      english: string;
      arabic: string;
    };
  };
  
  // Content optimization
  contentOptimization: {
    targetWordCount: number;
    readabilityTarget: 'easy' | 'medium' | 'hard';
    headingStructure: boolean;
    internalLinks: {
      url: string;
      anchor: string;
      language: 'english' | 'arabic';
    }[];
  };
  
  // FAQ Schema
  faqSchema: {
    enabled: boolean;
    questions: {
      question: string;
      answer: string;
      language: 'english' | 'arabic';
    }[];
  };
  
  // Breadcrumbs
  breadcrumbs: {
    enabled: boolean;
    items: {
      name: string;
      url: string;
    }[];
  };
  
  // Technical
  canonical: string;
  robots: {
    index: boolean;
    follow: boolean;
  };
}

interface AdvancedArticleSEOProps {
  seoData: ArticleSEOData;
  onSEOChange: (seoData: ArticleSEOData) => void;
  content?: string; // Article content for analysis
  className?: string;
}

// Content Analysis Interface
interface ContentAnalysis {
  wordCount: number;
  readabilityScore: number;
  readabilityLevel: 'easy' | 'medium' | 'hard';
  keywordDensity: { [key: string]: number };
  headingStructure: {
    h1: number;
    h2: number;
    h3: number;
    h4: number;
    h5: number;
    h6: number;
  };
  sentenceCount: number;
  averageWordsPerSentence: number;
  passiveVoicePercentage: number;
  flesch: number;
  suggestions: string[];
}

const AdvancedArticleSEO: React.FC<AdvancedArticleSEOProps> = ({
  seoData,
  onSEOChange,
  content = '',
  className = ""
}) => {
  const [activeTab, setActiveTab] = useState('optimization');
  const [contentAnalysis, setContentAnalysis] = useState<ContentAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [newKeyword, setNewKeyword] = useState({ english: '', arabic: '' });
  const [newFAQ, setNewFAQ] = useState({ question: '', answer: '', language: 'english' as 'english' | 'arabic' });
  const [imageUploadMode, setImageUploadMode] = useState<'upload' | 'url'>('upload');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // SEO Score tracking
  const [seoScore, setSeoScore] = useState(0);
  
  // Analyze content for SEO insights
  const analyzeContent = useCallback(async () => {
    if (!content) return;
    
    setIsAnalyzing(true);
    
    // Simulate advanced content analysis
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Separate English and Arabic content for analysis
    const englishContent = content.split(' ').slice(0, Math.floor(content.split(' ').length / 2)).join(' ');
    const arabicContent = content.split(' ').slice(Math.floor(content.split(' ').length / 2)).join(' ');
    
    // Analyze primary content (English for now, can be made configurable)
    const primaryContent = seoData.title.english ? englishContent : content;
    const words = primaryContent.split(/\s+/).filter(word => word.length > 0);
    const sentences = primaryContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    // Calculate keyword density
    const keywordDensity: { [key: string]: number } = {};
    if (seoData.focusKeywords.primary) {
      const primaryCount = (primaryContent.toLowerCase().match(new RegExp(seoData.focusKeywords.primary.toLowerCase(), 'g')) || []).length;
      keywordDensity[seoData.focusKeywords.primary] = words.length > 0 ? (primaryCount / words.length) * 100 : 0;
    }
    
    // Analyze heading structure
    const headingStructure = {
      h1: (primaryContent.match(/<h1/g) || []).length,
      h2: (primaryContent.match(/<h2/g) || []).length,
      h3: (primaryContent.match(/<h3/g) || []).length,
      h4: (primaryContent.match(/<h4/g) || []).length,
      h5: (primaryContent.match(/<h5/g) || []).length,
      h6: (primaryContent.match(/<h6/g) || []).length,
    };
    
    // Calculate Flesch reading ease (simplified)
    const avgSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;
    const avgSyllablesPerWord = 1.5; // Simplified estimate
    const flesch = sentences.length > 0 ? 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord) : 0;
    
    let readabilityLevel: 'easy' | 'medium' | 'hard' = 'medium';
    if (flesch >= 60) readabilityLevel = 'easy';
    else if (flesch < 30) readabilityLevel = 'hard';
    
    // Generate SEO suggestions
    const suggestions: string[] = [];
    
    if (words.length < seoData.contentOptimization.targetWordCount) {
      suggestions.push(`Content is too short. Current: ${words.length} words. Target: ${seoData.contentOptimization.targetWordCount} words.`);
    }
    if (words.length > 3000) suggestions.push('Content is very long. Consider breaking it into sections or multiple articles.');
    if (!seoData.focusKeywords.primary) suggestions.push('Define a primary focus keyword for better optimization.');
    if (seoData.focusKeywords.primary && keywordDensity[seoData.focusKeywords.primary] < 0.5) {
      suggestions.push('Primary keyword density is low. Consider using it more naturally in your content.');
    }
    if (seoData.focusKeywords.primary && keywordDensity[seoData.focusKeywords.primary] > 3) {
      suggestions.push('Primary keyword density is too high. Avoid keyword stuffing.');
    }
    if (headingStructure.h1 === 0) suggestions.push('Add an H1 heading for better structure.');
    if (headingStructure.h2 === 0) suggestions.push('Add H2 headings to break up your content.');
    if (avgSentenceLength > 20) suggestions.push('Some sentences are too long. Break them up for better readability.');
    if (!seoData.featuredImage.url) suggestions.push('Add a featured image with descriptive alt text.');
    if (!seoData.description.english) suggestions.push('Write a compelling meta description to improve click-through rates.');
    if (seoData.title.english.length < 30) suggestions.push('SEO title is too short. Aim for 30-60 characters.');
    if (seoData.title.english.length > 60) suggestions.push('SEO title is too long. Keep it under 60 characters.');
    if (seoData.description.english.length < 120) suggestions.push('Meta description is too short. Aim for 120-160 characters.');
    if (seoData.description.english.length > 160) suggestions.push('Meta description is too long. Keep it under 160 characters.');
    
    const analysis: ContentAnalysis = {
      wordCount: words.length,
      readabilityScore: Math.round(flesch),
      readabilityLevel,
      keywordDensity,
      headingStructure,
      sentenceCount: sentences.length,
      averageWordsPerSentence: Math.round(avgSentenceLength),
      passiveVoicePercentage: Math.random() * 20, // Simplified
      flesch: Math.round(flesch),
      suggestions
    };
    
    setContentAnalysis(analysis);
    
    // Calculate SEO score
    let score = 0;
    
    // Only calculate score if there's actual content
    if (words.length > 0) {
      // Basic SEO elements (40 points total)
      if (seoData.title.english && seoData.title.english.length >= 30 && seoData.title.english.length <= 60) score += 15;
      if (seoData.description.english && seoData.description.english.length >= 120 && seoData.description.english.length <= 160) score += 15;
      if (seoData.focusKeywords.primary) score += 10;
      
      // Content quality (25 points total)
      if (words.length >= seoData.contentOptimization.targetWordCount) score += 10;
      if (headingStructure.h1 > 0) score += 5;
      if (headingStructure.h2 > 0) score += 5;
      if (readabilityLevel === seoData.contentOptimization.readabilityTarget) score += 5;
      
      // Technical SEO (20 points total)
      if (seoData.featuredImage.url && seoData.featuredImage.alt.english) score += 10;
      if (seoData.schema.enabled) score += 5;
      if (seoData.canonical) score += 5;
      
      // Advanced optimization (15 points total)
      if (seoData.focusKeywords.primary && keywordDensity[seoData.focusKeywords.primary] >= 0.5 && keywordDensity[seoData.focusKeywords.primary] <= 3) score += 10;
      if (seoData.contentOptimization.internalLinks.length > 0) score += 5;
    } else {
      // No content means 0% score
      score = 0;
    }
    
    setSeoScore(score);
    setIsAnalyzing(false);
  }, [content, seoData]);
  
  // Auto-analyze when content or SEO data changes
  useEffect(() => {
    const timer = setTimeout(() => {
      if (content) analyzeContent();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [content, seoData, analyzeContent]);
  
  // Handle input changes
  const handleInputChange = (field: string, value: any, language?: 'english' | 'arabic', subField?: string) => {
    let updatedData = { ...seoData };
    
    if (subField && language) {
      (updatedData as any)[field][subField][language] = value;
    } else if (language) {
      (updatedData as any)[field][language] = value;
    } else if (subField) {
      (updatedData as any)[field][subField] = value;
    } else {
      (updatedData as any)[field] = value;
    }
    
    onSEOChange(updatedData);
  };
  
  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const updatedData = {
          ...seoData,
          featuredImage: {
            ...seoData.featuredImage,
            url: event.target?.result as string
          }
        };
        onSEOChange(updatedData);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Add keyword
  const addKeyword = (language: 'english' | 'arabic') => {
    const keyword = newKeyword[language].trim();
    if (keyword && !seoData.keywords[language].includes(keyword)) {
      const updatedData = {
        ...seoData,
        keywords: {
          ...seoData.keywords,
          [language]: [...seoData.keywords[language], keyword]
        }
      };
      onSEOChange(updatedData);
      setNewKeyword(prev => ({ ...prev, [language]: '' }));
    }
  };
  
  // Remove keyword
  const removeKeyword = (language: 'english' | 'arabic', keyword: string) => {
    const updatedData = {
      ...seoData,
      keywords: {
        ...seoData.keywords,
        [language]: seoData.keywords[language].filter(k => k !== keyword)
      }
    };
    onSEOChange(updatedData);
  };
  
  // Add FAQ
  const addFAQ = () => {
    if (newFAQ.question.trim() && newFAQ.answer.trim()) {
      const updatedData = {
        ...seoData,
        faqSchema: {
          ...seoData.faqSchema,
          questions: [...seoData.faqSchema.questions, { ...newFAQ }]
        }
      };
      onSEOChange(updatedData);
      setNewFAQ({ question: '', answer: '', language: 'english' });
    }
  };
  
  // Add internal link
  const addInternalLink = () => {
    const url = prompt('Enter internal URL:');
    const anchor = prompt('Enter anchor text:');
    const language = prompt('Language (english/arabic):') as 'english' | 'arabic';
    
    if (url && anchor && (language === 'english' || language === 'arabic')) {
      const updatedData = {
        ...seoData,
        contentOptimization: {
          ...seoData.contentOptimization,
          internalLinks: [...seoData.contentOptimization.internalLinks, { url, anchor, language }]
        }
      };
      onSEOChange(updatedData);
    }
  };
  
  // Generate schema markup
  const generateArticleSchema = () => {
    const schema = {
      "@context": "https://schema.org",
      "@type": seoData.schema.type,
      "headline": seoData.title.english,
      "description": seoData.description.english,
      "image": seoData.featuredImage.url,
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
      "author": {
        "@type": "Organization",
        "name": "Mazaya Capital"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Mazaya Capital",
        "logo": {
          "@type": "ImageObject",
          "url": "/logo.png"
        }
      }
    };
    
    if (seoData.faqSchema.enabled && seoData.faqSchema.questions.length > 0) {
      (schema as any).mainEntity = seoData.faqSchema.questions.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }));
    }
    
    return schema;
  };
  
  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };
  
  const tabs = [
    { id: 'optimization', name: 'Content Optimization', icon: FiTarget },
    { id: 'analysis', name: 'SEO Analysis', icon: FiBarChart },
    { id: 'schema', name: 'Rich Snippets', icon: FiAward },
    { id: 'images', name: 'Image SEO', icon: FiImage },
    { id: 'advanced', name: 'Advanced', icon: FiSettings }
  ];

  return (
    <div className={className}>
      <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        {/* Header with Score */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium text-white">Article SEO Optimizer</h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">SEO Score:</span>
              <span className={`text-2xl font-bold ${getScoreColor(seoScore)}`}>
                {seoScore}%
              </span>
              {isAnalyzing && <FiRefreshCw className="h-4 w-4 text-blue-400 animate-spin" />}
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={analyzeContent}
              disabled={isAnalyzing || !content}
              className="inline-flex items-center px-3 py-1 border border-blue-600 rounded-md shadow-sm text-sm font-medium text-blue-400 hover:text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <FiCpu className="-ml-1 mr-2 h-4 w-4" />
              {isAnalyzing ? 'Analyzing...' : 'Analyze'}
            </button>
            <button
              onClick={() => setShowSuggestions(!showSuggestions)}
              className="inline-flex items-center px-3 py-1 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
            >
              <FiZap className="-ml-1 mr-2 h-4 w-4" />
              Tips
            </button>
          </div>
        </div>

        {/* SEO Suggestions */}
        {showSuggestions && contentAnalysis && (
          <div className="mb-6 bg-blue-900/20 border border-blue-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium text-blue-200 flex items-center">
                <FiZap className="mr-2 h-4 w-4" />
                SEO Recommendations
              </h4>
              <button
                onClick={() => setShowSuggestions(false)}
                className="text-blue-400 hover:text-blue-300"
              >
                <FiX className="h-4 w-4" />
              </button>
            </div>
            <ul className="space-y-2">
              {contentAnalysis.suggestions.map((suggestion, index) => (
                <li key={index} className="text-sm text-blue-100 flex items-start">
                  <FiCheck className="mr-2 h-3 w-3 mt-1 text-blue-400 flex-shrink-0" />
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-600 mb-6">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-[#00C2FF] text-[#00C2FF]'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'optimization' && (
          <div className="space-y-6">
            {/* Primary SEO Fields */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {/* English SEO */}
              <div className="space-y-4">
                <h4 className="text-md font-medium text-blue-400 flex items-center">
                  <FiTarget className="mr-2 h-4 w-4" />
                  English Optimization
                </h4>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300">SEO Title</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.title.english}
                    onChange={e => handleInputChange('title', e.target.value, 'english')}
                    placeholder="Optimized title for search engines"
                  />
                  <div className="mt-1 text-xs text-gray-400 flex justify-between">
                    <span>{seoData.title.english.length}/60 characters</span>
                    <span className={
                      seoData.title.english.length === 0 ? 'text-gray-500' :
                      seoData.title.english.length < 30 ? 'text-yellow-400' :
                      seoData.title.english.length > 60 ? 'text-red-400' : 'text-green-400'
                    }>
                      {seoData.title.english.length === 0 ? 'Required' :
                       seoData.title.english.length < 30 ? '⚠ Too short' :
                       seoData.title.english.length > 60 ? '⚠ Too long' : '✓ Good'}
                    </span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300">Meta Description</label>
                  <textarea
                    rows={3}
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.description.english}
                    onChange={e => handleInputChange('description', e.target.value, 'english')}
                    placeholder="Compelling description that will appear in search results"
                  />
                  <div className="mt-1 text-xs text-gray-400 flex justify-between">
                    <span>{seoData.description.english.length}/160 characters</span>
                    <span className={
                      seoData.description.english.length === 0 ? 'text-gray-500' :
                      seoData.description.english.length < 120 ? 'text-yellow-400' :
                      seoData.description.english.length > 160 ? 'text-red-400' : 'text-green-400'
                    }>
                      {seoData.description.english.length === 0 ? 'Required' :
                       seoData.description.english.length < 120 ? '⚠ Too short' :
                       seoData.description.english.length > 160 ? '⚠ Too long' : '✓ Perfect'}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">Primary Focus Keyword</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.focusKeywords.primary}
                    onChange={e => handleInputChange('focusKeywords', e.target.value, undefined, 'primary')}
                    placeholder="Main keyword to optimize for"
                  />
                  {contentAnalysis && seoData.focusKeywords.primary && (
                    <div className="mt-1 text-xs text-gray-400">
                      Density: {(contentAnalysis.keywordDensity[seoData.focusKeywords.primary] || 0).toFixed(1)}%
                      <span className={
                        (contentAnalysis.keywordDensity[seoData.focusKeywords.primary] || 0) < 0.5 ? ' text-yellow-400' :
                        (contentAnalysis.keywordDensity[seoData.focusKeywords.primary] || 0) > 3 ? ' text-red-400' : ' text-green-400'
                      }>
                        {(contentAnalysis.keywordDensity[seoData.focusKeywords.primary] || 0) < 0.5 ? ' (Too low)' :
                         (contentAnalysis.keywordDensity[seoData.focusKeywords.primary] || 0) > 3 ? ' (Too high)' : ' (Good)'}
                      </span>
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">Related Keywords</label>
                  <div className="mt-1 flex">
                    <input
                      type="text"
                      className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={newKeyword.english}
                      onChange={e => setNewKeyword(prev => ({ ...prev, english: e.target.value }))}
                      placeholder="Add related keyword"
                      onKeyPress={e => e.key === 'Enter' && addKeyword('english')}
                    />
                    <button
                      onClick={() => addKeyword('english')}
                      className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                    >
                      Add
                    </button>
                  </div>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {seoData.keywords.english.map(keyword => (
                      <span
                        key={keyword}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                      >
                        {keyword}
                        <button
                          onClick={() => removeKeyword('english', keyword)}
                          className="ml-1 text-gray-400 hover:text-white"
                        >
                          <FiX className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {/* Arabic SEO */}
              <div className="space-y-4">
                <h4 className="text-md font-medium text-blue-400">تحسين عربي</h4>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300">عنوان السيو</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.title.arabic}
                    onChange={e => handleInputChange('title', e.target.value, 'arabic')}
                    placeholder="عنوان محسن لمحركات البحث"
                  />
                  <div className="mt-1 text-xs text-gray-400 flex justify-between">
                    <span>{seoData.title.arabic.length}/60 حرف</span>
                    <span className={
                      seoData.title.arabic.length === 0 ? 'text-gray-500' :
                      seoData.title.arabic.length < 30 ? 'text-yellow-400' :
                      seoData.title.arabic.length > 60 ? 'text-red-400' : 'text-green-400'
                    }>
                      {seoData.title.arabic.length === 0 ? 'مطلوب' :
                       seoData.title.arabic.length < 30 ? '⚠ قصير جداً' :
                       seoData.title.arabic.length > 60 ? '⚠ طويل جداً' : '✓ جيد'}
                    </span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300">وصف الميتا</label>
                  <textarea
                    rows={3}
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.description.arabic}
                    onChange={e => handleInputChange('description', e.target.value, 'arabic')}
                    placeholder="وصف جذاب سيظهر في نتائج البحث"
                  />
                  <div className="mt-1 text-xs text-gray-400 flex justify-between">
                    <span>{seoData.description.arabic.length}/160 حرف</span>
                    <span className={
                      seoData.description.arabic.length === 0 ? 'text-gray-500' :
                      seoData.description.arabic.length < 120 ? 'text-yellow-400' :
                      seoData.description.arabic.length > 160 ? 'text-red-400' : 'text-green-400'
                    }>
                      {seoData.description.arabic.length === 0 ? 'مطلوب' :
                       seoData.description.arabic.length < 120 ? '⚠ قصير جداً' :
                       seoData.description.arabic.length > 160 ? '⚠ طويل جداً' : '✓ مثالي'}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">الكلمات المفتاحية</label>
                  <div className="mt-1 flex">
                    <input
                      type="text"
                      dir="rtl"
                      className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={newKeyword.arabic}
                      onChange={e => setNewKeyword(prev => ({ ...prev, arabic: e.target.value }))}
                      placeholder="أضف كلمة مفتاحية"
                      onKeyPress={e => e.key === 'Enter' && addKeyword('arabic')}
                    />
                    <button
                      onClick={() => addKeyword('arabic')}
                      className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                    >
                      إضافة
                    </button>
                  </div>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {seoData.keywords.arabic.map(keyword => (
                      <span
                        key={keyword}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                        dir="rtl"
                      >
                        {keyword}
                        <button
                          onClick={() => removeKeyword('arabic', keyword)}
                          className="mr-1 text-gray-400 hover:text-white"
                        >
                          <FiX className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Content Optimization Settings */}
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-md font-medium text-blue-400 mb-4 flex items-center">
                <FiEdit3 className="mr-2 h-4 w-4" />
                Content Optimization Goals
              </h4>
              <div className="mb-3 text-sm text-gray-400">
                Analysis is based on English content. Add English content to see meaningful results.
              </div>
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                <div>
                  <label className="block text-sm font-medium text-gray-300">Target Word Count (English)</label>
                  <input
                    type="number"
                    min="100"
                    max="5000"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.contentOptimization.targetWordCount}
                    onChange={e => handleInputChange('contentOptimization', parseInt(e.target.value) || 300, undefined, 'targetWordCount')}
                  />
                  {contentAnalysis && (
                    <div className="mt-1 text-xs text-gray-400">
                      Current: {contentAnalysis.wordCount} words
                      {contentAnalysis.wordCount >= seoData.contentOptimization.targetWordCount ? 
                        <span className="text-green-400 ml-1">✓ Goal reached</span> :
                        <span className="text-yellow-400 ml-1">Need {seoData.contentOptimization.targetWordCount - contentAnalysis.wordCount} more</span>
                      }
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Readability Target</label>
                  <select
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.contentOptimization.readabilityTarget}
                    onChange={e => handleInputChange('contentOptimization', e.target.value, undefined, 'readabilityTarget')}
                  >
                    <option value="easy">Easy (General Audience)</option>
                    <option value="medium">Medium (Educated)</option>
                    <option value="hard">Hard (Expert)</option>
                  </select>
                  {contentAnalysis && (
                    <div className="mt-1 text-xs text-gray-400">
                      Current: {contentAnalysis.readabilityLevel} (Score: {contentAnalysis.flesch})
                      {contentAnalysis.readabilityLevel === seoData.contentOptimization.readabilityTarget ?
                        <span className="text-green-400 ml-1">✓ Target achieved</span> :
                        <span className="text-yellow-400 ml-1">Target: {seoData.contentOptimization.readabilityTarget}</span>
                      }
                    </div>
                  )}
                </div>
                <div>
                  <label className="flex items-center mt-6">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                      checked={seoData.contentOptimization.headingStructure}
                      onChange={e => handleInputChange('contentOptimization', e.target.checked, undefined, 'headingStructure')}
                    />
                    <span className="ml-2 text-sm text-gray-300">Enforce Heading Structure</span>
                  </label>
                  <div className="mt-1 text-xs text-gray-400">
                    Requires H1 and H2 headings for better SEO
                  </div>
                </div>
              </div>
            </div>

            {/* Internal Links */}
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-medium text-blue-400 flex items-center">
                  <FiLink className="mr-2 h-4 w-4" />
                  Internal Links ({seoData.contentOptimization.internalLinks.length})
                </h4>
                <button
                  onClick={addInternalLink}
                  className="inline-flex items-center px-3 py-1 border border-gray-500 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-400"
                >
                  <FiLink className="mr-1 h-3 w-3" />
                  Add Link
                </button>
              </div>
              <div className="mb-3 text-sm text-gray-400">
                Add internal links to improve SEO and user experience. Specify language to match your content.
              </div>
              <div className="space-y-2">
                {seoData.contentOptimization.internalLinks.map((link, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-600 px-3 py-2 rounded text-sm">
                    <div className="flex-1">
                      <span className="text-gray-300 font-medium">{link.anchor}</span>
                      <span className="text-gray-500 ml-2">→ {link.url}</span>
                      <span className={`ml-2 px-2 py-0.5 rounded text-xs font-medium ${
                        link.language === 'english' ? 'bg-blue-900 text-blue-200' : 'bg-green-900 text-green-200'
                      }`}>
                        {link.language === 'english' ? 'EN' : 'AR'}
                      </span>
                    </div>
                    <button
                      onClick={() => {
                        const updated = {
                          ...seoData,
                          contentOptimization: {
                            ...seoData.contentOptimization,
                            internalLinks: seoData.contentOptimization.internalLinks.filter((_, i) => i !== index)
                          }
                        };
                        onSEOChange(updated);
                      }}
                      className="text-red-400 hover:text-red-300 ml-2"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                  </div>
                ))}
                {seoData.contentOptimization.internalLinks.length === 0 && (
                  <div className="text-gray-500 text-sm text-center py-6 border-2 border-dashed border-gray-600 rounded-lg">
                    <FiLink className="h-8 w-8 text-gray-600 mx-auto mb-2" />
                    <p>No internal links added yet.</p>
                    <p className="text-xs mt-1">Internal linking helps with SEO and user experience.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analysis' && (
          <div className="space-y-6">
            {contentAnalysis && contentAnalysis.wordCount > 0 ? (
              <>
                {/* Content Metrics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-gray-700 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-blue-400">{contentAnalysis.wordCount}</div>
                    <div className="text-sm text-gray-400">Words</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Target: {seoData.contentOptimization.targetWordCount}
                    </div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-green-400">{contentAnalysis.readabilityScore}</div>
                    <div className="text-sm text-gray-400">Readability</div>
                    <div className="text-xs text-gray-500 mt-1 capitalize">
                      {contentAnalysis.readabilityLevel}
                    </div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-400">{contentAnalysis.sentenceCount}</div>
                    <div className="text-sm text-gray-400">Sentences</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Avg: {contentAnalysis.averageWordsPerSentence} words
                    </div>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-purple-400">
                      {Object.keys(contentAnalysis.headingStructure).reduce((sum, key) => 
                        sum + contentAnalysis.headingStructure[key as keyof typeof contentAnalysis.headingStructure], 0
                      )}
                    </div>
                    <div className="text-sm text-gray-400">Headings</div>
                    <div className="text-xs text-gray-500 mt-1">
                      H1: {contentAnalysis.headingStructure.h1}, H2: {contentAnalysis.headingStructure.h2}
                    </div>
                  </div>
                </div>

                {/* Keyword Analysis */}
                {Object.keys(contentAnalysis.keywordDensity).length > 0 && (
                  <div className="bg-gray-700 rounded-lg p-4">
                    <h4 className="text-md font-medium text-blue-400 mb-4">Keyword Density Analysis</h4>
                    <div className="space-y-3">
                      {Object.entries(contentAnalysis.keywordDensity).map(([keyword, density]) => (
                        <div key={keyword} className="flex items-center justify-between">
                          <span className="text-gray-300">{keyword}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-24 bg-gray-600 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  density < 0.5 ? 'bg-yellow-400' : 
                                  density > 3 ? 'bg-red-400' : 'bg-green-400'
                                }`}
                                style={{ width: `${Math.min(density * 10, 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-400 w-12 text-right">
                              {density.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Heading Structure */}
                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-md font-medium text-blue-400 mb-4">Heading Structure</h4>
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                    {Object.entries(contentAnalysis.headingStructure).map(([level, count]) => (
                      <div key={level} className="text-center">
                        <div className="text-lg font-bold text-gray-300">{count}</div>
                        <div className="text-sm text-gray-500 uppercase">{level}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <FiBarChart className="h-16 w-16 text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-300 mb-2">No Content to Analyze</h3>
                <p className="text-gray-400 mb-6">Start writing your article content to see detailed SEO analysis and recommendations.</p>
                <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4 max-w-md mx-auto">
                  <div className="text-sm text-blue-200">
                    <strong>What you'll get:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-blue-100">
                      <li>Word count and readability analysis</li>
                      <li>Keyword density tracking</li>
                      <li>Heading structure optimization</li>
                      <li>SEO score and recommendations</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'schema' && (
          <div className="space-y-6">
            {/* Article Schema */}
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-medium text-blue-400 flex items-center">
                  <FiAward className="mr-2 h-4 w-4" />
                  Article Schema
                </h4>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                    checked={seoData.schema.enabled}
                    onChange={e => handleInputChange('schema', e.target.checked, undefined, 'enabled')}
                  />
                  <span className="ml-2 text-sm text-gray-300">Enable Schema</span>
                </label>
              </div>
              
              {seoData.schema.enabled && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Schema Type</label>
                    <select
                      className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={seoData.schema.type}
                      onChange={e => handleInputChange('schema', e.target.value, undefined, 'type')}
                    >
                      <option value="Article">Article</option>
                      <option value="BlogPosting">Blog Posting</option>
                      <option value="NewsArticle">News Article</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Generated Schema Preview</label>
                    <pre className="mt-1 bg-gray-600 p-3 rounded text-xs overflow-auto max-h-40 text-gray-300">
                      {JSON.stringify(generateArticleSchema(), null, 2)}
                    </pre>
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(JSON.stringify(generateArticleSchema(), null, 2));
                        alert('Schema copied to clipboard!');
                      }}
                      className="mt-2 inline-flex items-center px-3 py-1 border border-gray-500 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-400"
                    >
                      <FiCopy className="mr-1 h-3 w-3" />
                      Copy Schema
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* FAQ Schema */}
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-medium text-blue-400 flex items-center">
                  <FiBookOpen className="mr-2 h-4 w-4" />
                  FAQ Schema
                </h4>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                    checked={seoData.faqSchema.enabled}
                    onChange={e => handleInputChange('faqSchema', e.target.checked, undefined, 'enabled')}
                  />
                  <span className="ml-2 text-sm text-gray-300">Enable FAQ</span>
                </label>
              </div>
              
              {seoData.faqSchema.enabled && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Question</label>
                      <input
                        type="text"
                        className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={newFAQ.question}
                        onChange={e => setNewFAQ(prev => ({ ...prev, question: e.target.value }))}
                        placeholder="Frequently asked question"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Answer</label>
                      <input
                        type="text"
                        className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={newFAQ.answer}
                        onChange={e => setNewFAQ(prev => ({ ...prev, answer: e.target.value }))}
                        placeholder="Answer to the question"
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <select
                      className="bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={newFAQ.language}
                      onChange={e => setNewFAQ(prev => ({ ...prev, language: e.target.value as 'english' | 'arabic' }))}
                    >
                      <option value="english">English</option>
                      <option value="arabic">Arabic</option>
                    </select>
                    <button
                      onClick={addFAQ}
                      className="px-4 py-2 bg-[#00C2FF] text-white rounded-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                    >
                      Add FAQ
                    </button>
                  </div>
                  
                  <div className="space-y-2">
                    {seoData.faqSchema.questions.map((faq, index) => (
                      <div key={index} className="bg-gray-600 p-3 rounded">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="font-medium text-gray-200">{faq.question}</div>
                            <div className="text-sm text-gray-400 mt-1">{faq.answer}</div>
                            <span className={`inline-block mt-2 px-2 py-0.5 rounded text-xs ${
                              faq.language === 'english' ? 'bg-blue-900 text-blue-200' : 'bg-green-900 text-green-200'
                            }`}>
                              {faq.language === 'english' ? 'English' : 'Arabic'}
                            </span>
                          </div>
                          <button
                            onClick={() => {
                              const updated = {
                                ...seoData,
                                faqSchema: {
                                  ...seoData.faqSchema,
                                  questions: seoData.faqSchema.questions.filter((_, i) => i !== index)
                                }
                              };
                              onSEOChange(updated);
                            }}
                            className="text-red-400 hover:text-red-300 ml-2"
                          >
                            <FiX className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Breadcrumbs */}
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-medium text-blue-400">Breadcrumb Schema</h4>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                    checked={seoData.breadcrumbs.enabled}
                    onChange={e => handleInputChange('breadcrumbs', e.target.checked, undefined, 'enabled')}
                  />
                  <span className="ml-2 text-sm text-gray-300">Enable Breadcrumbs</span>
                </label>
              </div>
              
              {seoData.breadcrumbs.enabled && (
                <div className="text-sm text-gray-400">
                  <div className="flex items-center space-x-2">
                    <span>Home</span>
                    <span>›</span>
                    <span>Articles</span>
                    <span>›</span>
                    <span className="text-white">Current Article</span>
                  </div>
                  <div className="mt-2 text-xs text-gray-500">
                    Breadcrumb schema will be automatically generated based on your site structure.
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'images' && (
          <div className="space-y-6">
            {/* Featured Image */}
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-md font-medium text-blue-400 mb-4 flex items-center">
                <FiImage className="mr-2 h-4 w-4" />
                Featured Image SEO
              </h4>
              
              {/* Image Upload/URL Toggle */}
              <div className="flex space-x-4 mb-4">
                <button
                  onClick={() => setImageUploadMode('upload')}
                  className={`px-4 py-2 rounded-md text-sm font-medium ${
                    imageUploadMode === 'upload'
                      ? 'bg-[#00C2FF] text-white'
                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                  }`}
                >
                  <FiUpload className="inline mr-1 h-4 w-4" />
                  Upload Image
                </button>
                <button
                  onClick={() => setImageUploadMode('url')}
                  className={`px-4 py-2 rounded-md text-sm font-medium ${
                    imageUploadMode === 'url'
                      ? 'bg-[#00C2FF] text-white'
                      : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                  }`}
                >
                  <FiLink className="inline mr-1 h-4 w-4" />
                  Image URL
                </button>
              </div>

              {/* Image Upload */}
              {imageUploadMode === 'upload' && (
                <div className="space-y-4">
                  <div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full border-2 border-dashed border-gray-500 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
                    >
                      <FiUpload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-400">Click to upload or drag and drop</p>
                      <p className="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 10MB</p>
                    </button>
                  </div>
                </div>
              )}

              {/* Image URL */}
              {imageUploadMode === 'url' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300">Image URL</label>
                  <input
                    type="url"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.featuredImage.url}
                    onChange={e => handleInputChange('featuredImage', e.target.value, undefined, 'url')}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              )}

              {/* Image Preview and Alt Text */}
              {seoData.featuredImage.url && (
                <div className="mt-4 space-y-4">
                  <div className="relative">
                    <img
                      src={seoData.featuredImage.url}
                      alt="Featured"
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                      Featured Image
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Alt Text (English)</label>
                      <input
                        type="text"
                        className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={seoData.featuredImage.alt.english}
                        onChange={e => handleInputChange('featuredImage', e.target.value, 'english', 'alt')}
                        placeholder="Describe the image for screen readers and SEO"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Alt Text (Arabic)</label>
                      <input
                        type="text"
                        dir="rtl"
                        className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={seoData.featuredImage.alt.arabic}
                        onChange={e => handleInputChange('featuredImage', e.target.value, 'arabic', 'alt')}
                        placeholder="وصف الصورة للقارئات الآلية والسيو"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Caption (English)</label>
                      <input
                        type="text"
                        className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={seoData.featuredImage.caption.english}
                        onChange={e => handleInputChange('featuredImage', e.target.value, 'english', 'caption')}
                        placeholder="Optional caption that appears below the image"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300">Caption (Arabic)</label>
                      <input
                        type="text"
                        dir="rtl"
                        className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                        value={seoData.featuredImage.caption.arabic}
                        onChange={e => handleInputChange('featuredImage', e.target.value, 'arabic', 'caption')}
                        placeholder="تسمية توضيحية اختيارية تظهر أسفل الصورة"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Image SEO Tips */}
            <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
              <div className="flex">
                <FiInfo className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-blue-200">Image SEO Best Practices</h3>
                  <div className="mt-2 text-sm text-blue-100">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Use descriptive, keyword-rich file names before uploading</li>
                      <li>Optimize image size and compress for faster loading</li>
                      <li>Include your focus keyword in alt text naturally</li>
                      <li>Use proper image dimensions (1200x630 for social sharing)</li>
                      <li>Consider using WebP format for better compression</li>
                      <li>Add captions when they provide value to users</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'advanced' && (
          <div className="space-y-6">
            {/* Technical SEO */}
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-md font-medium text-blue-400 mb-4 flex items-center">
                <FiSettings className="mr-2 h-4 w-4" />
                Technical SEO
              </h4>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300">Canonical URL</label>
                  <input
                    type="url"
                    className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.canonical}
                    onChange={e => handleInputChange('canonical', e.target.value)}
                    placeholder="https://mazayacapital.com/articles/article-slug"
                  />
                  <div className="mt-1 text-xs text-gray-400">
                    <FiInfo className="inline mr-1 h-3 w-3" />
                    Prevents duplicate content issues
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Robots Directives</label>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                        checked={seoData.robots.index}
                        onChange={e => handleInputChange('robots', e.target.checked, undefined, 'index')}
                      />
                      <span className="ml-2 text-sm text-gray-300">Allow indexing</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                        checked={seoData.robots.follow}
                        onChange={e => handleInputChange('robots', e.target.checked, undefined, 'follow')}
                      />
                      <span className="ml-2 text-sm text-gray-300">Follow links</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-gray-700 rounded-lg p-4">
              <h4 className="text-md font-medium text-blue-400 mb-4">Quick Actions</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <button
                  onClick={() => {
                    const metaTags = `<title>${seoData.title.english}</title>\n<meta name="description" content="${seoData.description.english}">`;
                    navigator.clipboard.writeText(metaTags);
                    alert('Meta tags copied!');
                  }}
                  className="flex items-center justify-center px-3 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500"
                >
                  <FiCopy className="mr-1 h-4 w-4" />
                  Copy Meta
                </button>
                <button
                  onClick={() => {
                    const schema = JSON.stringify(generateArticleSchema(), null, 2);
                    navigator.clipboard.writeText(schema);
                    alert('Schema copied!');
                  }}
                  className="flex items-center justify-center px-3 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500"
                >
                  <FiDownload className="mr-1 h-4 w-4" />
                  Export Schema
                </button>
                <button
                  onClick={() => window.open('https://search.google.com/test/rich-results', '_blank')}
                  className="flex items-center justify-center px-3 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500"
                >
                  <FiTrendingUp className="mr-1 h-4 w-4" />
                  Test Rich Results
                </button>
                <button
                  onClick={() => window.open('https://www.google.com/webmasters/tools/page-speed-insights/', '_blank')}
                  className="flex items-center justify-center px-3 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500"
                >
                  <FiZap className="mr-1 h-4 w-4" />
                  PageSpeed Test
                </button>
              </div>
            </div>

            {/* Performance Recommendations */}
            <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4">
              <div className="flex">
                <FiZap className="h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-yellow-200">Performance Tips</h3>
                  <div className="mt-2 text-sm text-yellow-100">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Keep meta descriptions between 120-160 characters for optimal display</li>
                      <li>Use your focus keyword in the first 100 words of your content</li>
                      <li>Include internal links to related articles for better user engagement</li>
                      <li>Add FAQ schema to increase chances of appearing in featured snippets</li>
                      <li>Optimize images with descriptive alt text and proper compression</li>
                      <li>Regular content updates signal freshness to search engines</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer Summary */}
        <div className="mt-8 pt-6 border-t border-gray-600">
          <div className="flex flex-wrap items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-400">SEO Score:</span>
              <span className={`text-lg font-bold ${getScoreColor(seoScore)}`}>
                {seoScore}%
              </span>
              {contentAnalysis && (
                <>
                  <span className="text-gray-500">|</span>
                  <span className="text-sm text-gray-400">
                    {contentAnalysis.wordCount} words, {contentAnalysis.readabilityLevel} reading level
                  </span>
                </>
              )}
            </div>
            <div className="text-xs text-gray-500">
              Last analyzed: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedArticleSEO; 