"use client";

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface BackgroundEffectsProps {
  className?: string;
}

const BackgroundEffects = ({ className = "" }: BackgroundEffectsProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Star particles animation
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions with device pixel ratio for sharp rendering
    const pixelRatio = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    
    canvas.width = rect.width * pixelRatio;
    canvas.height = rect.height * pixelRatio;
    ctx.scale(pixelRatio, pixelRatio);
    
    canvas.style.width = `${rect.width}px`;
    canvas.style.height = `${rect.height}px`;
    
    const stars: Star[] = [];
    
    class Star {
      x: number;
      y: number;
      size: number;
      color: string;
      opacity: number;
      pulse: number;
      pulseSpeed: number;
      
      constructor() {
        // More evenly distribute stars
        this.x = Math.random() * rect.width;
        this.y = Math.random() * rect.height;
        
        // Varying sizes for stars
        this.size = Math.random() * 3 + 0.5;
        
        // Star colors from white to blue to light purple
        const colors = [
          'rgba(255, 255, 255, 1)',
          'rgba(200, 230, 255, 1)',
          'rgba(180, 220, 255, 1)',
          'rgba(140, 180, 255, 1)',
          'rgba(200, 200, 255, 1)'
        ];
        this.color = colors[Math.floor(Math.random() * colors.length)];
        
        // For twinkling effect
        this.opacity = Math.random() * 0.8 + 0.2;
        this.pulse = Math.random(); // Current position in pulse cycle (0-1)
        this.pulseSpeed = 0.005 + Math.random() * 0.015; // How fast this star pulses
      }
      
      update() {
        // Update the pulse cycle
        this.pulse += this.pulseSpeed;
        if (this.pulse > 1) this.pulse = 0;
        
        // Calculate opacity based on the pulse cycle using sine wave
        this.opacity = 0.2 + (Math.sin(this.pulse * Math.PI * 2) * 0.4 + 0.5) * 0.8;
      }
      
      draw() {
        if (!ctx) return;
        
        // Base circle for the star
        ctx.globalAlpha = this.opacity;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        
        // Glow effect
        const glow = ctx.createRadialGradient(
          this.x, this.y, 0,
          this.x, this.y, this.size * 4
        );
        glow.addColorStop(0, `rgba(140, 220, 255, ${this.opacity * 0.7})`);
        glow.addColorStop(1, 'rgba(140, 220, 255, 0)');
        
        ctx.globalAlpha = this.opacity * 0.4;
        ctx.fillStyle = glow;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * 4, 0, Math.PI * 2);
        ctx.fill();
        
        // Reset global alpha
        ctx.globalAlpha = 1;
      }
    }
    
    const createStars = () => {
      // Create a nice number of stars based on the canvas size
      const starCount = Math.min(400, Math.max(200, Math.floor((rect.width * rect.height) / 10000)));
      for (let i = 0; i < starCount; i++) {
        stars.push(new Star());
      }
    };
    
    const animate = () => {
      ctx.clearRect(0, 0, rect.width, rect.height);
      
      // Update and draw stars
      for (let i = 0; i < stars.length; i++) {
        stars[i].update();
        stars[i].draw();
      }
      
      requestAnimationFrame(animate);
    };
    
    createStars();
    animate();
    
    const handleResize = () => {
      // Update canvas dimensions
      const rect = canvas.getBoundingClientRect();
      const pixelRatio = window.devicePixelRatio || 1;
      
      canvas.width = rect.width * pixelRatio;
      canvas.height = rect.height * pixelRatio;
      ctx.scale(pixelRatio, pixelRatio);
      
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;
      
      // Recreate stars to match new dimensions
      stars.length = 0;
      createStars();
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none z-0 ${className}`}>
      {/* Canvas for star particles system */}
      <canvas 
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
      />
    </div>
  );
};

export default BackgroundEffects; 