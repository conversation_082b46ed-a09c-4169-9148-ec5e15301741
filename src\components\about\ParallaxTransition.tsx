"use client";

import React from 'react';

const ParallaxTransition = () => {
  return (
    <div className="relative h-[60px] md:h-[80px] overflow-hidden">
      {/* Top gradient blend */}
      <div className="absolute inset-0 bg-gradient-to-b from-[rgb(var(--color-background))] via-[rgb(var(--color-background))] to-transparent z-10"></div>
      
      {/* Bottom gradient blend */}
      <div className="absolute inset-0 top-1/2 bg-gradient-to-t from-[rgb(var(--color-background))] to-transparent z-10"></div>
      
      {/* Parallax Elements - these will move at different speeds */}
      {/* Using both start/end and left/right for RTL support */}
      <div className="absolute start-[10%] left-[10%] w-[300px] h-[300px] rounded-full bg-[rgb(var(--color-primary))]/5 blur-3xl transform translate-y-[30%] parallax-slow"></div>
      <div className="absolute end-[15%] right-[15%] w-[250px] h-[250px] rounded-full bg-[rgb(var(--color-secondary))]/5 blur-2xl transform translate-y-[20%] parallax-medium"></div>
      <div className="absolute start-[40%] left-[40%] w-[180px] h-[180px] rounded-full bg-[rgb(var(--color-primary))]/10 blur-xl transform translate-y-[40%] parallax-fast"></div>
    </div>
  );
};

export default ParallaxTransition;