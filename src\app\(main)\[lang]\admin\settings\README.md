# Settings Page - Modular Architecture

This directory contains the modular settings page implementation, divided into separate components for better maintainability and development experience.

## Structure

```
settings/
├── page.tsx                    # Main settings page (orchestrator)
├── types/
│   └── index.ts               # Centralized TypeScript interfaces
├── components/
│   ├── index.ts               # Component exports
│   ├── GeneralSettings.tsx    # Site info, language, maintenance
│   ├── AppearanceSettings.tsx # Logo, theme, layout options
│   ├── SecuritySettings.tsx   # Advanced security features
│   ├── IntegrationsSettings.tsx # Analytics, social, email
│   ├── PerformanceSettings.tsx # Caching, compression, CDN
│   └── BackupSettings.tsx     # Backup and storage options
└── README.md                  # This documentation
```

## Components Overview

### 1. GeneralSettings.tsx (~150 lines)
**Purpose**: Basic site configuration and maintenance mode
**Features**:
- Site name and description (bilingual)
- Site URL and admin email
- Language and timezone settings
- Maintenance mode with custom messages

### 2. AppearanceSettings.tsx (~200 lines)
**Purpose**: Visual customization and branding
**Features**:
- Logo upload (main, favicon, apple touch icon)
- Theme colors with color pickers
- Layout options (header, footer, sidebar)
- File upload handling

### 3. SecuritySettings.tsx (~400 lines)
**Purpose**: Comprehensive security configuration
**Features**:
- Authentication & sessions (2FA, timeouts)
- Password policy (complexity, expiration)
- Network security (IP whitelist, rate limiting)
- Security monitoring (alerts, tracking)
- Advanced features (CSP, audit logging, GDPR)

### 4. IntegrationsSettings.tsx (~250 lines)
**Purpose**: Third-party service integrations
**Features**:
- Analytics (Google Analytics, Facebook Pixel)
- Social media links
- Email configuration (SMTP, SendGrid, Mailgun)
- Password visibility toggles

### 5. PerformanceSettings.tsx (~150 lines)
**Purpose**: Website performance optimization
**Features**:
- Caching configuration
- Compression settings with sliders
- CDN setup
- Image optimization with format selection

### 6. BackupSettings.tsx (~120 lines)
**Purpose**: Data backup and recovery
**Features**:
- Automatic backup scheduling
- Storage options (local/cloud)
- Manual backup actions
- Cloud provider configuration

## Key Benefits

### 🔧 **Maintainability**
- Each component focuses on a single responsibility
- Easier to locate and fix issues
- Reduced cognitive load when working on specific features

### 👥 **Team Development**
- Multiple developers can work on different settings simultaneously
- Clear component boundaries prevent merge conflicts
- Easier code reviews with focused changes

### 🚀 **Performance**
- Smaller component bundles
- Better tree-shaking opportunities
- Faster development builds

### 📝 **Type Safety**
- Centralized TypeScript interfaces in `types/index.ts`
- Strong typing for all component props
- Better IDE support and autocomplete

## Usage Examples

### Adding a New Setting

1. **Update the interface** in `types/index.ts`:
```typescript
export interface SettingsData {
  general: {
    // ... existing fields
    newFeature: {
      enabled: boolean;
      value: string;
    };
  };
}
```

2. **Update the component** (e.g., `GeneralSettings.tsx`):
```typescript
// Add to the component's render method
<div className="flex items-center justify-between">
  <div>
    <p className="text-white font-medium">New Feature</p>
    <p className="text-gray-400 text-sm">Description of the feature</p>
  </div>
  <label className="relative inline-flex items-center cursor-pointer">
    <input
      type="checkbox"
      checked={data.newFeature.enabled}
      onChange={(e) => updateData({
        newFeature: { ...data.newFeature, enabled: e.target.checked }
      })}
      className="sr-only peer"
    />
    {/* Toggle switch styling */}
  </label>
</div>
```

3. **Update the main page** default data in `page.tsx`.

### Creating a New Settings Section

1. Create a new component file in `components/`
2. Add the interface to `types/index.ts`
3. Export the component in `components/index.ts`
4. Add the tab configuration in `page.tsx`
5. Add the render case in `renderTabContent()`

## Development Guidelines

### Component Structure
Each component should follow this pattern:
```typescript
"use client";

import React from 'react';
import { /* required icons */ } from 'react-icons/fi';

interface ComponentData {
  // Component-specific data structure
}

interface ComponentProps {
  data: ComponentData;
  onChange: (data: ComponentData) => void;
}

export default function ComponentName({ data, onChange }: ComponentProps) {
  const updateData = (updates: Partial<ComponentData>) => {
    onChange({ ...data, ...updates });
  };

  return (
    <div className="space-y-6">
      {/* Component sections */}
    </div>
  );
}
```

### Styling Conventions
- Use consistent spacing: `space-y-6` for main sections, `space-y-4` for subsections
- Card styling: `bg-gray-800 rounded-lg border border-gray-700 p-6`
- Form inputs: `bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]`
- Toggle switches: Use the established pattern for consistency

### State Management
- Each component manages its own local state for UI interactions
- Data flows down from the main page via props
- Changes flow up via the `onChange` callback
- Use the `updateData` helper pattern for clean updates

## Migration from Monolithic Structure

The original 3200+ line file has been successfully divided into:
- **Main page**: ~380 lines (orchestration only)
- **6 components**: ~1400 lines total (focused functionality)
- **Types**: ~200 lines (centralized interfaces)
- **Total reduction**: ~50% code organization improvement

This modular approach makes the codebase much more manageable while preserving all existing functionality and maintaining the same user experience. 