"use client";

import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import { useRouter, usePathname } from 'next/navigation';
import { FiGlobe, FiLoader } from 'react-icons/fi';

const LanguageSwitcher: React.FC = () => {
  const { locale, clearCacheAndChangeLanguage, isChangingLanguage } = useLanguage();
  const router = useRouter();
  const pathname = usePathname();

  // Get the current path without the locale prefix
  const getPathWithoutLocale = () => {
    if (!pathname) return '/';
    
    const segments = pathname.split('/');
    if (segments.length > 1 && (segments[1] === 'en' || segments[1] === 'ar')) {
      return '/' + segments.slice(2).join('/');
    }
    return pathname;
  };

  // Get the path for the alternate language
  const getAlternatePath = (newLocale: string) => {
    const pathWithoutLocale = getPathWithoutLocale();
    return `/${newLocale}${pathWithoutLocale === '/' ? '' : pathWithoutLocale}`;
  };

  // Handle language switch with cache clearing
  const handleLanguageSwitch = async () => {
    if (isChangingLanguage) return; // Prevent multiple clicks

    const newLocale = locale === 'en' ? 'ar' : 'en';
    const newPath = getAlternatePath(newLocale);
    
    console.log(`🔄 Switching language from ${locale} to ${newLocale}`);
    console.log(`📍 Navigating to: ${newPath}`);

    try {
      // 1. Clear caches and change language context
      await clearCacheAndChangeLanguage(newLocale);
      
      // 2. Navigate to new path with cache refresh
      router.push(newPath);
      
      // 3. Force router refresh to clear Next.js cache
      router.refresh();
      
      console.log(`✅ Language switch complete: ${locale} → ${newLocale}`);
    } catch (error) {
      console.error('❌ Error during language switch:', error);
      // Fallback to simple navigation if cache clearing fails
      router.push(newPath);
    }
  };

  return (
    <button
      onClick={handleLanguageSwitch}
      disabled={isChangingLanguage}
      className={`
        inline-flex items-center px-4 py-2 rounded-md transition-all duration-300 text-sm font-medium
        ${isChangingLanguage 
          ? 'bg-gray-500/20 text-gray-400 cursor-not-allowed' 
          : 'bg-[rgb(var(--color-text))]/10 hover:bg-[rgb(var(--color-text))]/20 text-[rgb(var(--color-text))] hover:text-[rgb(var(--color-primary))] border border-[rgb(var(--color-text))]/20 hover:border-[rgb(var(--color-primary))]/50'
        }
      `}
      aria-label={
        isChangingLanguage 
          ? 'Switching language...' 
          : locale === 'en' 
            ? 'Switch to Arabic' 
            : 'Switch to English'
      }
    >
      {isChangingLanguage ? (
        <>
          <FiLoader className="mr-2 h-4 w-4 animate-spin" />
          {locale === 'en' ? 'Switching...' : 'جاري التبديل...'}
        </>
      ) : (
        <>
          <FiGlobe className="mr-2 h-4 w-4" />
          {locale === 'en' ? 'العربية' : 'English'}
        </>
      )}
    </button>
  );
};

export default LanguageSwitcher; 