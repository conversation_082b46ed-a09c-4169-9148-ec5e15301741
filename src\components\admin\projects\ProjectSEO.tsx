import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>a<PERSON>oogle, FaTwitter, FaFacebook } from 'react-icons/fa';

interface ProjectSEOProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectSEO: React.FC<ProjectSEOProps> = ({
  project,
  language,
  setProject
}) => {
  // Get current SEO info based on selected language
  const currentSEO = project.localizedContent?.[language]?.seo || {
    metaTitle: '',
    metaDescription: '',
    keywords: [],
    ogTitle: '',
    ogDescription: '',
    twitterTitle: '',
    twitterDescription: '',
    twitterCardType: 'summary_large_image' // Default to summary with large image
  };
  
  // State for the current language's SEO info
  const [metaTitle, setMetaTitle] = useState(currentSEO.metaTitle);
  const [metaDescription, setMetaDescription] = useState(currentSEO.metaDescription);
  const [keywords, setKeywords] = useState(Array.isArray(currentSEO.keywords) ? currentSEO.keywords : []);
  const [ogTitle, setOgTitle] = useState(currentSEO.ogTitle);
  const [ogDescription, setOgDescription] = useState(currentSEO.ogDescription);
  const [twitterTitle, setTwitterTitle] = useState(currentSEO.twitterTitle);
  const [twitterDescription, setTwitterDescription] = useState(currentSEO.twitterDescription);
  const [twitterCardType, setTwitterCardType] = useState(currentSEO.twitterCardType || 'summary_large_image');
  
  // Create a ref for the file input
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Website domain for previews
  const domain = "mazaya.ae";
  
  // Get project title and slug for default values
  const projectTitle = language === 'en' ? project.title : project.titleAr;
  const projectSlug = language === 'en' ? project.slug : project.slugAr;
  const projectDescription = project.localizedContent?.[language]?.description || '';
  
  // Validation limits
  const META_TITLE_MIN = 30;
  const META_TITLE_MAX = 60;
  const META_DESC_MIN = 120;
  const META_DESC_MAX = 160;
  const OG_TITLE_MIN = 30;
  const OG_TITLE_MAX = 90;
  const OG_DESC_MIN = 100;
  const OG_DESC_MAX = 200;
  const TWITTER_TITLE_MIN = 30;
  const TWITTER_TITLE_MAX = 70;
  const TWITTER_DESC_MIN = 70;
  const TWITTER_DESC_MAX = 200;
  
  // Update the form values when language changes
  useEffect(() => {
    setMetaTitle(project.localizedContent?.[language]?.seo?.metaTitle || '');
    setMetaDescription(project.localizedContent?.[language]?.seo?.metaDescription || '');
    const currentKeywords = project.localizedContent?.[language]?.seo?.keywords || [];
    setKeywords(Array.isArray(currentKeywords) ? currentKeywords : currentKeywords.split(',').map((k: string) => k.trim()).filter(Boolean));
    setOgTitle(project.localizedContent?.[language]?.seo?.ogTitle || '');
    setOgDescription(project.localizedContent?.[language]?.seo?.ogDescription || '');
    setTwitterTitle(project.localizedContent?.[language]?.seo?.twitterTitle || '');
    setTwitterDescription(project.localizedContent?.[language]?.seo?.twitterDescription || '');
    setTwitterCardType(project.localizedContent?.[language]?.seo?.twitterCardType || 'summary_large_image');
  }, [language, project.localizedContent]);
  
  // Helper function for length validation
  const getValidationStatus = (text: string, min: number, max: number) => {
    const length = text.length;
    if (length === 0) return 'empty';
    if (length < min) return 'tooShort';
    if (length > max) return 'tooLong';
    return 'good';
  };
  
  // Helper function for validation status text
  const getValidationText = (status: string, min: number, max: number) => {
    if (language === 'en') {
      switch(status) {
        case 'empty': return 'Required field';
        case 'tooShort': return `Too short (min: ${min})`;
        case 'tooLong': return `Too long (max: ${max})`;
        case 'good': return 'Good length';
        default: return '';
      }
    } else {
      switch(status) {
        case 'empty': return 'حقل مطلوب';
        case 'tooShort': return `قصير جدًا (الحد الأدنى: ${min})`;
        case 'tooLong': return `طويل جدًا (الحد الأقصى: ${max})`;
        case 'good': return 'طول جيد';
        default: return '';
      }
    }
  };
  
  // Helper function for validation color
  const getValidationColor = (status: string) => {
    switch(status) {
      case 'empty': return 'text-gray-400';
      case 'tooShort': return 'text-yellow-400';
      case 'tooLong': return 'text-red-400';
      case 'good': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };
  
  // Validation statuses
  const metaTitleStatus = getValidationStatus(metaTitle, META_TITLE_MIN, META_TITLE_MAX);
  const metaDescStatus = getValidationStatus(metaDescription, META_DESC_MIN, META_DESC_MAX);
  const ogTitleStatus = getValidationStatus(ogTitle || '', OG_TITLE_MIN, OG_TITLE_MAX);
  const ogDescStatus = getValidationStatus(ogDescription || '', OG_DESC_MIN, OG_DESC_MAX);
  const twitterTitleStatus = getValidationStatus(twitterTitle || '', TWITTER_TITLE_MIN, TWITTER_TITLE_MAX);
  const twitterDescStatus = getValidationStatus(twitterDescription || '', TWITTER_DESC_MIN, TWITTER_DESC_MAX);
  
  // Handle keywords adding
  const [newKeyword, setNewKeyword] = useState('');

  const handleAddKeyword = () => {
    if (newKeyword.trim() && !keywords.includes(newKeyword.trim())) {
      const updatedKeywords = [...keywords, newKeyword.trim()];
      setKeywords(updatedKeywords);
      setNewKeyword(''); // Clear input
      updateProjectState({ keywords: updatedKeywords });
    }
  };

  const handleRemoveKeyword = (keywordToRemove: string) => {
    const updatedKeywords = keywords.filter((k: string) => k !== keywordToRemove);
    setKeywords(updatedKeywords);
    updateProjectState({ keywords: updatedKeywords });
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddKeyword();
    }
  };
  
  // Helper to update the project state with new SEO values
  const updateProjectState = (newValues: any) => {
    setProject((prevProject: any) => ({
      ...prevProject,
      localizedContent: {
        ...prevProject.localizedContent,
        [language]: {
          ...prevProject.localizedContent?.[language],
          seo: {
            ...(prevProject.localizedContent?.[language]?.seo || {}),
            ...newValues,
          }
        }
      }
    }));
  };
  
  // Function to generate URL for preview
  const getPreviewUrl = () => {
    if (!projectSlug) return `${domain}/projects/property`;
    return `${domain}/projects/${projectSlug}`;
  };

  // Function to truncate text for previews
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  
  // Handle featured image upload
  const handleFeaturedImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      
      // Update project state with new featured image
      setProject((prevProject: any) => ({
        ...prevProject,
        featuredImage: selectedFile
      }));
    }
  };
  
  // Function to trigger file input click
  const triggerFileInput = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent any default behavior
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  // Add the handlers back
  // Direct handlers for input fields
  const handleMetaTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setMetaTitle(newTitle);
    
    // If OG or Twitter titles are empty, use this as default
    if (!ogTitle) setOgTitle(newTitle);
    if (!twitterTitle) setTwitterTitle(newTitle);
    
    // Immediately update project state
    updateProjectState({
      metaTitle: newTitle,
      ogTitle: !ogTitle ? newTitle : ogTitle,
      twitterTitle: !twitterTitle ? newTitle : twitterTitle
    });
  };

  const handleMetaDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setMetaDescription(newDescription);
    
    // If OG or Twitter descriptions are empty, use this as default
    if (!ogDescription) setOgDescription(newDescription);
    if (!twitterDescription) setTwitterDescription(newDescription);
    
    // Immediately update project state
    updateProjectState({
      metaDescription: newDescription,
      ogDescription: !ogDescription ? newDescription : ogDescription,
      twitterDescription: !twitterDescription ? newDescription : twitterDescription
    });
  };

  const handleOgTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setOgTitle(newTitle);
    
    // Immediately update project state
    updateProjectState({ ogTitle: newTitle });
  };

  const handleOgDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setOgDescription(newDescription);
    
    // Immediately update project state
    updateProjectState({ ogDescription: newDescription });
  };

  const handleTwitterTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setTwitterTitle(newTitle);
    
    // Immediately update project state
    updateProjectState({ twitterTitle: newTitle });
  };

  const handleTwitterDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setTwitterDescription(newDescription);
    
    // Immediately update project state
    updateProjectState({ twitterDescription: newDescription });
  };

  // Handler for Twitter card type change
  const handleTwitterCardTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCardType = e.target.value;
    setTwitterCardType(newCardType);
    updateProjectState({ twitterCardType: newCardType });
  };
  
  return (
    <div className={`${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* SEO Form Fields */}
        <div>
          {/* Featured Image Upload */}
          <div className="mb-6 border-b border-gray-700 pb-6">
            <h4 className="text-sm font-medium text-gray-300 mb-4">
              {language === 'en' ? 'Featured Image' : 'الصورة المميزة'}
              <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? 'recommended: 1200x630px' : 'موصى به: 1200×630 بكسل'})</span>
            </h4>
            
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="w-28 h-28 bg-gray-700 rounded-md overflow-hidden flex items-center justify-center border border-gray-600">
                {project.featuredImage ? (
                  <img 
                    src={URL.createObjectURL(project.featuredImage)} 
                    alt="Featured" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                )}
              </div>
              
              <div className="flex-1">
                {/* Hidden file input */}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFeaturedImageUpload}
                  accept="image/*"
                  className="hidden"
                />
                
                {/* Custom button that triggers the file input */}
                <button
                  type="button"
                  onClick={triggerFileInput}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium bg-[#00C2FF] text-white hover:bg-[#009DB5] focus:outline-none"
                >
                  {language === 'en' ? 'Upload Image' : 'تحميل صورة'}
                </button>
                
                <p className="mt-2 text-xs text-gray-400">
                  {language === 'en' 
                    ? "This image will be used for social media shares and search results. For best results, use an image that is 1200x630 pixels." 
                    : "سيتم استخدام هذه الصورة لمشاركات وسائل التواصل الاجتماعي ونتائج البحث. للحصول على أفضل النتائج، استخدم صورة بحجم 1200×630 بكسل."}
                </p>
                
                {project.featuredImage && (
                  <button
                    type="button"
                    onClick={() => setProject({...project, featuredImage: null})}
                    className="mt-2 text-xs text-red-400 hover:text-red-300"
                  >
                    {language === 'en' ? 'Remove image' : 'إزالة الصورة'}
                  </button>
                )}
              </div>
            </div>
          </div>
          
          {/* SEO Meta Fields */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Meta Title' : 'عنوان الميتا'}
                <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? `recommended: ${META_TITLE_MIN}-${META_TITLE_MAX} characters` : `موصى به: ${META_TITLE_MIN}-${META_TITLE_MAX} حرفًا`})</span>
              </label>
              <input
                type="text"
                value={metaTitle}
                onChange={handleMetaTitleChange}
                className={`shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3 ${metaTitleStatus === 'empty' ? 'border-red-500' : ''}`}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={projectTitle || (language === 'en' ? "Mazaya Heights | Luxury Apartments in Dubai" : "مزايا هايتس | شقق فاخرة في دبي")}
                required
              />
              <div className="mt-1 flex items-center justify-between">
                <div className="text-xs text-gray-400">{metaTitle.length} {language === 'en' ? 'characters' : 'حرف'}</div>
                <div className={`text-xs ${getValidationColor(metaTitleStatus)}`}>
                  {getValidationText(metaTitleStatus, META_TITLE_MIN, META_TITLE_MAX)}
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Meta Description' : 'وصف الميتا'}
                <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? `recommended: ${META_DESC_MIN}-${META_DESC_MAX} characters` : `موصى به: ${META_DESC_MIN}-${META_DESC_MAX} حرفًا`})</span>
              </label>
              <textarea
                value={metaDescription}
                onChange={handleMetaDescriptionChange}
                className={`shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2 ${metaDescStatus === 'empty' ? 'border-red-500' : ''}`}
                rows={3}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
                placeholder={projectDescription || (language === 'en' 
                  ? "Mazaya Heights offers luxury apartments in Dubai with premium finishes, stunning views, and world-class amenities. Book your viewing today!" 
                  : "توفر مزايا هايتس شققًا فاخرة في دبي بتشطيبات متميزة وإطلالات خلابة ومرافق عالمية. احجز جولتك اليوم!")}
                required
              />
              <div className="mt-1 flex items-center justify-between">
                <div className="text-xs text-gray-400">{metaDescription.length} {language === 'en' ? 'characters' : 'حرف'}</div>
                <div className={`text-xs ${getValidationColor(metaDescStatus)}`}>
                  {getValidationText(metaDescStatus, META_DESC_MIN, META_DESC_MAX)}
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Keywords' : 'الكلمات المفتاحية'}
              </label>
              <div className="flex flex-wrap items-center gap-2 mb-2">
                {keywords.map((keyword: string, index: number) => (
                  <div 
                    key={index}
                    className="px-3 py-1 bg-gray-700 rounded-full flex items-center gap-2 text-sm"
                  >
                    <span>{keyword}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveKeyword(keyword)}
                      className="text-gray-400 hover:text-red-400"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex">
                <input
                  type="text"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-l-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={language === 'en' 
                    ? "Add keywords and press Enter" 
                    : "أضف كلمات مفتاحية واضغط على Enter"}
                />
                <button
                  type="button"
                  onClick={handleAddKeyword}
                  className="px-3 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5]"
                >
                  +
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-400">
                {language === 'en' 
                  ? `${keywords.length} keywords added. Add relevant keywords to improve search visibility.` 
                  : `تم إضافة ${keywords.length} من الكلمات المفتاحية. أضف كلمات مفتاحية ذات صلة لتحسين الظهور في البحث.`}
              </p>
            </div>
          </div>
          
          {/* Open Graph Settings */}
          <div className="border-t border-gray-700 pt-6 mb-6">
            <h4 className="text-sm font-medium text-gray-300 mb-4 flex items-center">
              <FaFacebook className="mr-2 text-blue-500" />
              {language === 'en' ? 'Social Media (Open Graph)' : 'وسائل التواصل الاجتماعي (Open Graph)'}
            </h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'OG Title' : 'عنوان OG'}
                  <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? `recommended: ${OG_TITLE_MIN}-${OG_TITLE_MAX} characters` : `موصى به: ${OG_TITLE_MIN}-${OG_TITLE_MAX} حرفًا`})</span>
                </label>
                <input
                  type="text"
                  value={ogTitle || ''}
                  onChange={handleOgTitleChange}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={metaTitle || projectTitle || (language === 'en' ? "Mazaya Heights | Luxury Living in Dubai" : "مزايا هايتس | الحياة الفاخرة في دبي")}
                />
                {ogTitle && (
                  <div className="mt-1 flex items-center justify-between">
                    <div className="text-xs text-gray-400">{ogTitle.length} {language === 'en' ? 'characters' : 'حرف'}</div>
                    <div className={`text-xs ${getValidationColor(ogTitleStatus)}`}>
                      {getValidationText(ogTitleStatus, OG_TITLE_MIN, OG_TITLE_MAX)}
                    </div>
                  </div>
                )}
                <p className="mt-1 text-xs text-gray-400">
                  {language === 'en' 
                    ? "This title will be shown when sharing on Facebook, LinkedIn, and other social media." 
                    : "سيظهر هذا العنوان عند المشاركة على فيسبوك ولينكد إن ووسائل التواصل الاجتماعي الأخرى."}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'OG Description' : 'وصف OG'}
                  <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? `recommended: ${OG_DESC_MIN}-${OG_DESC_MAX} characters` : `موصى به: ${OG_DESC_MIN}-${OG_DESC_MAX} حرفًا`})</span>
                </label>
                <textarea
                  value={ogDescription || ''}
                  onChange={handleOgDescriptionChange}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                  rows={2}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={metaDescription || (language === 'en' 
                    ? "Experience luxury living in Dubai's most prestigious location with breathtaking views and world-class amenities." 
                    : "استمتع بالحياة الفاخرة في أرقى موقع في دبي مع إطلالات خلابة ومرافق عالمية المستوى.")}
                />
                {ogDescription && (
                  <div className="mt-1 flex items-center justify-between">
                    <div className="text-xs text-gray-400">{ogDescription.length} {language === 'en' ? 'characters' : 'حرف'}</div>
                    <div className={`text-xs ${getValidationColor(ogDescStatus)}`}>
                      {getValidationText(ogDescStatus, OG_DESC_MIN, OG_DESC_MAX)}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Twitter Card Settings */}
          <div className="border-t border-gray-700 pt-6">
            <h4 className="text-sm font-medium text-gray-300 mb-4 flex items-center">
              <FaTwitter className="mr-2 text-blue-400" />
              {language === 'en' ? 'Twitter Card' : 'بطاقة تويتر'}
            </h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Twitter Card Type' : 'نوع بطاقة تويتر'}
                </label>
                <select
                  value={twitterCardType}
                  onChange={handleTwitterCardTypeChange}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                >
                  <option value="summary">
                    {language === 'en' ? 'Summary Card (Small Square Image)' : 'بطاقة ملخص (صورة مربعة صغيرة)'}
                  </option>
                  <option value="summary_large_image">
                    {language === 'en' ? 'Summary Card with Large Image' : 'بطاقة ملخص مع صورة كبيرة'}
                  </option>
                </select>
                <p className="mt-1 text-xs text-gray-400">
                  {language === 'en' 
                    ? "Select the card type that best represents your content. Different card types display differently on Twitter." 
                    : "حدد نوع البطاقة الذي يمثل محتواك بشكل أفضل. تظهر أنواع البطاقات المختلفة بشكل مختلف على تويتر."}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Twitter Title' : 'عنوان تويتر'}
                  <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? `recommended: ${TWITTER_TITLE_MIN}-${TWITTER_TITLE_MAX} characters` : `موصى به: ${TWITTER_TITLE_MIN}-${TWITTER_TITLE_MAX} حرفًا`})</span>
                </label>
                <input
                  type="text"
                  value={twitterTitle || ''}
                  onChange={handleTwitterTitleChange}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={metaTitle || projectTitle || (language === 'en' ? "Discover Mazaya Heights in Dubai" : "اكتشف مزايا هايتس في دبي")}
                />
                {twitterTitle && (
                  <div className="mt-1 flex items-center justify-between">
                    <div className="text-xs text-gray-400">{twitterTitle.length} {language === 'en' ? 'characters' : 'حرف'}</div>
                    <div className={`text-xs ${getValidationColor(twitterTitleStatus)}`}>
                      {getValidationText(twitterTitleStatus, TWITTER_TITLE_MIN, TWITTER_TITLE_MAX)}
                    </div>
                  </div>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Twitter Description' : 'وصف تويتر'}
                  <span className="text-xs text-gray-400 font-normal ml-1">({language === 'en' ? `recommended: ${TWITTER_DESC_MIN}-${TWITTER_DESC_MAX} characters` : `موصى به: ${TWITTER_DESC_MIN}-${TWITTER_DESC_MAX} حرفًا`})</span>
                </label>
                <textarea
                  value={twitterDescription || ''}
                  onChange={handleTwitterDescriptionChange}
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                  rows={2}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                  placeholder={metaDescription || (language === 'en' 
                    ? "A new standard of luxury living in Dubai with premium finishes and amenities." 
                    : "معيار جديد للعيش الفاخر في دبي مع تشطيبات ومرافق متميزة.")}
                />
                {twitterDescription && (
                  <div className="mt-1 flex items-center justify-between">
                    <div className="text-xs text-gray-400">{twitterDescription.length} {language === 'en' ? 'characters' : 'حرف'}</div>
                    <div className={`text-xs ${getValidationColor(twitterDescStatus)}`}>
                      {getValidationText(twitterDescStatus, TWITTER_DESC_MIN, TWITTER_DESC_MAX)}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Preview Section */}
        <div>
          <h4 className="text-sm font-medium text-gray-300 mb-4">
            {language === 'en' ? 'Live Preview' : 'معاينة مباشرة'}
          </h4>
          
          {/* Google Search Result Preview */}
          <div className="mb-6 bg-gray-900 p-4 rounded-md border border-gray-700">
            <div className="flex items-center mb-2">
              <FaGoogle className="mr-2 text-[#4285F4]" />
              <h4 className="text-sm font-medium text-gray-300">
                {language === 'en' ? 'Google Search Result' : 'نتيجة بحث جوجل'}
              </h4>
            </div>
            
            <div className={`bg-white rounded p-3 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
              <div className="text-xs text-gray-600">{getPreviewUrl()}</div>
              <div className="text-xl text-[#1a0dab] font-medium mt-1 hover:underline cursor-pointer">
                {metaTitle || projectTitle || (language === 'en' ? "Mazaya Heights | Luxury Apartments in Dubai" : "مزايا هايتس | شقق فاخرة في دبي")}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {metaDescription 
                  ? truncateText(metaDescription, 160)
                  : (language === 'en' 
                      ? "Mazaya Heights offers luxury apartments in Dubai with premium finishes, stunning views, and world-class amenities. Book your viewing today!"
                      : "توفر مزايا هايتس شققًا فاخرة في دبي بتشطيبات متميزة وإطلالات خلابة ومرافق عالمية. احجز جولتك اليوم!")}
              </div>
            </div>
          </div>
          
          {/* Facebook Preview */}
          <div className="mb-6 bg-gray-900 p-4 rounded-md border border-gray-700">
            <div className="flex items-center mb-2">
              <FaFacebook className="mr-2 text-[#1877F2]" />
              <h4 className="text-sm font-medium text-gray-300">
                {language === 'en' ? 'Facebook / LinkedIn Preview' : 'معاينة فيسبوك / لينكد إن'}
              </h4>
            </div>
            
            <div className="bg-[#f2f3f5] rounded-lg overflow-hidden border border-gray-200">
              <div className="h-48 bg-gray-300 flex items-center justify-center text-gray-500">
                {project.featuredImage ? (
                  <img 
                    src={URL.createObjectURL(project.featuredImage)} 
                    alt="Featured" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="text-center">
                    <svg className="w-10 h-10 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="block mt-2 text-sm">
                      {language === 'en' ? 'Featured Image' : 'الصورة المميزة'}
                    </span>
                  </div>
                )}
              </div>
              <div className={`p-3 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
                <div className="text-xs uppercase text-gray-500">{domain}</div>
                <div className="text-base font-medium text-gray-900 mt-1">
                  {ogTitle || metaTitle || projectTitle || (language === 'en' ? "Mazaya Heights | Luxury Living in Dubai" : "مزايا هايتس | الحياة الفاخرة في دبي")}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {ogDescription 
                    ? truncateText(ogDescription, 100)
                    : metaDescription 
                      ? truncateText(metaDescription, 100)
                      : (language === 'en' 
                          ? "Experience luxury living in Dubai's most prestigious location with breathtaking views and world-class amenities."
                          : "استمتع بالحياة الفاخرة في أرقى موقع في دبي مع إطلالات خلابة ومرافق عالمية المستوى.")}
                </div>
              </div>
            </div>
          </div>
          
          {/* Twitter Preview */}
          <div className="bg-gray-900 p-4 rounded-md border border-gray-700">
            <div className="flex items-center mb-2">
              <FaTwitter className="mr-2 text-[#1DA1F2]" />
              <h4 className="text-sm font-medium text-gray-300">
                {language === 'en' ? 'Twitter Preview' : 'معاينة تويتر'} 
                <span className="ml-2 text-xs text-gray-400">
                  ({language === 'en' 
                    ? twitterCardType === 'summary' ? 'small image' : 'large image'
                    : twitterCardType === 'summary' ? 'صورة صغيرة' : 'صورة كبيرة'})
                </span>
              </h4>
            </div>
            
            <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
              <div className={`${
                twitterCardType === 'summary' ? 'h-24 w-24 float-left mr-3' : 'h-40 w-full'
              } bg-gray-300 flex items-center justify-center text-gray-500`}>
                {project.featuredImage ? (
                  <img 
                    src={URL.createObjectURL(project.featuredImage)} 
                    alt="Featured" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="text-center">
                    <svg className="w-10 h-10 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className={`${twitterCardType === 'summary' ? 'text-xs' : 'text-sm'} block mt-1`}>
                      {language === 'en' ? 'Featured Image' : 'الصورة المميزة'}
                    </span>
                  </div>
                )}
              </div>
              <div className={`p-3 ${language === 'ar' ? 'text-right' : ''} ${twitterCardType === 'summary' ? 'min-h-[96px]' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
                <div className="text-sm font-bold text-gray-900">
                  {twitterTitle || ogTitle || metaTitle || projectTitle || (language === 'en' ? "Discover Mazaya Heights in Dubai" : "اكتشف مزايا هايتس في دبي")}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {twitterDescription 
                    ? truncateText(twitterDescription, twitterCardType === 'summary' ? 60 : 80)
                    : ogDescription 
                      ? truncateText(ogDescription, twitterCardType === 'summary' ? 60 : 80)
                      : metaDescription 
                        ? truncateText(metaDescription, twitterCardType === 'summary' ? 60 : 80)
                        : (language === 'en' 
                            ? "A new standard of luxury living in Dubai with premium finishes and amenities."
                            : "معيار جديد للعيش الفاخر في دبي مع تشطيبات ومرافق متميزة.")}
                </div>
                
                <div className="text-xs text-gray-500 mt-2 flex items-center">
                  <span className="inline-block w-4 h-4 rounded-full bg-gray-300 mr-1"></span>
                  {domain}
                </div>
              </div>
            </div>
            
            {/* Add explanation for the selected card type */}
            <div className="mt-3 text-xs text-gray-400">
              {language === 'en' ? (
                twitterCardType === 'summary' 
                  ? "Summary Cards display a small square image alongside title and description text." 
                  : "Summary Cards with Large Image display a large image above title and description text."
              ) : (
                twitterCardType === 'summary' 
                  ? "تعرض بطاقات الملخص صورة مربعة صغيرة إلى جانب نص العنوان والوصف." 
                  : "تعرض بطاقات الملخص مع صورة كبيرة صورة كبيرة فوق نص العنوان والوصف."
              )}
            </div>
          </div>
          
          {/* Additional information */}
          <div className="mt-6 text-sm text-gray-400">
            <p>
              {language === 'en'
                ? "Note: These are visual representations to help you understand how your content may appear on different platforms. Actual appearance may vary."
                : "ملاحظة: هذه تمثيلات مرئية لمساعدتك على فهم كيف قد يظهر المحتوى الخاص بك على منصات مختلفة. قد يختلف المظهر الفعلي."}
            </p>
          </div>
        </div>
      </div>
      
      {/* SEO Tips */}
      <div className="mt-8 bg-gray-900/50 p-4 rounded-md">
        <h4 className="text-sm font-medium text-gray-300 mb-2">
          {language === 'en' ? 'SEO Tips' : 'نصائح لتحسين محركات البحث'}
        </h4>
        <ul className="list-disc list-inside text-sm text-gray-400 space-y-1">
          <li>
            {language === 'en'
              ? `Meta titles should be ${META_TITLE_MIN}-${META_TITLE_MAX} characters long to avoid being cut off in search results.`
              : `يجب أن تكون عناوين الميتا بطول ${META_TITLE_MIN}-${META_TITLE_MAX} حرفًا لتجنب اقتطاعها في نتائج البحث.`}
          </li>
          <li>
            {language === 'en'
              ? `Meta descriptions should be ${META_DESC_MIN}-${META_DESC_MAX} characters to provide enough context while avoiding truncation.`
              : `يجب أن تكون أوصاف الميتا بطول ${META_DESC_MIN}-${META_DESC_MAX} حرفًا لتوفير سياق كافٍ مع تجنب الاقتطاع.`}
          </li>
          <li>
            {language === 'en'
              ? 'Include relevant keywords naturally in your title and description without keyword stuffing.'
              : 'قم بتضمين الكلمات المفتاحية ذات الصلة بشكل طبيعي في العنوان والوصف دون حشو الكلمات المفتاحية.'}
          </li>
          <li>
            {language === 'en'
              ? 'Social media (Open Graph and Twitter) can drive significant traffic, so optimize these fields for sharing.'
              : 'يمكن أن تجلب وسائل التواصل الاجتماعي (Open Graph و Twitter) حركة مرور كبيرة ، لذا قم بتحسين هذه الحقول للمشاركة.'}
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ProjectSEO; 