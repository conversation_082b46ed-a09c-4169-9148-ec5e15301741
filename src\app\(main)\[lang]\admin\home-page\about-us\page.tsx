"use client";

import { useState, useEffect } from 'react';
import { FiSave, FiEdit3, FiX, FiFileText, FiImage, FiAlertCircle, FiCheckCircle, FiLoader } from 'react-icons/fi';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/contexts/ToastContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { createToast } from '@/utils/toast';
import { authenticatedApiCall } from '@/utils/api';

interface AboutUsContent {
  id?: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  subtitle: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  imageOverlay: {
    badge: {
      en: string;
      ar: string;
    };
    text: {
      en: string;
      ar: string;
    };
  };
  mainImage: string;
  mainImageFile: string;
  imageAltText: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
  updated_by?: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export default function HomePageAboutUsManagement() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { showToast } = useToast();
  const { forceUpdateKey } = useLanguage();
  
  const [aboutUsContent, setAboutUsContent] = useState<AboutUsContent>({
    badge: {
      en: "About Mazaya Capital",
      ar: "حول مزايا كابيتال"
    },
    title: {
      en: "Welcome to",
      ar: "مرحبا بك في"
    },
    subtitle: {
      en: "Mazaya Capital",
      ar: "مزايا كابيتال"
    },
    description: {
      en: "A leading real estate development company specializing in premium properties across the UAE. With a focus on innovation, quality, and sustainability, we transform visions into exceptional developments.",
      ar: "مزايا كابيتال هي شركة تطوير عقاري رائدة تخصصت في إنشاء منازل ومنشآت تجارية ومشاريع متعددة الاستخدامات، تأتي بقيمة استثنائية للمستثمرين والمقيمين."
    },
    imageOverlay: {
      badge: {
        en: "Architectural Excellence",
        ar: "التميز المعماري"
      },
      text: {
        en: "Shaping Iconic Landmarks Since 2005",
        ar: "نشكل المعالم الأيقونية منذ عام ٢٠٠٥"
      }
    },
    mainImage: "/images/home/<USER>",
    mainImageFile: "",
    imageAltText: {
      en: "Mazaya Capital office building showcasing modern architecture",
      ar: "مبنى مكاتب مزايا كابيتال يعرض العمارة الحديثة"
    }
  });

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  
  // Simplified state management like hero section
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [originalContent, setOriginalContent] = useState<AboutUsContent | null>(null);

  // Helper function to get full image URL
  const getFullImageUrl = (imagePath: string): string => {
    if (!imagePath) return '';
    
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }
    
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    return `${apiBaseUrl}${imagePath}`;
  };

  // Set initial image preview
  useEffect(() => {
    setImagePreview(getFullImageUrl(aboutUsContent.mainImage));
  }, []);

  const handleContentChange = (newContent: AboutUsContent) => {
    setAboutUsContent(newContent);
    setHasChanges(JSON.stringify(newContent) !== JSON.stringify(originalContent));
  };

  // Fetch About Us content when authentication is ready
  useEffect(() => {
    // Only fetch when auth is not loading and user is authenticated
    if (!authLoading && isAuthenticated) {
      fetchAboutUsContent();
    } else if (!authLoading && !isAuthenticated) {
      // Auth is ready but user is not authenticated
      setIsLoading(false);
      showToast(createToast.error(
        'Authentication required',
        'Please log in to access this page'
      ));
    }
  }, [authLoading, isAuthenticated]);

  const fetchAboutUsContent = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Making authenticated API call to fetch about us content...');
      const response = await authenticatedApiCall('/api/admin/home-page/about-us/', {
        method: 'GET'
      });

      console.log('📥 About Us content response:', response);

      if (response.success && response.data) {
        const processedData = {
          ...response.data,
          mainImageFile: response.data.mainImageFile || "",
          imageAltText: {
            en: response.data.imageAltText?.en || "",
            ar: response.data.imageAltText?.ar || ""
          }
        };
        setAboutUsContent(processedData);
        setOriginalContent(processedData);
        setImagePreview(getFullImageUrl(processedData.mainImage));
        setHasChanges(false);
      } else {
        // Check if it's an authentication error
        if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
          showToast(createToast.error(
            'Authentication expired',
            'Please log in again to continue'
          ));
        } else {
          showToast(createToast.error(
            response.message || 'Failed to load About Us content',
            'Please try refreshing the page'
          ));
        }
      }
    } catch (error) {
      console.error('Error fetching About Us content:', error);
      showToast(createToast.error(
        'Failed to load About Us content',
        'Please check your connection and try again'
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAll = async () => {
    console.log('🚀 Save button clicked');
    console.log('📊 Current state:', {
      hasChanges,
      isAuthenticated,
      isSaving
    });
    
    if (!hasChanges) {
      console.log('❌ No changes to save');
      showToast(createToast.info('No changes to save'));
      return;
    }

    // Check authentication before saving
    if (!isAuthenticated) {
      console.log('❌ Not authenticated');
      showToast(createToast.error(
        'Authentication required',
        'Please log in to save changes'
      ));
      return;
    }

    console.log('✅ Starting save process...');
    setIsSaving(true);
    try {
      // Save content only (no image in this request)
      const contentData = {
        badge: aboutUsContent.badge,
        title: aboutUsContent.title,
        subtitle: aboutUsContent.subtitle,
        description: aboutUsContent.description,
        imageOverlay: aboutUsContent.imageOverlay,
        imageAltText: aboutUsContent.imageAltText
      };
      
      console.log('📦 Saving content data:', contentData);
      
      const response = await authenticatedApiCall('/api/admin/home-page/about-us/', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentData)
      });

      console.log('📥 Save response:', response);

      if (response.success) {
        showToast(createToast.success(
          response.message || 'About Us section updated successfully',
          'All changes have been saved'
        ));
        
        // Update the content with the response data
        if (response.data) {
          const processedData = {
            ...response.data,
            mainImageFile: response.data.mainImageFile || "",
            imageAltText: {
              en: response.data.imageAltText?.en || "",
              ar: response.data.imageAltText?.ar || ""
            }
          };
          setAboutUsContent(processedData);
          setOriginalContent(processedData);
          setImagePreview(getFullImageUrl(processedData.mainImage));
        }
        setHasChanges(false);
        setEditingSection(null);
      } else {
        // Handle validation errors
        if (response.errors) {
          const errorMessages = Object.entries(response.errors)
            .map(([field, messages]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
            .join('\n');
          
          showToast(createToast.error(
            'Validation failed',
            errorMessages
          ));
        } else {
          // Check if it's an authentication error
          if (response.message?.includes('Authentication') || response.message?.includes('Unauthorized')) {
            showToast(createToast.error(
              'Authentication expired',
              'Please log in again to continue'
            ));
          } else {
            showToast(createToast.error(
              response.message || 'Failed to save changes',
              'Please check your input and try again'
            ));
          }
        }
      }
    } catch (error) {
      console.error('Error saving About Us content:', error);
      showToast(createToast.error(
        'Failed to save changes',
        'Please check your connection and try again'
      ));
    } finally {
      setIsSaving(false);
    }
  };

  const handleImageUpload = async (file: File) => {
    console.log('📁 Starting image upload:', {
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Validate file
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    
    if (file.size > maxSize) {
      console.error('❌ File too large:', file.size);
      showToast(createToast.error(
        'File too large',
        'File size must be less than 5MB'
      ));
      return;
    }
    
    if (!allowedTypes.includes(file.type)) {
      console.error('❌ Invalid file type:', file.type);
      showToast(createToast.error(
        'Invalid file type',
        'Only JPEG, PNG, and WebP files are allowed'
      ));
      return;
    }

    setIsUploadingImage(true);
    
    try {
      // Get auth tokens the same way as profile page
      const userData = localStorage.getItem('adminUser');
      const tokensData = localStorage.getItem('adminTokens');
      
      if (!userData || !tokensData) {
        showToast(createToast.error(
          'Authentication required',
          'Please log in to upload images'
        ));
        return;
      }

      const tokens = JSON.parse(tokensData);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

      // Create FormData using the same pattern as profile page
      const formData = new FormData();
      formData.append('image', file); // Try 'image' as field name first
      // If this fails, try: 'mainImage', 'file', 'photo', or 'mainImageFile'

      console.log('🔄 Uploading image using direct fetch...');
      const response = await fetch(`${apiBaseUrl}/api/admin/home-page/about-us/image/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${tokens.access}`,
        },
        body: formData
      });

      console.log('📥 Image upload response status:', response.status);
      const data = await response.json();
      console.log('📥 Image upload response data:', data);

      if (data.success && data.data) {
        // Update the content with new image path
        const updatedContent = {
          ...aboutUsContent,
          mainImage: data.data.mainImage
        };
        setAboutUsContent(updatedContent);
        setImagePreview(getFullImageUrl(data.data.mainImage));
        setHasChanges(true); // Mark as changed so user can save
        
        showToast(createToast.success(
          'Image uploaded successfully',
          'Don\'t forget to save your changes'
        ));
      } else {
        showToast(createToast.error(
          data.message || 'Failed to upload image',
          'Please try again'
        ));
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      showToast(createToast.error(
        'Failed to upload image',
        'Please check your connection and try again'
      ));
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    console.log('📁 File input changed:', file);
    
    if (file) {
      // Upload immediately like profile page
      handleImageUpload(file);
    }
    
    // Clear the input so the same file can be selected again
    e.target.value = '';
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">
            {authLoading ? 'Checking authentication...' : 'Loading About Us content...'}
          </p>
        </div>
      </div>
    );
  }

  // If auth is ready but user is not authenticated, the useEffect will handle the redirect
  if (!authLoading && !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <p className="text-gray-400">Authentication required. Please log in to continue.</p>
        </div>
      </div>
    );
  }

  return (
    <div key={forceUpdateKey}>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - About Us Section</h1>
          <p className="text-gray-400 mt-1">Manage the about us section content and images</p>
          {aboutUsContent.updated_at && (
            <p className="text-gray-500 text-sm mt-2">
              Last updated: {new Date(aboutUsContent.updated_at).toLocaleString()}
              {aboutUsContent.updated_by && (
                <span className="ml-2">
                  by {aboutUsContent.updated_by.first_name} {aboutUsContent.updated_by.last_name}
                </span>
              )}
            </p>
          )}
        </div>
        <button
          onClick={handleSaveAll}
          disabled={(!hasChanges) || isSaving}
          className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${
            (hasChanges) && !isSaving
              ? 'bg-[#00C2FF] text-white hover:bg-[#00C2FF]/90'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isSaving ? (
            <>
              <FiLoader className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
          <FiSave className="mr-2 h-4 w-4" />
              Save Changes
            </>
          )}
        </button>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="mt-6 space-y-8">
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <div className="w-5 h-5 bg-gray-600 rounded mr-2 animate-pulse"></div>
                <div className="h-6 w-32 bg-gray-600 rounded animate-pulse"></div>
              </div>
              <div className="h-8 w-16 bg-gray-600 rounded animate-pulse"></div>
            </div>
            <div className="space-y-4">
              <div className="h-4 w-full bg-gray-600 rounded animate-pulse"></div>
              <div className="h-4 w-3/4 bg-gray-600 rounded animate-pulse"></div>
              <div className="h-4 w-1/2 bg-gray-600 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      ) : (
      <div className="mt-6 space-y-8">
        {/* Main Content Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiFileText className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Main Content
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'main' ? null : 'main')}
                disabled={isSaving}
                className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 disabled:opacity-50"
            >
              {editingSection === 'main' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'main' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'main' ? (
            <div className="space-y-6">
              {/* Badge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Badge (English)
                  </label>
                  <input
                    type="text"
                    value={aboutUsContent.badge.en}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      badge: { ...aboutUsContent.badge, en: e.target.value }
                    })}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Badge (Arabic)
                  </label>
                  <input
                    type="text"
                    value={aboutUsContent.badge.ar}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      badge: { ...aboutUsContent.badge, ar: e.target.value }
                    })}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                    dir="rtl"
                  />
                </div>
              </div>

              {/* Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Title (English)
                  </label>
                  <input
                    type="text"
                    value={aboutUsContent.title.en}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      title: { ...aboutUsContent.title, en: e.target.value }
                    })}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Title (Arabic)
                  </label>
                  <input
                    type="text"
                    value={aboutUsContent.title.ar}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      title: { ...aboutUsContent.title, ar: e.target.value }
                    })}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                    dir="rtl"
                  />
                </div>
              </div>

              {/* Subtitle */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (English)</label>
                  <input
                    type="text"
                    value={aboutUsContent.subtitle.en}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      subtitle: { ...aboutUsContent.subtitle, en: e.target.value }
                    })}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (Arabic)</label>
                  <input
                    type="text"
                    value={aboutUsContent.subtitle.ar}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      subtitle: { ...aboutUsContent.subtitle, ar: e.target.value }
                    })}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                    dir="rtl"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                  <textarea
                    value={aboutUsContent.description.en}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      description: { ...aboutUsContent.description, en: e.target.value }
                    })}
                    rows={5}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                  <textarea
                    value={aboutUsContent.description.ar}
                    onChange={(e) => handleContentChange({
                      ...aboutUsContent,
                      description: { ...aboutUsContent.description, ar: e.target.value }
                    })}
                    rows={5}
                    className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                    dir="rtl"
                  />
                </div>
              </div>

                {/* Image Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  Main Image
                </label>
                  <input
                    type="file"
                    accept="image/jpeg,image/png,image/webp"
                    onChange={handleImageSelect}
                    disabled={isSaving || isUploadingImage}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] disabled:opacity-50"
                  />
                  {isUploadingImage && (
                    <div className="mt-2 p-2 bg-blue-900/20 border border-blue-700 rounded-md">
                      <p className="text-sm text-blue-400 flex items-center">
                        <FiLoader className="mr-2 h-4 w-4 animate-spin" />
                        Uploading image...
                      </p>
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-1">
                    Supported formats: JPEG, PNG, WebP. Maximum size: 5MB.
                  </p>
                </div>

                {/* Alt Text */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Image Alt Text (English)</label>
                    <input
                      type="text"
                      value={aboutUsContent.imageAltText.en}
                      onChange={(e) => handleContentChange({
                        ...aboutUsContent,
                        imageAltText: { ...aboutUsContent.imageAltText, en: e.target.value }
                      })}
                      placeholder="Describe the image in English"
                      className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Image Alt Text (Arabic)</label>
                  <input
                      type="text"
                      value={aboutUsContent.imageAltText.ar}
                      onChange={(e) => handleContentChange({
                        ...aboutUsContent,
                        imageAltText: { ...aboutUsContent.imageAltText, ar: e.target.value }
                      })}
                      placeholder="وصف الصورة بالعربية"
                      className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                      dir="rtl"
                    />
                  </div>
                </div>

                {/* Image Preview */}
                {imagePreview && (
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Image Preview</label>
                    <img 
                      src={imagePreview} 
                      alt="Preview" 
                      className="w-full h-48 object-cover rounded-lg border border-gray-600"
                    />
                  </div>
                )}
            </div>
          ) : (
            <div className="space-y-6">
                {/* Display Mode */}
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-white bg-gray-700 p-3 rounded">{aboutUsContent.badge.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{aboutUsContent.badge.ar}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-white bg-gray-700 p-3 rounded">{aboutUsContent.title.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{aboutUsContent.title.ar}</p>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Description</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{aboutUsContent.description.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm text-right" dir="rtl">{aboutUsContent.description.ar}</p>
                  </div>
                </div>
              </div>

              {imagePreview && (
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Image</label>
                    <img 
                      src={imagePreview} 
                      alt={aboutUsContent.imageAltText.en || "About Us"} 
                      className="w-full h-48 object-cover rounded-lg border border-gray-600"
                    />
                </div>
              )}
            </div>
          )}
        </div>

          {/* Image Overlay Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiImage className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Image Overlay Content
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'overlay' ? null : 'overlay')}
                disabled={isSaving}
                className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600 disabled:opacity-50"
            >
              {editingSection === 'overlay' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'overlay' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'overlay' ? (
            <div className="space-y-6">
              {/* Overlay Badge */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Overlay Badge (English)</label>
                    <input
                      type="text"
                      value={aboutUsContent.imageOverlay.badge.en}
                      onChange={(e) => handleContentChange({
                        ...aboutUsContent,
                        imageOverlay: {
                          ...aboutUsContent.imageOverlay,
                          badge: { ...aboutUsContent.imageOverlay.badge, en: e.target.value }
                        }
                      })}
                      className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Overlay Badge (Arabic)</label>
                    <input
                      type="text"
                      value={aboutUsContent.imageOverlay.badge.ar}
                      onChange={(e) => handleContentChange({
                        ...aboutUsContent,
                        imageOverlay: {
                          ...aboutUsContent.imageOverlay,
                          badge: { ...aboutUsContent.imageOverlay.badge, ar: e.target.value }
                        }
                      })}
                      className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                      dir="rtl"
                    />
                </div>
              </div>

              {/* Overlay Text */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Overlay Text (English)</label>
                    <input
                      type="text"
                      value={aboutUsContent.imageOverlay.text.en}
                      onChange={(e) => handleContentChange({
                        ...aboutUsContent,
                        imageOverlay: {
                          ...aboutUsContent.imageOverlay,
                          text: { ...aboutUsContent.imageOverlay.text, en: e.target.value }
                        }
                      })}
                      className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] border-gray-600`}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Overlay Text (Arabic)</label>
                    <input
                      type="text"
                      value={aboutUsContent.imageOverlay.text.ar}
                      onChange={(e) => handleContentChange({
                        ...aboutUsContent,
                        imageOverlay: {
                          ...aboutUsContent.imageOverlay,
                          text: { ...aboutUsContent.imageOverlay.text, ar: e.target.value }
                        }
                      })}
                      className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] text-right border-gray-600`}
                      dir="rtl"
                    />
                  </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Overlay Badge</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <div className="inline-block px-4 py-1.5 rounded-full bg-[#00C2FF]/20 border border-[#00C2FF]/40">
                      <span className="text-sm font-medium tracking-wide text-[#00C2FF]">{aboutUsContent.imageOverlay.badge.en}</span>
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <div className="inline-block px-4 py-1.5 rounded-full bg-[#00C2FF]/20 border border-[#00C2FF]/40 text-right" dir="rtl">
                      <span className="text-sm font-medium tracking-wide text-[#00C2FF]">{aboutUsContent.imageOverlay.badge.ar}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-3">Current Overlay Text</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">English</span>
                    <p className="text-2xl font-bold text-white bg-gray-700 p-3 rounded">{aboutUsContent.imageOverlay.text.en}</p>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                    <p className="text-2xl font-bold text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{aboutUsContent.imageOverlay.text.ar}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      )}
    </div>
  );
} 