import React from 'react';

interface ContactOptionButtonProps {
  href: string;
  ariaLabel: string;
  icon: React.ReactNode;
  target?: string;
  rel?: string;
}

export default function ContactOptionButton({
  href,
  ariaLabel,
  icon,
  target,
  rel
}: ContactOptionButtonProps) {
  return (
    <a
      href={href}
      aria-label={ariaLabel}
      target={target}
      rel={rel}
      className="w-12 h-12 rounded-full bg-white flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      style={{ boxShadow: "0 4px 16px 0 rgba(0,194,255,0.10)" }}
    >
      {icon}
    </a>
  );
} 