import React from 'react';

interface ProjectHeroSectionProps {
  project: any;
  language: 'en' | 'ar';
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

const ProjectHeroSection: React.FC<ProjectHeroSectionProps> = ({ 
  project, 
  language, 
  handleInputChange 
}) => {
  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Project Hero Section' : 'قسم العرض الرئيسي للمشروع'}
      </h2>
      
      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
        <div className="sm:col-span-6">
          <label htmlFor="heroTitle" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Hero Title *' : 'عنوان القسم الرئيسي *'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="heroTitle"
              id="heroTitle"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.heroTitle : project.heroTitleAr}
              onChange={handleInputChange}
              placeholder={language === 'en' ? "Premier Luxury Living at Mazaya Heights" : "الحياة الفاخرة في مزايا هايتس"}
              required
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>
        
        <div className="sm:col-span-6">
          <label htmlFor="heroSubtitle" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Hero Subtitle' : 'العنوان الفرعي'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="heroSubtitle"
              id="heroSubtitle"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.heroSubtitle : project.heroSubtitleAr}
              onChange={handleInputChange}
              placeholder={language === 'en' ? "Experience the epitome of luxury living..." : "اختبر قمة الحياة الفاخرة..."}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>
        
        <div className="sm:col-span-6">
          <label htmlFor="shortSummary" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Project Summary' : 'ملخص المشروع'}
          </label>
          <div className="mt-1">
            <textarea
              id="shortSummary"
              name="shortSummary"
              rows={3}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              value={language === 'en' ? project.shortSummary : project.shortSummaryAr}
              onChange={handleInputChange}
              placeholder={language === 'en' ? "Mazaya Heights is our flagship luxury residential tower located in the heart of Downtown Dubai. The 45-story tower offers unparalleled views of the city skyline and Dubai Fountain. With premium finishes and smart home technology in every unit, Mazaya Heights represents the pinnacle of urban living." : "مزايا هايتس هو برجنا السكني الفاخر الرئيسي الواقع في قلب وسط مدينة دبي. يوفر البرج المكون من 45 طابقًا إطلالات لا مثيل لها على أفق المدينة ونافورة دبي. مع التشطيبات الفاخرة وتكنولوجيا المنزل الذكي في كل وحدة ، يمثل مزايا هايتس قمة الحياة الحضرية."}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
          <p className="mt-1 text-xs text-gray-400">
            {language === 'en' ? 'This will appear in the orange highlighted box on the project page' : 'سيظهر هذا في المربع البرتقالي المميز على صفحة المشروع'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProjectHeroSection; 