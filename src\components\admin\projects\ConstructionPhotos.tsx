import React, { useState } from 'react';
import { FiPlus, FiX, FiEdit, FiMove } from 'react-icons/fi';

interface ConstructionPhotosProps {
  project: any;
  language: 'en' | 'ar';
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ConstructionPhotos: React.FC<ConstructionPhotosProps> = ({ 
  project, 
  language, 
  setProject 
}) => {
  const [newImage, setNewImage] = useState<File | null>(null);
  const [newImageTitleEn, setNewImageTitleEn] = useState('');
  const [newImageTitleAr, setNewImageTitleAr] = useState('');
  const [newImageAltEn, setNewImageAltEn] = useState('');
  const [newImageAltAr, setNewImageAltAr] = useState('');
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingTitleEn, setEditingTitleEn] = useState('');
  const [editingTitleAr, setEditingTitleAr] = useState('');
  const [editingAltEn, setEditingAltEn] = useState('');
  const [editingAltAr, setEditingAltAr] = useState('');
  const [showNewImageForm, setShowNewImageForm] = useState(false);
  const [draggedItem, setDraggedItem] = useState<number | null>(null);

  // Handle file upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setNewImage(e.target.files[0]);
    }
  };
  
  // Add new image
  const addImage = () => {
    if (newImage) {
      setProject({
        ...project,
        constructionPhotos: [...(project.constructionPhotos || []), { 
          image: newImage, 
          titleEn: newImageTitleEn,
          titleAr: newImageTitleAr,
          altEn: newImageAltEn, 
          altAr: newImageAltAr 
        }]
      });
      setNewImage(null);
      setNewImageTitleEn('');
      setNewImageTitleAr('');
      setNewImageAltEn('');
      setNewImageAltAr('');
      setShowNewImageForm(false);
      
      // Reset the input
      const fileInput = document.getElementById('new-image-input') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    }
  };
  
  // Drag and drop handlers for reordering
  const handleDragStart = (index: number) => {
    setDraggedItem(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };

  const handleDrop = (dropIndex: number) => {
    if (draggedItem === null || draggedItem === dropIndex) return;
    
    const newPhotos = [...(project.constructionPhotos || [])];
    const draggedItemContent = newPhotos[draggedItem];
    
    // Remove the dragged item
    newPhotos.splice(draggedItem, 1);
    
    // Add it at the new position
    newPhotos.splice(dropIndex, 0, draggedItemContent);
    
    // Update the project state with the new order
    setProject({
      ...project,
      constructionPhotos: newPhotos
    });
    
    setDraggedItem(null);
  };
  
  // Handle starting to edit image alt text
  const startEditItem = (index: number) => {
    setEditingIndex(index);
    setEditingTitleEn(project.constructionPhotos[index].titleEn || '');
    setEditingTitleAr(project.constructionPhotos[index].titleAr || '');
    setEditingAltEn(project.constructionPhotos[index].altEn || '');
    setEditingAltAr(project.constructionPhotos[index].altAr || '');
  };

  // Save edited item alt text
  const saveItemEdit = () => {
    if (editingIndex !== null) {
      const updatedPhotos = [...(project.constructionPhotos || [])];
      updatedPhotos[editingIndex] = {
        ...updatedPhotos[editingIndex],
        titleEn: editingTitleEn,
        titleAr: editingTitleAr,
        altEn: editingAltEn,
        altAr: editingAltAr
      };
      
      setProject({
        ...project,
        constructionPhotos: updatedPhotos
      });
      
      // Reset editing state
      setEditingIndex(null);
      setEditingTitleEn('');
      setEditingTitleAr('');
      setEditingAltEn('');
      setEditingAltAr('');
    }
  };

  // Cancel editing item
  const cancelItemEdit = () => {
    setEditingIndex(null);
    setEditingTitleEn('');
    setEditingTitleAr('');
    setEditingAltEn('');
    setEditingAltAr('');
  };

  return (
    <div className="mt-6 bg-gray-800 shadow rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Construction Photos' : 'صور البناء'}
      </h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {/* Existing Images */}
        {project.constructionPhotos && project.constructionPhotos.map((item: any, index: number) => (
          <div 
            key={index} 
            className={`bg-gray-700 rounded-lg overflow-hidden h-full flex flex-col relative ${draggedItem === index ? 'opacity-50' : 'opacity-100'}`}
            draggable={editingIndex !== index}
            onDragStart={() => handleDragStart(index)}
            onDragOver={handleDragOver}
            onDrop={() => handleDrop(index)}
          >
            {editingIndex === index ? (
              <>
                <div className="h-48 bg-gray-800 overflow-hidden">
                  <div 
                    className="w-full h-full cursor-pointer group relative"
                    onClick={() => {
                      const fileInput = document.createElement('input');
                      fileInput.type = 'file';
                      fileInput.accept = 'image/*';
                      fileInput.onchange = (e) => {
                        const target = e.target as HTMLInputElement;
                        if (target.files && target.files[0]) {
                          const newFile = target.files[0];
                          // Update the item with the new image but keep the alt text
                          const updatedPhotos = [...(project.constructionPhotos || [])];
                          updatedPhotos[editingIndex] = {
                            ...updatedPhotos[editingIndex],
                            image: newFile
                          };
                          
                          setProject({
                            ...project,
                            constructionPhotos: updatedPhotos
                          });
                        }
                      };
                      fileInput.click();
                    }}
                  >
                    <img 
                      src={URL.createObjectURL(item.image)} 
                      alt={item.altEn || `construction photo ${index + 1}`}
                      className="object-cover w-full h-full" 
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center text-white">
                      <p className="text-sm font-medium">{language === 'en' ? 'Click to replace image' : 'انقر لاستبدال الصورة'}</p>
                    </div>
                  </div>
                </div>
                <div className="p-4 flex-grow flex flex-col">
                  <div className="mb-3 mt-auto">
                    <label className="block text-xs font-medium text-gray-300 mb-1">
                      {language === 'en' ? 'English Title' : 'العنوان بالإنجليزية'}
                    </label>
                    <input
                      type="text"
                      value={editingTitleEn}
                      onChange={(e) => setEditingTitleEn(e.target.value)}
                      className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-8 py-1 px-2"
                      dir="ltr"
                    />
                  </div>
                  
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-300 mb-1">
                      {language === 'en' ? 'Arabic Title' : 'العنوان بالعربية'}
                    </label>
                    <input
                      type="text"
                      value={editingTitleAr}
                      onChange={(e) => setEditingTitleAr(e.target.value)}
                      className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-8 py-1 px-2"
                      dir="rtl"
                    />
                  </div>
                  
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-300 mb-1">
                      {language === 'en' ? 'English Alt Text' : 'النص البديل بالإنجليزية'}
                    </label>
                    <input
                      type="text"
                      value={editingAltEn}
                      onChange={(e) => setEditingAltEn(e.target.value)}
                      className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-8 py-1 px-2"
                      dir="ltr"
                    />
                  </div>
                  
                  <div className="mb-3">
                    <label className="block text-xs font-medium text-gray-300 mb-1">
                      {language === 'en' ? 'Arabic Alt Text' : 'النص البديل بالعربية'}
                    </label>
                    <input
                      type="text"
                      value={editingAltAr}
                      onChange={(e) => setEditingAltAr(e.target.value)}
                      className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-8 py-1 px-2"
                      dir="rtl"
                    />
                  </div>
                  
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={cancelItemEdit}
                      className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500"
                    >
                      {language === 'en' ? 'Cancel' : 'إلغاء'}
                    </button>
                    <button
                      type="button"
                      onClick={saveItemEdit}
                      className="px-3 py-1 text-xs bg-[#00C2FF] text-white rounded hover:bg-[#009DB5]"
                    >
                      {language === 'en' ? 'Save' : 'حفظ'}
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="h-48 bg-gray-800 overflow-hidden relative group">
                  {project.constructionPhotos.length > 1 && (
                    <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1.5 z-10 text-gray-300">
                      <FiMove className="h-4 w-4" />
                    </div>
                  )}
                  <img 
                    src={URL.createObjectURL(item.image)} 
                    alt={language === 'en' ? (item.altEn || `construction photo ${index + 1}`) : (item.altAr || `صورة البناء ${index + 1}`)} 
                    className="object-cover w-full h-full" 
                  />
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-50">
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={() => startEditItem(index)}
                        className="p-1 bg-blue-600 rounded-full text-white"
                        title={language === 'en' ? 'Edit alt text' : 'تحرير النص البديل'}
                      >
                        <FiEdit className="h-5 w-5" />
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          const updatedPhotos = [...(project.constructionPhotos || [])];
                          updatedPhotos.splice(index, 1);
                          setProject({
                            ...project,
                            constructionPhotos: updatedPhotos
                          });
                        }}
                        className="p-1 bg-red-600 rounded-full text-white"
                        title={language === 'en' ? 'Remove image' : 'إزالة الصورة'}
                      >
                        <FiX className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <p className="text-xs text-gray-400 truncate mb-1">{item.image.name}</p>
                  <div className="flex flex-col gap-0.5">
                    <p className="text-xs text-gray-300 truncate">
                      <span className="font-medium">EN Title:</span> {item.titleEn || "(No title)"}
                    </p>
                    <p className="text-xs text-gray-300 truncate" dir="rtl">
                      <span className="font-medium">عنوان:</span> {item.titleAr || "(لا يوجد عنوان)"}
                    </p>
                    <p className="text-xs text-gray-300 truncate">
                      <span className="font-medium">EN Alt:</span> {item.altEn || "(No alt text)"}
                    </p>
                    <p className="text-xs text-gray-300 truncate" dir="rtl">
                      <span className="font-medium">نص بديل:</span> {item.altAr || "(لا يوجد نص بديل)"}
                    </p>
                  </div>
                </div>
              </>
            )}
          </div>
        ))}
        
        {/* New Image Form or Add Button Card */}
        {showNewImageForm ? (
          <div className="bg-gray-700 rounded-lg overflow-hidden h-full flex flex-col relative border-2 border-gray-600 shadow-lg">
            <div className="h-48 bg-gray-800 flex items-center justify-center border-b border-gray-600">
              {newImage ? (
                <div 
                  className="relative w-full h-full cursor-pointer group"
                  onClick={() => document.getElementById('new-image-input')?.click()}
                >
                  <img 
                    src={URL.createObjectURL(newImage)} 
                    alt={language === 'en' ? "Construction preview" : "معاينة البناء"}
                    className="object-cover w-full h-full" 
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col items-center justify-center text-white">
                    <p className="text-sm font-medium mb-1">{language === 'en' ? 'Click to change image' : 'انقر لتغيير الصورة'}</p>
                    <p className="text-xs opacity-75">{newImage.name}</p>
                  </div>
                </div>
              ) : (
                <div 
                  className="w-full h-full flex items-center justify-center cursor-pointer bg-gray-750 hover:bg-gray-700"
                  onClick={() => document.getElementById('new-image-input')?.click()}
                >
                  <div className="text-center">
                    <p className="text-gray-300 font-medium mb-2">
                      {language === 'en' ? 'Click to select image' : 'انقر لتحديد صورة'}
                    </p>
                    <div className="w-12 h-12 rounded-full bg-[#00C2FF] flex items-center justify-center mx-auto">
                      <FiPlus className="h-6 w-6 text-white" />
                    </div>
                  </div>
                </div>
              )}
              <input
                type="file"
                id="new-image-input"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
            </div>
            
            <div className="p-5 flex-grow flex flex-col bg-gray-750">
              <div className="mb-4">
                <p className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'English Title' : 'العنوان بالإنجليزية'}
                </p>
                <input
                  type="text"
                  value={newImageTitleEn}
                  onChange={(e) => setNewImageTitleEn(e.target.value)}
                  placeholder="Title in English"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir="ltr"
                />
              </div>
              
              <div className="mb-4">
                <p className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Arabic Title' : 'العنوان بالعربية'}
                </p>
                <input
                  type="text"
                  value={newImageTitleAr}
                  onChange={(e) => setNewImageTitleAr(e.target.value)}
                  placeholder="العنوان بالعربية"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir="rtl"
                />
              </div>
              
              <div className="mb-4">
                <p className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'English Alt Text' : 'النص البديل بالإنجليزية'}
                </p>
                <input
                  type="text"
                  value={newImageAltEn}
                  onChange={(e) => setNewImageAltEn(e.target.value)}
                  placeholder="Describe this image"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir="ltr"
                />
              </div>
              
              <div className="mb-5">
                <p className="block text-sm font-medium text-gray-300 mb-1">
                  {language === 'en' ? 'Arabic Alt Text' : 'النص البديل بالعربية'}
                </p>
                <input
                  type="text"
                  value={newImageAltAr}
                  onChange={(e) => setNewImageAltAr(e.target.value)}
                  placeholder="وصف هذه الصورة"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                  dir="rtl"
                />
              </div>
              
              <div className="flex justify-end space-x-2 mt-auto">
                <button
                  type="button"
                  onClick={() => {
                    setNewImage(null);
                    setNewImageTitleEn('');
                    setNewImageTitleAr('');
                    setNewImageAltEn('');
                    setNewImageAltAr('');
                    setShowNewImageForm(false);
                    const fileInput = document.getElementById('new-image-input') as HTMLInputElement;
                    if (fileInput) fileInput.value = '';
                  }}
                  className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
                >
                  {language === 'en' ? 'Cancel' : 'إلغاء'}
                </button>
                <button
                  type="button"
                  onClick={addImage}
                  disabled={!newImage}
                  className={`px-4 py-2 text-sm font-medium text-white rounded ${
                    newImage ? 'bg-[#00C2FF] hover:bg-[#009DB5]' : 'bg-gray-600 cursor-not-allowed'
                  }`}
                >
                  {language === 'en' ? 'Save' : 'حفظ'}
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div 
            className="bg-gray-750 rounded-lg overflow-hidden flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors border-2 border-dashed border-gray-600 h-48"
            onClick={() => setShowNewImageForm(true)}
          >
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-[#00C2FF] flex items-center justify-center mx-auto mb-2">
                <FiPlus className="h-8 w-8 text-white" />
              </div>
              <p className="text-gray-300 font-medium">
                {language === 'en' ? 'Add New Image' : 'إضافة صورة جديدة'}
              </p>
            </div>
          </div>
        )}
      </div>
      
      <p className="mt-1 text-xs text-gray-400 mb-4">
        {language === 'en' 
          ? 'Upload images for the project construction with descriptive alt text for accessibility. Drag and drop to reorder.'
          : 'قم بتحميل صور لمشروع البناء مع نص بديل وصفي للوصول. اسحب وأفلت لإعادة الترتيب.'}
      </p>
    </div>
  );
};

export default ConstructionPhotos; 