"use client";

import React, { useState, useMemo } from 'react';
import { 
  FiMail, FiSearch, FiFilter, FiEye, FiEyeOff, FiClock, 
  FiUser, FiPhone, FiMessageSquare, FiCalendar, FiMoreVertical,
  FiTrash2, FiStar, FiArchive, FiDownload, FiRefreshCw
} from 'react-icons/fi';
import InquiryDetailModal from './components/InquiryDetailModal';
import BulkActionsBar from './components/BulkActionsBar';

interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone?: string;
  interest: string;
  subject: string;
  message: string;
  isRead: boolean;
  isStarred: boolean;
  isArchived: boolean;
  createdAt: Date;
  lastViewedAt?: Date;
  priority: 'high' | 'medium' | 'low';
  status: 'new' | 'in-progress' | 'responded' | 'closed';
}

// Mock data for demonstration
const mockInquiries: Inquiry[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+971 50 123 4567',
    interest: 'Luxury Apartments',
    subject: 'Inquiry about Downtown Dubai Properties',
    message: 'I am interested in purchasing a luxury apartment in Downtown Dubai. Could you please provide more information about available units and pricing?',
    isRead: false,
    isStarred: true,
    isArchived: false,
    createdAt: new Date('2024-01-15T10:30:00'),
    priority: 'high',
    status: 'new'
  },
  {
    id: '2',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+971 55 987 6543',
    interest: 'Investment Properties',
    subject: 'Investment Opportunities in Dubai Marina',
    message: 'Hello, I am looking for investment opportunities in Dubai Marina. What are the current market trends and expected ROI?',
    isRead: true,
    isStarred: false,
    isArchived: false,
    createdAt: new Date('2024-01-14T14:20:00'),
    lastViewedAt: new Date('2024-01-14T16:45:00'),
    priority: 'medium',
    status: 'in-progress'
  },
  {
    id: '3',
    name: 'Mohammed Hassan',
    email: '<EMAIL>',
    interest: 'Commercial Properties',
    subject: 'Office Space Rental Inquiry',
    message: 'I need office space for my business in Business Bay. Please share available options.',
    isRead: true,
    isStarred: false,
    isArchived: false,
    createdAt: new Date('2024-01-13T09:15:00'),
    lastViewedAt: new Date('2024-01-13T11:30:00'),
    priority: 'low',
    status: 'responded'
  },
  {
    id: '4',
    name: 'Emily Chen',
    email: '<EMAIL>',
    phone: '+971 52 456 7890',
    interest: 'Villa Rentals',
    subject: 'Family Villa in Emirates Hills',
    message: 'Looking for a family villa in Emirates Hills for long-term rental. Budget around AED 300,000 per year.',
    isRead: false,
    isStarred: false,
    isArchived: false,
    createdAt: new Date('2024-01-12T16:45:00'),
    priority: 'high',
    status: 'new'
  },
  {
    id: '5',
    name: 'David Wilson',
    email: '<EMAIL>',
    interest: 'Penthouse',
    subject: 'Penthouse Viewing Request',
    message: 'I would like to schedule a viewing for penthouses in Palm Jumeirah. Available this weekend?',
    isRead: true,
    isStarred: true,
    isArchived: false,
    createdAt: new Date('2024-01-11T11:20:00'),
    lastViewedAt: new Date('2024-01-11T13:15:00'),
    priority: 'medium',
    status: 'closed'
  }
];

export default function InquiriesPage() {
  const [inquiries, setInquiries] = useState<Inquiry[]>(mockInquiries);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedInquiries, setSelectedInquiries] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'priority'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Filter and search logic
  const filteredInquiries = useMemo(() => {
    let filtered = inquiries.filter(inquiry => {
      const matchesSearch = 
        inquiry.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        inquiry.message.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || inquiry.status === filterStatus;
      const matchesPriority = filterPriority === 'all' || inquiry.priority === filterPriority;
      
      return matchesSearch && matchesStatus && matchesPriority && !inquiry.isArchived;
    });

    // Sort inquiries
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [inquiries, searchTerm, filterStatus, filterPriority, sortBy, sortOrder]);

  // Statistics
  const stats = useMemo(() => {
    const total = inquiries.filter(i => !i.isArchived).length;
    const unread = inquiries.filter(i => !i.isRead && !i.isArchived).length;
    const starred = inquiries.filter(i => i.isStarred && !i.isArchived).length;
    const highPriority = inquiries.filter(i => i.priority === 'high' && !i.isArchived).length;
    
    return { total, unread, starred, highPriority };
  }, [inquiries]);

  const markAsRead = (id: string) => {
    setInquiries(prev => prev.map(inquiry => 
      inquiry.id === id 
        ? { ...inquiry, isRead: true, lastViewedAt: new Date() }
        : inquiry
    ));
  };

  const toggleStar = (id: string) => {
    setInquiries(prev => prev.map(inquiry => 
      inquiry.id === id 
        ? { ...inquiry, isStarred: !inquiry.isStarred }
        : inquiry
    ));
  };

  const updateStatus = (id: string, status: Inquiry['status']) => {
    setInquiries(prev => prev.map(inquiry => 
      inquiry.id === id ? { ...inquiry, status } : inquiry
    ));
  };

  const archiveInquiry = (id: string) => {
    setInquiries(prev => prev.map(inquiry => 
      inquiry.id === id ? { ...inquiry, isArchived: true } : inquiry
    ));
  };

  const deleteInquiry = (id: string) => {
    setInquiries(prev => prev.filter(inquiry => inquiry.id !== id));
  };

  const handleInquiryClick = (inquiry: Inquiry) => {
    setSelectedInquiry(inquiry);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedInquiry(null);
  };

  // Bulk actions
  const handleSelectInquiry = (id: string) => {
    setSelectedInquiries(prev => 
      prev.includes(id) 
        ? prev.filter(inquiryId => inquiryId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    setSelectedInquiries(filteredInquiries.map(inquiry => inquiry.id));
  };

  const handleClearSelection = () => {
    setSelectedInquiries([]);
  };

  const handleBulkMarkAsRead = () => {
    setInquiries(prev => prev.map(inquiry => 
      selectedInquiries.includes(inquiry.id)
        ? { ...inquiry, isRead: true, lastViewedAt: new Date() }
        : inquiry
    ));
    setSelectedInquiries([]);
  };

  const handleBulkStar = () => {
    setInquiries(prev => prev.map(inquiry => 
      selectedInquiries.includes(inquiry.id)
        ? { ...inquiry, isStarred: true }
        : inquiry
    ));
    setSelectedInquiries([]);
  };

  const handleBulkUnstar = () => {
    setInquiries(prev => prev.map(inquiry => 
      selectedInquiries.includes(inquiry.id)
        ? { ...inquiry, isStarred: false }
        : inquiry
    ));
    setSelectedInquiries([]);
  };

  const handleBulkArchive = () => {
    setInquiries(prev => prev.map(inquiry => 
      selectedInquiries.includes(inquiry.id)
        ? { ...inquiry, isArchived: true }
        : inquiry
    ));
    setSelectedInquiries([]);
  };

  const handleBulkDelete = () => {
    setInquiries(prev => prev.filter(inquiry => !selectedInquiries.includes(inquiry.id)));
    setSelectedInquiries([]);
  };

  const handleBulkExport = () => {
    const selectedData = inquiries.filter(inquiry => selectedInquiries.includes(inquiry.id));
    const csvContent = [
      ['Name', 'Email', 'Phone', 'Interest', 'Subject', 'Message', 'Status', 'Priority', 'Created At'],
      ...selectedData.map(inquiry => [
        inquiry.name,
        inquiry.email,
        inquiry.phone || '',
        inquiry.interest,
        inquiry.subject,
        inquiry.message,
        inquiry.status,
        inquiry.priority,
        inquiry.createdAt.toISOString()
      ])
    ].map(row => row.map(field => `"${field}"`).join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inquiries-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    setSelectedInquiries([]);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400 bg-red-400/10';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10';
      case 'low': return 'text-green-400 bg-green-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-blue-400 bg-blue-400/10';
      case 'in-progress': return 'text-yellow-400 bg-yellow-400/10';
      case 'responded': return 'text-green-400 bg-green-400/10';
      case 'closed': return 'text-gray-400 bg-gray-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center">
              <FiMail className="mr-3 h-8 w-8 text-[#00C2FF]" />
              Inquiries Management
            </h1>
            <p className="text-gray-400 mt-1">Manage contact form submissions and customer inquiries</p>
          </div>
          <div className="flex space-x-3">
            <button 
              onClick={handleBulkExport}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center"
            >
              <FiDownload className="mr-2 h-4 w-4" />
              Export All
            </button>
            <button className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors flex items-center">
              <FiRefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Inquiries</p>
                <p className="text-2xl font-bold text-white">{stats.total}</p>
              </div>
              <FiMail className="h-8 w-8 text-[#00C2FF]" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Unread</p>
                <p className="text-2xl font-bold text-red-400">{stats.unread}</p>
              </div>
              <FiEyeOff className="h-8 w-8 text-red-400" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Starred</p>
                <p className="text-2xl font-bold text-yellow-400">{stats.starred}</p>
              </div>
              <FiStar className="h-8 w-8 text-yellow-400" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">High Priority</p>
                <p className="text-2xl font-bold text-red-400">{stats.highPriority}</p>
              </div>
              <FiClock className="h-8 w-8 text-red-400" />
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <FiSearch className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search inquiries by name, email, subject, or message..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center"
            >
              <FiFilter className="mr-2 h-4 w-4" />
              Filters
            </button>
          </div>
          
          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Status</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="all">All Status</option>
                  <option value="new">New</option>
                  <option value="in-progress">In Progress</option>
                  <option value="responded">Responded</option>
                  <option value="closed">Closed</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Priority</label>
                <select
                  value={filterPriority}
                  onChange={(e) => setFilterPriority(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="all">All Priorities</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Sort By</label>
                <select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [by, order] = e.target.value.split('-');
                    setSortBy(by as 'date' | 'name' | 'priority');
                    setSortOrder(order as 'asc' | 'desc');
                  }}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="date-desc">Newest First</option>
                  <option value="date-asc">Oldest First</option>
                  <option value="name-asc">Name A-Z</option>
                  <option value="name-desc">Name Z-A</option>
                  <option value="priority-desc">High Priority First</option>
                  <option value="priority-asc">Low Priority First</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bulk Actions Bar */}
      <BulkActionsBar
        selectedCount={selectedInquiries.length}
        totalCount={filteredInquiries.length}
        onMarkAllAsRead={handleBulkMarkAsRead}
        onStarAll={handleBulkStar}
        onUnstarAll={handleBulkUnstar}
        onArchiveAll={handleBulkArchive}
        onDeleteAll={handleBulkDelete}
        onExportSelected={handleBulkExport}
        onClearSelection={handleClearSelection}
        onSelectAll={handleSelectAll}
      />

      {/* Inquiries List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">
              Inquiries ({filteredInquiries.length})
            </h2>
            {filteredInquiries.length > 0 && (
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={selectedInquiries.length === filteredInquiries.length}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleSelectAll();
                    } else {
                      handleClearSelection();
                    }
                  }}
                  className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-gray-800"
                />
                <span className="text-sm text-gray-400">Select all</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="divide-y divide-gray-700">
          {filteredInquiries.length === 0 ? (
            <div className="p-8 text-center">
              <FiMail className="mx-auto h-12 w-12 text-gray-500 mb-4" />
              <p className="text-gray-400">No inquiries found matching your criteria.</p>
            </div>
          ) : (
            filteredInquiries.map((inquiry) => (
              <div
                key={inquiry.id}
                className={`p-4 hover:bg-gray-700/50 transition-colors ${
                  !inquiry.isRead ? 'bg-blue-900/20' : ''
                }`}
              >
                <div className="flex items-start space-x-4">
                  <div className="flex items-center pt-1">
                    <input
                      type="checkbox"
                      checked={selectedInquiries.includes(inquiry.id)}
                      onChange={() => handleSelectInquiry(inquiry.id)}
                      onClick={(e) => e.stopPropagation()}
                      className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-gray-800"
                    />
                  </div>
                  
                  <div 
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => handleInquiryClick(inquiry)}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        <FiUser className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-white">{inquiry.name}</span>
                        {!inquiry.isRead && (
                          <span className="inline-block w-2 h-2 bg-blue-400 rounded-full"></span>
                        )}
                      </div>
                      
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(inquiry.priority)}`}>
                        {inquiry.priority.toUpperCase()}
                      </span>
                      
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(inquiry.status)}`}>
                        {inquiry.status.replace('-', ' ').toUpperCase()}
                      </span>
                      
                      {inquiry.isStarred && (
                        <FiStar className="h-4 w-4 text-yellow-400 fill-current" />
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-gray-400">
                          <FiMail className="mr-2 h-4 w-4" />
                          {inquiry.email}
                        </div>
                        {inquiry.phone && (
                          <div className="flex items-center text-sm text-gray-400">
                            <FiPhone className="mr-2 h-4 w-4" />
                            {inquiry.phone}
                          </div>
                        )}
                        <div className="flex items-center text-sm text-gray-400">
                          <FiMessageSquare className="mr-2 h-4 w-4" />
                          Interest: {inquiry.interest}
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-gray-400">
                          <FiCalendar className="mr-2 h-4 w-4" />
                          Created: {formatDate(inquiry.createdAt)}
                        </div>
                        {inquiry.lastViewedAt && (
                          <div className="flex items-center text-sm text-gray-400">
                            <FiEye className="mr-2 h-4 w-4" />
                            Last viewed: {formatDate(inquiry.lastViewedAt)}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="mb-3">
                      <h4 className="font-medium text-white mb-1">{inquiry.subject}</h4>
                      <p className="text-gray-400 text-sm line-clamp-2">{inquiry.message}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!inquiry.isRead && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          markAsRead(inquiry.id);
                        }}
                        className="p-2 text-gray-400 hover:text-blue-400 transition-colors"
                        title="Mark as read"
                      >
                        <FiEye className="h-4 w-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleStar(inquiry.id);
                      }}
                      className={`p-2 transition-colors ${
                        inquiry.isStarred ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-400'
                      }`}
                      title={inquiry.isStarred ? 'Remove star' : 'Add star'}
                    >
                      <FiStar className={`h-4 w-4 ${inquiry.isStarred ? 'fill-current' : ''}`} />
                    </button>
                    
                    <div className="relative group">
                      <button 
                        onClick={(e) => e.stopPropagation()}
                        className="p-2 text-gray-400 hover:text-white transition-colors"
                      >
                        <FiMoreVertical className="h-4 w-4" />
                      </button>
                      
                      <div className="absolute right-0 top-8 w-48 bg-gray-700 rounded-lg shadow-lg border border-gray-600 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                        <div className="py-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              updateStatus(inquiry.id, 'in-progress');
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-600"
                          >
                            Mark as In Progress
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              updateStatus(inquiry.id, 'responded');
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-600"
                          >
                            Mark as Responded
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              updateStatus(inquiry.id, 'closed');
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-600"
                          >
                            Mark as Closed
                          </button>
                          <hr className="border-gray-600 my-1" />
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              archiveInquiry(inquiry.id);
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-600 flex items-center"
                          >
                            <FiArchive className="mr-2 h-4 w-4" />
                            Archive
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              if (confirm('Are you sure you want to delete this inquiry?')) {
                                deleteInquiry(inquiry.id);
                              }
                            }}
                            className="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-600 flex items-center"
                          >
                            <FiTrash2 className="mr-2 h-4 w-4" />
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Inquiry Detail Modal */}
      <InquiryDetailModal
        inquiry={selectedInquiry}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onUpdateStatus={updateStatus}
        onToggleStar={toggleStar}
        onArchive={archiveInquiry}
        onDelete={deleteInquiry}
        onMarkAsRead={markAsRead}
      />
    </div>
  );
} 