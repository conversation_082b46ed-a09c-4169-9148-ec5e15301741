import React from 'react';
import Link from 'next/link';
import { FiSave, FiArrowLeft } from 'react-icons/fi';

interface ProjectHeaderProps {
  title: string;
  subtitle: string;
  handleSave: () => void;
  isSaving: boolean;
  language: 'en' | 'ar';
}

const ProjectHeader: React.FC<ProjectHeaderProps> = ({ 
  title, 
  subtitle, 
  handleSave, 
  isSaving,
  language
}) => {
  return (
    <div className="sm:flex sm:items-center sm:justify-between">
      <div>
        <h1 className="text-3xl font-bold text-white">
          {title}
        </h1>
        <p className="mt-2 text-sm text-gray-400">
          {subtitle}
        </p>
      </div>
      <div className="mt-4 sm:mt-0 flex space-x-3">
        <Link
          href="/admin/projects"
          className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
        >
          <FiArrowLeft className="mr-2 -ml-1 h-5 w-5 text-gray-400" />
          {language === 'en' ? 'Back' : 'رجوع'}
        </Link>
        <button
          type="button"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          onClick={handleSave}
          disabled={isSaving}
        >
          <FiSave className="mr-2 -ml-1 h-5 w-5" />
          {isSaving 
            ? (language === 'en' ? 'Creating...' : 'جاري الإنشاء...') 
            : (language === 'en' ? 'Create Project' : 'إنشاء المشروع')}
        </button>
      </div>
    </div>
  );
};

export default ProjectHeader; 