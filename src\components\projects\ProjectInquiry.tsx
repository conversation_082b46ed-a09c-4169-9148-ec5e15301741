import { forwardRef } from "react";

interface ProjectInquiryProps {
  project: any;
}

const ProjectInquiry = forwardRef<HTMLElement, ProjectInquiryProps>(({ project }, ref) => {
  return (
    <section ref={ref} id="inquiry" className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Inquire About {project.title}</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Interested in this property? Complete the form below, and our dedicated sales team will contact you shortly.</p>
      </div>
      
      <div className="max-w-6xl mx-auto px-1 sm:px-2 md:px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-12">
          {/* Form */}
          <div className="bg-white rounded-xl shadow-sm p-3 sm:p-4 md:p-6 border border-gray-100">
            <form className="space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">Full Name</label>
                  <input 
                    type="text" 
                    className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">Email Address</label>
                  <input 
                    type="email" 
                    className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">Phone Number</label>
                  <input 
                    type="tel" 
                    className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300"
                    placeholder="+****************"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">Nationality</label>
                  <input 
                    type="text" 
                    className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300"
                    placeholder="Enter your nationality"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-gray-700 font-medium mb-2">Interested In</label>
                <select className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300 text-gray-700">
                  <option value="" className="text-gray-700">Select a property type</option>
                  {project.investment.propertyTypes.map((type: string, index: number) => (
                    <option key={index} value={type} className="text-gray-700">{type}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-gray-700 font-medium mb-2">Investment Budget</label>
                <select className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300 text-gray-700">
                  <option value="" className="text-gray-700">Select your budget range</option>
                  <option value="500k-750k" className="text-gray-700">$500,000 - $750,000</option>
                  <option value="750k-1m" className="text-gray-700">$750,000 - $1,000,000</option>
                  <option value="1m-1.5m" className="text-gray-700">$1,000,000 - $1,500,000</option>
                  <option value="1.5m-2m" className="text-gray-700">$1,500,000 - $2,000,000</option>
                  <option value="2m+" className="text-gray-700">$2,000,000+</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-700 font-medium mb-2">Message</label>
                <textarea 
                  className="w-full px-2 sm:px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF] outline-none transition-all duration-300 h-32 resize-none"
                  placeholder="Please include any specific questions or requirements..."
                ></textarea>
              </div>
              
              <div>
                <div className="flex items-center">
                  <input type="checkbox" id="subscribe" className="me-2" />
                  <label htmlFor="subscribe" className="text-gray-700">Keep me updated on new properties and exclusive offers</label>
                </div>
              </div>
              
              <button type="submit" className="w-full bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white py-3 px-6 rounded-lg font-medium hover:shadow-lg transition-all duration-300">
                Submit Inquiry
              </button>
            </form>
          </div>
          
          {/* Contact Info & Benefits */}
          <div className="space-y-6 md:space-y-8">
            {/* Direct Contact */}
            <div className="bg-white rounded-xl shadow-sm p-3 sm:p-4 md:p-6 border border-gray-100">
              <h3 className="text-2xl font-bold text-gray-900 mb-4 md:mb-6">Contact Us Directly</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-10 h-10 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-3 md:me-4 flex-shrink-0">
                    <svg className="h-5 w-5 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">Phone</h4>
                    <p className="text-gray-700">+971 4 123 4567</p>
                    <p className="text-gray-500 text-sm mt-1">Available 7 days a week, 9am - 8pm</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-10 h-10 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-3 md:me-4 flex-shrink-0">
                    <svg className="h-5 w-5 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">Email</h4>
                    <p className="text-gray-700"><EMAIL></p>
                    <p className="text-gray-500 text-sm mt-1">We'll respond within 24 hours</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="w-10 h-10 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mt-1 me-3 md:me-4 flex-shrink-0">
                    <svg className="h-5 w-5 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">Visit Our Sales Center</h4>
                    <p className="text-gray-700">Sheikh Mohammed bin Rashid Blvd, Downtown Dubai</p>
                    <p className="text-gray-500 text-sm mt-1">Open daily from 10am - 7pm</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Benefits */}
            <div className="bg-gradient-to-br from-[#0D1526] to-[#232F3E] rounded-xl shadow-md p-3 sm:p-4 md:p-6 text-white">
              <h3 className="text-2xl font-bold mb-4 md:mb-6">Why Choose {project.title}?</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="mt-0.5 me-2 md:me-3 text-[#00C2FF]">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-white/90">Prime location in Downtown Dubai with panoramic views</p>
                </div>
                <div className="flex items-start">
                  <div className="mt-0.5 me-2 md:me-3 text-[#00C2FF]">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-white/90">Premium finishes and integrated smart home technology</p>
                </div>
                <div className="flex items-start">
                  <div className="mt-0.5 me-2 md:me-3 text-[#00C2FF]">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-white/90">Attractive payment plan with only 30% down payment</p>
                </div>
                <div className="flex items-start">
                  <div className="mt-0.5 me-2 md:me-3 text-[#00C2FF]">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-white/90">World-class amenities including infinity pool and fitness center</p>
                </div>
                <div className="flex items-start">
                  <div className="mt-0.5 me-2 md:me-3 text-[#00C2FF]">
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <p className="text-white/90">High rental yield potential of 7-9% annually</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

ProjectInquiry.displayName = "ProjectInquiry";

export default ProjectInquiry; 