"use client";

import React, { useEffect, useState } from 'react';

const ArchitecturalBackground = () => {
  // State to handle responsive sizing
  const [isMobile, setIsMobile] = useState(false);
  
  // Detect screen size changes
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none z-0">
      {/* Enhanced gradient background with more blur for text readability */}
      <div className="absolute inset-0 bg-gradient-to-br from-background/95 via-background/85 to-background/90 backdrop-blur-sm z-0"></div>
      
      {/* Start Side Tools */}
      <div className="absolute start-0 top-0 bottom-0 w-24 md:w-40 z-1 flex flex-col justify-between items-center py-20">
        {/* Ruler */}
        <div className="architectural-tool ruler-tool transform -rotate-12 hover:rotate-0 transition-transform duration-500">
          <svg width="150" height="40" viewBox="0 0 150 40" xmlns="http://www.w3.org/2000/svg">
            <rect x="0" y="10" width="150" height="20" className="tool-body" />
            {Array.from({ length: 15 }).map((_, i) => (
              <React.Fragment key={`ruler-mark-${i}`}>
                <line x1={i * 10} y1="10" x2={i * 10} y2="20" className="tool-mark" />
                <text x={i * 10 + 2} y="17" className="tool-text">{i}</text>
              </React.Fragment>
            ))}
          </svg>
        </div>
        
        {/* Set Square */}
        <div className="architectural-tool set-square-tool transform rotate-12 hover:rotate-0 transition-transform duration-500">
          <svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="set-square-grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="rgb(var(--color-primary))" stopOpacity="0.7" />
                <stop offset="100%" stopColor="rgb(var(--color-primary))" stopOpacity="0.4" />
              </linearGradient>
              <filter id="set-square-glow" height="130%">
                <feGaussianBlur in="SourceAlpha" stdDeviation="1" result="blur" />
                <feOffset in="blur" dx="0" dy="0" result="offsetBlur" />
                <feComponentTransfer in="offsetBlur" result="brightBlur">
                  <feFuncA type="linear" slope="0.5" />
                </feComponentTransfer>
                <feMerge>
                  <feMergeNode in="brightBlur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>
            
            {/* Main triangle shape with 45-45-90 degrees */}
            <path 
              d="M15,105 L105,105 L15,15 Z" 
              className="tool-body" 
              fill="url(#set-square-grad)" 
              filter="url(#set-square-glow)"
            />
            
            {/* Edge highlighting for 3D effect */}
            <path d="M15,105 L105,105" className="tool-edge-highlight" strokeWidth="1.2" />
            <path d="M105,105 L15,15" className="tool-edge-highlight" strokeWidth="1.2" />
            <path d="M15,15 L15,105" className="tool-edge-highlight" strokeWidth="1.2" />
            
            {/* 45° angle marker */}
            <path d="M35,105 A20,20 0 0,1 15,85" className="tool-angle-marker" fill="none" strokeDasharray="2,1" />
            <text x="25" y="90" className="tool-text" fontSize="6">45°</text>
            
            {/* 45° angle marker on other side */}
            <path d="M105,85 A20,20 0 0,1 85,105" className="tool-angle-marker" fill="none" strokeDasharray="2,1" />
            <text x="90" y="90" className="tool-text" fontSize="6">45°</text>
            
            {/* 90° angle marker */}
            <path d="M25,15 A10,10 0 0,0 15,25" className="tool-angle-marker" fill="none" />
            <text x="18" y="21" className="tool-text" fontSize="6">90°</text>
            
            {/* Measurement markings along hypotenuse */}
            {Array.from({ length: 9 }).map((_, i) => {
              const offset = i * 10;
              const x = 15 + offset;
              const y = 105 - offset;
              const length = i % 5 === 0 ? 4 : (i % 2 === 0 ? 3 : 2);
              const angle = Math.atan(-1) * (180 / Math.PI); // -45 degrees
              
              return (
                <React.Fragment key={`hypotenuse-mark-${i}`}>
                  <line 
                    x1={x} 
                    y1={y} 
                    x2={x + length * Math.cos((angle * Math.PI) / 180)} 
                    y2={y + length * Math.sin((angle * Math.PI) / 180)} 
                    className={i % 5 === 0 ? "tool-mark-major" : "tool-mark"} 
                    strokeWidth={i % 5 === 0 ? "1.2" : "0.8"}
                  />
                  {i % 5 === 0 && i > 0 && (
                    <text 
                      x={x + 5 * Math.cos((angle * Math.PI) / 180)} 
                      y={y + 5 * Math.sin((angle * Math.PI) / 180)} 
                      className="tool-text" 
                      fontSize="5.5"
                    >
                      {i}
                    </text>
                  )}
                </React.Fragment>
              );
            })}
            
            {/* Measurement markings along bottom edge */}
            {Array.from({ length: 9 }).map((_, i) => {
              const x = 15 + i * 10;
              const length = i % 5 === 0 ? 4 : (i % 2 === 0 ? 3 : 2);
              
              return (
                <React.Fragment key={`bottom-mark-${i}`}>
                  <line 
                    x1={x} 
                    y1={105} 
                    x2={x} 
                    y2={105 - length} 
                    className={i % 5 === 0 ? "tool-mark-major" : "tool-mark"} 
                    strokeWidth={i % 5 === 0 ? "1.2" : "0.8"}
                  />
                  {i % 5 === 0 && i > 0 && (
                    <text 
                      x={x} 
                      y={110} 
                      className="tool-text" 
                      fontSize="5.5" 
                      textAnchor="middle"
                    >
                      {i}
                    </text>
                  )}
                </React.Fragment>
              );
            })}
            
            {/* Measurement markings along vertical edge */}
            {Array.from({ length: 9 }).map((_, i) => {
              const y = 105 - i * 10;
              const length = i % 5 === 0 ? 4 : (i % 2 === 0 ? 3 : 2);
              
              return (
                <React.Fragment key={`vertical-mark-${i}`}>
                  <line 
                    x1={15} 
                    y1={y} 
                    x2={15 + length} 
                    y2={y} 
                    className={i % 5 === 0 ? "tool-mark-major" : "tool-mark"} 
                    strokeWidth={i % 5 === 0 ? "1.2" : "0.8"}
                  />
                  {i % 5 === 0 && i > 0 && (
                    <text 
                      x={10} 
                      y={y + 2} 
                      className="tool-text" 
                      fontSize="5.5" 
                      textAnchor="end"
                    >
                      {i}
                    </text>
                  )}
                </React.Fragment>
              );
            })}
            
            {/* Center hole */}
            <circle cx="45" cy="75" r="4" className="tool-hole" />
            
            {/* Small finger notch */}
            <path d="M55,105 A5,5 0 0,0 65,105" className="tool-detail" fill="none" strokeWidth="1.5" />
            
            {/* Manufacturer label (subtle) */}
            <text x="40" y="65" className="tool-text-subtle" fontSize="6" transform="rotate(-45, 40, 65)">ARCHITECT</text>
          </svg>
        </div>
        
        {/* T-Square */}
        <div className="architectural-tool t-square-tool transform -rotate-6 hover:rotate-0 transition-transform duration-500">
          <svg width="150" height="160" viewBox="0 0 150 160" xmlns="http://www.w3.org/2000/svg">
            {/* Horizontal part */}
            <rect x="0" y="5" width="150" height="15" rx="1" className="tool-body" />
            
            {/* Vertical part with subtle gradient */}
            <defs>
              <linearGradient id="t-square-grad" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="rgb(var(--color-primary))" stopOpacity="0.7" />
                <stop offset="100%" stopColor="rgb(var(--color-primary))" stopOpacity="0.5" />
              </linearGradient>
            </defs>
            <rect x="60" y="20" width="30" height="135" rx="1" fill="url(#t-square-grad)" className="tool-body" />
            
            {/* Measurement marks on top edge */}
            {Array.from({ length: 15 }).map((_, i) => (
              <React.Fragment key={`t-square-mark-${i}`}>
                <line x1={i * 10} y1="5" x2={i * 10} y2="2" className="tool-mark" strokeWidth="0.8" />
                {i % 2 === 0 && (
                  <text x={i * 10} y="0" className="tool-text" fontSize="6" textAnchor="middle">{i}</text>
                )}
              </React.Fragment>
            ))}
            
            {/* Measurement marks on right edge of vertical part */}
            {Array.from({ length: 14 }).map((_, i) => (
              <React.Fragment key={`t-square-v-mark-${i}`}>
                <line x1="90" y1={20 + (i * 10)} x2="93" y2={20 + (i * 10)} className="tool-mark" strokeWidth="0.8" />
                {i % 2 === 0 && (
                  <text x="96" y={22 + (i * 10)} className="tool-text" fontSize="6">{i}</text>
                )}
              </React.Fragment>
            ))}
            
            {/* Detail lines for realism */}
            <line x1="10" y1="10" x2="140" y2="10" className="tool-detail" strokeWidth="0.5" />
            <line x1="10" y1="15" x2="140" y2="15" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="25" x2="85" y2="25" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="35" x2="85" y2="35" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="45" x2="85" y2="45" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="55" x2="85" y2="55" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="65" x2="85" y2="65" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="75" x2="85" y2="75" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="85" x2="85" y2="85" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="95" x2="85" y2="95" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="105" x2="85" y2="105" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="115" x2="85" y2="115" className="tool-detail" strokeWidth="0.5" />
            <line x1="65" y1="125" x2="85" y2="125" className="tool-detail" strokeWidth="0.5" />
            
            {/* Edge highlighting for 3D effect */}
            <line x1="0" y1="5" x2="150" y2="5" className="tool-edge-highlight" />
            <line x1="60" y1="20" x2="60" y2="155" className="tool-edge-highlight" />
          </svg>
        </div>
      </div>
      
      {/* End Side Tools */}
      <div className="absolute end-0 top-0 bottom-0 w-24 md:w-40 z-1 flex flex-col justify-between items-center py-20">
        {/* Compass */}
        <div className="architectural-tool compass-tool transform rotate-12 hover:rotate-0 transition-transform duration-500">
          <svg width="80" height="120" viewBox="0 0 80 120" xmlns="http://www.w3.org/2000/svg">
            <circle cx="40" cy="40" r="35" className="tool-compass-arc" fill="none" />
            <line x1="40" y1="10" x2="40" y2="70" className="tool-compass-leg" />
            <line x1="20" y1="40" x2="60" y2="40" className="tool-compass-leg" />
            <path d="M20,75 L40,5 L60,75 Z" className="tool-compass-body" fill="none" />
            <circle cx="40" cy="40" r="3" className="tool-compass-joint" />
            <circle cx="40" cy="5" r="2" className="tool-compass-point" />
            <circle cx="40" cy="75" r="2" className="tool-compass-point" />
          </svg>
        </div>
        
        {/* Caliper */}
        <div className="architectural-tool caliper-tool transform rotate-0 hover:rotate-12 transition-transform duration-500">
          <svg width="140" height="80" viewBox="0 0 140 80" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="caliper-grad" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="rgb(var(--color-primary))" stopOpacity="0.9" />
                <stop offset="50%" stopColor="rgb(var(--color-primary))" stopOpacity="0.7" />
                <stop offset="100%" stopColor="rgb(var(--color-primary))" stopOpacity="0.8" />
              </linearGradient>
              <linearGradient id="caliper-metal" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="rgba(255,255,255,0.7)" />
                <stop offset="50%" stopColor="rgba(180,180,255,0.2)" />
                <stop offset="100%" stopColor="rgba(255,255,255,0.5)" />
              </linearGradient>
              <filter id="caliper-shadow" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow dx="0" dy="0" stdDeviation="1" floodColor="rgb(var(--color-primary))" floodOpacity="0.4"/>
              </filter>
              <filter id="display-inset" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow dx="0.5" dy="0.5" stdDeviation="0.5" floodColor="#000" floodOpacity="0.5"/>
              </filter>
            </defs>
            
            {/* Main body - digital caliper */}
            <g className="caliper-body" filter="url(#caliper-shadow)">
              {/* Main scale bar with metallic effect */}
              <rect x="15" y="30" width="120" height="5" rx="1" className="tool-body" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.3" />
              <rect x="15" y="30" width="120" height="2" rx="1" fill="url(#caliper-metal)" opacity="0.3" />
              
              {/* Fixed jaw with angled tip */}
              <path d="M15,30 L15,15 L17,10 L22,10 L24,15 L30,15 L30,30" 
                className="tool-body" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.8" />
              <line x1="17" y1="10" x2="24" y2="15" className="tool-detail" strokeWidth="0.8" />
              <rect x="15" y="15" width="15" height="15" rx="0" fill="url(#caliper-metal)" opacity="0.2" />
              
              {/* Inside measuring faces */}
              <rect x="24" y="17" width="1" height="10" fill="rgb(var(--color-primary))" opacity="0.9" />
              <rect x="84" y="17" width="1" height="10" fill="rgb(var(--color-primary))" opacity="0.9" />
              
              {/* Movable jaw assembly with angled tip */}
              <g className="movable-jaw" transform="translate(55, 0)">
                {/* Jaw extension */}
                <path d="M0,30 L0,15 L2,10 L7,10 L9,15 L30,15 L30,25 L0,25" 
                  className="tool-body" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.8" />
                <line x1="2" y1="10" x2="9" y2="15" className="tool-detail" strokeWidth="0.8" />
                <rect x="0" y="15" width="15" height="10" rx="0" fill="url(#caliper-metal)" opacity="0.2" />
                
                {/* Vernier housing with digital display */}
                <rect x="5" y="25" width="30" height="15" rx="1" className="tool-body" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.8" />
                
                {/* Digital display with inset effect */}
                <rect x="8" y="27" width="24" height="11" rx="1" className="digital-display" 
                  fill="rgba(20,30,80,0.9)" stroke="rgb(var(--color-primary))" strokeWidth="0.5" filter="url(#display-inset)" />
                
                {/* LCD digits with segments */}
                <g transform="translate(10, 33)" fill="rgba(120,200,255,0.9)">
                  <text className="tool-text" fontSize="7" fontFamily="monospace" fontWeight="bold">60.25</text>
                  <text className="tool-text" fontSize="4" x="25" y="0" fontFamily="monospace">mm</text>
                </g>
                
                {/* Brand name */}
                <text x="20" y="24" className="tool-text-subtle" fontSize="3.5" textAnchor="middle" fontWeight="bold">MITUTOYO</text>
                
                {/* Thumb roller for precision adjustment */}
                <g transform="translate(25, 40)">
                  <circle cx="0" cy="0" r="5" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.8" />
                  {/* Roller ridges */}
                  {Array.from({ length: 8 }).map((_, i) => {
                    const angle = i * 45;
                    const rad = angle * Math.PI / 180;
                    return (
                      <line 
                        key={`ridge-${i}`} 
                        x1={3.5 * Math.cos(rad)} 
                        y1={3.5 * Math.sin(rad)} 
                        x2={5 * Math.cos(rad)} 
                        y2={5 * Math.sin(rad)} 
                        stroke="rgb(var(--color-primary))" 
                        strokeWidth="1"
                        opacity="0.9"
                      />
                    );
                  })}
                  <circle cx="0" cy="0" r="2" fill="rgba(var(--color-background), 0.3)" stroke="rgb(var(--color-primary))" strokeWidth="0.5" />
                </g>

                {/* Lock screw */}
                <rect x="2" y="35" width="6" height="3" rx="1" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.5" />
                <line x1="5" y1="35" x2="5" y2="38" stroke="rgb(var(--color-primary))" strokeWidth="0.5" />
              </g>
              
              {/* Depth measuring rod */}
              <rect x="135" y="29.5" width="3" height="2" rx="0.5" className="tool-body" fill="url(#caliper-grad)" stroke="rgb(var(--color-primary))" strokeWidth="0.3" />
              <rect x="126" y="29.5" width="9" height="1" rx="0" fill="url(#caliper-grad)" opacity="0.9" />
            </g>
            
            {/* Measurement scale on main bar - larger range */}
            <g className="measurement-scale">
              {/* Larger hash marks every 10mm */}
              {Array.from({ length: 12 }).map((_, i) => {
                const x = 15 + (i * 10);
                return (
                  <React.Fragment key={`scale-major-${i}`}>
                    <line 
                      x1={x} 
                      y1="30" 
                      x2={x} 
                      y2="25" 
                      className="tool-mark-major" 
                      strokeWidth="0.8" 
                      opacity="0.9"
                    />
                  </React.Fragment>
                );
              })}
              
              {/* Smaller hash marks every 5mm */}
              {Array.from({ length: 23 }).map((_, i) => {
                if (i % 2 !== 0) {
                  const x = 15 + (i * 5);
                  return (
                    <line 
                      key={`scale-mid-${i}`}
                      x1={x} 
                      y1="30" 
                      x2={x} 
                      y2="27" 
                      className="tool-mark" 
                      strokeWidth="0.5" 
                      opacity="0.8"
                    />
                  );
                }
                return null;
              })}
              
              {/* Smallest hash marks every 1mm */}
              {Array.from({ length: 120 }).map((_, i) => {
                if (i % 5 !== 0) {
                  const x = 15 + i;
                  return (
                    <line 
                      key={`scale-minor-${i}`}
                      x1={x} 
                      y1="30" 
                      x2={x} 
                      y2="28.5" 
                      className="tool-mark" 
                      strokeWidth="0.3" 
                      opacity="0.6"
                    />
                  );
                }
                return null;
              })}
            </g>
            
            {/* Measurement visualization */}
            <line x1="25" y1="12" x2="85" y2="12" className="measurement-line" stroke="rgb(var(--color-secondary))" strokeWidth="0.8" strokeDasharray="2 1" strokeOpacity="0.7" />
            <circle cx="25" cy="12" r="0.8" fill="rgb(var(--color-secondary))" opacity="0.9" />
            <circle cx="85" cy="12" r="0.8" fill="rgb(var(--color-secondary))" opacity="0.9" />
            
            {/* Other buttons */}
            <rect x="72" y="38" width="5" height="2" rx="1" fill="rgb(100,150,255)" stroke="rgb(var(--color-primary))" strokeWidth="0.3" opacity="0.9" />
            <text x="74.5" y="44" className="tool-text-subtle" fontSize="2" textAnchor="middle">ORIGIN</text>
            
            {/* Technical specs */}
            <text x="70" y="55" className="tool-text-subtle" fontSize="3.5" opacity="0.7">DIGITAL CALIPER • 0.01mm PRECISION</text>
            <text x="70" y="60" className="tool-text-subtle" fontSize="2.5" opacity="0.6">RANGE: 0-150mm • STAINLESS STEEL</text>
          </svg>
        </div>
        
        {/* Protractor */}
        <div className="architectural-tool protractor-tool transform -rotate-6 hover:rotate-0 transition-transform duration-500">
          <svg width="120" height="80" viewBox="0 0 120 80" xmlns="http://www.w3.org/2000/svg">
            {/* Semi-circle arc */}
            <path d="M10,60 A50,50 0 0,1 110,60" className="tool-protractor-arc" fill="none" />
            
            {/* Base rectangle */}
            <rect x="10" y="60" width="100" height="15" className="tool-body" />
            
            {/* Center mark and dot */}
            <line x1="60" y1="60" x2="60" y2="65" className="tool-mark" strokeWidth="1.2" />
            <circle cx="60" cy="60" r="1.5" className="tool-hole" />
            
            {/* Degree markings along the curved edge - fixed to start from start */}
            {Array.from({ length: 19 }).map((_, i) => {
              // Calculate angle (0 at start, 90 at top, 180 at end)
              const angle = i * 10; 
              // Convert to radians (with 0 starting from start)
              const radians = ((180 - angle) * Math.PI) / 180;
              const isLongMark = i % 3 === 0; // Longer marks at 0, 30, 60, 90, 120, 150, 180 degrees
              
              // Calculate x position: 0° is at x=10, 90° is at x=60, 180° is at x=110
              const x = 10 + (100 * i / 18);
              // Calculate y position along the arc
              const y = 60 - Math.sqrt(2500 - Math.pow(x - 60, 2)); // Arc equation
              
              // Calculate text position slightly outside the arc
              const textOffset = isLongMark ? 6 : 3;
              const textX = x;
              const textY = y - textOffset;
              
              return (
                <React.Fragment key={`protractor-deg-${i}`}>
                  <line 
                    x1={x} 
                    y1={y} 
                    x2={x} 
                    y2={y + (isLongMark ? 5 : 3)} 
                    className={isLongMark ? "tool-mark-major" : "tool-mark"}
                    strokeWidth={isLongMark ? "1.2" : "0.8"}
                  />
                  {isLongMark && (
                    <text 
                      x={textX} 
                      y={textY} 
                      className="tool-text" 
                      fontSize="6"
                      textAnchor="middle"
                      dominantBaseline="middle"
                    >
                      {angle}°
                    </text>
                  )}
                </React.Fragment>
              );
            })}
            
            {/* Measurement marks along straight edge */}
            {Array.from({ length: 11 }).map((_, i) => (
              <line 
                key={`protractor-mark-${i}`} 
                x1={10 + (i * 10)} 
                y1="60" 
                x2={10 + (i * 10)} 
                y2={i % 2 === 0 ? "67" : "65"} 
                className={i % 2 === 0 ? "tool-mark-major" : "tool-mark"}
                strokeWidth={i % 2 === 0 ? "1.2" : "0.8"}
              />
            ))}
            {Array.from({ length: 6 }).map((_, i) => (
              <text 
                key={`protractor-bottom-text-${i}`} 
                x={10 + (i * 20)} 
                y="75" 
                className="tool-text"
                fontSize="6"
                textAnchor="middle"
              >
                {i * 2}
              </text>
            ))}
          </svg>
        </div>
      </div>
      
      <svg 
        width="100%" 
        height="100%" 
        viewBox="0 0 1000 1000" 
        xmlns="http://www.w3.org/2000/svg"
        className="architectural-blueprint relative z-1"
        style={{ 
          transform: isMobile ? 'scale(2)' : 'scale(1.5)', 
          transformOrigin: isMobile ? 'center center' : 'center',
          opacity: isMobile ? 0.4 : 0.6
        }}
      >
        {/* Horizontal grid lines - fewer on mobile */}
        {Array.from({ length: isMobile ? 10 : 20 }).map((_, i) => (
          <line 
            key={`h-line-${i}`} 
            x1="0" 
            y1={i * (isMobile ? 100 : 50)} 
            x2="1000" 
            y2={i * (isMobile ? 100 : 50)} 
            className="grid-line"
          />
        ))}
        
        {/* Vertical grid lines - fewer on mobile */}
        {Array.from({ length: isMobile ? 10 : 20 }).map((_, i) => (
          <line 
            key={`v-line-${i}`} 
            x1={i * (isMobile ? 100 : 50)} 
            y1="0" 
            x2={i * (isMobile ? 100 : 50)} 
            y2="1000" 
            className="grid-line"
          />
        ))}
        
        {/* Main House Outline */}
        <rect x="200" y="200" width="600" height="500" className="house-outline" />
        
        {/* House Interior - Rooms */}
        {/* Living Room */}
        <rect x="200" y="200" width="250" height="200" className="room-outline" />
        <text x="260" y="300" className="room-label">LIVING ROOM</text>
        
        {/* Kitchen */}
        <rect x="450" y="200" width="150" height="200" className="room-outline" />
        <text x="480" y="300" className="room-label">KITCHEN</text>
        
        {/* Dining Room */}
        <rect x="600" y="200" width="200" height="200" className="room-outline" />
        <text x="640" y="300" className="room-label">DINING</text>
        
        {/* Bedroom 1 */}
        <rect x="200" y="400" width="200" height="150" className="room-outline" />
        <text x="240" y="480" className="room-label">BEDROOM 1</text>
        
        {/* Bathroom 1 */}
        <rect x="400" y="400" width="100" height="150" className="room-outline" />
        <text x="410" y="480" className="room-label">BATH</text>
        
        {/* Bedroom 2 */}
        <rect x="500" y="400" width="150" height="150" className="room-outline" />
        <text x="530" y="480" className="room-label">BEDROOM 2</text>
        
        {/* Master Bedroom */}
        <rect x="200" y="550" width="250" height="150" className="room-outline" />
        <text x="240" y="630" className="room-label">MASTER BEDROOM</text>
        
        {/* Master Bathroom */}
        <rect x="450" y="550" width="100" height="150" className="room-outline" />
        <text x="460" y="630" className="room-label">BATH</text>
        
        {/* Office/Study */}
        <rect x="550" y="550" width="250" height="150" className="room-outline" />
        <text x="630" y="630" className="room-label">OFFICE</text>
        
        {/* Hallway */}
        <rect x="650" y="400" width="150" height="150" className="room-outline" />
        <text x="680" y="480" className="room-label">HALL</text>
        
        {/* Doors */}
        {/* Front Door */}
        <path d="M400,200 A40,40 0 0,1 400,240" className="door" />
        <line x1="380" y1="200" x2="420" y2="200" className="door-line" />
        
        {/* Living Room to Kitchen */}
        <path d="M450,250 A30,30 0 0,0 450,290" className="door" />
        <line x1="450" y1="240" x2="450" y2="300" className="door-line" />
        
        {/* Kitchen to Dining */}
        <path d="M600,250 A30,30 0 0,0 600,290" className="door" />
        <line x1="600" y1="240" x2="600" y2="300" className="door-line" />
        
        {/* Living Room to Bedroom 1 */}
        <path d="M300,400 A30,30 0 0,1 330,400" className="door" />
        <line x1="290" y1="400" x2="340" y2="400" className="door-line" />
        
        {/* Bedroom 1 to Bathroom */}
        <path d="M400,450 A20,20 0 0,0 400,480" className="door" />
        <line x1="400" y1="440" x2="400" y2="490" className="door-line" />
        
        {/* Hallway to Bedroom 2 */}
        <path d="M650,475 A25,25 0 0,1 675,475" className="door" />
        <line x1="640" y1="475" x2="685" y2="475" className="door-line" />
        
        {/* Hallway to Office */}
        <path d="M675,550 A25,25 0 0,1 675,525" className="door" />
        <line x1="675" y1="560" x2="675" y2="515" className="door-line" />
        
        {/* Master Bedroom to Master Bath */}
        <path d="M450,600 A20,20 0 0,0 450,630" className="door" />
        <line x1="450" y1="590" x2="450" y2="640" className="door-line" />
        
        {/* Windows */}
        <line x1="250" y1="200" x2="350" y2="200" className="window-line" />
        <line x1="500" y1="200" x2="550" y2="200" className="window-line" />
        <line x1="650" y1="200" x2="750" y2="200" className="window-line" />
        <line x1="200" y1="450" x2="200" y2="500" className="window-line" />
        <line x1="800" y1="250" x2="800" y2="350" className="window-line" />
        <line x1="800" y1="600" x2="800" y2="650" className="window-line" />
        <line x1="250" y1="700" x2="350" y2="700" className="window-line" />
        <line x1="600" y1="700" x2="700" y2="700" className="window-line" />
        
        {/* Furniture elements */}
        {/* Living Room */}
        <rect x="250" y="250" width="80" height="40" className="furniture" /> {/* Sofa */}
        <rect x="350" y="250" width="60" height="60" className="furniture" /> {/* Coffee Table */}
        
        {/* Kitchen */}
        <rect x="450" y="220" width="150" height="30" className="furniture" /> {/* Counter */}
        <circle cx="500" cy="350" r="20" className="furniture" /> {/* Kitchen Table */}
        
        {/* Dining Room */}
        <rect x="650" y="270" width="100" height="60" className="furniture" /> {/* Dining Table */}
        
        {/* Bedroom 1 */}
        <rect x="220" y="450" width="100" height="60" className="furniture" /> {/* Bed */}
        
        {/* Master Bedroom */}
        <rect x="220" y="600" width="120" height="70" className="furniture" /> {/* King Bed */}
        
        {/* Office */}
        <rect x="650" y="600" width="80" height="40" className="furniture" /> {/* Desk */}
        
        {/* Bathroom fixtures */}
        <circle cx="430" cy="420" r="15" className="bathroom-fixture" /> {/* Sink */}
        <rect x="410" y="490" width="30" height="40" className="bathroom-fixture" /> {/* Toilet */}
        <rect x="470" y="570" width="60" height="40" className="bathroom-fixture" /> {/* Shower */}
        
        {/* Dimension Lines */}
        <line x1="200" y1="180" x2="800" y2="180" className="dimension-line" />
        <line x1="180" y1="200" x2="180" y2="700" className="dimension-line" />
        <line x1="200" y1="720" x2="800" y2="720" className="dimension-line" />
        <line x1="820" y1="200" x2="820" y2="700" className="dimension-line" />
        
        {/* Enhanced Animated Light Points */}
        {/* Light 1: Tracing the outer perimeter */}
        <circle className="light-point light-1" cx="0" cy="0" r="4">
          <animateMotion 
            path="M200,200 L800,200 L800,700 L200,700 L200,200" 
            dur="15s" 
            repeatCount="indefinite"
          />
        </circle>
        
        {/* Light 2: Moving through living areas */}
        <circle className="light-point light-2" cx="0" cy="0" r="4">
          <animateMotion 
            path="M300,200 L300,400 L450,400 L450,200 L600,200 L600,400 L650,400 L650,400" 
            dur="12s" 
            repeatCount="indefinite"
          />
        </circle>
        
        {/* Light 3: Following bedroom path */}
        <circle className="light-point light-3" cx="0" cy="0" r="3.5">
          <animateMotion 
            path="M200,550 L450,550 L450,700 L550,700 L550,550 L800,550" 
            dur="18s" 
            repeatCount="indefinite"
          />
        </circle>
        
        {/* Light 4: Moving through doors */}
        <circle className="light-point light-4" cx="0" cy="0" r="3">
          <animateMotion 
            path="M400,220 L450,270 L600,270 L300,400 L400,465 L650,475 L675,525 L450,615" 
            dur="20s" 
            repeatCount="indefinite"
          />
        </circle>
        
        {/* Light 5: Window path */}
        <circle className="light-point light-5" cx="0" cy="0" r="3">
          <animateMotion 
            path="M250,200 L350,200 L500,200 L550,200 L650,200 L750,200 L800,250 L800,350 L800,600 L800,650 L700,700 L600,700 L350,700 L250,700" 
            dur="25s" 
            repeatCount="indefinite"
          />
        </circle>
      </svg>
      
      {/* Add enhanced CSS for the background */}
      <style jsx>{`
        .architectural-blueprint {
          filter: blur(0.5px);
          transition: transform 0.3s ease, opacity 0.3s ease;
        }
        
        .architectural-tool {
          opacity: 0.6;
          transition: all 0.5s ease;
          transform-origin: center;
        }
        
        .architectural-tool:hover {
          opacity: 0.9;
          filter: drop-shadow(0 0 10px rgb(var(--color-primary)));
        }
        
        .tool-body {
          fill: rgba(var(--color-primary), 0.1);
          stroke: rgb(var(--color-primary));
          stroke-width: 1.5;
          stroke-opacity: 0.6;
        }
        
        .tool-edge {
          stroke: rgb(var(--color-primary));
          stroke-width: 2;
          stroke-opacity: 0.7;
        }
        
        .tool-mark {
          stroke: rgb(var(--color-primary));
          stroke-width: 1;
          stroke-opacity: 0.7;
        }
        
        .tool-mark-major {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.2;
          stroke-opacity: 0.85;
        }
        
        .tool-text {
          fill: rgb(var(--color-primary));
          font-size: 8px;
          font-family: monospace;
          opacity: 0.7;
        }
        
        .tool-hole {
          fill: rgba(var(--color-background), 0.8);
          stroke: rgb(var(--color-primary));
          stroke-width: 1;
          stroke-opacity: 0.7;
        }
        
        .tool-compass-arc {
          stroke: rgb(var(--color-primary));
          stroke-width: 1;
          stroke-opacity: 0.6;
          stroke-dasharray: 5, 3;
        }
        
        .tool-compass-leg {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.5;
          stroke-opacity: 0.7;
        }
        
        .tool-compass-body {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.5;
          stroke-opacity: 0.7;
        }
        
        .tool-compass-joint {
          fill: rgb(var(--color-primary));
          opacity: 0.8;
        }
        
        .tool-compass-point {
          fill: rgb(var(--color-secondary));
          opacity: 0.9;
        }
        
        .tool-protractor-arc {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.5;
          stroke-opacity: 0.7;
          stroke-dasharray: 2, 2;
        }
        
        .tool-detail {
          stroke: rgb(var(--color-primary));
          stroke-opacity: 0.4;
        }
        
        .tool-edge-highlight {
          stroke: rgba(255, 255, 255, 0.3);
          stroke-width: 1;
        }
        
        .tool-angle-marker {
          stroke: rgb(var(--color-primary));
          stroke-opacity: 0.7;
          stroke-width: 0.8;
        }
        
        .tool-text-subtle {
          fill: rgb(var(--color-primary));
          opacity: 0.35;
          font-family: monospace;
        }
        
        .tool-mark-dot {
          fill: rgb(var(--color-primary));
          opacity: 0.75;
        }
        
        .measurement-line {
          animation: pulseMedium 5s infinite ease-in-out;
        }
        
        /* Animate tools subtly */
        @keyframes floatTool {
          0% { transform: translateY(0px); }
          50% { transform: translateY(-5px); }
          100% { transform: translateY(0px); }
        }
        
        .ruler-tool {
          animation: floatTool 8s infinite ease-in-out;
        }
        
        .set-square-tool {
          animation: floatTool 10s infinite ease-in-out;
        }
        
        .t-square-tool {
          animation: floatTool 7s infinite ease-in-out;
        }
        
        .compass-tool {
          animation: floatTool 9s infinite ease-in-out;
        }
        
        .protractor-tool {
          animation: floatTool 6s infinite ease-in-out;
        }
        
        @keyframes glowEffect {
          0% { filter: blur(3px) drop-shadow(0 0 10px rgb(var(--color-primary))); opacity: 0.8; }
          50% { filter: blur(6px) drop-shadow(0 0 15px rgb(var(--color-primary))); opacity: 1; }
          100% { filter: blur(3px) drop-shadow(0 0 10px rgb(var(--color-primary))); opacity: 0.8; }
        }
        
        @keyframes glowEffectSecondary {
          0% { filter: blur(3px) drop-shadow(0 0 10px rgb(var(--color-secondary))); opacity: 0.7; }
          50% { filter: blur(6px) drop-shadow(0 0 18px rgb(var(--color-secondary))); opacity: 0.9; }
          100% { filter: blur(3px) drop-shadow(0 0 10px rgb(var(--color-secondary))); opacity: 0.7; }
        }
        
        @keyframes pulseThin {
          0% { stroke-opacity: 0.12; }
          50% { stroke-opacity: 0.18; }
          100% { stroke-opacity: 0.12; }
        }
        
        @keyframes pulseMedium {
          0% { stroke-opacity: 0.18; }
          50% { stroke-opacity: 0.28; }
          100% { stroke-opacity: 0.18; }
        }
        
        @keyframes pulseThick {
          0% { stroke-opacity: 0.3; }
          50% { stroke-opacity: 0.45; }
          100% { stroke-opacity: 0.3; }
        }
        
        @keyframes pulseFill {
          0% { fill-opacity: 0.04; }
          50% { fill-opacity: 0.08; }
          100% { fill-opacity: 0.04; }
        }
        
        .grid-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 0.5;
          stroke-opacity: 0.12;
          animation: pulseThin 8s infinite ease-in-out;
        }
        
        .house-outline {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 2.5;
          stroke-opacity: 0.35;
          animation: pulseThick 10s infinite ease-in-out;
        }
        
        .room-outline {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 1.8;
          stroke-opacity: 0.3;
          animation: pulseThick 12s infinite ease-in-out;
        }
        
        .room-label {
          fill: rgb(var(--color-primary));
          font-size: 12px;
          font-family: monospace;
          text-anchor: middle;
          opacity: 0.25;
          animation: pulseMedium 10s infinite ease-in-out;
        }
        
        .door {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 1.4;
          stroke-opacity: 0.28;
          animation: pulseMedium 12s infinite ease-in-out;
        }
        
        .door-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.4;
          stroke-opacity: 0.25;
          animation: pulseMedium 12s infinite ease-in-out;
        }
        
        .window-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 2.5;
          stroke-opacity: 0.35;
          animation: pulseThick 10s infinite ease-in-out;
        }
        
        .dimension-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 0.8;
          stroke-opacity: 0.18;
          stroke-dasharray: 5, 3;
          animation: pulseMedium 18s infinite ease-in-out;
        }
        
        .furniture {
          fill: rgb(var(--color-primary));
          fill-opacity: 0.1;
          stroke: rgb(var(--color-primary));
          stroke-width: 0.8;
          stroke-opacity: 0.22;
          animation: pulseFill 14s infinite ease-in-out, pulseMedium 14s infinite ease-in-out;
        }
        
        .bathroom-fixture {
          fill: rgb(var(--color-primary));
          fill-opacity: 0.12;
          stroke: rgb(var(--color-primary));
          stroke-width: 1;
          stroke-opacity: 0.22;
          animation: pulseFill 10s infinite ease-in-out;
        }
        
        .light-point {
          fill: #fff;
          filter: blur(2px);
          opacity: 0.9;
        }
        
        .light-1 {
          fill: rgb(var(--color-primary));
          filter: blur(5px) drop-shadow(0 0 12px rgb(var(--color-primary)));
          animation: glowEffect 3s infinite;
        }
        
        .light-2 {
          fill: rgb(var(--color-secondary));
          filter: blur(5px) drop-shadow(0 0 12px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 4s infinite;
        }
        
        .light-3 {
          fill: rgb(var(--color-primary));
          filter: blur(4px) drop-shadow(0 0 10px rgb(var(--color-primary)));
          animation: glowEffect 4s infinite;
        }
        
        .light-4 {
          fill: rgb(var(--color-primary));
          filter: blur(4px) drop-shadow(0 0 10px rgb(var(--color-primary)));
          animation: glowEffect 4s infinite;
        }
        
        .light-5 {
          fill: rgb(var(--color-secondary));
          filter: blur(4px) drop-shadow(0 0 10px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 3.5s infinite;
        }
        
        /* Media queries for responsive adjustments */
        @media (max-width: 768px) {
          .room-label {
            font-size: 10px;
          }
          
          .architectural-tool {
            transform: scale(0.7);
          }
        }
      `}</style>
    </div>
  );
};

export default ArchitecturalBackground; 