import React, { useState, useRef } from 'react';
import { Transforms } from 'slate';
import { useSlate } from 'slate-react';
import { FiImage, FiFile, FiVideo, FiUpload, FiLink } from 'react-icons/fi';

interface FileUploadToolbarProps {
  icon: React.ReactNode;
}

const FileUploadToolbar = ({ icon }: FileUploadToolbarProps) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadType, setUploadType] = useState<'image' | 'video' | 'file'>('image');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // In a real app, you would upload the file to your server/storage
    // and then use the returned URL
    const reader = new FileReader();
    reader.onload = (event) => {
      const url = event.target?.result as string;
      
      if (uploadType === 'image') {
        insertImage(url, file.name);
      } else if (uploadType === 'video') {
        insertVideo(url, file.name);
      } else {
        insertFile(url, file.name);
      }
    };
    
    if (uploadType === 'image' || uploadType === 'video') {
      reader.readAsDataURL(file);
    } else {
      // For files, we would normally get a URL from the server
      // For this demo, we'll just use the filename
      insertFile('#', file.name);
    }
  };

  const insertImage = (url: string, alt: string = '') => {
    const image = {
      type: 'image',
      url,
      alt,
      children: [{ text: '' }]
    };
    
    Transforms.insertNodes(editor, image);
  };

  const insertVideo = (url: string, caption: string = '') => {
    const video = {
      type: 'video',
      url,
      caption,
      children: [{ text: '' }]
    };
    
    Transforms.insertNodes(editor, video);
  };

  const insertFile = (url: string, filename: string = '') => {
    const file = {
      type: 'file-embed',
      url,
      filename,
      children: [{ text: filename || 'Download File' }]
    };
    
    Transforms.insertNodes(editor, file);
  };

  const handleImageUrlInput = () => {
    const url = prompt('Enter image URL:');
    if (url) {
      insertImage(url);
    }
  };

  const handleVideoUrlInput = () => {
    const url = prompt('Enter video URL (YouTube, Vimeo, etc.):');
    if (url) {
      insertVideo(url);
    }
  };

  const triggerFileUpload = (type: 'image' | 'video' | 'file') => {
    setUploadType(type);
    if (fileInputRef.current) {
      // Set accept attribute based on type
      if (type === 'image') {
        fileInputRef.current.accept = 'image/*';
      } else if (type === 'video') {
        fileInputRef.current.accept = 'video/*';
      } else {
        fileInputRef.current.accept = '*/*';
      }
      fileInputRef.current.click();
    }
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
      />
      
      {showOptions && (
        <div className="absolute top-full left-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[180px]">
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              triggerFileUpload('image');
              setShowOptions(false);
            }}
          >
            <FiUpload size={16} />
            <span>Upload Image</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleImageUrlInput();
              setShowOptions(false);
            }}
          >
            <FiLink size={16} />
            <span>Image from URL</span>
          </button>
          
          <div className="border-t border-white/10 my-1"></div>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              triggerFileUpload('video');
              setShowOptions(false);
            }}
          >
            <FiVideo size={16} />
            <span>Upload Video</span>
          </button>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              handleVideoUrlInput();
              setShowOptions(false);
            }}
          >
            <FiLink size={16} />
            <span>Video from URL</span>
          </button>
          
          <div className="border-t border-white/10 my-1"></div>
          
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              triggerFileUpload('file');
              setShowOptions(false);
            }}
          >
            <FiFile size={16} />
            <span>Upload File</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default FileUploadToolbar; 