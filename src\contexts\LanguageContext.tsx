"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import { clearAllLanguageCaches, preloadLanguageResources, generateForceUpdateKey } from '@/utils/cache';

type LanguageContextType = {
  locale: string;
  changeLanguage: (newLocale: string) => void;
  isRTL: boolean;
  isChangingLanguage: boolean;
  forceUpdateKey: string;
  clearCacheAndChangeLanguage: (newLocale: string) => Promise<void>;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentLocale, setCurrentLocale] = useState('en');
  const [isChangingLanguage, setIsChangingLanguage] = useState(false);
  const [forceUpdateKey, setForceUpdateKey] = useState(generateForceUpdateKey());
  const isRTL = currentLocale === 'ar';
  const pathname = usePathname();

  // Initialize locale from URL path on first load
  useEffect(() => {
    if (pathname) {
      const segments = pathname.split('/');
      if (segments.length > 1) {
        const urlLocale = segments[1];
        if (urlLocale === 'en' || urlLocale === 'ar') {
          setCurrentLocale(urlLocale);
        }
      }
    }
  }, [pathname]);

  // Update document direction and language whenever locale changes
  useEffect(() => {
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLocale;
    
    // Save to localStorage as a fallback
    localStorage.setItem('locale', currentLocale);
    
    // Force update components when language changes
    setForceUpdateKey(generateForceUpdateKey());
    
    console.log(`🌐 Language changed to: ${currentLocale} (RTL: ${isRTL})`);
  }, [currentLocale, isRTL]);

  const changeLanguage = (newLocale: string) => {
    if (newLocale === 'en' || newLocale === 'ar') {
      setCurrentLocale(newLocale);
    }
  };

  // Enhanced language change with cache clearing
  const clearCacheAndChangeLanguage = async (newLocale: string): Promise<void> => {
    if (newLocale !== 'en' && newLocale !== 'ar') {
      console.error('❌ Invalid locale:', newLocale);
      return;
    }

    if (newLocale === currentLocale) {
      console.log('ℹ️ Same language selected, no change needed');
      return;
    }

    console.log(`🔄 Starting language change from ${currentLocale} to ${newLocale}...`);
    setIsChangingLanguage(true);

    try {
      // 1. Clear all caches first
      await clearAllLanguageCaches();
      
      // 2. Preload resources for new language (optional optimization)
      preloadLanguageResources(newLocale);
      
      // 3. Small delay to ensure cache clearing is complete
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 4. Change the language
      setCurrentLocale(newLocale);
      
      console.log(`✅ Language change completed: ${currentLocale} → ${newLocale}`);
    } catch (error) {
      console.error('❌ Error during language change:', error);
    } finally {
      // Always reset the changing state
      setTimeout(() => {
        setIsChangingLanguage(false);
      }, 300);
    }
  };

  return (
    <LanguageContext.Provider value={{ 
      locale: currentLocale, 
      changeLanguage, 
      isRTL, 
      isChangingLanguage,
      forceUpdateKey,
      clearCacheAndChangeLanguage 
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}; 