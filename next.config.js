/** @type {import('next').NextConfig} */
const nextConfig = {
  // Environment variables
  env: {
    DJANGO_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000',
  },
  
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'http',
        hostname: '127.0.0.1',
        port: '8000',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      // Add other patterns as needed for external images
    ],
    // Remove deprecated domains configuration
    // Configure image optimization
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    // Add image loader to handle errors better
    loader: 'default',
    // Disable image optimization for problematic images
    unoptimized: false,
    // Add formats for better compatibility
    formats: ['image/webp', 'image/avif'],
  },
  
  // API rewrites for development
  async rewrites() {
    return [
      {
        source: '/api/django/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000'}/api/:path*`,
      },
      {
        source: '/media/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000'}/media/:path*`,
      },
    ];
  },
  
  // Cache control headers for language switching
  async headers() {
    return [
      {
        source: '/(.*)',
        locale: false,
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, s-maxage=0, must-revalidate',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ];
  },
  
  experimental: {
    serverActions: {
      allowedOrigins: ['localhost:3000', 'localhost:3001', '127.0.0.1:8000', 'localhost:8000'],
    },
  },
  
  // Suppress hydration warnings caused by browser extensions
  reactStrictMode: true,
  compiler: {
    // Remove console.logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },
  
  // i18n configuration for App Router
  // Documentation: https://nextjs.org/docs/app/building-your-application/routing/internationalization
};

module.exports = nextConfig;