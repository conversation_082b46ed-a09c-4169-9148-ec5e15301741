"use client";

import React, { useState } from 'react';
import { 
  FiX, FiUser, FiMail, FiPhone, FiCalendar, FiClock, 
  FiEdit, FiTrash2, FiShield, FiMapPin, FiSettings,
  FiEye, FiCheck, FiAlertCircle, FiGlobe, FiBell
} from 'react-icons/fi';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: 'admin' | 'manager' | 'agent' | 'client';
  status: 'active' | 'inactive' | 'suspended' | 'pending';
  avatar?: string;
  department?: string;
  position?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
  permissions: string[];
  notes?: string;
  address?: {
    street: string;
    city: string;
    country: string;
    zipCode: string;
  };
  preferences: {
    language: 'en' | 'ar';
    timezone: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
}

interface UserDetailModalProps {
  user: User | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (user: User) => void;
  onDelete: (id: string) => void;
  onStatusChange: (id: string, status: User['status']) => void;
}

export default function UserDetailModal({
  user,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onStatusChange
}: UserDetailModalProps) {
  const [activeTab, setActiveTab] = useState<'details' | 'permissions' | 'activity' | 'preferences'>('details');

  if (!isOpen || !user) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(date);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'manager': return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'agent': return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'client': return 'text-purple-400 bg-purple-400/10 border-purple-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'inactive': return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
      case 'suspended': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'pending': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const handleStatusChange = (newStatus: User['status']) => {
    onStatusChange(user.id, newStatus);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={`${user.firstName} ${user.lastName}`}
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <div className="w-16 h-16 rounded-full bg-gray-600 flex items-center justify-center">
                  <FiUser className="h-8 w-8 text-gray-400" />
                </div>
              )}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white">
                {user.firstName} {user.lastName}
              </h2>
              <p className="text-gray-400">{user.email}</p>
              <div className="flex items-center space-x-2 mt-2">
                <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getRoleColor(user.role)}`}>
                  {user.role.toUpperCase()}
                </span>
                <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(user.status)}`}>
                  {user.status.toUpperCase()}
                </span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'details', label: 'Details', icon: FiUser },
              { id: 'permissions', label: 'Permissions', icon: FiShield },
              { id: 'activity', label: 'Activity', icon: FiClock },
              { id: 'preferences', label: 'Preferences', icon: FiSettings }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as typeof activeTab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-[#00C2FF] text-[#00C2FF]'
                      : 'border-transparent text-gray-400 hover:text-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="p-6">
            {activeTab === 'details' && (
              <div className="space-y-6">
                {/* Personal Information */}
                <div className="bg-gray-700/30 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <FiUser className="mr-2 h-5 w-5 text-[#00C2FF]" />
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-gray-400">First Name</p>
                        <p className="text-white font-medium">{user.firstName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">Last Name</p>
                        <p className="text-white font-medium">{user.lastName}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1">
                          <p className="text-sm text-gray-400">Email Address</p>
                          <a 
                            href={`mailto:${user.email}`}
                            className="text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
                          >
                            {user.email}
                          </a>
                        </div>
                        {user.isEmailVerified ? (
                          <FiCheck className="h-4 w-4 text-green-400" title="Email verified" />
                        ) : (
                          <FiAlertCircle className="h-4 w-4 text-yellow-400" title="Email not verified" />
                        )}
                      </div>
                    </div>
                    <div className="space-y-3">
                      {user.phone && (
                        <div className="flex items-center space-x-2">
                          <div className="flex-1">
                            <p className="text-sm text-gray-400">Phone Number</p>
                            <a 
                              href={`tel:${user.phone}`}
                              className="text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
                            >
                              {user.phone}
                            </a>
                          </div>
                          {user.isPhoneVerified ? (
                            <FiCheck className="h-4 w-4 text-green-400" title="Phone verified" />
                          ) : (
                            <FiAlertCircle className="h-4 w-4 text-yellow-400" title="Phone not verified" />
                          )}
                        </div>
                      )}
                      {user.department && (
                        <div>
                          <p className="text-sm text-gray-400">Department</p>
                          <p className="text-white">{user.department}</p>
                        </div>
                      )}
                      {user.position && (
                        <div>
                          <p className="text-sm text-gray-400">Position</p>
                          <p className="text-white">{user.position}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Address Information */}
                {user.address && (
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                      <FiMapPin className="mr-2 h-5 w-5 text-[#00C2FF]" />
                      Address Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-400">Street</p>
                        <p className="text-white">{user.address.street}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">City</p>
                        <p className="text-white">{user.address.city}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">Country</p>
                        <p className="text-white">{user.address.country}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">Zip Code</p>
                        <p className="text-white">{user.address.zipCode}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Notes */}
                {user.notes && (
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-white mb-4">Notes</h3>
                    <p className="text-gray-300">{user.notes}</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'permissions' && (
              <div className="space-y-6">
                <div className="bg-gray-700/30 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <FiShield className="mr-2 h-5 w-5 text-[#00C2FF]" />
                    User Permissions
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-400 mb-2">Role</p>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getRoleColor(user.role)}`}>
                        {user.role.toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400 mb-2">Permissions</p>
                      <div className="flex flex-wrap gap-2">
                        {user.permissions.map((permission, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gray-600 text-gray-300 rounded text-sm"
                          >
                            {permission}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="space-y-6">
                <div className="bg-gray-700/30 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <FiClock className="mr-2 h-5 w-5 text-[#00C2FF]" />
                    Activity Timeline
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <FiCalendar className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-400">Account Created</p>
                        <p className="text-white">{formatDate(user.createdAt)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <FiEdit className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-400">Last Updated</p>
                        <p className="text-white">{formatDate(user.updatedAt)}</p>
                      </div>
                    </div>
                    {user.lastLogin && (
                      <div className="flex items-center space-x-3">
                        <FiEye className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm text-gray-400">Last Login</p>
                          <p className="text-white">{formatDate(user.lastLogin)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'preferences' && (
              <div className="space-y-6">
                <div className="bg-gray-700/30 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <FiGlobe className="mr-2 h-5 w-5 text-[#00C2FF]" />
                    Language & Region
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-400">Language</p>
                      <p className="text-white">{user.preferences.language === 'en' ? 'English' : 'Arabic'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-400">Timezone</p>
                      <p className="text-white">{user.preferences.timezone}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-700/30 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                    <FiBell className="mr-2 h-5 w-5 text-[#00C2FF]" />
                    Notification Preferences
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Email Notifications</span>
                      <span className={`px-2 py-1 rounded text-sm ${
                        user.preferences.notifications.email 
                          ? 'bg-green-400/10 text-green-400' 
                          : 'bg-red-400/10 text-red-400'
                      }`}>
                        {user.preferences.notifications.email ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">SMS Notifications</span>
                      <span className={`px-2 py-1 rounded text-sm ${
                        user.preferences.notifications.sms 
                          ? 'bg-green-400/10 text-green-400' 
                          : 'bg-red-400/10 text-red-400'
                      }`}>
                        {user.preferences.notifications.sms ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-300">Push Notifications</span>
                      <span className={`px-2 py-1 rounded text-sm ${
                        user.preferences.notifications.push 
                          ? 'bg-green-400/10 text-green-400' 
                          : 'bg-red-400/10 text-red-400'
                      }`}>
                        {user.preferences.notifications.push ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700 bg-gray-800/50">
          <div className="flex items-center space-x-3">
            <div className="relative group">
              <select
                value={user.status}
                onChange={(e) => handleStatusChange(e.target.value as User['status'])}
                className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="suspended">Suspended</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => onEdit(user)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors flex items-center"
            >
              <FiEdit className="mr-2 h-4 w-4" />
              Edit User
            </button>
            
            <button
              onClick={() => {
                if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                  onDelete(user.id);
                  onClose();
                }
              }}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors flex items-center"
            >
              <FiTrash2 className="mr-2 h-4 w-4" />
              Delete
            </button>
            
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 