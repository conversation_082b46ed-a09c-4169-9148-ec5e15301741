"use client";

import React, { useState, useEffect } from 'react';
import { Inter } from "next/font/google";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { 
  FiMail, FiArrowLeft, FiCheck, FiClock, FiAlertCircle, FiHome 
} from 'react-icons/fi';
import NoSSR from "@/components/NoSSR";

// Configure the Inter font
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
});

interface FormData {
  email: string;
}

interface FormErrors {
  email?: string;
  general?: string;
}

function ForgotPasswordPageContent() {
  const router = useRouter();
  const { requestPasswordReset, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    email: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (isMounted && isAuthenticated) {
      router.push('/en/admin');
    }
  }, [isMounted, isAuthenticated, router]);

  // Countdown timer for resend functionality
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear field error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      const result = await requestPasswordReset(formData.email);

      if (result.success) {
        setIsSubmitted(true);
        setCountdown(60); // 60 second countdown for resend
      } else {
        setErrors({
          general: result.message || 'Failed to send reset email. Please try again.'
        });
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setErrors({
        general: 'An error occurred. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    if (countdown > 0) return;

    setIsLoading(true);
    try {
      const result = await requestPasswordReset(formData.email);
      if (result.success) {
        setCountdown(60);
      }
    } catch (error) {
      console.error('Resend error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isMounted) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${inter.className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${inter.className}`}>
        <div className="max-w-md w-full space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="h-20 w-20 relative">
                <Image
                  src="/images/logo/mazaya-logo-mark.svg"
                  alt="Mazaya Capital Logo"
                  className="w-full h-full object-cover"
                  width={80}
                  height={80}
                  priority
                />
              </div>
            </div>
            <h2 className="text-3xl font-bold text-white">Check Your Email</h2>
            <p className="mt-2 text-gray-400">We've sent password reset instructions to your email</p>
          </div>

          {/* Success Message */}
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 shadow-xl">
            <div className="text-center space-y-6">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-900/20">
                <FiCheck className="h-8 w-8 text-green-400" />
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-white mb-2">Email Sent Successfully</h3>
                <p className="text-gray-400 text-sm mb-4">
                  We've sent a password reset link to <strong className="text-white">{formData.email}</strong>
                </p>
                <div className="bg-blue-900/20 border border-blue-600/30 rounded-lg p-4 mb-6">
                  <div className="flex items-start">
                    <FiClock className="h-5 w-5 text-blue-400 mr-3 mt-0.5" />
                    <div className="text-sm text-blue-300">
                      <p className="font-medium mb-1">Important Security Notice:</p>
                      <ul className="space-y-1 text-blue-200">
                        <li>• The reset link expires in 1 hour</li>
                        <li>• The link can only be used once</li>
                        <li>• Check your spam folder if you don't see the email</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <button
                  onClick={handleResend}
                  disabled={countdown > 0 || isLoading}
                  className={`w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg transition-all duration-200 ${
                    countdown > 0 || isLoading
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-[#00C2FF] text-white hover:bg-[#00C2FF]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] focus:ring-offset-gray-800'
                  }`}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Sending...
                    </div>
                  ) : countdown > 0 ? (
                    `Resend in ${countdown}s`
                  ) : (
                    'Resend Email'
                  )}
                </button>

                <Link
                  href="/en/login"
                  className="w-full flex justify-center py-3 px-4 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 focus:ring-offset-gray-800 transition-all duration-200"
                >
                  <FiArrowLeft className="mr-2 h-4 w-4" />
                  Back to Login
                </Link>
              </div>
            </div>
          </div>

          {/* Back to website */}
          <div className="text-center">
            <Link 
              href="/"
              className="inline-flex items-center text-sm text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
            >
              <FiHome className="mr-1 h-4 w-4" />
              Back to Website
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 ${inter.className}`}>
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="h-20 w-20 relative">
              <Image
                src="/images/logo/mazaya-logo-mark.svg"
                alt="Mazaya Capital Logo"
                className="w-full h-full object-cover"
                width={80}
                height={80}
                priority
              />
            </div>
          </div>
          <h2 className="text-3xl font-bold text-white">Reset Password</h2>
          <p className="mt-2 text-gray-400">Enter your email to receive reset instructions</p>
        </div>

        {/* Reset Form */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 shadow-xl">
          {/* General Error Message */}
          {errors.general && (
            <div className="mb-4 p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
              <div className="flex items-center">
                <FiAlertCircle className="h-5 w-5 text-red-400 mr-3" />
                <p className="text-red-400 text-sm">{errors.general}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-400 mb-2">
                Email Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-500" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`block w-full pl-10 pr-3 py-3 border rounded-lg bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors ${
                    errors.email 
                      ? 'border-red-500 focus:ring-red-500' 
                      : 'border-gray-600 focus:ring-[#00C2FF] focus:border-[#00C2FF]'
                  }`}
                  placeholder="Enter your email address"
                />
              </div>
              {errors.email && (
                <p className="mt-2 text-sm text-red-400 flex items-center">
                  <FiAlertCircle className="h-4 w-4 mr-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading || !formData.email}
              className={`group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white transition-all duration-200 ${
                isLoading || !formData.email
                  ? 'bg-gray-600 cursor-not-allowed'
                  : 'bg-[#00C2FF] hover:bg-[#00C2FF]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] focus:ring-offset-gray-800'
              }`}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending Reset Link...
                </div>
              ) : (
                <div className="flex items-center">
                  <FiMail className="mr-2 h-4 w-4" />
                  Send Reset Link
                </div>
              )}
            </button>

            {/* Back to Login */}
            <div className="text-center">
              <Link
                href="/en/login"
                className="inline-flex items-center text-sm text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
              >
                <FiArrowLeft className="mr-1 h-4 w-4" />
                Back to Login
              </Link>
            </div>
          </form>
        </div>

        {/* Back to website */}
        <div className="text-center">
          <Link 
            href="/"
            className="inline-flex items-center text-sm text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
          >
            <FiHome className="mr-1 h-4 w-4" />
            Back to Website
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function ForgotPasswordPage() {
  return (
    <NoSSR>
      <ForgotPasswordPageContent />
    </NoSSR>
  );
} 