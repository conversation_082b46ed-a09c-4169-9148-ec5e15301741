// Toast utility functions for common patterns

export const toastMessages = {
  success: {
    avatarUpdated: 'Avatar updated successfully!',
    profileUpdated: 'Profile updated successfully!',
    settingsSaved: 'Settings saved successfully!',
    dataSaved: 'Data saved successfully!',
  },
  error: {
    uploadFailed: 'Upload failed. Please try again.',
    saveFailed: 'Failed to save changes.',
    networkError: 'Network error occurred. Please check your connection.',
    invalidFile: 'Please select a valid file.',
    fileTooLarge: 'File size is too large.',
    authRequired: 'Authentication required.',
    permissionDenied: 'Permission denied.',
  },
  info: {
    processing: 'Processing your request...',
    uploading: 'Uploading file...',
    saving: 'Saving changes...',
  },
  warning: {
    unsavedChanges: 'You have unsaved changes.',
    sessionExpiring: 'Your session is about to expire.',
  }
};

// Helper functions for common toast patterns
export const createToast = {
  success: (title: string, message?: string) => ({ type: 'success' as const, title, message }),
  error: (title: string, message?: string) => ({ type: 'error' as const, title, message }),
  info: (title: string, message?: string) => ({ type: 'info' as const, title, message }),
  warning: (title: string, message?: string) => ({ type: 'warning' as const, title, message }),
}; 