import { Metadata } from "next";
import { use } from "react";
import Hero from "@/components/home/<USER>";
import FeaturedProjects from "@/components/home/<USER>";
import CompanyIntro from "@/components/home/<USER>";
import Statistics from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import LatestArticles from "@/components/home/<USER>";
import { LangParams } from "./params";

export async function generateMetadata({ 
  params 
}: LangParams): Promise<Metadata> {
  // No need to use React.use() in server components
  return {
    title: "Mazaya Capital - Premium Real Estate Development & Investment",
    description: "Mazaya Capital is a leading real estate development and investment company providing premium properties and investment opportunities.",
  };
}

export default function Home({ params }: LangParams) {
  // In server components, we don't need React.use() on the server,
  // but we're leaving this code here for consistency with the client component
  // const resolvedParams = use(params as unknown as Promise<LangParams["params"]>);
  return (
    <div className="flex flex-col">
      <Hero />
      <CompanyIntro />
      <FeaturedProjects />
      <Statistics />
      <LatestArticles />
      <Testimonials />
    </div>
  );
} 