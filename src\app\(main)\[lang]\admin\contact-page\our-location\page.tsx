"use client";

import { useState } from 'react';
import { <PERSON>Save, <PERSON>Eye, FiMapPin, FiExternalLink, FiEdit } from 'react-icons/fi';

interface LocationData {
  sectionTitle: {
    english: string;
    arabic: string;
  };
  mapSettings: {
    embedUrl: string;
    width: string;
    height: string;
    allowFullscreen: boolean;
    loading: 'lazy' | 'eager';
  };
  locationInfo: {
    name: {
      english: string;
      arabic: string;
    };
    city: {
      english: string;
      arabic: string;
    };
  };
  buttonSettings: {
    text: {
      english: string;
      arabic: string;
    };
    enabled: boolean;
    openInNewTab: boolean;
  };
}

export default function OurLocationPage() {
  const [locationData, setLocationData] = useState<LocationData>({
    sectionTitle: {
      english: "Our Locations",
      arabic: "مواقعنا"
    },
    mapSettings: {
      embedUrl: "https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d3443.130260114471!2d30.926833999999996!3d29.961240999999998!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMjnCsDU3JzQwLjUiTiAzMMKwNTUnMzYuNiJF!5e1!3m2!1sar!2seg!4v1746798794569!5m2!1sar!2seg",
      width: "100%",
      height: "500px",
      allowFullscreen: true,
      loading: "lazy"
    },
    locationInfo: {
      name: {
        english: "Mazaya Capital Egypt",
        arabic: "مزايا كابيتال مصر"
      },
      city: {
        english: "Cairo, Egypt",
        arabic: "القاهرة، مصر"
      },
    },
    buttonSettings: {
      text: {
        english: "Open in Google Maps",
        arabic: "فتح في خرائط جوجل"
      },
      enabled: true,
      openInNewTab: true
    }
  });

  const [showPreview, setShowPreview] = useState(false);

  // Handle input changes
  const handleInputChange = (section: keyof LocationData, field: string, value: string | boolean, language?: 'english' | 'arabic') => {
    setLocationData(prev => {
      const newData = { ...prev };
      if (language) {
        (newData[section] as any)[field][language] = value;
      } else {
        (newData[section] as any)[field] = value;
      }
      return newData;
    });
  };

  // Handle save
  const handleSave = () => {
    console.log('Saving location data:', locationData);
    alert('Location content saved successfully!');
  };

  // Generate Google Maps URL - generic since coordinates are removed
  const generateMapsUrl = () => {
    return "https://www.google.com/maps";
  };

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Our Location Section</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage the location map and information for the contact page
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            {showPreview ? 'Hide Preview' : 'Show Preview'}
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Save Changes
          </button>
        </div>
      </div>

      {/* Preview Section */}
      {showPreview && (
        <div className="mt-6 bg-gray-700 rounded-lg p-6 border border-gray-600">
          <h3 className="text-lg font-medium text-white mb-4">Preview</h3>
          <div className="bg-gray-800 p-6 rounded-lg">
            <div className="container mx-auto">
              <div className="flex items-center mb-8">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white mr-4 shadow-lg">
                  <FiMapPin className="h-5 w-5" />
                </div>
                <h2 className="text-3xl font-bold text-white">{locationData.sectionTitle.english}</h2>
              </div>
              <div className="rounded-xl overflow-hidden shadow-xl border border-gray-600">
                <div className="w-full overflow-hidden">
                  <div className="relative" style={{ height: locationData.mapSettings.height }}>
                    <iframe
                      src={locationData.mapSettings.embedUrl}
                      width={locationData.mapSettings.width}
                      height="100%"
                      allowFullScreen={locationData.mapSettings.allowFullscreen}
                      loading={locationData.mapSettings.loading}
                      referrerPolicy="no-referrer-when-downgrade"
                      className="absolute inset-0"
                      style={{ border: 0 }}
                    ></iframe>
                  </div>
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                    <div className="container mx-auto">
                      <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex items-center">
                          <div className="me-4">
                            <FiMapPin className="h-10 w-10" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold">{locationData.locationInfo.name.english}</h3>
                            <p className="text-white/80">{locationData.locationInfo.city.english}</p>
                          </div>
                        </div>
                        {locationData.buttonSettings.enabled && (
                          <button className="mt-4 md:mt-0 bg-white text-blue-600 hover:bg-gray-100 px-6 py-3 rounded-lg font-medium transition-colors duration-300 shadow-lg flex items-center">
                            <FiExternalLink className="h-5 w-5 me-2" />
                            {locationData.buttonSettings.text.english}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 space-y-6">
        {/* Section Title */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <FiEdit className="mr-2 h-5 w-5 text-blue-400" />
            Section Title
          </h3>
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-300">Title (English)</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={locationData.sectionTitle.english}
                onChange={e => handleInputChange('sectionTitle', 'english', e.target.value, 'english')}
                placeholder="Our Locations"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300">Title (Arabic)</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={locationData.sectionTitle.arabic}
                onChange={e => handleInputChange('sectionTitle', 'arabic', e.target.value, 'arabic')}
                placeholder="مواقعنا"
              />
            </div>
          </div>
        </div>

        {/* Map Settings */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <FiMapPin className="mr-2 h-5 w-5 text-green-400" />
            Map Settings
          </h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">Google Maps Embed URL</label>
              <textarea
                rows={3}
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={locationData.mapSettings.embedUrl}
                onChange={e => handleInputChange('mapSettings', 'embedUrl', e.target.value)}
                placeholder="https://www.google.com/maps/embed?pb=..."
              />
              <p className="mt-1 text-xs text-gray-400">
                Get this URL from Google Maps → Share → Embed a map → Copy HTML
              </p>
            </div>
            
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
              <div>
                <label className="block text-sm font-medium text-gray-300">Map Width</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.mapSettings.width}
                  onChange={e => handleInputChange('mapSettings', 'width', e.target.value)}
                  placeholder="100%"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">Map Height</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.mapSettings.height}
                  onChange={e => handleInputChange('mapSettings', 'height', e.target.value)}
                  placeholder="500px"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">Loading</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.mapSettings.loading}
                  onChange={e => handleInputChange('mapSettings', 'loading', e.target.value as 'lazy' | 'eager')}
                >
                  <option value="lazy">Lazy</option>
                  <option value="eager">Eager</option>
                </select>
              </div>
            </div>

            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                  checked={locationData.mapSettings.allowFullscreen}
                  onChange={e => handleInputChange('mapSettings', 'allowFullscreen', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-300">Allow Fullscreen</span>
              </label>
            </div>
          </div>
        </div>

        {/* Location Information */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <FiMapPin className="mr-2 h-5 w-5 text-orange-400" />
            Location Information
          </h3>
          
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-6">
            {/* English Location Info */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-blue-400">English</h4>
              <div>
                <label className="block text-sm font-medium text-gray-300">Location Name</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.locationInfo.name.english}
                  onChange={e => handleInputChange('locationInfo', 'name', e.target.value, 'english')}
                  placeholder="Mazaya Capital Egypt"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">City</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.locationInfo.city.english}
                  onChange={e => handleInputChange('locationInfo', 'city', e.target.value, 'english')}
                  placeholder="Cairo, Egypt"
                />
              </div>
            </div>

            {/* Arabic Location Info */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-blue-400">العربية</h4>
              <div>
                <label className="block text-sm font-medium text-gray-300">اسم الموقع</label>
                <input
                  type="text"
                  dir="rtl"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.locationInfo.name.arabic}
                  onChange={e => handleInputChange('locationInfo', 'name', e.target.value, 'arabic')}
                  placeholder="مزايا كابيتال مصر"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">المدينة</label>
                <input
                  type="text"
                  dir="rtl"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={locationData.locationInfo.city.arabic}
                  onChange={e => handleInputChange('locationInfo', 'city', e.target.value, 'arabic')}
                  placeholder="القاهرة، مصر"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Button Settings */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4 flex items-center">
            <FiExternalLink className="mr-2 h-5 w-5 text-purple-400" />
            Button Settings
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                  checked={locationData.buttonSettings.enabled}
                  onChange={e => handleInputChange('buttonSettings', 'enabled', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-300">Enable "Open in Google Maps" Button</span>
              </label>
            </div>

            {locationData.buttonSettings.enabled && (
              <>
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Button Text (English)</label>
                    <input
                      type="text"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={locationData.buttonSettings.text.english}
                      onChange={e => handleInputChange('buttonSettings', 'text', e.target.value, 'english')}
                      placeholder="Open in Google Maps"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300">Button Text (Arabic)</label>
                    <input
                      type="text"
                      dir="rtl"
                      className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={locationData.buttonSettings.text.arabic}
                      onChange={e => handleInputChange('buttonSettings', 'text', e.target.value, 'arabic')}
                      placeholder="فتح في خرائط جوجل"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                      checked={locationData.buttonSettings.openInNewTab}
                      onChange={e => handleInputChange('buttonSettings', 'openInNewTab', e.target.checked)}
                    />
                    <span className="ml-2 text-sm text-gray-300">Open in New Tab</span>
                  </label>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-300 mb-2">Google Maps URL:</h5>
                  <div className="bg-gray-600 rounded px-3 py-2 text-sm text-gray-200 break-all">
                    {generateMapsUrl()}
                  </div>
                  <p className="mt-2 text-xs text-gray-400">
                    This will open Google Maps in a new tab.
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 