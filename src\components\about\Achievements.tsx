"use client";

import React, { useEffect, useRef, useState } from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

// Define direct color values instead of CSS variables
const COLORS = {
  primary: "#00C2FF",
  primaryRgb: "0, 194, 255", 
  secondary: "#7B61FF",
  secondaryRgb: "123, 97, 255",
  background: "#0D1526",
  backgroundRgb: "13, 21, 38",
  text: "#FFFFFF",
  textSecondary: "rgba(255, 255, 255, 0.7)"
};

const Achievements = () => {
  const [activeFilter, setActiveFilter] = useState("all");
  const sectionRef = useRef(null);
  const achievementRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [animatedItems, setAnimatedItems] = useState<Set<number>>(new Set());

  // Add language context
  const { locale } = useLanguage();

  // Categories added for filtering
  const categories = [
    { id: "all", label: "All Achievements" },
    { id: "awards", label: "Awards" },
    { id: "business", label: "Business Milestones" },
  ];

  const achievements = [
    {
      year: "2023",
      title: "Developer of the Year",
      description: "Recognized as the Developer of the Year by Real Estate Excellence Awards.",
      category: "awards",
      icon: "trophy"
    },
    {
      year: "2022", 
      title: "Sustainable Development Award",
      description: "Received the Green Building Award for our commitment to sustainable practices.",
      category: "awards",
      icon: "leaf"
    },
    {
      year: "2021",
      title: "Community Impact Award", 
      description: "Honored for our contributions to community development and social responsibility.",
      category: "awards",
      icon: "heart"
    },
    {
      year: "2020",
      title: "Best Luxury Development",
      description: "Our flagship residential project was named Best Luxury Development of the Year.",
      category: "awards", 
      icon: "star"
    },
    {
      year: "2019",
      title: "Innovation in Design",
      description: "Awarded for pioneering innovative architectural design in commercial properties.",
      category: "awards",
      icon: "lightbulb"
    },
    {
      year: "2018",
      title: "Market Expansion",
      description: "Successfully expanded operations into two new regional markets.",
      category: "business",
      icon: "globe"
    },
  ];

  // Effect for initial animation and observer setup
  useEffect(() => {
    // Set visible immediately to avoid blank sections
    setIsVisible(true);
    
    // Initialize refs array based on total achievements (not just filtered ones)
    achievementRefs.current = achievementRefs.current.slice(0, achievements.length);
    
    if (!sectionRef.current) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = achievementRefs.current.findIndex(ref => ref === entry.target);
            if (index !== -1) {
              setAnimatedItems(prev => new Set([...prev, index]));
            }
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px"
      }
    );
    
    observer.observe(sectionRef.current);
    
    // Observe each achievement card
    achievementRefs.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });
    
    return () => {
      observer.disconnect();
    };
  }, []);

  // When filter changes, reset animations for newly visible items
  useEffect(() => {
    // Small delay to let DOM update
    const timer = setTimeout(() => {
      // Find visible items based on current filter
      const visibleItems = achievements
        .map((_, index) => index)
        .filter(index => 
          activeFilter === "all" || 
          achievements[index].category === activeFilter
        );
        
      // Check if any visible items need animation
      visibleItems.forEach(index => {
        const ref = achievementRefs.current[index];
        if (ref && !animatedItems.has(index)) {
          // Add to animated items
          setAnimatedItems(prev => new Set([...prev, index]));
        }
      });
    }, 50);
    
    return () => clearTimeout(timer);
  }, [activeFilter, animatedItems]);

  // Handle filter change
  const handleFilterChange = (filterId: string) => {
    setActiveFilter(filterId);
  };

  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "trophy":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M5.166 2.621v.858c-1.035.148-2.059.33-3.071.543a.75.75 0 0 0-.584.859 6.753 6.753 0 0 0 6.138 5.6 6.73 6.73 0 0 0 2.743 1.346A6.707 6.707 0 0 1 9.279 15H8.54c-1.036 0-1.875.84-1.875 1.875V19.5h-.75a2.25 2.25 0 0 0-2.25 2.25c0 .414.336.75.75.75h15a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-2.25-2.25h-.75v-2.625c0-1.036-.84-1.875-1.875-1.875h-.739a6.706 6.706 0 0 1-1.112-3.173 6.73 6.73 0 0 0 2.743-1.347 6.753 6.753 0 0 0 6.139-********* 0 0 0-.585-.858 47.077 47.077 0 0 0-3.07-.543V2.62a.75.75 0 0 0-.658-.744 49.22 49.22 0 0 0-6.093-.377c-2.063 0-4.096.128-6.093.377a.75.75 0 0 0-.657.744Zm0 2.629c0 1.196.312 2.32.857 3.294A5.266 5.266 0 0 1 3.16 5.337a45.6 45.6 0 0 1 2.006-.343v.256Zm13.5 0v-.256c.674.1 1.343.214 2.006.343a5.265 5.265 0 0 1-2.863 3.207 6.72 6.72 0 0 0 .857-3.294Z" clipRule="evenodd" />
          </svg>
        );
      case "leaf":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 0 0 3.136 17.3a.75.75 0 0 0 1.187.447c.324-.225.651-.436.981-.632a.75.75 0 0 0 .074-1.25 7.518 7.518 0 0 1-1.22-1.463 7.49 7.49 0 0 1 1.103-2.878c.48.658 1.08 1.253 1.76 1.786a.75.75 0 0 0 1.214-.554 7.493 7.493 0 0 1 2.897-5.461 7.492 7.492 0 0 1 5.273-*********** 0 0 0 .853-.854 7.491 7.491 0 0 1 1.208-5.27 7.489 7.489 0 0 1 5.457-2.898.75.75 0 0 0 .555-1.215 9.635 9.635 0 0 0-1.784-1.76 7.489 7.489 0 0 1 2.876-1.103 7.519 7.519 0 0 1 1.46 ********** 0 0 0 1.253-.076c.196-.328.407-.655.632-.98a.75.75 0 0 0-.448-1.186 9 9 0 0 0-10.95 2.358.75.75 0 0 0 .079 1.151 7.549 7.549 0 0 1 1.72 1.707 9.742 9.742 0 0 0-6.175 3.537Z" clipRule="evenodd" />
          </svg>
        );
      case "heart":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path d="m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z" />
          </svg>
        );
      case "star":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z" clipRule="evenodd" />
          </svg>
        );
      case "lightbulb":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path d="M12 .75a8.25 8.25 0 0 0-4.135 15.39c.686.398 1.115 1.008 1.134 1.623a.75.75 0 0 0 .577.706c.352.083.71.148 1.074.195.323.041.6-.218.6-.544v-4.661a6.75 6.75 0 1 1 1.5 0v4.661c0 .326.277.585.6.544.364-.047.722-.112 1.074-.195a.75.75 0 0 0 .577-.706c.02-.615.448-1.225 1.134-1.623A8.25 8.25 0 0 0 12 .75Z" />
            <path fillRule="evenodd" d="M9.75 15.75a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75V16.5a.75.75 0 0 1 .75-.75h.75ZM15.75 16.5a.75.75 0 0 0-.75-.75h-.75a.75.75 0 0 0-.75.75v2.25c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V16.5Z" clipRule="evenodd" />
          </svg>
        );
      case "globe":
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path d="M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5">
            <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const filteredAchievements = activeFilter === "all" 
    ? achievements 
    : achievements.filter(a => a.category === activeFilter);

  return (
    <section ref={sectionRef} className="py-24 overflow-hidden relative">
      {/* Custom Gradient Background with Lighting Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#08101F] via-[#0D1A2D] to-[#121E32] z-0">
        {/* Lighting effects */}
        <div className="absolute top-0 inset-x-0 w-full h-full">
          {/* Main light source - top right */}
          <div className="absolute -top-40 end-[-5rem] w-[600px] h-[600px] rounded-full bg-[rgba(0,194,255,0.1)] blur-[120px] opacity-60"></div>
          
          {/* Secondary light source - bottom left */}
          <div className="absolute -bottom-80 start-[-10rem] w-[500px] h-[500px] rounded-full bg-[rgba(123,97,255,0.1)] blur-[100px] opacity-50"></div>
          
          {/* Accent light spot */}
          <div className="absolute top-1/4 start-1/3 w-40 h-40 rounded-full bg-[rgba(0,194,255,0.2)] blur-[60px] opacity-40"></div>
          
          {/* Small accent light spots */}
          <div className="absolute bottom-1/3 end-1/4 w-24 h-24 rounded-full bg-[rgba(123,97,255,0.15)] blur-[40px] opacity-30"></div>
          <div className="absolute top-1/2 end-1/5 w-16 h-16 rounded-full bg-[rgba(0,194,255,0.15)] blur-[30px] opacity-25"></div>
          
          {/* Light rays */}
          <div className="absolute top-0 start-1/2 -translate-x-1/2 w-[200px] h-[400px] bg-gradient-to-b from-[rgba(0,194,255,0.1)] to-transparent opacity-20 skew-x-12 blur-[40px]"></div>
          <div className="absolute bottom-0 end-1/4 w-[150px] h-[300px] bg-gradient-to-t from-[rgba(123,97,255,0.1)] to-transparent opacity-15 -skew-x-12 blur-[40px]"></div>
          
          {/* Subtle grid overlay */}
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[size:40px_40px] opacity-20"></div>
        </div>
      </div>
      
      {/* Decorative elements */}
      <div className="absolute top-0 inset-x-0 w-full h-full pointer-events-none overflow-hidden z-10">
        <div className="absolute end-[-10rem] top-20 w-80 h-80 rounded-full bg-[rgba(0,194,255,0.05)] blur-[100px] transform -rotate-12"></div>
        <div className="absolute start-[-10rem] bottom-20 w-80 h-80 rounded-full bg-[rgba(123,97,255,0.05)] blur-[100px] transform rotate-12"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header with animation */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4 relative inline-block">
            {t('achievements.title', locale).includes('Achievements') || t('achievements.title', locale).includes('إنجازاتنا') ? (
              <>
                {t('achievements.title', locale).split(' ').slice(0, -1).join(' ')} <span className="text-[#00C2FF]">{t('achievements.title', locale).split(' ').slice(-1)[0]}</span>
              </>
            ) : (
              t('achievements.title', locale)
            )}
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
          <p className="text-xl text-[rgba(255,255,255,0.7)] max-w-3xl mx-auto">
            {t('achievements.description', locale)}
          </p>
        </div>

        {/* Category filter buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleFilterChange(category.id)}
              className={`px-5 py-2 rounded-full text-sm transition-all duration-300 ${
                activeFilter === category.id
                  ? 'bg-[#00C2FF] text-white shadow-md'
                  : 'bg-[rgba(13,21,38,0.7)] backdrop-blur-sm text-[rgba(255,255,255,0.7)] hover:bg-[rgba(13,21,38,0.8)] border border-[rgba(255,255,255,0.1)]'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>

        {/* Achievement cards - using a wrapping div with key to force re-render on filter change */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {achievements.map((achievement, index) => {
              // Only render if it matches the current filter
              const isVisible = activeFilter === "all" || achievement.category === activeFilter;
              const isAnimated = animatedItems.has(index);
              
              if (!isVisible) return null;
              
              return (
                <div 
                  key={index}
                  ref={(el) => {achievementRefs.current[index] = el}}
                  className={`achievement-card ${isAnimated ? 'animate-in' : ''}`}
                  style={{
                    "--delay": index
                  } as React.CSSProperties}
                >
                  <div className="bg-[rgba(13,21,38,0.6)] backdrop-blur-sm rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 h-full flex flex-col border border-[rgba(255,255,255,0.1)] overflow-hidden">
                    {/* Card header with year and icon */}
                    <div className="border-b border-[rgba(255,255,255,0.1)] p-4 flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="bg-[rgba(0,194,255,0.1)] w-8 h-8 rounded-full flex items-center justify-center text-[#00C2FF] ms-3">
                          {renderIcon(achievement.icon)}
                        </div>
                        <span className="font-bold text-lg text-white ms-3">{achievement.year}</span>
                      </div>
                      <div className="text-xs px-2 py-1 bg-[rgba(13,21,38,0.8)] rounded-full uppercase tracking-wide text-[rgba(255,255,255,0.7)] border border-[rgba(255,255,255,0.1)]">
                        {achievement.category === "awards" ? "Award" : "Milestone"}
                      </div>
                    </div>
                    
                    {/* Card content */}
                    <div className="p-4 flex-1 flex flex-col">
                      <h3 className="font-bold text-white mb-2">{achievement.title}</h3>
                      <p className="text-[rgba(255,255,255,0.7)] text-sm">{achievement.description}</p>
                      
                      {/* Accent line */}
                      <div className="mt-auto pt-3">
                        <div className="w-12 h-0.5 bg-[rgba(0,194,255,0.4)]"></div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .achievement-card {
          opacity: 0;
          transform: translateY(20px);
          transition: opacity 0.5s ease, transform 0.5s ease;
          transition-delay: calc(var(--delay) * 50ms);
        }
        
        .animate-in {
          opacity: 1 !important;
          transform: translateY(0) !important;
        }
      `}</style>
    </section>
  );
};

export default Achievements; 