import { useState, useEffect } from "react";
import Link from "next/link";

interface ProjectNavigationProps {
  project: any;
  activeTab: string;
  isScrolled: boolean;
  scrollToSection: (sectionId: string) => void;
}

const ProjectNavigation = ({ project, activeTab, isScrolled, scrollToSection }: ProjectNavigationProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isMobileMenuOpen && !target.closest(".mobile-menu-container")) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isMobileMenuOpen]);

  // Navigation items
  const navItems = [
    { id: "overview", label: "Overview" },
    { id: "gallery", label: "Gallery" },
    { id: "amenities", label: "Amenities" },
    { id: "location", label: "Location" },
    { id: "investment", label: "Investment" },
    { id: "progress", label: "Progress" },
    { id: "inquiry", label: "Inquiry" },
  ];

  return (
    <div className={`${isScrolled ? "fixed top-0 start-0 end-0" : ""} bg-[#0D1526] border-t border-white/10 shadow-md z-30`}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-14">
          {/* Project Title */}
          <div className="flex items-center">
            <h2 className="text-white font-medium text-sm md:text-base">{project.title}</h2>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex space-x-1">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                  activeTab === item.id
                    ? "text-[#00C2FF] bg-white/10"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                }`}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* Inquiry Button */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => scrollToSection("inquiry")}
              className="hidden md:block bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white px-4 py-1.5 rounded-md text-sm font-medium hover:shadow-lg transition-all duration-300"
            >
              Quick Inquiry
            </button>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden rounded-md p-1.5 bg-white/10 text-white"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Menu"
            >
              <svg
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`lg:hidden mobile-menu-container absolute start-0 end-0 transition-all duration-300 transform z-50 ${
          isMobileMenuOpen ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4 pointer-events-none"
        }`}
      >
        <div className="bg-[#0D1526] p-4 shadow-xl mx-4 border border-white/10 rounded-b-lg">
          <nav className="space-y-1">
            {navItems.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  scrollToSection(item.id);
                  setIsMobileMenuOpen(false);
                }}
                className={`block w-full text-start px-3 py-2 rounded-md text-sm transition-all duration-300 ${
                  activeTab === item.id
                    ? "text-[#00C2FF] bg-white/10 font-medium"
                    : "text-white/80 hover:text-white hover:bg-white/10"
                }`}
              >
                {item.label}
              </button>
            ))}
            <div className="pt-2 mt-2 border-t border-white/10">
              <button
                onClick={() => {
                  scrollToSection("inquiry");
                  setIsMobileMenuOpen(false);
                }}
                className="block w-full bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] text-white px-3 py-2 rounded-md text-sm font-medium hover:shadow-lg"
              >
                Quick Inquiry
              </button>
            </div>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default ProjectNavigation;