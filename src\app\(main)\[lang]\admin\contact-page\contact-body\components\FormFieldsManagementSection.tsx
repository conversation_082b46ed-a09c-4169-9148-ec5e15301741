"use client";

import { FiUser, FiMail, FiPhone, FiInfo, FiEdit } from 'react-icons/fi';

interface FormField {
  id: string;
  name: string;
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select';
  label: {
    english: string;
    arabic: string;
  };
  placeholder: {
    english: string;
    arabic: string;
  };
  required: boolean;
  enabled: boolean;
  options?: {
    english: string[];
    arabic: string[];
  };
  icon?: string;
}

interface FormFieldsManagementSectionProps {
  fields: FormField[];
  onFieldChange: (fieldId: string, property: string, value: any, language?: 'english' | 'arabic') => void;
}

export default function FormFieldsManagementSection({ fields, onFieldChange }: FormFieldsManagementSectionProps) {
  // Helper function to get icon component
  const getIcon = (iconName: string) => {
    const icons: { [key: string]: any } = {
      FiUser: FiUser,
      FiMail: FiMail, 
      FiPhone: FiPhone,
      FiInfo: FiInfo,
      FiEdit: FiEdit
    };
    const IconComponent = icons[iconName];
    return IconComponent ? <IconComponent className="h-6 w-6" /> : <FiInfo className="h-6 w-6" />;
  };

  return (
    <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
      <h3 className="text-lg font-medium text-white mb-4 flex items-center">
        <div className="w-6 h-6 bg-green-500 rounded mr-2 flex items-center justify-center">
          <span className="text-white text-xs font-bold">2</span>
        </div>
        Form Fields Management
      </h3>
      <p className="text-gray-400 text-sm mb-6">Configure form field labels, placeholders, and validation. Field types are fixed to work with the API.</p>
      
      {/* Existing Fields */}
      <div className="space-y-4 mb-6">
        {fields.map((field, index) => (
          <div key={field.id} className="bg-gray-700 rounded-lg p-4 border-l-4 border-blue-500">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-md font-medium text-gray-300 flex items-center">
                {getIcon(field.icon || 'FiInfo')}
                <span className="ml-2">{field.label.english} ({field.type})</span>
                {field.required && <span className="ml-2 text-red-400">*</span>}
              </h4>
            </div>
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Label (English)</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={field.label.english}
                  onChange={e => onFieldChange(field.id, 'label', e.target.value, 'english')}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">التسمية (عربي)</label>
                <input
                  type="text"
                  dir="rtl"
                  className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={field.label.arabic}
                  onChange={e => onFieldChange(field.id, 'label', e.target.value, 'arabic')}
                />
              </div>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                    checked={field.required}
                    onChange={e => onFieldChange(field.id, 'required', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-300">Required</span>
                </label>
              </div>
            </div>
            
            {/* Placeholder Fields */}
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Placeholder (English)</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={field.placeholder.english}
                  onChange={e => onFieldChange(field.id, 'placeholder', e.target.value, 'english')}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">النص الإرشادي (عربي)</label>
                <input
                  type="text"
                  dir="rtl"
                  className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={field.placeholder.arabic}
                  onChange={e => onFieldChange(field.id, 'placeholder', e.target.value, 'arabic')}
                />
              </div>
            </div>

            {/* Select Options (only for select fields) */}
            {field.type === 'select' && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">Dropdown Options</label>
                <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">English Options</label>
                    <textarea
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={field.options?.english?.join('\n') || ''}
                      onChange={e => onFieldChange(field.id, 'options', {
                        english: e.target.value.split('\n').filter(o => o.trim()),
                        arabic: field.options?.arabic || []
                      })}
                      placeholder="Enter each option on a new line"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">خيارات عربية</label>
                    <textarea
                      rows={3}
                      dir="rtl"
                      className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                      value={field.options?.arabic?.join('\n') || ''}
                      onChange={e => onFieldChange(field.id, 'options', {
                        english: field.options?.english || [],
                        arabic: e.target.value.split('\n').filter(o => o.trim())
                      })}
                      placeholder="أدخل كل خيار في سطر منفصل"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 