"use client";

const MapLocation = () => {
  // Define the openGoogleMaps function for the Egypt location
  const openGoogleMaps = () => {
    // Egypt location coordinates
    window.open("https://maps.google.com/?q=29.961241,30.926834", "_blank");
  };

  return (
    <div className="w-full overflow-hidden">
      {/* Google Maps iframe for Egypt location */}
      <div className="h-[500px] relative">
        <iframe 
          src="https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d3443.130260114471!2d30.926833999999996!3d29.961240999999998!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMjnCsDU3JzQwLjUiTiAzMMKwNTUnMzYuNiJF!5e1!3m2!1sar!2seg!4v1746798794569!5m2!1sar!2seg" 
          width="100%" 
          height="100%" 
          style={{ border: 0 }} 
          allowFullScreen 
          loading="lazy" 
          referrerPolicy="no-referrer-when-downgrade"
          className="absolute inset-0"
        ></iframe>
      </div>
      
      <div className="bg-brand-gradient text-white p-6">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center">
              <div className="me-4">
                <svg className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold">Mazaya Capital Egypt</h3>
                <p className="text-white/80">Cairo, Egypt</p>
              </div>
            </div>
            <button
              onClick={openGoogleMaps}
              className="mt-4 md:mt-0 bg-background text-primary hover:bg-background/90 px-6 py-3 rounded-lg font-medium transition-colors duration-300 shadow-lg flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 me-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002-2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              Open in Google Maps
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapLocation;