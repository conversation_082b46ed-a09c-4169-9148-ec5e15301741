"use client";

import React, { useState } from 'react';
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiMove, FiAward, FiTarget, FiFilter } from 'react-icons/fi';

interface Achievement {
  id: string;
  year: string;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  category: string;
  icon: string;
  order: number;
}

interface Category {
  id: string;
  label: {
    en: string;
    ar: string;
  };
}

interface AchievementsSection {
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  categories: Category[];
  achievements: Achievement[];
}

const iconOptions = [
  { name: 'Trophy', icon: '🏆', value: 'trophy' },
  { name: 'Leaf', icon: '🍃', value: 'leaf' },
  { name: 'Heart', icon: '❤️', value: 'heart' },
  { name: 'Star', icon: '⭐', value: 'star' },
  { name: 'Lightbulb', icon: '💡', value: 'lightbulb' },
  { name: 'Globe', icon: '🌍', value: 'globe' },
  { name: 'Medal', icon: '🏅', value: 'medal' },
  { name: 'Certificate', icon: '📜', value: 'certificate' },
  { name: 'Target', icon: '🎯', value: 'target' },
  { name: 'Growth', icon: '📈', value: 'growth' },
];

export default function AchievementsManagementPage() {
  // Initial data based on current achievements
  const [achievementsData, setAchievementsData] = useState<AchievementsSection>({
    title: {
      en: "Our Achievements",
      ar: "إنجازاتنا"
    },
    description: {
      en: "Celebrating our recognition, awards, and significant milestones in our journey of excellence",
      ar: "نحتفل بتقديرنا وجوائزنا والمعالم المهمة في رحلتنا نحو التميز"
    },
    categories: [
      { id: "all", label: { en: "All Achievements", ar: "جميع الإنجازات" } },
      { id: "awards", label: { en: "Awards", ar: "الجوائز" } },
      { id: "business", label: { en: "Business Milestones", ar: "المعالم التجارية" } },
    ],
    achievements: [
      {
        id: "developer-of-year-2023",
        year: "2023",
        title: { en: "Developer of the Year", ar: "مطور العام" },
        description: {
          en: "Recognized as the Developer of the Year by Real Estate Excellence Awards.",
          ar: "تم الاعتراف بنا كمطور العام من قبل جوائز التميز العقاري."
        },
        category: "awards",
        icon: "trophy",
        order: 1
      },
      {
        id: "sustainable-development-2022",
        year: "2022",
        title: { en: "Sustainable Development Award", ar: "جائزة التطوير المستدام" },
        description: {
          en: "Received the Green Building Award for our commitment to sustainable practices.",
          ar: "حصلنا على جائزة البناء الأخضر لالتزامنا بالممارسات المستدامة."
        },
        category: "awards",
        icon: "leaf",
        order: 2
      },
      {
        id: "community-impact-2021",
        year: "2021",
        title: { en: "Community Impact Award", ar: "جائزة التأثير المجتمعي" },
        description: {
          en: "Honored for our contributions to community development and social responsibility.",
          ar: "تم تكريمنا لمساهماتنا في التنمية المجتمعية والمسؤولية الاجتماعية."
        },
        category: "awards",
        icon: "heart",
        order: 3
      },
      {
        id: "luxury-development-2020",
        year: "2020",
        title: { en: "Best Luxury Development", ar: "أفضل تطوير فاخر" },
        description: {
          en: "Our flagship residential project was named Best Luxury Development of the Year.",
          ar: "تم اختيار مشروعنا السكني الرئيسي كأفضل تطوير فاخر للعام."
        },
        category: "awards",
        icon: "star",
        order: 4
      },
      {
        id: "innovation-design-2019",
        year: "2019",
        title: { en: "Innovation in Design", ar: "الابتكار في التصميم" },
        description: {
          en: "Awarded for pioneering innovative architectural design in commercial properties.",
          ar: "حصلنا على الجائزة لريادتنا في التصميم المعماري المبتكر في العقارات التجارية."
        },
        category: "awards",
        icon: "lightbulb",
        order: 5
      },
      {
        id: "market-expansion-2018",
        year: "2018",
        title: { en: "Market Expansion", ar: "توسع السوق" },
        description: {
          en: "Successfully expanded operations into two new regional markets.",
          ar: "نجحنا في توسيع العمليات إلى سوقين إقليميين جديدين."
        },
        category: "business",
        icon: "globe",
        order: 6
      }
    ]
  });

  const [editingSection, setEditingSection] = useState(false);
  const [editingAchievement, setEditingAchievement] = useState<string | null>(null);
  const [editingCategories, setEditingCategories] = useState(false);
  const [showAddAchievementForm, setShowAddAchievementForm] = useState(false);
  const [showAddCategoryForm, setShowAddCategoryForm] = useState(false);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState("all");

  const handleSectionSave = (newData: Partial<AchievementsSection>) => {
    setAchievementsData(prev => ({ ...prev, ...newData }));
    setEditingSection(false);
  };

  const handleAchievementSave = (achievementId: string, newAchievement: Achievement) => {
    setAchievementsData(prev => ({
      ...prev,
      achievements: prev.achievements.map(a => a.id === achievementId ? newAchievement : a)
    }));
    setEditingAchievement(null);
  };

  const handleAchievementDelete = (achievementId: string) => {
    if (confirm('Are you sure you want to delete this achievement?')) {
      setAchievementsData(prev => ({
        ...prev,
        achievements: prev.achievements.filter(a => a.id !== achievementId)
      }));
    }
  };

  const handleAchievementAdd = (newAchievement: Achievement) => {
    const maxOrder = Math.max(...achievementsData.achievements.map(a => a.order), 0);
    const achievementWithOrder = { ...newAchievement, order: maxOrder + 1 };
    setAchievementsData(prev => ({
      ...prev,
      achievements: [...prev.achievements, achievementWithOrder]
    }));
    setShowAddAchievementForm(false);
  };

  const handleCategoryAdd = (newCategory: Category) => {
    setAchievementsData(prev => ({
      ...prev,
      categories: [...prev.categories, newCategory]
    }));
    setShowAddCategoryForm(false);
  };

  const handleCategoryDelete = (categoryId: string) => {
    if (categoryId === 'all') {
      alert('Cannot delete the "All" category');
      return;
    }

    if (confirm('Are you sure you want to delete this category? Achievements in this category will be moved to "All".')) {
      setAchievementsData(prev => ({
        ...prev,
        categories: prev.categories.filter(c => c.id !== categoryId),
        achievements: prev.achievements.map(a => 
          a.category === categoryId ? { ...a, category: 'all' } : a
        )
      }));
    }
  };

  const handleDragStart = (achievementId: string) => {
    setDraggedItem(achievementId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetId) return;

    const draggedIndex = achievementsData.achievements.findIndex(a => a.id === draggedItem);
    const targetIndex = achievementsData.achievements.findIndex(a => a.id === targetId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    const newAchievements = [...achievementsData.achievements];
    const [draggedAchievement] = newAchievements.splice(draggedIndex, 1);
    newAchievements.splice(targetIndex, 0, draggedAchievement);

    // Update order values
    const reorderedAchievements = newAchievements.map((achievement, index) => ({
      ...achievement,
      order: index + 1
    }));

    setAchievementsData(prev => ({
      ...prev,
      achievements: reorderedAchievements
    }));

    setDraggedItem(null);
  };

  const handleSaveAll = () => {
    console.log('Saving achievements data:', achievementsData);
    alert('Achievements updated successfully!');
  };

  const filteredAchievements = achievementsData.achievements
    .filter(achievement => activeFilter === "all" || achievement.category === activeFilter)
    .sort((a, b) => a.order - b.order);

  const getIconDisplay = (iconValue: string) => {
    const iconOption = iconOptions.find(option => option.value === iconValue);
    return iconOption ? iconOption.icon : '🏆';
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Achievements Management</h1>
          <p className="text-gray-400 mt-1">Manage company achievements, awards, and milestones</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Header
            </h2>
            <button
              onClick={() => setEditingSection(!editingSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection ? (
            <SectionEditForm 
              data={achievementsData}
              onSave={handleSectionSave}
              onCancel={() => setEditingSection(false)}
            />
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{achievementsData.title.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <p className="text-white bg-gray-700 p-2 rounded">{achievementsData.title.ar}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                  <p className="text-gray-300 bg-gray-700 p-2 rounded text-sm">{achievementsData.description.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                  <p className="text-gray-300 bg-gray-700 p-2 rounded text-sm">{achievementsData.description.ar}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Categories Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiFilter className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Categories
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowAddCategoryForm(true)}
                className="inline-flex items-center px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-500"
              >
                <FiPlus className="mr-1 h-4 w-4" />
                Add Category
              </button>
              <button
                onClick={() => setEditingCategories(!editingCategories)}
                className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
              >
                {editingCategories ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                {editingCategories ? 'Cancel' : 'Edit'}
              </button>
            </div>
          </div>

          {showAddCategoryForm && (
            <div className="mb-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <CategoryAddForm
                onSave={handleCategoryAdd}
                onCancel={() => setShowAddCategoryForm(false)}
              />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {achievementsData.categories.map((category) => (
              <div key={category.id} className="bg-gray-700 p-4 rounded-lg border border-gray-600">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-medium text-white">{category.label.en}</h3>
                    <p className="text-sm text-gray-400 mt-1">{category.label.ar}</p>
                    <p className="text-xs text-gray-500 mt-2">
                      {achievementsData.achievements.filter(a => a.category === category.id).length} achievements
                    </p>
                  </div>
                  {editingCategories && category.id !== 'all' && (
                    <button
                      onClick={() => handleCategoryDelete(category.id)}
                      className="text-red-400 hover:text-red-300 ml-2"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Achievements Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiTarget className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Achievements ({filteredAchievements.length})
            </h2>
            <button
              onClick={() => setShowAddAchievementForm(true)}
              className="inline-flex items-center px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-500"
            >
              <FiPlus className="mr-1 h-4 w-4" />
              Add Achievement
            </button>
          </div>

          {/* Filter Tabs */}
          <div className="flex space-x-2 mb-6">
            {achievementsData.categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`px-3 py-1 text-sm rounded transition-colors ${
                  activeFilter === category.id
                    ? 'bg-[#00C2FF] text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {category.label.en}
              </button>
            ))}
          </div>

          {showAddAchievementForm && (
            <div className="mb-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <AchievementAddForm
                categories={achievementsData.categories}
                onSave={handleAchievementAdd}
                onCancel={() => setShowAddAchievementForm(false)}
              />
            </div>
          )}

          <div className="space-y-4">
            {filteredAchievements.map((achievement) => (
              <div
                key={achievement.id}
                draggable
                onDragStart={() => handleDragStart(achievement.id)}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, achievement.id)}
                className={`bg-gray-700 rounded-lg border border-gray-600 p-4 cursor-move hover:border-[#00C2FF] transition-colors ${
                  draggedItem === achievement.id ? 'opacity-50' : ''
                }`}
              >
                {editingAchievement === achievement.id ? (
                  <AchievementEditForm
                    achievement={achievement}
                    categories={achievementsData.categories}
                    onSave={(updatedAchievement) => handleAchievementSave(achievement.id, updatedAchievement)}
                    onCancel={() => setEditingAchievement(null)}
                  />
                ) : (
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="flex items-center space-x-2">
                        <FiMove className="h-4 w-4 text-gray-400" />
                        <span className="text-2xl">{getIconDisplay(achievement.icon)}</span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-medium text-white">{achievement.title.en}</h3>
                          <span className="px-2 py-1 bg-[#00C2FF]/20 text-[#00C2FF] rounded-full text-xs">
                            {achievement.year}
                          </span>
                          <span className="px-2 py-1 bg-gray-600 text-gray-300 rounded-full text-xs">
                            {achievementsData.categories.find(c => c.id === achievement.category)?.label.en}
                          </span>
                        </div>
                        <p className="text-gray-400 text-sm mb-1">{achievement.title.ar}</p>
                        <p className="text-gray-300 text-sm">{achievement.description.en}</p>
                        <p className="text-gray-400 text-sm">{achievement.description.ar}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingAchievement(achievement.id)}
                        className="text-[#00C2FF] hover:text-[#00C2FF]/80"
                      >
                        <FiEdit3 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleAchievementDelete(achievement.id)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredAchievements.length === 0 && (
            <div className="text-center py-12">
              <FiTarget className="h-12 w-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No achievements found for this category.</p>
              <button
                onClick={() => setShowAddAchievementForm(true)}
                className="mt-4 inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
              >
                <FiPlus className="mr-2 h-4 w-4" />
                Add First Achievement
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Section Edit Form Component
function SectionEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: AchievementsSection;
  onSave: (data: Partial<AchievementsSection>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState({
    title: data.title,
    description: data.description
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Achievement Edit Form Component
function AchievementEditForm({ 
  achievement, 
  categories,
  onSave, 
  onCancel 
}: { 
  achievement: Achievement;
  categories: Category[];
  onSave: (achievement: Achievement) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<Achievement>(achievement);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Year *</label>
          <input
            type="text"
            value={formData.year}
            onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Category *</label>
          <select
            value={formData.category}
            onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          >
            {categories.filter(c => c.id !== 'all').map(category => (
              <option key={category.id} value={category.id}>{category.label.en}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
        <select
          value={formData.icon}
          onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
          className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        >
          {iconOptions.map(option => (
            <option key={option.value} value={option.value}>{option.icon} {option.name}</option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
        <textarea
          value={formData.description.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, en: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
        <textarea
          value={formData.description.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, ar: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Achievement Add Form Component
function AchievementAddForm({ 
  categories,
  onSave, 
  onCancel 
}: { 
  categories: Category[];
  onSave: (achievement: Achievement) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<Achievement>({
    id: '',
    year: new Date().getFullYear().toString(),
    title: { en: '', ar: '' },
    description: { en: '', ar: '' },
    category: categories.filter(c => c.id !== 'all')[0]?.id || '',
    icon: 'trophy',
    order: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.en || !formData.year) {
      alert('Please fill in required fields');
      return;
    }

    const newAchievement = {
      ...formData,
      id: `${formData.title.en.toLowerCase().replace(/\s+/g, '-')}-${formData.year}`
    };

    onSave(newAchievement);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Achievement</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Year *</label>
            <input
              type="text"
              value={formData.year}
              onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Category *</label>
            <select
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            >
              {categories.filter(c => c.id !== 'all').map(category => (
                <option key={category.id} value={category.id}>{category.label.en}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
            <input
              type="text"
              value={formData.title.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
            <input
              type="text"
              value={formData.title.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <select
            value={formData.icon}
            onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          >
            {iconOptions.map(option => (
              <option key={option.value} value={option.value}>{option.icon} {option.name}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            Add Achievement
          </button>
        </div>
      </form>
    </div>
  );
}

// Category Add Form Component
function CategoryAddForm({ 
  onSave, 
  onCancel 
}: { 
  onSave: (category: Category) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<Category>({
    id: '',
    label: { en: '', ar: '' }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.label.en) {
      alert('Please fill in the English label');
      return;
    }

    const newCategory = {
      ...formData,
      id: formData.label.en.toLowerCase().replace(/\s+/g, '_')
    };

    onSave(newCategory);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Category</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Label (English) *</label>
            <input
              type="text"
              value={formData.label.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                label: { ...prev.label, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Label (Arabic)</label>
            <input
              type="text"
              value={formData.label.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                label: { ...prev.label, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            Add Category
          </button>
        </div>
      </form>
    </div>
  );
} 