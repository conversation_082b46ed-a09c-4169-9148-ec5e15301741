"use client";

import React from 'react';
import { 
  FiTrash2, FiArchive, FiStar, FiDownload, 
  FiX, FiEye
} from 'react-icons/fi';

interface BulkActionsBarProps {
  selectedCount: number;
  onMarkAllAsRead: () => void;
  onStarAll: () => void;
  onUnstarAll: () => void;
  onArchiveAll: () => void;
  onDeleteAll: () => void;
  onExportSelected: () => void;
  onClearSelection: () => void;
  onSelectAll: () => void;
  totalCount: number;
}

export default function BulkActionsBar({
  selectedCount,
  onMarkAllAsRead,
  onStarAll,
  onUnstarAll,
  onArchiveAll,
  onDeleteAll,
  onExportSelected,
  onClearSelection,
  onSelectAll,
  totalCount
}: BulkActionsBarProps) {
  if (selectedCount === 0) return null;

  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-white font-medium">
              {selectedCount} of {totalCount} selected
            </span>
            <button
              onClick={onSelectAll}
              className="text-[#00C2FF] hover:text-[#00C2FF]/80 text-sm"
            >
              Select all
            </button>
            <button
              onClick={onClearSelection}
              className="text-gray-400 hover:text-white text-sm"
            >
              Clear selection
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onMarkAllAsRead}
            className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors flex items-center text-sm"
            title="Mark all as read"
          >
            <FiEye className="mr-2 h-4 w-4" />
            Mark as Read
          </button>

          <button
            onClick={onStarAll}
            className="px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-500 transition-colors flex items-center text-sm"
            title="Star all selected"
          >
            <FiStar className="mr-2 h-4 w-4" />
            Star
          </button>

          <button
            onClick={onUnstarAll}
            className="px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors flex items-center text-sm"
            title="Remove star from all selected"
          >
            <FiStar className="mr-2 h-4 w-4" />
            Unstar
          </button>

          <button
            onClick={onExportSelected}
            className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 transition-colors flex items-center text-sm"
            title="Export selected inquiries"
          >
            <FiDownload className="mr-2 h-4 w-4" />
            Export
          </button>

          <button
            onClick={onArchiveAll}
            className="px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-500 transition-colors flex items-center text-sm"
            title="Archive all selected"
          >
            <FiArchive className="mr-2 h-4 w-4" />
            Archive
          </button>

          <button
            onClick={() => {
              if (confirm(`Are you sure you want to delete ${selectedCount} inquiries? This action cannot be undone.`)) {
                onDeleteAll();
              }
            }}
            className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors flex items-center text-sm"
            title="Delete all selected"
          >
            <FiTrash2 className="mr-2 h-4 w-4" />
            Delete
          </button>

          <button
            onClick={onClearSelection}
            className="p-2 text-gray-400 hover:text-white transition-colors"
            title="Clear selection"
          >
            <FiX className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
} 