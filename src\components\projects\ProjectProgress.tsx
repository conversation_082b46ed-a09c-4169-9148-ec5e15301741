import { forwardRef, useState } from "react";

interface ProjectProgressProps {
  project: any;
}

const ProjectProgress = forwardRef<HTMLElement, ProjectProgressProps>(({ project }, ref) => {
  // State for carousel
  const [currentSlide, setCurrentSlide] = useState(0);
  
  // Sample construction photos (in a real app, these would come from the project data)
  const constructionPhotos = [
    { id: 1, title: "Foundation Work" },
    { id: 2, title: "Structural Framework" },
    { id: 3, title: "Exterior Façade" },
    { id: 4, title: "Interior Progress" },
    { id: 5, title: "Amenities Installation" },
    { id: 6, title: "Finishing Touches" },
  ];

  // Navigate to next slide
  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === constructionPhotos.length - 1 ? 0 : prev + 1));
  };

  // Navigate to previous slide
  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? constructionPhotos.length - 1 : prev - 1));
  };

  // Navigate to specific slide
  const goToSlide = (slideIndex: number) => {
    setCurrentSlide(slideIndex);
  };

  return (
    <section ref={ref} id="progress" className="mb-20">
      <div className="text-center mb-12 md:mb-16 px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Construction Progress</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Track the development journey of {project.title} from foundation to completion.</p>
      </div>
      
      <div className="max-w-4xl mx-auto px-4">
        {/* Progress Bar */}
        <div className="relative h-2 bg-gray-200 rounded-full mb-10 md:mb-12">
          <div className="absolute start-0 top-0 h-full w-[85%] bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] rounded-full"></div>
          <div className="absolute -end-2 md:-end-6 top-1/2 transform -translate-y-1/2 text-[#7B61FF] font-bold text-sm md:text-base whitespace-nowrap">
            85%
          </div>
        </div>

        {/* Timeline */}
        <div className="relative">
          {/* Vertical Line - adjusted for mobile */}
          <div className="absolute start-[30px] md:start-[50px] top-0 bottom-0 w-0.5 bg-gray-200"></div>
          
          {/* Milestones */}
          <div className="space-y-10 md:space-y-16">
            {/* Milestone 1 - Completed */}
            <div className="flex">
              <div className="relative">
                <div className="w-[60px] md:w-[100px] flex items-center justify-center">
                  <div className="w-5 h-5 md:w-6 md:h-6 bg-[#00C2FF] rounded-full z-10 ring-4 ring-[#00C2FF]/10"></div>
                </div>
                <div className="absolute top-3 start-[30px] md:start-[50px] h-16 w-0.5 bg-gray-200"></div>
              </div>
              <div className="flex-1 bg-white p-4 md:p-6 rounded-xl shadow-sm ms-2 md:ms-4 border border-gray-100">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-2 gap-2">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900">Foundation Completed</h3>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium w-fit">Completed</span>
                </div>
                <p className="text-gray-600 mb-2 text-sm md:text-base">The foundation work has been finished ahead of schedule with all structural integrity tests passed successfully.</p>
                <div className="text-xs md:text-sm text-gray-500">January 2021</div>
              </div>
            </div>
            
            {/* Milestone 2 - Completed */}
            <div className="flex">
              <div className="relative">
                <div className="w-[60px] md:w-[100px] flex items-center justify-center">
                  <div className="w-5 h-5 md:w-6 md:h-6 bg-[#00C2FF] rounded-full z-10 ring-4 ring-[#00C2FF]/10"></div>
                </div>
                <div className="absolute top-3 start-[30px] md:start-[50px] h-16 w-0.5 bg-gray-200"></div>
              </div>
              <div className="flex-1 bg-white p-4 md:p-6 rounded-xl shadow-sm ms-2 md:ms-4 border border-gray-100">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-2 gap-2">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900">Structural Framework</h3>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium w-fit">Completed</span>
                </div>
                <p className="text-gray-600 mb-2 text-sm md:text-base">All 45 floors have been structurally completed with reinforced concrete framework in place.</p>
                <div className="text-xs md:text-sm text-gray-500">June 2021</div>
              </div>
            </div>
            
            {/* Milestone 3 - Completed */}
            <div className="flex">
              <div className="relative">
                <div className="w-[60px] md:w-[100px] flex items-center justify-center">
                  <div className="w-5 h-5 md:w-6 md:h-6 bg-[#00C2FF] rounded-full z-10 ring-4 ring-[#00C2FF]/10"></div>
                </div>
                <div className="absolute top-3 start-[30px] md:start-[50px] h-16 w-0.5 bg-gray-200"></div>
              </div>
              <div className="flex-1 bg-white p-4 md:p-6 rounded-xl shadow-sm ms-2 md:ms-4 border border-gray-100">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-2 gap-2">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900">Exterior Façade</h3>
                  <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium w-fit">Completed</span>
                </div>
                <p className="text-gray-600 mb-2 text-sm md:text-base">Exterior glass façade installation complete with energy-efficient materials for optimal temperature control.</p>
                <div className="text-xs md:text-sm text-gray-500">October 2021</div>
              </div>
            </div>
            
            {/* Milestone 4 - In Progress */}
            <div className="flex">
              <div className="relative">
                <div className="w-[60px] md:w-[100px] flex items-center justify-center">
                  <div className="w-5 h-5 md:w-6 md:h-6 bg-[#00C2FF] rounded-full z-10 ring-4 ring-[#00C2FF]/10"></div>
                </div>
                <div className="absolute top-3 start-[30px] md:start-[50px] h-16 w-0.5 bg-gray-200"></div>
              </div>
              <div className="flex-1 bg-white p-4 md:p-6 rounded-xl shadow-sm ms-2 md:ms-4 border border-gray-100">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-2 gap-2">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900">Interior Finishing</h3>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium w-fit">In Progress</span>
                </div>
                <p className="text-gray-600 mb-2 text-sm md:text-base">Luxury interior finishes being installed across all residential units with premium materials and fixtures.</p>
                <div className="text-xs md:text-sm text-gray-500">Current Phase - 85% Complete</div>
              </div>
            </div>
            
            {/* Milestone 5 - Upcoming */}
            <div className="flex opacity-50">
              <div className="relative">
                <div className="w-[60px] md:w-[100px] flex items-center justify-center">
                  <div className="w-5 h-5 md:w-6 md:h-6 bg-gray-300 rounded-full z-10 ring-4 ring-gray-200"></div>
                </div>
              </div>
              <div className="flex-1 bg-white p-4 md:p-6 rounded-xl shadow-sm ms-2 md:ms-4 border border-gray-100">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-2 gap-2">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900">Final Inspections & Handover</h3>
                  <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium w-fit">Upcoming</span>
                </div>
                <p className="text-gray-600 mb-2 text-sm md:text-base">Final quality checks, smart home system testing, and preparation for unit handover to owners.</p>
                <div className="text-xs md:text-sm text-gray-500">Estimated: December 2023</div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Construction Photos Carousel */}
        <div className="mt-12 md:mt-16">
          <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 md:mb-6">Construction Photos</h3>
          
          <div className="relative">
            {/* Carousel Container */}
            <div className="relative overflow-hidden rounded-xl bg-gray-100">
              {/* Main Image Slide */}
              <div className="relative h-48 sm:h-64 md:h-80 bg-gray-200 rounded-xl overflow-hidden">
                {constructionPhotos.map((photo, index) => (
                  <div 
                    key={photo.id} 
                    className={`absolute inset-0 flex items-center justify-center transition-opacity duration-500 ease-in-out ${
                      index === currentSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'
                    }`}
                  >
                    <span className="text-gray-500 text-sm md:text-base">{photo.title}</span>
                  </div>
                ))}
              </div>
              
              {/* Navigation Arrows */}
              <button 
                onClick={prevSlide}
                className="absolute top-1/2 start-2 z-20 transform -translate-y-1/2 w-8 h-8 md:w-10 md:h-10 rounded-full bg-white/80 flex items-center justify-center shadow-md hover:bg-white transition-all duration-200"
                aria-label="Previous slide"
              >
                <svg className="h-4 w-4 md:h-5 md:w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <button 
                onClick={nextSlide}
                className="absolute top-1/2 end-2 z-20 transform -translate-y-1/2 w-8 h-8 md:w-10 md:h-10 rounded-full bg-white/80 flex items-center justify-center shadow-md hover:bg-white transition-all duration-200"
                aria-label="Next slide"
              >
                <svg className="h-4 w-4 md:h-5 md:w-5 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
            
            {/* Thumbnails / Indicators */}
            <div className="flex justify-center mt-4 space-x-2">
              {constructionPhotos.map((photo, index) => (
                <button
                  key={photo.id}
                  onClick={() => goToSlide(index)}
                  className={`w-2 h-2 md:w-3 md:h-3 rounded-full transition-all duration-200 ${
                    currentSlide === index 
                      ? 'bg-[#00C2FF] scale-125' 
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
            
            {/* Caption / Counter */}
            <div className="text-center mt-3 text-sm text-gray-500">
              {currentSlide + 1} / {constructionPhotos.length}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

ProjectProgress.displayName = "ProjectProgress";

export default ProjectProgress;