"use client";

import React, { useState, useMemo } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, FiSearch, FiFilter, FiEye, FiEdit, FiTrash2, 
  FiPlus, FiRefreshCw, FiDownload, FiMoreVertical, FiShield,
  FiUserCheck, FiUserX, FiSettings, FiCheck, FiX, FiSave
} from 'react-icons/fi';

interface Role {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  userCount: number;
  createdAt: Date;
  updatedAt: Date;
  permissions: string[];
  isSystemRole: boolean; // Cannot be deleted
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'suspended' | 'pending';
}

// Mock data for roles
const mockRoles: Role[] = [
  {
    id: '1',
    name: 'Super Admin',
    description: 'Full system access with all permissions',
    isActive: true,
    userCount: 1,
    createdAt: new Date('2023-01-01T09:00:00'),
    updatedAt: new Date('2024-01-15T10:30:00'),
    permissions: ['all'],
    isSystemRole: true
  },
  {
    id: '2',
    name: 'Admin',
    description: 'Administrative access to most system features',
    isActive: true,
    userCount: 2,
    createdAt: new Date('2023-01-01T09:00:00'),
    updatedAt: new Date('2024-01-10T14:20:00'),
    permissions: ['dashboard', 'users', 'settings', 'pages', 'inquiries'],
    isSystemRole: true
  },
  {
    id: '3',
    name: 'Manager',
    description: 'Management level access to sales and reporting',
    isActive: true,
    userCount: 3,
    createdAt: new Date('2023-02-15T09:00:00'),
    updatedAt: new Date('2024-01-12T11:15:00'),
    permissions: ['dashboard', 'inquiries', 'users_view', 'reports'],
    isSystemRole: false
  },
  {
    id: '4',
    name: 'Sales Agent',
    description: 'Sales team access to client management and properties',
    isActive: true,
    userCount: 8,
    createdAt: new Date('2023-03-01T09:00:00'),
    updatedAt: new Date('2024-01-08T16:45:00'),
    permissions: ['dashboard', 'inquiries', 'properties'],
    isSystemRole: false
  },
  {
    id: '5',
    name: 'Content Editor',
    description: 'Content management for website pages and articles',
    isActive: true,
    userCount: 2,
    createdAt: new Date('2023-06-01T09:00:00'),
    updatedAt: new Date('2024-01-05T13:30:00'),
    permissions: ['pages', 'articles', 'media'],
    isSystemRole: false
  },
  {
    id: '6',
    name: 'Client',
    description: 'Basic client access to personal dashboard',
    isActive: true,
    userCount: 150,
    createdAt: new Date('2023-01-01T09:00:00'),
    updatedAt: new Date('2024-01-14T09:20:00'),
    permissions: ['profile', 'inquiries_own'],
    isSystemRole: true
  },
  {
    id: '7',
    name: 'Viewer',
    description: 'Read-only access for auditing purposes',
    isActive: false,
    userCount: 0,
    createdAt: new Date('2023-08-15T09:00:00'),
    updatedAt: new Date('2023-12-20T10:15:00'),
    permissions: ['dashboard_view', 'reports_view'],
    isSystemRole: false
  }
];

// Mock users data for role assignment
const mockUsers: User[] = [
  { id: '1', firstName: 'Ahmed', lastName: 'Al-Rashid', email: '<EMAIL>', role: 'Super Admin', status: 'active' },
  { id: '2', firstName: 'Sarah', lastName: 'Johnson', email: '<EMAIL>', role: 'Manager', status: 'active' },
  { id: '3', firstName: 'Mohammed', lastName: 'Hassan', email: '<EMAIL>', role: 'Sales Agent', status: 'active' },
  { id: '4', firstName: 'Emily', lastName: 'Chen', email: '<EMAIL>', role: 'Client', status: 'active' },
  { id: '5', firstName: 'David', lastName: 'Wilson', email: '<EMAIL>', role: 'Sales Agent', status: 'suspended' }
];

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>(mockRoles);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);

  // Filter and search logic
  const filteredRoles = useMemo(() => {
    let filtered = roles.filter(role => {
      const matchesSearch = 
        role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        role.description.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = filterStatus === 'all' || 
        (filterStatus === 'active' && role.isActive) ||
        (filterStatus === 'inactive' && !role.isActive);
      
      return matchesSearch && matchesStatus;
    });

    return filtered.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }, [roles, searchTerm, filterStatus]);

  // Statistics
  const stats = useMemo(() => {
    const total = roles.length;
    const active = roles.filter(r => r.isActive).length;
    const inactive = roles.filter(r => !r.isActive).length;
    const totalUsers = roles.reduce((sum, role) => sum + role.userCount, 0);
    
    return { total, active, inactive, totalUsers };
  }, [roles]);

  const handleCreateRole = (roleData: Partial<Role>) => {
    const newRole: Role = {
      id: Date.now().toString(),
      name: roleData.name || '',
      description: roleData.description || '',
      isActive: true,
      userCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      permissions: roleData.permissions || [],
      isSystemRole: false
    };

    setRoles(prev => [newRole, ...prev]);
    setIsCreateModalOpen(false);
  };

  const handleEditRole = (roleData: Partial<Role>) => {
    if (!editingRole) return;
    
    setRoles(prev => prev.map(role => 
      role.id === editingRole.id 
        ? { ...role, ...roleData, updatedAt: new Date() }
        : role
    ));
    setIsEditModalOpen(false);
    setEditingRole(null);
  };

  const handleDeleteRole = (id: string) => {
    const role = roles.find(r => r.id === id);
    if (role?.isSystemRole) {
      alert('System roles cannot be deleted.');
      return;
    }
    
    if (role && role.userCount > 0) {
      alert(`Cannot delete role "${role.name}" because it has ${role.userCount} assigned users. Please reassign users first.`);
      return;
    }

    setRoles(prev => prev.filter(role => role.id !== id));
  };

  const handleToggleStatus = (id: string) => {
    setRoles(prev => prev.map(role => 
      role.id === id ? { ...role, isActive: !role.isActive, updatedAt: new Date() } : role
    ));
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'text-green-400 bg-green-400/10 border-green-400/20' 
      : 'text-red-400 bg-red-400/10 border-red-400/20';
  };

  const getUsersInRole = (roleName: string) => {
    return mockUsers.filter(user => user.role === roleName);
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center">
              <FiShield className="mr-3 h-8 w-8 text-[#00C2FF]" />
              Role Management
            </h1>
            <p className="text-gray-400 mt-1">Manage system roles and their permissions</p>
          </div>
          <div className="flex space-x-3">
            <button 
              onClick={() => setIsCreateModalOpen(true)}
              className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors flex items-center"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Role
            </button>
            <button className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center">
              <FiRefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Roles</p>
                <p className="text-2xl font-bold text-white">{stats.total}</p>
              </div>
              <FiShield className="h-8 w-8 text-[#00C2FF]" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Active Roles</p>
                <p className="text-2xl font-bold text-green-400">{stats.active}</p>
              </div>
              <FiUserCheck className="h-8 w-8 text-green-400" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Inactive Roles</p>
                <p className="text-2xl font-bold text-red-400">{stats.inactive}</p>
              </div>
              <FiUserX className="h-8 w-8 text-red-400" />
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Users</p>
                <p className="text-2xl font-bold text-purple-400">{stats.totalUsers}</p>
              </div>
              <FiUsers className="h-8 w-8 text-purple-400" />
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <FiSearch className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search roles by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            </div>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center"
            >
              <FiFilter className="mr-2 h-4 w-4" />
              Filters
            </button>
          </div>
          
          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Status</label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Roles List */}
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">
            Roles ({filteredRoles.length})
          </h2>
        </div>
        
        <div className="divide-y divide-gray-700">
          {filteredRoles.length === 0 ? (
            <div className="p-8 text-center">
              <FiShield className="mx-auto h-12 w-12 text-gray-500 mb-4" />
              <p className="text-gray-400">No roles found matching your criteria.</p>
            </div>
          ) : (
            filteredRoles.map((role) => (
              <div
                key={role.id}
                className="p-4 hover:bg-gray-700/50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="font-medium text-white text-lg">{role.name}</h3>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(role.isActive)}`}>
                        {role.isActive ? 'ACTIVE' : 'INACTIVE'}
                      </span>
                      {role.isSystemRole && (
                        <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded text-xs font-medium border border-blue-400/20">
                          SYSTEM
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-400 mb-3">{role.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div className="flex items-center text-sm text-gray-400">
                        <FiUsers className="mr-2 h-4 w-4" />
                        {role.userCount} users assigned
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <FiSettings className="mr-2 h-4 w-4" />
                        {role.permissions.length} permissions
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <FiEdit className="mr-2 h-4 w-4" />
                        Updated: {formatDate(role.updatedAt)}
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      {role.permissions.slice(0, 5).map((permission, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs"
                        >
                          {permission}
                        </span>
                      ))}
                      {role.permissions.length > 5 && (
                        <span className="px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs">
                          +{role.permissions.length - 5} more
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => {
                        setSelectedRole(role);
                        setIsUsersModalOpen(true);
                      }}
                      className="p-2 text-gray-400 hover:text-blue-400 transition-colors"
                      title="View users"
                    >
                      <FiEye className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => {
                        setEditingRole(role);
                        setIsEditModalOpen(true);
                      }}
                      className="p-2 text-gray-400 hover:text-green-400 transition-colors"
                      title="Edit role"
                    >
                      <FiEdit className="h-4 w-4" />
                    </button>
                    
                    <button
                      onClick={() => handleToggleStatus(role.id)}
                      className={`p-2 transition-colors ${
                        role.isActive 
                          ? 'text-gray-400 hover:text-red-400' 
                          : 'text-gray-400 hover:text-green-400'
                      }`}
                      title={role.isActive ? 'Deactivate role' : 'Activate role'}
                    >
                      {role.isActive ? <FiUserX className="h-4 w-4" /> : <FiUserCheck className="h-4 w-4" />}
                    </button>
                    
                    {!role.isSystemRole && (
                      <button
                        onClick={() => {
                          if (confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
                            handleDeleteRole(role.id);
                          }
                        }}
                        className="p-2 text-gray-400 hover:text-red-400 transition-colors"
                        title="Delete role"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Create Role Modal */}
      <CreateRoleModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateRole}
      />

      {/* Edit Role Modal */}
      <EditRoleModal
        role={editingRole}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingRole(null);
        }}
        onSubmit={handleEditRole}
      />

      {/* Users in Role Modal */}
      <UsersInRoleModal
        role={selectedRole}
        users={selectedRole ? getUsersInRole(selectedRole.name) : []}
        isOpen={isUsersModalOpen}
        onClose={() => {
          setIsUsersModalOpen(false);
          setSelectedRole(null);
        }}
      />
    </div>
  );
}

// Create Role Modal Component
function CreateRoleModal({ isOpen, onClose, onSubmit }: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Role>) => void;
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: [] as string[]
  });

  const availablePermissions = [
    'dashboard', 'users', 'users_view', 'settings', 'pages', 'inquiries', 
    'reports', 'properties', 'articles', 'media', 'profile', 'inquiries_own'
  ];

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
    setFormData({ name: '', description: '', permissions: [] });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-2xl">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Create New Role</h2>
          <button onClick={onClose} className="p-2 text-gray-400 hover:text-white">
            <FiX className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Role Name *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] resize-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Permissions</label>
              <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                {availablePermissions.map(permission => (
                  <label key={permission} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.permissions.includes(permission)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData(prev => ({ 
                            ...prev, 
                            permissions: [...prev.permissions, permission] 
                          }));
                        } else {
                          setFormData(prev => ({ 
                            ...prev, 
                            permissions: prev.permissions.filter(p => p !== permission) 
                          }));
                        }
                      }}
                      className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF]"
                    />
                    <span className="text-gray-300 text-sm">{permission}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90"
            >
              Create Role
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Edit Role Modal Component
function EditRoleModal({ role, isOpen, onClose, onSubmit }: {
  role: Role | null;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<Role>) => void;
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: [] as string[]
  });

  React.useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description,
        permissions: [...role.permissions]
      });
    }
  }, [role]);

  const availablePermissions = [
    'dashboard', 'users', 'users_view', 'settings', 'pages', 'inquiries', 
    'reports', 'properties', 'articles', 'media', 'profile', 'inquiries_own'
  ];

  if (!isOpen || !role) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-2xl">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Edit Role: {role.name}</h2>
          <button onClick={onClose} className="p-2 text-gray-400 hover:text-white">
            <FiX className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Role Name *</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                required
                disabled={role.isSystemRole}
              />
              {role.isSystemRole && (
                <p className="text-xs text-gray-500 mt-1">System role names cannot be changed</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Description</label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] resize-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">Permissions</label>
              <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                {availablePermissions.map(permission => (
                  <label key={permission} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.permissions.includes(permission)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData(prev => ({ 
                            ...prev, 
                            permissions: [...prev.permissions, permission] 
                          }));
                        } else {
                          setFormData(prev => ({ 
                            ...prev, 
                            permissions: prev.permissions.filter(p => p !== permission) 
                          }));
                        }
                      }}
                      className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF]"
                    />
                    <span className="text-gray-300 text-sm">{permission}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90"
            >
              Update Role
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Users in Role Modal Component
function UsersInRoleModal({ role, users, isOpen, onClose }: {
  role: Role | null;
  users: User[];
  isOpen: boolean;
  onClose: () => void;
}) {
  if (!isOpen || !role) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-3xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Users in Role: {role.name}</h2>
          <button onClick={onClose} className="p-2 text-gray-400 hover:text-white">
            <FiX className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
          {users.length === 0 ? (
            <div className="text-center py-8">
              <FiUsers className="mx-auto h-12 w-12 text-gray-500 mb-4" />
              <p className="text-gray-400">No users assigned to this role.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {users.map(user => (
                <div key={user.id} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{user.firstName} {user.lastName}</p>
                    <p className="text-gray-400 text-sm">{user.email}</p>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    user.status === 'active' ? 'bg-green-400/10 text-green-400' :
                    user.status === 'suspended' ? 'bg-red-400/10 text-red-400' :
                    'bg-gray-400/10 text-gray-400'
                  }`}>
                    {user.status.toUpperCase()}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
} 