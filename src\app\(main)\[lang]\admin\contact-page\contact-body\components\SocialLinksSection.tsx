"use client";

import { FiGlobe, FiPlus, FiTrash2, FiEdit3, FiSearch, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { useState, useRef, useEffect } from 'react';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';
import React from 'react';

interface SocialLinks {
  title: {
    english: string;
    arabic: string;
  };
  description: {
    english: string;
    arabic: string;
  };
  connectOnlineTitle: {
    english: string;
    arabic: string;
  };
  connectOnlineDescription: {
    english: string;
    arabic: string;
  };
  links: {
    id: string;
    platform: string;
    url: string;
    enabled: boolean;
    icon: string;
    ariaLabel: {
      english: string;
      arabic: string;
    };
  }[];
}

interface SocialLinksSectionProps {
  socialLinks: SocialLinks;
  onUpdate: (path: string[], value: string | boolean) => void;
  onAddPlatform: () => void;
  onRemovePlatform: (id: string) => void;
}

export default function SocialLinksSection({ socialLinks, onUpdate, onAddPlatform, onRemovePlatform }: SocialLinksSectionProps) {
  const [selectedIconIndex, setSelectedIconIndex] = useState<{[key: string]: number}>({});
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconSet, setSelectedIconSet] = useState<string>('fa');
  const iconLibsCarouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLibsLeft, setCanScrollLibsLeft] = useState(false);
  const [canScrollLibsRight, setCanScrollLibsRight] = useState(false);

  // Safety check: Ensure socialLinks.links is always an array
  const safeLinks = Array.isArray(socialLinks?.links) ? socialLinks.links : [];

  // Icon libraries configuration
  const iconLibraries = [
    { id: 'fc', name: 'Flat Color', library: FcIcons },
    { id: 'fa', name: 'Font Awesome', library: FaIcons },
    { id: 'fa6', name: 'Font Awesome 6', library: FaIconsSolid },
    { id: 'bs', name: 'Bootstrap', library: BsIcons },
    { id: 'ri', name: 'Remix', library: RiIcons },
    { id: 'gi', name: 'Game Icons', library: GiIcons },
    { id: 'tb', name: 'Tabler', library: TbIcons },
    { id: 'md', name: 'Material Design', library: MdIcons },
    { id: 'hi', name: 'Heroicons', library: HiIcons },
    { id: 'ai', name: 'Ant Design', library: AiIcons },
    { id: 'io', name: 'Ionicons 4', library: IoIcons },
    { id: 'io5', name: 'Ionicons 5', library: Io5Icons },
    { id: 'pi', name: 'Phosphor', library: PiIcons },
  ];

  // Check if library has matching icons
  const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
    if (!searchTerm.trim()) return true;
    
    const library = iconLibraries.find(lib => lib.id === libraryId)?.library;
    if (!library) return false;
    
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).some(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    );
  };

  // Get icon component from icon name
  const getIconComponent = (iconName: string): React.ReactElement | null => {
    if (!iconName) return null;
    
    if (iconName.startsWith('Fc')) {
      const IconFc = FcIcons[iconName as keyof typeof FcIcons];
      return IconFc ? <IconFc className="text-3xl" /> : null;
    } else if (iconName.startsWith('Fa6')) {
      const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
      return IconFa6 ? <IconFa6 className="text-3xl" /> : null;
    } else if (iconName.startsWith('Fa')) {
      const IconFa = FaIcons[iconName as keyof typeof FaIcons];
      return IconFa ? <IconFa className="text-3xl" /> : null;
    } else if (iconName.startsWith('Bs')) {
      const IconBs = BsIcons[iconName as keyof typeof BsIcons];
      return IconBs ? <IconBs className="text-3xl" /> : null;
    } else if (iconName.startsWith('Ri')) {
      const IconRi = RiIcons[iconName as keyof typeof RiIcons];
      return IconRi ? <IconRi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Gi')) {
      const IconGi = GiIcons[iconName as keyof typeof GiIcons];
      return IconGi ? <IconGi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Tb')) {
      const IconTb = TbIcons[iconName as keyof typeof TbIcons];
      return IconTb ? <IconTb className="text-3xl" /> : null;
    } else if (iconName.startsWith('Md')) {
      const IconMd = MdIcons[iconName as keyof typeof MdIcons];
      return IconMd ? <IconMd className="text-3xl" /> : null;
    } else if (iconName.startsWith('Hi')) {
      const IconHi = HiIcons[iconName as keyof typeof HiIcons];
      return IconHi ? <IconHi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Ai')) {
      const IconAi = AiIcons[iconName as keyof typeof AiIcons];
      return IconAi ? <IconAi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Io5')) {
      const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
      return IconIo5 ? <IconIo5 className="text-3xl" /> : null;
    } else if (iconName.startsWith('Io')) {
      const IconIo = IoIcons[iconName as keyof typeof IoIcons];
      return IconIo ? <IconIo className="text-3xl" /> : null;
    } else if (iconName.startsWith('Pi')) {
      const IconPi = PiIcons[iconName as keyof typeof PiIcons];
      return IconPi ? <IconPi className="text-3xl" /> : null;
    }
    
    return null;
  };

  // Get filtered icons from library
  const getFilteredIcons = (library: any, searchTerm: string) => {
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).filter(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    ).slice(0, 50); // Limit to 50 icons for performance
  };

  // Scroll functions for icon library tabs
  const scrollLibsLeft = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollLibsRight = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  const checkLibsScrollPosition = () => {
    if (iconLibsCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = iconLibsCarouselRef.current;
      setCanScrollLibsLeft(scrollLeft > 0);
      setCanScrollLibsRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkLibsScrollPosition();
    const carousel = iconLibsCarouselRef.current;
    if (carousel) {
      carousel.addEventListener('scroll', checkLibsScrollPosition);
      return () => carousel.removeEventListener('scroll', checkLibsScrollPosition);
    }
  }, []);

  const handleIconSelect = (linkId: string, iconValue: string) => {
    onUpdate(['links', safeLinks.findIndex(l => l.id === linkId).toString(), 'icon'], iconValue);
    setSelectedIconIndex(prev => ({ ...prev, [linkId]: -1 }));
  };

  return (
    <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
      <h3 className="text-lg font-medium text-white mb-4 flex items-center">
        <div className="w-6 h-6 bg-red-500 rounded mr-2 flex items-center justify-center">
          <span className="text-white text-xs font-bold">5</span>
        </div>
        Social Media Links
      </h3>
      <p className="text-gray-400 text-sm mb-6">Configure social media presence and connection information with full customization</p>
      
      {/* Main Section Titles */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-6">
        <div className="space-y-4">
          <h4 className="text-md font-medium text-blue-400">English Content</h4>
          <div>
            <label className="block text-sm font-medium text-gray-300">Main Section Title</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.title.english}
              onChange={e => onUpdate(['title', 'english'], e.target.value)}
              placeholder="Connect With Us"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Main Description</label>
            <textarea
              rows={2}
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.description.english}
              onChange={e => onUpdate(['description', 'english'], e.target.value)}
              placeholder="Social media description..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Connect Online Title</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.connectOnlineTitle.english}
              onChange={e => onUpdate(['connectOnlineTitle', 'english'], e.target.value)}
              placeholder="Connect Online"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Connect Online Description</label>
            <textarea
              rows={2}
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.connectOnlineDescription.english}
              onChange={e => onUpdate(['connectOnlineDescription', 'english'], e.target.value)}
              placeholder="Connect online description..."
            />
          </div>
        </div>

        <div className="space-y-4">
          <h4 className="text-md font-medium text-blue-400">المحتوى العربي</h4>
          <div>
            <label className="block text-sm font-medium text-gray-300">عنوان القسم الرئيسي</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.title.arabic}
              onChange={e => onUpdate(['title', 'arabic'], e.target.value)}
              placeholder="تواصل معنا"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">الوصف الرئيسي</label>
            <textarea
              rows={2}
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.description.arabic}
              onChange={e => onUpdate(['description', 'arabic'], e.target.value)}
              placeholder="وصف وسائل التواصل الاجتماعي..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">عنوان التواصل الإلكتروني</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.connectOnlineTitle.arabic}
              onChange={e => onUpdate(['connectOnlineTitle', 'arabic'], e.target.value)}
              placeholder="تواصل إلكترونياً"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">وصف التواصل الإلكتروني</label>
            <textarea
              rows={2}
              dir="rtl"
              className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={socialLinks.connectOnlineDescription.arabic}
              onChange={e => onUpdate(['connectOnlineDescription', 'arabic'], e.target.value)}
              placeholder="وصف التواصل الإلكتروني..."
            />
          </div>
        </div>
      </div>

      {/* Social Media Platforms */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-md font-medium text-gray-300">Social Media Platforms</h4>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onAddPlatform();
            }}
            className="inline-flex items-center px-3 py-1 border border-green-500 rounded-md text-sm font-medium text-green-400 hover:bg-green-500 hover:text-white transition-colors"
          >
            <FiPlus className="mr-1 h-4 w-4" />
            Add Platform
          </button>
        </div>

        {safeLinks.map((link, index) => (
          <div key={link.id} className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h5 className="text-sm font-medium text-gray-200">Platform #{index + 1}</h5>
              {safeLinks.length > 1 && (
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onRemovePlatform(link.id);
                  }}
                  className="inline-flex items-center px-2 py-1 border border-red-500 rounded text-xs font-medium text-red-400 hover:bg-red-500 hover:text-white transition-colors"
                >
                  <FiTrash2 className="mr-1 h-3 w-3" />
                  Remove
                </button>
              )}
            </div>

            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-300">Platform Name</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={link.platform}
                  onChange={e => onUpdate(['links', index.toString(), 'platform'], e.target.value)}
                  placeholder="Facebook"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">URL</label>
                <input
                  type="url"
                  className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={link.url}
                  onChange={e => onUpdate(['links', index.toString(), 'url'], e.target.value)}
                  placeholder="https://..."
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-300">Aria Label (English)</label>
                <input
                  type="text"
                  className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={link.ariaLabel.english}
                  onChange={e => onUpdate(['links', index.toString(), 'ariaLabel', 'english'], e.target.value)}
                  placeholder="Facebook"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">Aria Label (Arabic)</label>
                <input
                  type="text"
                  dir="rtl"
                  className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={link.ariaLabel.arabic}
                  onChange={e => onUpdate(['links', index.toString(), 'ariaLabel', 'arabic'], e.target.value)}
                  placeholder="فيسبوك"
                />
              </div>
            </div>

            {/* Advanced Icon Selection */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-300">Icon</label>
                <button
                  onClick={() => setSelectedIconIndex(prev => ({ 
                    ...prev, 
                    [link.id]: prev[link.id] === -1 ? -1 : (prev[link.id] >= 0 ? -1 : 0)
                  }))}
                  className="inline-flex items-center px-2 py-1 border border-blue-500 rounded text-xs font-medium text-blue-400 hover:bg-blue-500 hover:text-white transition-colors"
                >
                  <FiEdit3 className="mr-1 h-3 w-3" />
                  {selectedIconIndex[link.id] >= 0 ? 'Close' : 'Change Icon'}
                </button>
              </div>
              
              {/* Current Icon Display */}
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  {link.icon ? (
                    <div className="text-white text-lg">
                      {getIconComponent(link.icon) || <span className="text-sm font-bold">{link.platform.charAt(0)}</span>}
                    </div>
                  ) : (
                    <span className="text-white text-sm font-bold">{link.platform.charAt(0)}</span>
                  )}
                </div>
                <span className="text-gray-300">Current: {link.icon || 'No icon selected'}</span>
              </div>

              {/* Advanced Icon Selection Panel */}
              {selectedIconIndex[link.id] >= 0 && (
                <div className="bg-gray-600 rounded-lg border border-gray-500 overflow-hidden">
                  {/* Icon Library Tabs */}
                  <div className="relative bg-gray-700 border-b border-gray-600">
                    <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                      <button
                        type="button"
                        onClick={scrollLibsLeft}
                        disabled={!canScrollLibsLeft}
                        className={`p-1 rounded-r transition-colors ${
                          canScrollLibsLeft 
                            ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                            : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                        }`}
                        aria-label="Scroll library tabs left"
                      >
                        <FiChevronLeft className="h-3 w-3" />
                      </button>
                    </div>
                    
                    <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                      <button
                        type="button"
                        onClick={scrollLibsRight}
                        disabled={!canScrollLibsRight}
                        className={`p-1 rounded-l transition-colors ${
                          canScrollLibsRight 
                            ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                            : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                        }`}
                        aria-label="Scroll library tabs right"
                      >
                        <FiChevronRight className="h-3 w-3" />
                      </button>
                    </div>

                    {/* Scrollable Tabs Container */}
                    <div 
                      ref={iconLibsCarouselRef}
                      className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
                      style={{
                        scrollbarWidth: 'none',
                        msOverflowStyle: 'none',
                      }}
                    >
                      {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                        <button
                          key={lib.id}
                          type="button"
                          onClick={() => setSelectedIconSet(lib.id)}
                          className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                            selectedIconSet === lib.id
                              ? 'bg-[#00C2FF] text-white'
                              : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                          }`}
                        >
                          {lib.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Search Input */}
                  <div className="p-2 border-b border-gray-600">
                    <div className="relative">
                      <FiSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 h-3 w-3" />
                      <input
                        type="text"
                        placeholder="Search icons..."
                        value={iconSearchTerm}
                        onChange={(e) => setIconSearchTerm(e.target.value)}
                        className="w-full pl-7 pr-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
                      />
                    </div>
                  </div>

                  {/* Icon Grid */}
                  <div className="p-2 max-h-64 overflow-y-auto">
                    <div className="grid grid-cols-6 gap-1">
                      {(() => {
                        const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                        if (!currentLibrary) return null;

                        return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                          const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary];
                          
                          return (
                            <button
                              key={iconName}
                              type="button"
                              onClick={() => {
                                handleIconSelect(link.id, iconName);
                              }}
                              className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors group"
                              title={iconName}
                            >
                              {IconComponent && React.createElement(IconComponent, {
                                className: "text-lg text-white group-hover:text-[#00C2FF] transition-colors"
                              })}
                            </button>
                          );
                        });
                      })()}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4 text-[#00C2FF] bg-gray-600 border-gray-500 rounded focus:ring-[#00C2FF]"
                  checked={link.enabled}
                  onChange={e => onUpdate(['links', index.toString(), 'enabled'], e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-300">Enabled</span>
              </label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 