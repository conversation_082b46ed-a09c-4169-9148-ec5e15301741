"use client";

import React, { useState, useMemo } from 'react';
import { 
  FiKey, FiSearch, FiSave, FiRefreshCw, FiShield, FiCheck, FiX,
  FiHome, FiMail, FiMonitor, FiInfo, FiGrid, FiFileText, FiPhone,
  FiSettings, FiUsers, FiEye, FiEdit, FiPlus, FiTrash2
} from 'react-icons/fi';

interface Role {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  permissions: string[];
  isSystemRole: boolean;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ComponentType<any>;
  subPermissions?: SubPermission[];
}

interface SubPermission {
  id: string;
  name: string;
  description: string;
}

// Mock roles data
const mockRoles: Role[] = [
  {
    id: '1',
    name: 'Super Admin',
    description: 'Full system access with all permissions',
    isActive: true,
    permissions: ['all'],
    isSystemRole: true
  },
  {
    id: '2',
    name: 'Admin',
    description: 'Administrative access to most system features',
    isActive: true,
    permissions: [
      'dashboard_full', 'inquiries_full', 'users_full', 'settings_full',
      'home_page_full', 'about_us_full', 'projects_page_full', 'articles_page_full', 'contact_page_full'
    ],
    isSystemRole: true
  },
  {
    id: '3',
    name: 'Manager',
    description: 'Management level access to sales and reporting',
    isActive: true,
    permissions: [
      'dashboard_view', 'inquiries_full', 'users_view', 'home_page_view', 'projects_page_view'
    ],
    isSystemRole: false
  },
  {
    id: '4',
    name: 'Sales Agent',
    description: 'Sales team access to client management and properties',
    isActive: true,
    permissions: [
      'dashboard_view', 'inquiries_manage', 'projects_page_view'
    ],
    isSystemRole: false
  },
  {
    id: '5',
    name: 'Content Editor',
    description: 'Content management for website pages and articles',
    isActive: true,
    permissions: [
      'dashboard_view', 'home_page_full', 'about_us_full', 'projects_page_full', 'articles_page_full', 'contact_page_full'
    ],
    isSystemRole: false
  },
  {
    id: '6',
    name: 'Client',
    description: 'Basic client access to personal dashboard',
    isActive: true,
    permissions: ['dashboard_view'],
    isSystemRole: true
  }
];

// Define all available permissions with categories
const availablePermissions: Permission[] = [
  // Main Section
  {
    id: 'dashboard',
    name: 'Dashboard',
    description: 'Access to main dashboard',
    category: 'Main',
    icon: FiHome,
    subPermissions: [
      { id: 'dashboard_view', name: 'View', description: 'View dashboard statistics and data' },
      { id: 'dashboard_full', name: 'Full Access', description: 'Full dashboard access including management' }
    ]
  },
  {
    id: 'inquiries',
    name: 'Inquiries',
    description: 'Manage customer inquiries and contact forms',
    category: 'Main',
    icon: FiMail,
    subPermissions: [
      { id: 'inquiries_view', name: 'View', description: 'View inquiries list and details' },
      { id: 'inquiries_manage', name: 'Manage', description: 'Respond to and manage inquiries' },
      { id: 'inquiries_full', name: 'Full Access', description: 'Full inquiries management including deletion' }
    ]
  },

  // Pages Section
  {
    id: 'home_page',
    name: 'Home Page',
    description: 'Manage home page content and sections',
    category: 'Pages',
    icon: FiMonitor,
    subPermissions: [
      { id: 'home_page_view', name: 'View', description: 'View home page content' },
      { id: 'home_page_edit', name: 'Edit', description: 'Edit home page sections' },
      { id: 'home_page_full', name: 'Full Access', description: 'Full home page management' }
    ]
  },
  {
    id: 'about_us',
    name: 'About Us Page',
    description: 'Manage about us page content',
    category: 'Pages',
    icon: FiInfo,
    subPermissions: [
      { id: 'about_us_view', name: 'View', description: 'View about us content' },
      { id: 'about_us_edit', name: 'Edit', description: 'Edit about us sections' },
      { id: 'about_us_full', name: 'Full Access', description: 'Full about us management' }
    ]
  },
  {
    id: 'projects_page',
    name: 'Projects Page',
    description: 'Manage projects and categories',
    category: 'Pages',
    icon: FiGrid,
    subPermissions: [
      { id: 'projects_page_view', name: 'View', description: 'View projects and categories' },
      { id: 'projects_page_edit', name: 'Edit', description: 'Edit projects and categories' },
      { id: 'projects_page_full', name: 'Full Access', description: 'Full projects management' }
    ]
  },
  {
    id: 'articles_page',
    name: 'Articles Page',
    description: 'Manage articles and blog content',
    category: 'Pages',
    icon: FiFileText,
    subPermissions: [
      { id: 'articles_page_view', name: 'View', description: 'View articles and categories' },
      { id: 'articles_page_edit', name: 'Edit', description: 'Edit articles and categories' },
      { id: 'articles_page_full', name: 'Full Access', description: 'Full articles management' }
    ]
  },
  {
    id: 'contact_page',
    name: 'Contact Us Page',
    description: 'Manage contact page content',
    category: 'Pages',
    icon: FiPhone,
    subPermissions: [
      { id: 'contact_page_view', name: 'View', description: 'View contact page content' },
      { id: 'contact_page_edit', name: 'Edit', description: 'Edit contact page sections' },
      { id: 'contact_page_full', name: 'Full Access', description: 'Full contact page management' }
    ]
  },

  // Settings Section
  {
    id: 'settings',
    name: 'Settings',
    description: 'System settings and configuration',
    category: 'Settings',
    icon: FiSettings,
    subPermissions: [
      { id: 'settings_view', name: 'View', description: 'View system settings' },
      { id: 'settings_edit', name: 'Edit', description: 'Edit system settings' },
      { id: 'settings_full', name: 'Full Access', description: 'Full settings management' }
    ]
  },
  {
    id: 'users',
    name: 'Users',
    description: 'User management and administration',
    category: 'Settings',
    icon: FiUsers,
    subPermissions: [
      { id: 'users_view', name: 'View', description: 'View users list and details' },
      { id: 'users_edit', name: 'Edit', description: 'Edit user information' },
      { id: 'users_full', name: 'Full Access', description: 'Full user management including creation/deletion' }
    ]
  },
  {
    id: 'authorization',
    name: 'Authorization',
    description: 'Role and permission management',
    category: 'Settings',
    icon: FiShield,
    subPermissions: [
      { id: 'authorization_view', name: 'View', description: 'View roles and permissions' },
      { id: 'authorization_edit', name: 'Edit', description: 'Edit roles and permissions' },
      { id: 'authorization_full', name: 'Full Access', description: 'Full authorization management' }
    ]
  }
];

export default function RolePermissionsPage() {
  const [roles] = useState<Role[]>(mockRoles);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [rolePermissions, setRolePermissions] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Filter permissions based on search
  const filteredPermissions = useMemo(() => {
    if (!searchTerm) return availablePermissions;
    
    return availablePermissions.filter(permission =>
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  // Group permissions by category
  const groupedPermissions = useMemo(() => {
    const groups: Record<string, Permission[]> = {};
    filteredPermissions.forEach(permission => {
      if (!groups[permission.category]) {
        groups[permission.category] = [];
      }
      groups[permission.category].push(permission);
    });
    return groups;
  }, [filteredPermissions]);

  const handleRoleSelect = (role: Role) => {
    if (hasChanges) {
      if (!confirm('You have unsaved changes. Are you sure you want to switch roles?')) {
        return;
      }
    }
    
    setSelectedRole(role);
    setRolePermissions([...role.permissions]);
    setHasChanges(false);
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    let newPermissions = [...rolePermissions];
    
    if (checked) {
      if (!newPermissions.includes(permissionId)) {
        newPermissions.push(permissionId);
      }
    } else {
      newPermissions = newPermissions.filter(p => p !== permissionId);
    }
    
    setRolePermissions(newPermissions);
    setHasChanges(true);
  };

  const handleSavePermissions = () => {
    if (!selectedRole) return;
    
    // In a real app, this would make an API call
    console.log('Saving permissions for role:', selectedRole.name, rolePermissions);
    
    // Update the role in the local state (in real app, this would be handled by state management)
    const updatedRole = { ...selectedRole, permissions: rolePermissions };
    setSelectedRole(updatedRole);
    setHasChanges(false);
    
    alert('Permissions saved successfully!');
  };

  const handleResetPermissions = () => {
    if (!selectedRole) return;
    
    setRolePermissions([...selectedRole.permissions]);
    setHasChanges(false);
  };

  const isPermissionChecked = (permissionId: string) => {
    if (selectedRole?.permissions.includes('all')) return true;
    return rolePermissions.includes(permissionId);
  };

  const isPermissionDisabled = (permissionId: string) => {
    return selectedRole?.permissions.includes('all') || selectedRole?.isSystemRole;
  };

  const getPermissionLevel = (permission: Permission) => {
    if (!selectedRole || !permission.subPermissions) return 'none';
    
    const hasView = rolePermissions.includes(permission.subPermissions.find(sp => sp.id.includes('_view'))?.id || '');
    const hasEdit = rolePermissions.includes(permission.subPermissions.find(sp => sp.id.includes('_edit'))?.id || '');
    const hasFull = rolePermissions.includes(permission.subPermissions.find(sp => sp.id.includes('_full'))?.id || '');
    
    if (hasFull) return 'full';
    if (hasEdit) return 'edit';
    if (hasView) return 'view';
    return 'none';
  };

  const setPermissionLevel = (permission: Permission, level: string) => {
    if (!permission.subPermissions) return;
    
    let newPermissions = [...rolePermissions];
    
    // Remove all existing permissions for this category
    permission.subPermissions.forEach(sp => {
      newPermissions = newPermissions.filter(p => p !== sp.id);
    });
    
    // Add the selected permission level
    if (level !== 'none') {
      const targetPermission = permission.subPermissions.find(sp => sp.id.includes(`_${level}`));
      if (targetPermission) {
        newPermissions.push(targetPermission.id);
      }
    }
    
    setRolePermissions(newPermissions);
    setHasChanges(true);
  };

  return (
    <div className="min-h-screen bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-white flex items-center">
              <FiKey className="mr-3 h-8 w-8 text-[#00C2FF]" />
              Role Permissions Manager
            </h1>
            <p className="text-gray-400 mt-1">Configure permissions for each role across all system features</p>
          </div>
          <div className="flex space-x-3">
            {hasChanges && (
              <>
                <button 
                  onClick={handleResetPermissions}
                  className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center"
                >
                  <FiX className="mr-2 h-4 w-4" />
                  Reset
                </button>
                <button 
                  onClick={handleSavePermissions}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors flex items-center"
                >
                  <FiSave className="mr-2 h-4 w-4" />
                  Save Changes
                </button>
              </>
            )}
            <button className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors flex items-center">
              <FiRefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </button>
          </div>
        </div>

        {hasChanges && (
          <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiEdit className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="ml-3">
                <p className="text-yellow-400 font-medium">You have unsaved changes</p>
                <p className="text-yellow-300 text-sm">Don't forget to save your permission changes.</p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Role Selection Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="p-4 border-b border-gray-700">
              <h2 className="text-lg font-semibold text-white">Select Role</h2>
            </div>
            <div className="p-4">
              <div className="space-y-2">
                {roles.filter(role => role.isActive).map((role) => (
                  <button
                    key={role.id}
                    onClick={() => handleRoleSelect(role)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedRole?.id === role.id
                        ? 'bg-[#00C2FF]/20 border border-[#00C2FF]/30 text-[#00C2FF]'
                        : 'bg-gray-700/30 hover:bg-gray-700/50 text-gray-300 hover:text-white'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{role.name}</p>
                        <p className="text-sm opacity-75">{role.description}</p>
                      </div>
                      {role.isSystemRole && (
                        <span className="px-2 py-1 bg-blue-400/10 text-blue-400 rounded text-xs font-medium border border-blue-400/20">
                          SYSTEM
                        </span>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Permissions Management */}
        <div className="lg:col-span-3">
          {!selectedRole ? (
            <div className="bg-gray-800 rounded-lg border border-gray-700 p-8 text-center">
              <FiShield className="mx-auto h-12 w-12 text-gray-500 mb-4" />
              <p className="text-gray-400 text-lg">Select a role to manage its permissions</p>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-lg border border-gray-700">
              <div className="p-4 border-b border-gray-700">
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-lg font-semibold text-white">
                      Permissions for: {selectedRole.name}
                    </h2>
                    <p className="text-gray-400 text-sm">{selectedRole.description}</p>
                  </div>
                  
                  {selectedRole.permissions.includes('all') && (
                    <span className="px-3 py-1 bg-green-400/10 text-green-400 rounded-full text-sm font-medium border border-green-400/20">
                      ALL PERMISSIONS
                    </span>
                  )}
                </div>

                {/* Search */}
                <div className="mt-4 relative">
                  <FiSearch className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search permissions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              <div className="p-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                {Object.entries(groupedPermissions).map(([category, permissions]) => (
                  <div key={category} className="mb-6">
                    <h3 className="text-white font-medium mb-3 flex items-center">
                      <span className="w-2 h-2 bg-[#00C2FF] rounded-full mr-2"></span>
                      {category}
                    </h3>
                    
                    <div className="space-y-3">
                      {permissions.map((permission) => {
                        const Icon = permission.icon;
                        const currentLevel = getPermissionLevel(permission);
                        
                        return (
                          <div
                            key={permission.id}
                            className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start space-x-3">
                                <Icon className="h-5 w-5 text-[#00C2FF] mt-0.5" />
                                <div>
                                  <h4 className="text-white font-medium">{permission.name}</h4>
                                  <p className="text-gray-400 text-sm">{permission.description}</p>
                                </div>
                              </div>
                              
                              {permission.subPermissions ? (
                                <div className="flex items-center space-x-2">
                                  <select
                                    value={currentLevel}
                                    onChange={(e) => setPermissionLevel(permission, e.target.value)}
                                    disabled={isPermissionDisabled(permission.id)}
                                    className="px-3 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-[#00C2FF] disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    <option value="none">No Access</option>
                                    <option value="view">View Only</option>
                                    <option value="edit">Edit</option>
                                    <option value="full">Full Access</option>
                                  </select>
                                </div>
                              ) : (
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    checked={isPermissionChecked(permission.id)}
                                    onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                                    disabled={isPermissionDisabled(permission.id)}
                                    className="rounded border-gray-600 text-[#00C2FF] focus:ring-[#00C2FF] focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                                  />
                                </label>
                              )}
                            </div>
                            
                            {permission.subPermissions && (
                              <div className="mt-3 ml-8 space-y-2">
                                {permission.subPermissions.map((subPermission) => (
                                  <div key={subPermission.id} className="flex items-center justify-between text-sm">
                                    <div>
                                      <span className="text-gray-300">{subPermission.name}</span>
                                      <span className="text-gray-500 ml-2">- {subPermission.description}</span>
                                    </div>
                                    <div className="flex items-center">
                                      {isPermissionChecked(subPermission.id) ? (
                                        <FiCheck className="h-4 w-4 text-green-400" />
                                      ) : (
                                        <FiX className="h-4 w-4 text-gray-500" />
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
                
                {Object.keys(groupedPermissions).length === 0 && (
                  <div className="text-center py-8">
                    <FiSearch className="mx-auto h-12 w-12 text-gray-500 mb-4" />
                    <p className="text-gray-400">No permissions found matching your search.</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 