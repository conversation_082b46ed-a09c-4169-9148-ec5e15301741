"use client";

import { useParams } from "next/navigation";

// Import hooks
import useScrollSpy from "@/hooks/useScrollSpy";
import useFloatingContact from "@/hooks/useFloatingContact";

// Import components
import ProjectNavigation from "@/components/projects/ProjectNavigation";
import ProjectHero from "@/components/projects/ProjectHero";
import ProjectOverview from "@/components/projects/ProjectOverview";
import ProjectGallery from "@/components/projects/ProjectGallery";
import ProjectAmenities from "@/components/projects/ProjectAmenities";
import ProjectLocation from "@/components/projects/ProjectLocation";
import ProjectInvestment from "@/components/projects/ProjectInvestment";
import ProjectProgress from "@/components/projects/ProjectProgress";
import ProjectInquiry from "@/components/projects/ProjectInquiry";


import FloatingContactButton from "@/components/common/FloatingContactButton";

// Import data
import { projects } from "@/data/projectsData";

export default function ProjectDetailPage() {
  const params = useParams();
  const slug = Array.isArray(params?.slug) ? params?.slug[0] : params?.slug as string;
  
  // Project data
  const project = projects.find((p) => p.slug === slug);
  
  // Handle project not found
  if (!project) {
    return null; // This will trigger Next.js notFound() automatically in production
  }
  
  // Custom hooks
  const { 
    activeTab, 
    isScrolled, 
    scrollToSection,
    overviewRef,
    galleryRef,
    amenitiesRef,
    locationRef,
    investmentRef,
    progressRef,
    inquiryRef
  } = useScrollSpy();
  
  const { isOpen, toggleOpen } = useFloatingContact();

  return (
    <div className="flex flex-col min-h-screen bg-[#f9fafb]">
      <ProjectHero project={project} scrollToSection={scrollToSection} />

      {/* Project Navigation - will be sticky when scrolled */}
      <ProjectNavigation 
        project={project} 
        activeTab={activeTab} 
        isScrolled={isScrolled} 
        scrollToSection={scrollToSection} 
      />

      {/* Transition to white content */}
      <div className="h-16 md:h-24 bg-gradient-to-b from-[#0D1526] to-[#f9fafb]"></div>

      <div className="container mx-auto px-4 py-16">
        <ProjectOverview project={project} ref={overviewRef} />
        <ProjectGallery project={project} ref={galleryRef} />
        <ProjectAmenities project={project} ref={amenitiesRef} />
        <ProjectLocation project={project} ref={locationRef} />
        <ProjectInvestment project={project} ref={investmentRef} />
        <ProjectProgress project={project} ref={progressRef} />
        <ProjectInquiry project={project} ref={inquiryRef} />
      </div>
      
      {/* Floating Contact Button for Mobile */}
      <FloatingContactButton 
        isOpen={isOpen}
        toggleOpen={toggleOpen}
        projectTitle={project.title}
        projectSlug={project.slug}
      />
    </div>
  );
} 