export interface SettingsData {
  general: {
    siteName: {
      en: string;
      ar: string;
    };
    siteDescription: {
      en: string;
      ar: string;
    };
    siteUrl: string;
    adminEmail: string;
    timezone: string;
    language: {
      default: 'en' | 'ar';
      enabled: string[];
    };
    maintenance: {
      enabled: boolean;
      message: {
        en: string;
        ar: string;
      };
    };
  };
  appearance: {
    logo: {
      favicon: string;
      appleTouchIcon: string;
    };
  };
  security: {
    twoFactorAuth: boolean;
    sessionTimeout: number;
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireNumbers: boolean;
      requireSymbols: boolean;
    };
    ipWhitelist: string[];
    rateLimiting: {
      enabled: boolean;
      maxRequests: number;
      timeWindow: number;
    };
    bruteForceProtection: {
      enabled: boolean;
      maxAttempts: number;
      lockoutDuration: number;
    };
    sslSettings: {
      enforceHttps: boolean;
      hstsEnabled: boolean;
    };
    auditLogging: {
      enabled: boolean;
      logFailedLogins: boolean;
      logAdminActions: boolean;
      retentionDays: number;
    };
  };
  integrations: {
    analytics: {
      googleAnalytics: {
        enabled: boolean;
        trackingId: string;
      };
      facebookPixel: {
        enabled: boolean;
        pixelId: string;
      };
      googleTagManager: {
        enabled: boolean;
        containerId: string;
      };
      hotjar: {
        enabled: boolean;
        siteId: string;
      };
    };
    maps: {
      googleMaps: {
        enabled: boolean;
        apiKey: string;
      };
      mapbox: {
        enabled: boolean;
        accessToken: string;
      };
    };
    chat: {
      whatsapp: {
        enabled: boolean;
        phoneNumber: string;
        welcomeMessage: {
          en: string;
          ar: string;
        };
      };
      tawk: {
        enabled: boolean;
        propertyId: string;
        widgetId: string;
      };
      intercom: {
        enabled: boolean;
        appId: string;
      };
      zendesk: {
        enabled: boolean;
        key: string;
      };
    };
    marketing: {
      mailchimp: {
        enabled: boolean;
        apiKey: string;
        audienceId: string;
      };
      hubspot: {
        enabled: boolean;
        portalId: string;
      };
      salesforce: {
        enabled: boolean;
        organizationId: string;
      };
    };
    social: {
      facebook: string;
      twitter: string;
      instagram: string;
      linkedin: string;
      youtube: string;
      tiktok: string;
      snapchat: string;
    };
    communication: {
      calendly: {
        enabled: boolean;
        username: string;
      };
      zoom: {
        enabled: boolean;
        meetingId: string;
      };
      googleMeet: {
        enabled: boolean;
        meetingLink: string;
      };
    };
    seo: {
      googleSearchConsole: {
        enabled: boolean;
        verificationCode: string;
      };
      bingWebmaster: {
        enabled: boolean;
        verificationCode: string;
      };
      yandexWebmaster: {
        enabled: boolean;
        verificationCode: string;
      };
    };
    email: {
      provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
      smtp: {
        host: string;
        port: number;
        username: string;
        password: string;
        encryption: 'tls' | 'ssl' | 'none';
      };
      sendgrid: {
        apiKey: string;
      };
      mailgun: {
        domain: string;
        apiKey: string;
      };
      ses: {
        accessKeyId: string;
        secretAccessKey: string;
        region: string;
      };
    };
  };
  seo: {
    general: {
      siteTitle: {
        en: string;
        ar: string;
      };
      siteDescription: {
        en: string;
        ar: string;
      };
      keywords: {
        en: string[];
        ar: string[];
      };
      canonicalUrl: string;
      hreflang: {
        enabled: boolean;
        defaultLanguage: 'en' | 'ar';
        alternateUrls: {
          en: string;
          ar: string;
        };
      };
    };
    metaTags: {
      robots: {
        index: boolean;
        follow: boolean;
        archive: boolean;
        snippet: boolean;
        imageIndex: boolean;
        maxSnippet: number;
        maxImagePreview: 'none' | 'standard' | 'large';
        maxVideoPreview: number;
      };
      openGraph: {
        enabled: boolean;
        type: 'website' | 'business.business';
        title: {
          en: string;
          ar: string;
        };
        description: {
          en: string;
          ar: string;
        };
        image: string;
        imageAlt: {
          en: string;
          ar: string;
        };
        url: string;
        siteName: {
          en: string;
          ar: string;
        };
        locale: {
          en: string;
          ar: string;
        };
      };
      twitter: {
        enabled: boolean;
        card: 'summary' | 'summary_large_image' | 'app' | 'player';
        site: string;
        creator: string;
        title: {
          en: string;
          ar: string;
        };
        description: {
          en: string;
          ar: string;
        };
        image: string;
        imageAlt: {
          en: string;
          ar: string;
        };
      };
      customMeta: {
        name: string;
        content: string;
        property?: string;
      }[];
    };
    structuredData: {
      enabled: boolean;
      organization: {
        enabled: boolean;
        name: {
          en: string;
          ar: string;
        };
        description: {
          en: string;
          ar: string;
        };
        url: string;
        logo: string;
        contactPoint: {
          telephone: string;
          contactType: string;
          areaServed: string[];
          availableLanguage: string[];
        };
        address: {
          streetAddress: {
            en: string;
            ar: string;
          };
          addressLocality: {
            en: string;
            ar: string;
          };
          addressRegion: {
            en: string;
            ar: string;
          };
          postalCode: string;
          addressCountry: string;
        };
        sameAs: string[];
      };
      realEstate: {
        enabled: boolean;
        businessType: 'RealEstateAgent' | 'RealEstateBusiness';
        priceRange: string;
        paymentAccepted: string[];
        currenciesAccepted: string[];
        openingHours: string[];
      };
      breadcrumbs: {
        enabled: boolean;
        separator: string;
        showHome: boolean;
      };
      faq: {
        enabled: boolean;
        questions: {
          question: {
            en: string;
            ar: string;
          };
          answer: {
            en: string;
            ar: string;
          };
        }[];
      };
    };
    sitemaps: {
      enabled: boolean;
      autoGenerate: boolean;
      includeImages: boolean;
      includeVideos: boolean;
      changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
      priority: number;
      excludePages: string[];
      customSitemaps: {
        name: string;
        url: string;
        type: 'xml' | 'txt';
      }[];
    };
    robotsTxt: {
      enabled: boolean;
      userAgent: string;
      disallow: string[];
      allow: string[];
      crawlDelay: number;
      sitemapUrl: string;
      customDirectives: {
        directive: string;
        value: string;
      }[];
    };
    analytics: {
      googleSearchConsole: {
        enabled: boolean;
        verificationCode: string;
        siteUrl: string;
      };
      bingWebmaster: {
        enabled: boolean;
        verificationCode: string;
      };
      yandexWebmaster: {
        enabled: boolean;
        verificationCode: string;
      };
      googleAnalytics: {
        enabled: boolean;
        trackingId: string;
        enhancedEcommerce: boolean;
      };
    };
    localSEO: {
      enabled: boolean;
      businessName: {
        en: string;
        ar: string;
      };
      businessType: string;
      address: {
        street: {
          en: string;
          ar: string;
        };
        city: {
          en: string;
          ar: string;
        };
        state: {
          en: string;
          ar: string;
        };
        zipCode: string;
        country: {
          en: string;
          ar: string;
        };
      };
      coordinates: {
        latitude: number;
        longitude: number;
      };
      phone: string;
      email: string;
      website: string;
      hours: {
        [key: string]: {
          open: string;
          close: string;
          closed: boolean;
        };
      };
      googleMyBusiness: {
        enabled: boolean;
        placeId: string;
        reviewsWidget: boolean;
      };
    };
    realEstateSpecific: {
      propertyTypes: string[];
      serviceAreas: {
        name: {
          en: string;
          ar: string;
        };
        coordinates: {
          latitude: number;
          longitude: number;
        };
        radius: number;
      }[];
      specializations: {
        en: string[];
        ar: string[];
      };
      certifications: string[];
      languages: string[];
    };
    monitoring: {
      enabled: boolean;
      keywordTracking: {
        enabled: boolean;
        keywords: {
          keyword: string;
          language: 'en' | 'ar';
          targetUrl: string;
          priority: 'high' | 'medium' | 'low';
        }[];
      };
      competitorAnalysis: {
        enabled: boolean;
        competitors: {
          name: string;
          url: string;
          trackKeywords: boolean;
        }[];
      };
      rankingReports: {
        enabled: boolean;
        frequency: 'daily' | 'weekly' | 'monthly';
        recipients: string[];
      };
    };
  };
  performance: {
    caching: {
      enabled: boolean;
      strategy: 'memory' | 'redis' | 'file' | 'hybrid';
      duration: {
        pages: number;
        api: number;
        static: number;
        database: number;
      };
      redis: {
        host: string;
        port: number;
        password: string;
        database: number;
      };
      purgeRules: {
        autoOnUpdate: boolean;
        scheduledPurge: boolean;
        purgeFrequency: 'hourly' | 'daily' | 'weekly';
      };
    };
    compression: {
      enabled: boolean;
      algorithm: 'gzip' | 'brotli' | 'deflate';
      level: number;
      minSize: number;
      types: string[];
    };
    cdn: {
      enabled: boolean;
      provider: 'cloudflare' | 'aws' | 'azure' | 'google' | 'custom';
      cloudflare: {
        zoneId: string;
        apiKey: string;
        email: string;
      };
      aws: {
        distributionId: string;
        accessKeyId: string;
        secretAccessKey: string;
        region: string;
      };
      azure: {
        profileName: string;
        endpointName: string;
        resourceGroup: string;
        subscriptionId: string;
      };
      google: {
        projectId: string;
        keyFile: string;
      };
      custom: {
        url: string;
        apiKey: string;
      };
      settings: {
        minify: {
          html: boolean;
          css: boolean;
          js: boolean;
        };
        browserCache: number;
        edgeCache: number;
      };
    };
    imageOptimization: {
      enabled: boolean;
      quality: {
        jpeg: number;
        webp: number;
        png: number;
      };
      formats: {
        webp: boolean;
        avif: boolean;
        jpeg: boolean;
        png: boolean;
        gif: boolean;
        svg: boolean;
      };
      responsive: {
        enabled: boolean;
        breakpoints: number[];
      };
      lazy: {
        enabled: boolean;
        threshold: number;
      };
      processing: {
        resize: boolean;
        crop: boolean;
        watermark: boolean;
        progressive: boolean;
      };
    };
    database: {
      optimization: {
        enabled: boolean;
        indexOptimization: boolean;
        queryOptimization: boolean;
        connectionPooling: boolean;
      };
      caching: {
        enabled: boolean;
        queryCache: boolean;
        resultCache: boolean;
        cacheDuration: number;
      };
      cleanup: {
        enabled: boolean;
        oldLogs: boolean;
        tempFiles: boolean;
        frequency: 'daily' | 'weekly' | 'monthly';
      };
    };
    realEstate: {
      propertyImages: {
        preload: boolean;
        thumbnailGeneration: boolean;
        virtualTourOptimization: boolean;
      };
      maps: {
        tileCache: boolean;
        vectorOptimization: boolean;
        clusterMarkers: boolean;
      };
      search: {
        indexing: boolean;
        facetedSearch: boolean;
        autoComplete: boolean;
      };
    };
    monitoring: {
      enabled: boolean;
      metrics: {
        pageSpeed: boolean;
        coreWebVitals: boolean;
        serverResponse: boolean;
        databaseQueries: boolean;
      };
      alerts: {
        enabled: boolean;
        thresholds: {
          pageLoadTime: number;
          serverResponseTime: number;
          errorRate: number;
        };
        notifications: {
          email: string[];
          webhook: string;
        };
      };
      reporting: {
        enabled: boolean;
        frequency: 'daily' | 'weekly' | 'monthly';
        recipients: string[];
      };
    };
    advanced: {
      preloading: {
        enabled: boolean;
        criticalResources: boolean;
        nextPagePrediction: boolean;
      };
      serviceWorker: {
        enabled: boolean;
        cacheStrategy: 'cache-first' | 'network-first' | 'stale-while-revalidate';
        offlineSupport: boolean;
      };
      http2: {
        enabled: boolean;
        serverPush: boolean;
      };
      security: {
        contentSecurityPolicy: boolean;
        hsts: boolean;
        xssProtection: boolean;
      };
    };
  };
  backup: {
    autoBackup: {
      enabled: boolean;
      frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
      time: string;
      retention: number;
      maxBackups: number;
    };
    backupTypes: {
      database: boolean;
      files: boolean;
      media: boolean;
      configurations: boolean;
      userUploads: boolean;
    };
    storage: {
      local: {
        enabled: boolean;
        path: string;
        maxSize: number;
      };
      cloud: {
        primary: 'aws' | 'google' | 'azure' | 'dropbox' | 'onedrive';
        aws: {
          enabled: boolean;
          bucket: string;
          region: string;
          accessKeyId: string;
          secretAccessKey: string;
          storageClass: 'STANDARD' | 'STANDARD_IA' | 'GLACIER' | 'DEEP_ARCHIVE';
        };
        google: {
          enabled: boolean;
          bucket: string;
          projectId: string;
          keyFile: string;
          storageClass: 'STANDARD' | 'NEARLINE' | 'COLDLINE' | 'ARCHIVE';
        };
        azure: {
          enabled: boolean;
          containerName: string;
          accountName: string;
          accountKey: string;
          tier: 'Hot' | 'Cool' | 'Archive';
        };
        dropbox: {
          enabled: boolean;
          accessToken: string;
          appKey: string;
        };
        onedrive: {
          enabled: boolean;
          clientId: string;
          clientSecret: string;
          tenantId: string;
        };
      };
    };
    compression: {
      enabled: boolean;
      level: number;
      format: 'zip' | 'tar.gz' | '7z';
    };
    encryption: {
      enabled: boolean;
      algorithm: 'AES-256' | 'AES-128';
      password: string;
    };
    notifications: {
      email: {
        enabled: boolean;
        recipients: string[];
        onSuccess: boolean;
        onFailure: boolean;
        onWarning: boolean;
      };
      webhook: {
        enabled: boolean;
        url: string;
        secret: string;
      };
    };
    monitoring: {
      healthCheck: boolean;
      integrityCheck: boolean;
      performanceMetrics: boolean;
      alertThresholds: {
        backupSize: number;
        duration: number;
        failureCount: number;
      };
    };
    restore: {
      pointInTimeRecovery: boolean;
      incrementalBackups: boolean;
      testRestores: {
        enabled: boolean;
        frequency: 'weekly' | 'monthly';
      };
    };
  };
}

export interface TabConfig {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
} 