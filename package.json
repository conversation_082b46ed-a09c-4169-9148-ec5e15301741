{"name": "mazaya-website-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/postcss": "^4.1.6", "@tiptap/extension-bubble-menu": "^2.12.0", "@tiptap/extension-character-count": "^2.12.0", "@tiptap/extension-code-block": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-dropcursor": "^2.12.0", "@tiptap/extension-floating-menu": "^2.12.0", "@tiptap/extension-focus": "^2.12.0", "@tiptap/extension-font-family": "^2.12.0", "@tiptap/extension-gapcursor": "^2.12.0", "@tiptap/extension-hard-break": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-horizontal-rule": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-mention": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.12.0", "@tiptap/extension-typography": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/extension-youtube": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "clsx": "^2.1.1", "docx": "^8.2.3", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.12.2", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "is-hotkey": "^0.2.0", "jspdf": "^3.0.1", "katex": "^0.16.8", "marked": "^9.1.2", "next": "^15.3.2", "next-i18next": "^15.4.2", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-countup": "^6.5.3", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-icons": "^4.11.0", "slate": "^0.100.0", "slate-history": "^0.100.0", "slate-hyperscript": "^0.100.0", "slate-react": "^0.100.0", "socket.io-client": "^4.7.2", "turndown": "^7.2.0", "y-indexeddb": "^9.0.12", "y-websocket": "^1.5.0", "yjs": "^13.6.8"}, "devDependencies": {"@types/node": "^20.8.10", "@types/react": "^18.2.36", "@types/react-dom": "^18.2.14", "@types/turndown": "^5.0.5", "autoprefixer": "^10.4.21", "eslint": "^8.53.0", "eslint-config-next": "^14.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.2.2"}}