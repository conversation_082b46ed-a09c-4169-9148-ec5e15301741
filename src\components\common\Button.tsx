"use client";

import { forwardRef } from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  href?: string;
  external?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      children,
      variant = "primary",
      size = "md",
      isLoading = false,
      href,
      external = false,
      ...props
    },
    ref
  ) => {
    const baseStyles = "inline-flex items-center justify-center font-medium transition-colors rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[rgb(var(--color-primary))] disabled:opacity-50 disabled:pointer-events-none";

    const variants = {
      primary: "bg-[rgb(var(--color-primary))] text-white hover:bg-[rgb(var(--color-primary-dark))]",
      secondary: "bg-gray-100 text-gray-900 hover:bg-gray-200",
      outline: "bg-transparent border border-[rgb(var(--color-primary))] text-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary))]/10",
      ghost: "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
    };

    const sizes = {
      sm: "text-sm px-3 py-1.5",
      md: "text-base px-4 py-2",
      lg: "text-lg px-6 py-3",
    };

    const allStyles = cn(baseStyles, variants[variant], sizes[size], className);

    // If it's a link, use Next.js Link component
    if (href) {
      const linkProps = external
        ? {
            href,
            target: "_blank",
            rel: "noopener noreferrer",
          }
        : { href };

      return (
        <Link {...linkProps} className={allStyles}>
          {isLoading ? (
            <div className="me-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
          ) : null}
          {children}
        </Link>
      );
    }

    // Otherwise, render as a button
    return (
      <button ref={ref} className={allStyles} disabled={isLoading} {...props}>
        {isLoading ? (
          <div className="me-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
        ) : null}
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;