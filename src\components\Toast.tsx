"use client";

import React, { useEffect, useState } from 'react';
import { FiCheckCircle, FiAlertCircle, FiInfo, FiX } from 'react-icons/fi';

export interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ 
  id, 
  type, 
  title, 
  message, 
  duration = 5000, 
  onClose 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose(id);
    }, 300);
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <FiCheckCircle className="h-5 w-5 text-green-400" />;
      case 'error':
        return <FiAlertCircle className="h-5 w-5 text-red-400" />;
      case 'warning':
        return <FiAlertCircle className="h-5 w-5 text-yellow-400" />;
      case 'info':
        return <FiInfo className="h-5 w-5 text-blue-400" />;
      default:
        return <FiInfo className="h-5 w-5 text-gray-400" />;
    }
  };

  const getColorClasses = () => {
    switch (type) {
      case 'success':
        return 'border-green-500/30 bg-green-900/20 shadow-green-500/10';
      case 'error':
        return 'border-red-500/30 bg-red-900/20 shadow-red-500/10';
      case 'warning':
        return 'border-yellow-500/30 bg-yellow-900/20 shadow-yellow-500/10';
      case 'info':
        return 'border-blue-500/30 bg-blue-900/20 shadow-blue-500/10';
      default:
        return 'border-gray-500/30 bg-gray-900/20 shadow-gray-500/10';
    }
  };

  return (
    <div
      className={`
        relative flex items-start p-4 mb-3 rounded-lg border backdrop-blur-sm
        transition-all duration-300 ease-in-out transform
        ${getColorClasses()}
        ${isVisible && !isLeaving 
          ? 'translate-x-0 opacity-100 scale-100' 
          : 'translate-x-full opacity-0 scale-95'
        }
        shadow-lg max-w-sm w-full min-w-[320px]
      `}
    >
      {/* Icon */}
      <div className="flex-shrink-0 mr-3 mt-0.5">
        {getIcon()}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-semibold text-white mb-1 leading-tight">
          {title}
        </h4>
        {message && (
          <p className="text-sm text-gray-300 leading-relaxed">
            {message}
          </p>
        )}
      </div>

      {/* Close Button */}
      <button
        onClick={handleClose}
        className="flex-shrink-0 ml-3 p-1 rounded-md hover:bg-white/10 transition-colors duration-200"
        aria-label="Close notification"
      >
        <FiX className="h-4 w-4 text-gray-400 hover:text-white transition-colors" />
      </button>

      {/* Progress Bar */}
      {duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/10 rounded-b-lg overflow-hidden">
          <div
            className={`h-full transition-all ease-linear ${
              type === 'success' ? 'bg-green-400' :
              type === 'error' ? 'bg-red-400' :
              type === 'warning' ? 'bg-yellow-400' :
              'bg-blue-400'
            }`}
            style={{
              animation: `shrink ${duration}ms linear forwards`,
            }}
          />
        </div>
      )}

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

export default Toast; 