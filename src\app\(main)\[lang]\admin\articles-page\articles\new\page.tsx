"use client";

import { useState } from 'react';
import Link from 'next/link';
import { FiSave, FiUpload, FiEye, FiX, FiCalendar, FiTag, FiUser, FiArrowLeft, FiPlus } from 'react-icons/fi';
import Image from 'next/image';
import dynamic from 'next/dynamic';
import ArticleSEOSettings from '@/components/ui/ArticleSEOSettings';

// Dynamically import RichTextEditor to avoid SSR issues
const RichTextEditor = dynamic(() => import('@/components/ui/RichTextEditor'), { ssr: false });

// Interface for new article
interface NewArticleData {
  title: {
    english: string;
    arabic: string;
  };
  slug: {
    english: string;
    arabic: string;
  };
  excerpt: {
    english: string;
    arabic: string;
  };
  content: {
    english: string;
    arabic: string;
  };
  featuredImage: string;
  categoryId: number | null;
  authorId: number | null;
  status: 'draft' | 'published' | 'archived';
  publishedAt: string | null;
  tags: string[];
  seo: {
    // Basic Meta
    title: {
      english: string;
      arabic: string;
    };
    description: {
      english: string;
      arabic: string;
    };
    keywords: {
      english: string[];
      arabic: string[];
    };
    
    // Focus Keywords for analysis
    focusKeywords: {
      primary: string;
      secondary: string[];
    };
    
    // Article Schema
    schema: {
      enabled: boolean;
      type: 'Article' | 'BlogPosting' | 'NewsArticle';
      customSchema: string;
    };
    
    // Images
    featuredImage: {
      url: string;
      alt: {
        english: string;
        arabic: string;
      };
      caption: {
        english: string;
        arabic: string;
      };
    };
    
    // Content optimization
    contentOptimization: {
      targetWordCount: number;
      readabilityTarget: 'easy' | 'medium' | 'hard';
      headingStructure: boolean;
      internalLinks: {
        url: string;
        anchor: string;
        language: 'english' | 'arabic';
      }[];
    };
    
    // FAQ Schema
    faqSchema: {
      enabled: boolean;
      questions: {
        question: string;
        answer: string;
        language: 'english' | 'arabic';
      }[];
    };
    
    // Breadcrumbs
    breadcrumbs: {
      enabled: boolean;
      items: {
        name: string;
        url: string;
      }[];
    };
    
    // Technical
    canonical: string;
    robots: {
      index: boolean;
      follow: boolean;
    };
  };
}

// Mock data
const MOCK_CATEGORIES = [
  { id: 1, name: "Real Estate News" },
  { id: 2, name: "Investment Tips" },
  { id: 3, name: "Market Analysis" },
  { id: 4, name: "Property Guides" },
  { id: 5, name: "Industry Insights" }
];

const MOCK_AUTHORS = [
  { id: 1, name: "John Smith" },
  { id: 2, name: "Sarah Johnson" },
  { id: 3, name: "Ahmed Al-Rashid" },
  { id: 4, name: "Maria Garcia" }
];

export default function NewArticlePage() {
  const [articleData, setArticleData] = useState<NewArticleData>({
    title: {
      english: "",
      arabic: ""
    },
    slug: {
      english: "",
      arabic: ""
    },
    excerpt: {
      english: "",
      arabic: ""
    },
    content: {
      english: "",
      arabic: ""
    },
    featuredImage: "",
    categoryId: null,
    authorId: null,
    status: "draft",
    publishedAt: null,
    tags: [],
    seo: {
      title: {
        english: "",
        arabic: ""
      },
      description: {
        english: "",
        arabic: ""
      },
      keywords: {
        english: [],
        arabic: []
      },
      focusKeywords: {
        primary: "",
        secondary: []
      },
      schema: {
        enabled: false,
        type: "Article",
        customSchema: ""
      },
      featuredImage: {
        url: "",
        alt: {
          english: "",
          arabic: ""
        },
        caption: {
          english: "",
          arabic: ""
        }
      },
      contentOptimization: {
        targetWordCount: 300,
        readabilityTarget: "medium",
        headingStructure: true,
        internalLinks: []
      },
      faqSchema: {
        enabled: false,
        questions: []
      },
      breadcrumbs: {
        enabled: false,
        items: []
      },
      canonical: "",
      robots: {
        index: true,
        follow: true
      }
    }
  });

  const [newTag, setNewTag] = useState('');
  const [showPreview, setShowPreview] = useState(false);

  // Generate slug from title
  const generateSlug = (title: string, isArabic: boolean = false) => {
    if (isArabic) {
      return title.toLowerCase().replace(/\s+/g, '-');
    }
    return title.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-').replace(/^-|-$/g, '');
  };

  // Handle input changes
  const handleInputChange = (section: string, field: string, value: string | number | null, language?: 'english' | 'arabic') => {
    setArticleData(prev => {
      const newData = { ...prev };
      
      if (language && section === 'seo') {
        (newData as any)[section][field][language] = value;
      } else if (language) {
        (newData as any)[section][language] = value;
        // Auto-generate slug when title changes
        if (field === 'title' && value && typeof value === 'string') {
          (newData as any).slug[language] = generateSlug(value, language === 'arabic');
        }
      } else {
        (newData as any)[section] = value;
      }
      
      return newData;
    });
  };

  // Handle SEO changes via the ArticleSEOSettings component
  const handleSEODataChange = (newSEOData: any) => {
    setArticleData(prev => ({
      ...prev,
      seo: newSEOData
    }));
  };

  // Add tag
  const addTag = () => {
    const tag = newTag.trim();
    if (tag && !articleData.tags.includes(tag)) {
      setArticleData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setNewTag('');
    }
  };

  // Remove tag
  const removeTag = (tag: string) => {
    setArticleData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setArticleData(prev => ({ 
          ...prev, 
          featuredImage: e.target?.result as string 
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle save
  const handleSave = () => {
    // Validation
    if (!articleData.title.english || !articleData.title.arabic) {
      alert('Please fill in both English and Arabic titles');
      return;
    }
    
    if (!articleData.categoryId || !articleData.authorId) {
      alert('Please select category and author');
      return;
    }

    // Set published date if status is published
    const finalData = {
      ...articleData,
      publishedAt: articleData.status === 'published' && !articleData.publishedAt 
        ? new Date().toISOString() 
        : articleData.publishedAt
    };

    console.log('Saving new article:', finalData);
    alert('Article saved successfully!');
    // Here you would typically navigate back to the articles list
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-900/50 text-green-400';
      case 'draft':
        return 'bg-yellow-900/50 text-yellow-400';
      case 'archived':
        return 'bg-gray-900/50 text-gray-400';
      default:
        return 'bg-gray-900/50 text-gray-400';
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <div className="flex items-center space-x-3">
            <Link
              href="/admin/articles-page/articles"
              className="inline-flex items-center text-gray-400 hover:text-white"
            >
              <FiArrowLeft className="h-5 w-5 mr-1" />
              Back to Articles
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-white mt-2">Create New Article</h1>
          <p className="mt-2 text-sm text-gray-400">
            Add a new article with bilingual content and SEO settings
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            Preview
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Save Article
          </button>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Article Preview</h3>
                  <button
                    onClick={() => setShowPreview(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                
                <div className="prose max-w-none">
                  <div className="mb-4">
                    {articleData.featuredImage && (
                      <Image
                        src={articleData.featuredImage}
                        alt="Featured"
                        width={800}
                        height={400}
                        className="w-full h-64 object-cover rounded-lg"
                      />
                    )}
                  </div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {articleData.title.english || 'Article Title'}
                  </h1>
                  <p className="text-lg text-gray-600 mb-4">
                    {articleData.excerpt.english || 'Article excerpt...'}
                  </p>
                  <div className="text-gray-800">
                    {articleData.content.english || 'Article content will appear here...'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Content - 2 columns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Title & Slug */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Title & URL</h3>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div className="space-y-4">
                <h4 className="text-md font-medium text-blue-400">English</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-300">Title *</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.title.english}
                    onChange={e => handleInputChange('title', 'title', e.target.value, 'english')}
                    placeholder="Enter article title"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">URL Slug</label>
                  <input
                    type="text"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.slug.english}
                    onChange={e => handleInputChange('slug', 'slug', e.target.value, 'english')}
                    placeholder="auto-generated"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <h4 className="text-md font-medium text-blue-400">العربية</h4>
                <div>
                  <label className="block text-sm font-medium text-gray-300">العنوان *</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.title.arabic}
                    onChange={e => handleInputChange('title', 'title', e.target.value, 'arabic')}
                    placeholder="أدخل عنوان المقال"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300">الرابط</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.slug.arabic}
                    onChange={e => handleInputChange('slug', 'slug', e.target.value, 'arabic')}
                    placeholder="تلقائي"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Excerpt */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Excerpt</h3>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-300">English Excerpt</label>
                <textarea
                  rows={4}
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.excerpt.english}
                  onChange={e => handleInputChange('excerpt', 'excerpt', e.target.value, 'english')}
                  placeholder="Brief description of the article..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300">الملخص العربي</label>
                <textarea
                  rows={4}
                  dir="rtl"
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.excerpt.arabic}
                  onChange={e => handleInputChange('excerpt', 'excerpt', e.target.value, 'arabic')}
                  placeholder="وصف مختصر للمقال..."
                />
              </div>
            </div>
          </div>

          {/* Content */}          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">            <h3 className="text-lg font-medium text-white mb-4">Content</h3>            <div className="space-y-6">              <div>                <label className="block text-sm font-medium text-gray-300 mb-2">English Content</label>                <RichTextEditor                  content={articleData.content.english}                  onChange={(value) => handleInputChange('content', 'content', value, 'english')}                  placeholder="Write the full article content here..."                  direction="ltr"                />              </div>              <div>                <label className="block text-sm font-medium text-gray-300 mb-2">المحتوى العربي</label>                <RichTextEditor                  content={articleData.content.arabic}                  onChange={(value) => handleInputChange('content', 'content', value, 'arabic')}                  placeholder="اكتب محتوى المقال الكامل هنا..."                  direction="rtl"                />              </div>            </div>          </div>

          {/* SEO Settings */}
          <ArticleSEOSettings 
            seoData={articleData.seo}
            onSEOChange={handleSEODataChange}
            content={articleData.content.english || articleData.content.arabic || ''}
          />
        </div>

        {/* Sidebar - 1 column */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center">
              <FiCalendar className="mr-2 h-5 w-5 text-green-500" />
              Publish Settings
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300">Status</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.status}
                  onChange={e => handleInputChange('status', 'status', e.target.value as 'draft' | 'published' | 'archived')}
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                  <option value="archived">Archived</option>
                </select>
                <span className={`mt-2 inline-flex px-2 py-1 text-xs leading-5 font-semibold rounded-full ${getStatusColor(articleData.status)}`}>
                  {articleData.status}
                </span>
              </div>
              
              {articleData.status === 'published' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300">Publish Date</label>
                  <input
                    type="datetime-local"
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={articleData.publishedAt ? articleData.publishedAt.slice(0, 16) : ''}
                    onChange={e => handleInputChange('publishedAt', 'publishedAt', e.target.value ? new Date(e.target.value).toISOString() : null)}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Category & Author */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center">
              <FiTag className="mr-2 h-5 w-5 text-purple-500" />
              Classification
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300">Category *</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.categoryId || ''}
                  onChange={e => handleInputChange('categoryId', 'categoryId', parseInt(e.target.value) || null)}
                >
                  <option value="">Select Category</option>
                  {MOCK_CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300">Author *</label>
                <select
                  className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={articleData.authorId || ''}
                  onChange={e => handleInputChange('authorId', 'authorId', parseInt(e.target.value) || null)}
                >
                  <option value="">Select Author</option>
                  {MOCK_AUTHORS.map(author => (
                    <option key={author.id} value={author.id}>
                      {author.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Featured Image</h3>
            <div className="space-y-4">
              <div>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="featured-upload"
                />
                <label
                  htmlFor="featured-upload"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF] w-full justify-center"
                >
                  <FiUpload className="-ml-1 mr-2 h-5 w-5" />
                  Upload Image
                </label>
              </div>
              
              {articleData.featuredImage && (
                <div className="mt-4">
                  <Image
                    src={articleData.featuredImage}
                    alt="Featured image"
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
            <h3 className="text-lg font-medium text-white mb-4">Tags</h3>
            <div className="space-y-4">
              <div className="flex">
                <input
                  type="text"
                  className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={newTag}
                  onChange={e => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  onKeyPress={e => e.key === 'Enter' && addTag()}
                />
                <button
                  onClick={addTag}
                  className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                >
                  <FiPlus className="h-4 w-4" />
                </button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {articleData.tags.map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                  >
                    {tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-1 text-gray-400 hover:text-white"
                    >
                      <FiX className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 