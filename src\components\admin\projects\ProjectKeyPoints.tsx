import React, { useState } from 'react';
import { FiPlus, FiX } from 'react-icons/fi';

interface ProjectKeyPointsProps {
  language: 'en' | 'ar';
  getCurrentContent: () => any;
  addItem: (array: string, value: string | File | null) => void;
  removeItem: (array: string, index: number) => void;
}

const ProjectKeyPoints: React.FC<ProjectKeyPointsProps> = ({ 
  language, 
  getCurrentContent, 
  addItem, 
  removeItem 
}) => {
  const [newKeyPoint, setNewKeyPoint] = useState('');

  return (
    <div className="mt-6">
      <h3 className="text-md font-medium text-gray-300">
        {language === 'en' ? 'Key Points' : 'النقاط الرئيسية'}
      </h3>
      
      <div className="mb-4 mt-3">
        <div className="flex space-x-2">
          <input
            type="text"
            value={newKeyPoint}
            onChange={(e) => setNewKeyPoint(e.target.value)}
            className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
            placeholder={language === 'en' ? "Add a key point about the project" : "أضف نقطة رئيسية حول المشروع"}
            dir={language === 'ar' ? 'rtl' : 'ltr'}
          />
          <button
            type="button"
            onClick={() => {
              addItem('keyPoints', newKeyPoint);
              setNewKeyPoint('');
            }}
            className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiPlus className="h-4 w-4" />
          </button>
        </div>
      </div>
      
      {getCurrentContent().keyPoints.length === 0 ? (
        <p className="text-sm text-gray-400 italic">
          {language === 'en' 
            ? 'No key points added yet. These are the main selling points that will be highlighted.'
            : 'لم تتم إضافة نقاط رئيسية بعد. هذه هي نقاط البيع الرئيسية التي سيتم تسليط الضوء عليها.'}
        </p>
      ) : (
        <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
          {getCurrentContent().keyPoints.map((keyPoint: string, index: number) => (
            <div key={index} className="flex justify-between items-center bg-gray-700 px-3 py-2 rounded-md">
              <span className="text-sm text-gray-300">{keyPoint}</span>
              <button
                type="button"
                onClick={() => removeItem('keyPoints', index)}
                className="text-gray-400 hover:text-red-500"
              >
                <FiX className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectKeyPoints; 