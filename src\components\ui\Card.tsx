import React from 'react';

type CardProps = {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'outline' | 'gradient' | 'glass';
  hoverable?: boolean;
  glowEffect?: 'none' | 'normal' | 'strong' | 'pulse';
};

const Card = ({
  children,
  className = '',
  variant = 'default',
  hoverable = false,
  glowEffect = 'none',
  ...props
}: CardProps & React.HTMLAttributes<HTMLDivElement>) => {
  // Base styles - Updated to match hero section cards
  const baseStyles = "overflow-hidden";
  
  // Variant styles - Updated to match hero design language
  const variantStyles = {
    default: "rounded-xl bg-white/5 backdrop-blur-md border border-white/10",
    outline: "rounded-xl bg-white/5 backdrop-blur-md border border-white/20",
    gradient: "rounded-xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-white/10 backdrop-blur-md",
    glass: "rounded-xl bg-white/10 backdrop-blur-md border border-white/20", // Match Hero card style
  };
  
  // Hover styles - Simplified with no glow
  const hoverStyles = hoverable 
    ? "transition-all duration-300 hover:border-white/30 hover:translate-y-[-2px]" 
    : "";
  
  // Glow effect styles - All set to empty to remove glow effects
  const glowStyles = {
    none: "",
    normal: "",
    strong: "",
    pulse: "",
  };
  
  // Combined styles
  const cardStyles = `${baseStyles} ${variantStyles[variant]} ${hoverStyles} ${glowStyles[glowEffect]} ${className}`;
  
  return (
    <div className={cardStyles} {...props}>
      {children}
    </div>
  );
};

export default Card; 