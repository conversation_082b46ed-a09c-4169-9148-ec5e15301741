"use client";

import React, { useState } from 'react';
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiImage, FiUpload, <PERSON>Eye, FiLink } from 'react-icons/fi';

interface HeroImage {
  id: string;
  url: string;
  alt: {
    en: string;
    ar: string;
  };
  position: 'first' | 'second' | 'third';
  rotation: string;
}

interface AboutHeroSection {
  title: {
    about: {
      en: string;
      ar: string;
    };
    mazaya: {
      en: string;
      ar: string;
    };
    capital: {
      en: string;
      ar: string;
    };
  };
  description: {
    en: string;
    ar: string;
  };
  scrollText: {
    en: string;
    ar: string;
  };
  images: HeroImage[];
}

const positionOptions = [
  { name: 'First (Top Right)', value: 'first' },
  { name: 'Second (Bottom Left)', value: 'second' },
  { name: 'Third (Center)', value: 'third' },
];

const rotationOptions = [
  { name: 'Rotate Right (3°)', value: 'rotate-3' },
  { name: 'Rotate Left (-3°)', value: '-rotate-3' },
  { name: 'Rotate Right (6°)', value: 'rotate-6' },
  { name: 'Rotate Left (-6°)', value: '-rotate-6' },
  { name: 'No Rotation', value: 'rotate-0' },
];

export default function AboutHeroManagementPage() {
  // Initial data based on current AboutHero component
  const [aboutHeroData, setAboutHeroData] = useState<AboutHeroSection>({
    title: {
      about: {
        en: "About",
        ar: "حول"
      },
      mazaya: {
        en: "Mazaya",
        ar: "مزايا"
      },
      capital: {
        en: "Capital",
        ar: "كابيتال"
      }
    },
    description: {
      en: "At Mazaya Capital, we are committed to delivering exceptional real estate solutions that exceed expectations. Our mission is to create sustainable communities that enhance quality of life while generating superior returns for our investors through innovative development practices and unwavering commitment to excellence.",
      ar: "في مزايا كابيتال، نحن ملتزمون بتقديم حلول عقارية استثنائية تتجاوز التوقعات. مهمتنا هي إنشاء مجتمعات مستدامة تعزز جودة الحياة مع تحقيق عوائد متفوقة لمستثمرينا من خلال ممارسات التطوير المبتكرة والالتزام الثابت بالتميز."
    },
    scrollText: {
      en: "Scroll to explore",
      ar: "مرر لاستكشاف"
    },
    images: [
      {
        id: "hero-image-1",
        url: "/images/luxury-property-1.jpg",
        alt: {
          en: "Luxury Property Development",
          ar: "تطوير عقاري فاخر"
        },
        position: "first",
        rotation: "rotate-3",
      },
      {
        id: "hero-image-2",
        url: "/images/luxury-property-2.jpg",
        alt: {
          en: "Premium Real Estate",
          ar: "عقارات متميزة"
        },
        position: "second",
        rotation: "-rotate-3",
      },
      {
        id: "hero-image-3",
        url: "/images/luxury-property-3.jpg",
        alt: {
          en: "Modern Architecture",
          ar: "عمارة حديثة"
        },
        position: "third",
        rotation: "rotate-6",
      }
    ]
  });

  const [editingSection, setEditingSection] = useState<'title' | 'description' | 'scroll' | null>(null);
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [showAddImageForm, setShowAddImageForm] = useState(false);
  const [draggedImage, setDraggedImage] = useState<string | null>(null);
  const [dragOverPosition, setDragOverPosition] = useState<string | null>(null);

  const handleSectionSave = (section: 'title' | 'description' | 'scroll', newData: any) => {
    setAboutHeroData(prev => ({
      ...prev,
      [section]: newData
    }));
    setEditingSection(null);
  };

  const handleImageSave = (imageId: string, newImage: HeroImage) => {
    console.log('Saving image:', imageId, newImage);
    
    setAboutHeroData(prev => {
      const currentImages = [...prev.images];
      const currentImageIndex = currentImages.findIndex(img => img.id === imageId);
      const currentImage = currentImages[currentImageIndex];
      
      // Check if the position has changed
      if (currentImage.position !== newImage.position) {
        // Find if there's already an image in the target position
        const imageInTargetPosition = currentImages.find(img => 
          img.id !== imageId && img.position === newImage.position
        );
        
        if (imageInTargetPosition) {
          // Swap positions: move the existing image to the current image's old position
          const targetImageIndex = currentImages.findIndex(img => img.id === imageInTargetPosition.id);
          currentImages[targetImageIndex] = {
            ...imageInTargetPosition,
            position: currentImage.position
          };
        }
      }
      
      // Update the current image with new data
      currentImages[currentImageIndex] = newImage;
      
      const newState = {
        ...prev,
        images: currentImages
      };
      
      console.log('Updated state:', newState);
      return newState;
    });
    setEditingImage(null);
  };

  const handleImageDelete = (imageId: string) => {
    if (confirm('Are you sure you want to delete this image?')) {
      setAboutHeroData(prev => ({
        ...prev,
        images: prev.images.filter(img => img.id !== imageId)
      }));
    }
  };

  const handleImageAdd = (newImage: HeroImage) => {
    console.log('Adding new image:', newImage);
    
    // Prevent adding more than 3 images
    if (aboutHeroData.images.length >= 3) {
      alert('Maximum of 3 hero images allowed');
      return;
    }
    
    setAboutHeroData(prev => {
      // Check if the position is already taken
      const existingImageInPosition = prev.images.find(img => img.position === newImage.position);
      
      if (existingImageInPosition) {
        // If position is taken, find an available position
        const positions: ('first' | 'second' | 'third')[] = ['first', 'second', 'third'];
        const availablePosition = positions.find(pos => 
          !prev.images.some(img => img.position === pos)
        );
        
        if (availablePosition) {
          newImage.position = availablePosition;
        }
      }
      
      const newState = {
        ...prev,
        images: [...prev.images, newImage]
      };
      console.log('Updated state after adding:', newState);
      return newState;
    });
    setShowAddImageForm(false);
  };

  const handleSaveAll = () => {
    // Here you would typically send the data to your backend
    console.log('Saving about hero data:', aboutHeroData);
    alert('About Hero data saved successfully!');
  };

  // Drag and Drop handlers
  const handleDragStart = (e: React.DragEvent, imageId: string) => {
    setDraggedImage(imageId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', imageId);
  };

  const handleDragOver = (e: React.DragEvent, targetPosition: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverPosition(targetPosition);
  };

  const handleDragEnter = (e: React.DragEvent, targetPosition: string) => {
    e.preventDefault();
    setDragOverPosition(targetPosition);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    // Only clear dragOverPosition if we're leaving the drop zone completely
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverPosition(null);
    }
  };

  const handleDrop = (e: React.DragEvent, targetPosition: string) => {
    e.preventDefault();
    
    if (!draggedImage) return;
    
    const draggedImageData = aboutHeroData.images.find(img => img.id === draggedImage);
    if (!draggedImageData || draggedImageData.position === targetPosition) {
      setDraggedImage(null);
      setDragOverPosition(null);
      return;
    }

    // Find the image currently in the target position
    const targetImage = aboutHeroData.images.find(img => img.position === targetPosition);
    
    setAboutHeroData(prev => {
      const newImages = prev.images.map(img => {
        if (img.id === draggedImage) {
          // Move dragged image to target position
          return { ...img, position: targetPosition as any };
        } else if (targetImage && img.id === targetImage.id) {
          // Move target image to dragged image's original position
          return { ...img, position: draggedImageData.position };
        }
        return img;
      });
      
      return { ...prev, images: newImages };
    });
    
    setDraggedImage(null);
    setDragOverPosition(null);
  };

  const handleDragEnd = () => {
    setDraggedImage(null);
    setDragOverPosition(null);
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">About Us - About Hero Section</h1>
          <p className="text-gray-400 mt-1">Manage the hero section of the about page</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Title Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Hero Title
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'title' ? null : 'title')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'title' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'title' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'title' ? (
            <TitleEditForm
              data={aboutHeroData.title}
              onSave={(newData) => handleSectionSave('title', newData)}
              onCancel={() => setEditingSection(null)}
            />
          ) : (
            <div className="space-y-4">
              {/* Title Preview */}
              <div className="bg-gray-900 rounded-lg p-6 border border-gray-600">
                <h3 className="text-sm font-medium text-gray-400 mb-4">Title Preview</h3>
                <div className="space-y-4">
                  <div className="flex flex-wrap items-end">
                    <span className="text-4xl md:text-6xl font-bold text-white me-3">
                      {aboutHeroData.title.about.en}
                    </span>
                    <span className="text-4xl md:text-6xl font-bold text-[#00C2FF] me-3">
                      {aboutHeroData.title.mazaya.en}
                    </span>
                    <span className="text-4xl md:text-6xl font-bold text-white">
                      {aboutHeroData.title.capital.en}
                    </span>
                  </div>
                  <div className="flex flex-wrap items-end" dir="rtl">
                    <span className="text-4xl md:text-6xl font-bold text-white me-3">
                      {aboutHeroData.title.about.ar}
                    </span>
                    <span className="text-4xl md:text-6xl font-bold text-[#00C2FF] me-3">
                      {aboutHeroData.title.mazaya.ar}
                    </span>
                    <span className="text-4xl md:text-6xl font-bold text-white">
                      {aboutHeroData.title.capital.ar}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">First Word</label>
                  <p className="text-white">{aboutHeroData.title.about.en}</p>
                  <p className="text-gray-400 text-sm">{aboutHeroData.title.about.ar}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Company Name (Highlighted)</label>
                  <p className="text-[#00C2FF]">{aboutHeroData.title.mazaya.en}</p>
                  <p className="text-[#00C2FF]/80 text-sm">{aboutHeroData.title.mazaya.ar}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Last Word</label>
                  <p className="text-white">{aboutHeroData.title.capital.en}</p>
                  <p className="text-gray-400 text-sm">{aboutHeroData.title.capital.ar}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Description Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Hero Description</h2>
            <button
              onClick={() => setEditingSection(editingSection === 'description' ? null : 'description')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'description' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'description' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'description' ? (
            <DescriptionEditForm
              data={aboutHeroData.description}
              onSave={(newData) => handleSectionSave('description', newData)}
              onCancel={() => setEditingSection(null)}
            />
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                <p className="text-gray-300">{aboutHeroData.description.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                <p className="text-gray-300" dir="rtl">{aboutHeroData.description.ar}</p>
              </div>
            </div>
          )}
        </div>

        {/* Scroll Text Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Scroll Indicator Text</h2>
            <button
              onClick={() => setEditingSection(editingSection === 'scroll' ? null : 'scroll')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'scroll' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'scroll' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'scroll' ? (
            <ScrollTextEditForm
              data={aboutHeroData.scrollText}
              onSave={(newData) => handleSectionSave('scroll', newData)}
              onCancel={() => setEditingSection(null)}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Scroll Text (English)</label>
                <p className="text-white">{aboutHeroData.scrollText.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Scroll Text (Arabic)</label>
                <p className="text-white">{aboutHeroData.scrollText.ar}</p>
              </div>
            </div>
          )}
        </div>

        {/* Hero Images Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">
              Hero Images ({aboutHeroData.images.length}/3)
            </h2>
            {aboutHeroData.images.length < 3 ? (
            <button
              onClick={() => setShowAddImageForm(true)}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Hero Image
            </button>
            ) : (
              <div className="text-sm text-gray-400">
                Maximum images reached (3/3)
              </div>
            )}
          </div>

          {/* Position-based layout with drag and drop */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {positionOptions.map((position) => {
              const imageInPosition = aboutHeroData.images.find(img => img.position === position.value);
              const isDropTarget = dragOverPosition === position.value;
              
              return (
                <div
                  key={position.value}
                  className={`relative bg-gray-700 rounded-lg border-2 transition-all duration-200 ${
                    isDropTarget 
                      ? 'border-[#00C2FF] bg-[#00C2FF]/10' 
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                  onDragOver={(e) => handleDragOver(e, position.value)}
                  onDragEnter={(e) => handleDragEnter(e, position.value)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, position.value)}
                >
                  {/* Position Header */}
                  <div className="border-b border-gray-600 p-3">
                    <h3 className="text-sm font-medium text-white">{position.name}</h3>
                    <p className="text-xs text-gray-400">
                      {imageInPosition ? 'Drag to swap positions' : 'Drop an image here'}
                    </p>
                  </div>

                  {/* Image Content or Empty State */}
                  {imageInPosition ? (
                    <div
                      className={`cursor-move transition-all duration-200 ${
                        draggedImage === imageInPosition.id ? 'opacity-50 scale-95' : 'hover:scale-[1.02]'
                      }`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, imageInPosition.id)}
                      onDragEnd={handleDragEnd}
                    >
                      {/* Image Card Header */}
                      <div className="p-3 flex justify-between items-center">
                        <div className="flex items-center">
                          <div className="bg-[#00C2FF]/20 w-8 h-8 rounded-full flex items-center justify-center text-[#00C2FF] mr-3">
                            <FiImage className="h-4 w-4" />
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setEditingImage(imageInPosition.id)}
                            className="p-1 text-gray-400 hover:text-[#00C2FF] transition-colors"
                          >
                            <FiEdit3 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleImageDelete(imageInPosition.id)}
                            className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                          >
                            <FiTrash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>

                      {editingImage === imageInPosition.id ? (
                        <div className="p-4">
                          <HeroImageEditForm
                            image={imageInPosition}
                            onSave={(newImage) => handleImageSave(imageInPosition.id, newImage)}
                            onCancel={() => setEditingImage(null)}
                          />
                        </div>
                      ) : (
                        <div className="p-4">
                          {/* Image preview */}
                          <div className="w-full h-32 bg-gray-600 rounded-md overflow-hidden mb-3">
                            <img 
                              src={imageInPosition.url} 
                              alt={imageInPosition.alt.en}
                              className={`w-full h-full object-cover transform ${imageInPosition.rotation}`}
                            />
                          </div>
                          
                          <h3 className="font-medium text-white text-sm mb-1">{imageInPosition.alt.en}</h3>
                          <p className="text-gray-400 text-xs mb-2">{imageInPosition.alt.ar}</p>
                          
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-500">
                              {rotationOptions.find(r => r.value === imageInPosition.rotation)?.name}
                            </span>
                            <span className="px-2 py-1 bg-gray-600 rounded text-gray-300">
                              {imageInPosition.position}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    /* Empty State */
                    <div className="p-8 text-center">
                      <div className="w-16 h-16 mx-auto bg-gray-600 rounded-full flex items-center justify-center mb-3">
                        <FiImage className="h-8 w-8 text-gray-400" />
                      </div>
                      <p className="text-gray-400 text-sm">No image in this position</p>
                      <p className="text-gray-500 text-xs mt-1">Drag an image here</p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Add Image Form */}
          {showAddImageForm && (
            <div className="mt-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <HeroImageAddForm
                onSave={handleImageAdd}
                onCancel={() => setShowAddImageForm(false)}
                existingImages={aboutHeroData.images}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Title Edit Form Component
function TitleEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: AboutHeroSection['title'];
  onSave: (data: AboutHeroSection['title']) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">First Word (English)</label>
          <input
            type="text"
            value={formData.about.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              about: { ...prev.about, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">First Word (Arabic)</label>
          <input
            type="text"
            value={formData.about.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              about: { ...prev.about, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Company Name (English)</label>
          <input
            type="text"
            value={formData.mazaya.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              mazaya: { ...prev.mazaya, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Company Name (Arabic)</label>
          <input
            type="text"
            value={formData.mazaya.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              mazaya: { ...prev.mazaya, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Last Word (English)</label>
          <input
            type="text"
            value={formData.capital.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              capital: { ...prev.capital, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Last Word (Arabic)</label>
          <input
            type="text"
            value={formData.capital.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              capital: { ...prev.capital, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Description Edit Form Component
function DescriptionEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: AboutHeroSection['description'];
  onSave: (data: AboutHeroSection['description']) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
        <textarea
          value={formData.en}
          onChange={(e) => setFormData(prev => ({ ...prev, en: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
        <textarea
          value={formData.ar}
          onChange={(e) => setFormData(prev => ({ ...prev, ar: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Scroll Text Edit Form Component
function ScrollTextEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: AboutHeroSection['scrollText'];
  onSave: (data: AboutHeroSection['scrollText']) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Scroll Text (English)</label>
          <input
            type="text"
            value={formData.en}
            onChange={(e) => setFormData(prev => ({ ...prev, en: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Scroll Text (Arabic)</label>
          <input
            type="text"
            value={formData.ar}
            onChange={(e) => setFormData(prev => ({ ...prev, ar: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Hero Image Edit Form Component
function HeroImageEditForm({ 
  image, 
  onSave, 
  onCancel 
}: { 
  image: HeroImage;
  onSave: (image: HeroImage) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(image);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(image.url);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the image persists
      setFormData(prev => ({ ...prev, url: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('image-upload-edit')?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {/* Hidden File Input */}
      <input
        id="image-upload-edit"
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Clickable Image Preview */}
      <div 
        onClick={handleImageClick}
        className="w-full h-24 bg-gray-600 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
      >
        {previewUrl ? (
          <div className="relative w-full h-full">
            <img 
              src={previewUrl} 
              alt="Preview"
              className={`w-full h-full object-cover transform ${formData.rotation}`}
              onError={() => setPreviewUrl('/images/placeholder-property.jpg')}
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
              <FiUpload className="text-white opacity-0 group-hover:opacity-100 h-6 w-6" />
            </div>
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
            <div className="text-center">
              <FiUpload className="h-6 w-6 mx-auto mb-1" />
              <p className="text-xs">Click to upload</p>
            </div>
          </div>
        )}
      </div>

      {selectedFile && (
        <p className="text-xs text-gray-400">
          New: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
        </p>
      )}

      <div className="grid grid-cols-2 gap-2">
        <select
          value={formData.position}
          onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value as any }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        >
          {positionOptions.map(option => (
            <option key={option.value} value={option.value}>{option.name}</option>
          ))}
        </select>

        <select
          value={formData.rotation}
          onChange={(e) => setFormData(prev => ({ ...prev, rotation: e.target.value }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        >
          {rotationOptions.map(option => (
            <option key={option.value} value={option.value}>{option.name}</option>
          ))}
        </select>
      </div>

      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Alt Text (English)"
          value={formData.alt.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            alt: { ...prev.alt, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Alt Text (Arabic)"
          value={formData.alt.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            alt: { ...prev.alt, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save
        </button>
      </div>
    </form>
  );
}

// Hero Image Add Form Component
function HeroImageAddForm({ 
  onSave, 
  onCancel,
  existingImages
}: { 
  onSave: (image: HeroImage) => void;
  onCancel: () => void;
  existingImages: HeroImage[];
}) {
  // Find the first available position
  const getAvailablePosition = () => {
    const positions: ('first' | 'second' | 'third')[] = ['first', 'second', 'third'];
    return positions.find(pos => 
      !existingImages.some(img => img.position === pos)
    ) || 'first';
  };

  const [formData, setFormData] = useState<HeroImage>({
    id: '',
    url: '',
    alt: { en: '', ar: '' },
    position: getAvailablePosition(),
    rotation: 'rotate-3'
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the image persists
      setFormData(prev => ({ ...prev, url: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('image-upload-add')?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile) {
      alert('Please select an image file to upload');
      return;
    }
    
    if (!formData.alt.en) {
      alert('Please enter English alt text');
      return;
    }

    const newImage = {
      ...formData,
      id: `hero-image-${Date.now()}`
    };

    onSave(newImage);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Hero Image</h3>
      {existingImages.length >= 3 ? (
        <div className="text-center py-8">
          <p className="text-gray-400 mb-4">All positions are occupied. Please delete an existing image first.</p>
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Close
          </button>
        </div>
      ) : (
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Hidden File Input */}
        <input
          id="image-upload-add"
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* Clickable Image Upload Area */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Image Upload *</label>
          <div 
            onClick={handleImageClick}
            className="w-full h-48 bg-gray-600 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
          >
            {previewUrl ? (
              <div className="relative w-full h-full">
                <img 
                  src={previewUrl} 
                  alt="Preview"
                  className={`w-full h-full object-cover transform ${formData.rotation}`}
                  onError={(e) => {
                    e.currentTarget.src = '/images/placeholder-property.jpg';
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                  <div className="text-center text-white opacity-0 group-hover:opacity-100">
                    <FiUpload className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Click to change image</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                <div className="text-center">
                  <FiUpload className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium">Click to upload image</p>
                  <p className="text-sm">or drag and drop</p>
                </div>
              </div>
            )}
          </div>
          
          {selectedFile && (
            <div className="mt-2 p-3 bg-gray-700 rounded border border-gray-600">
              <div className="flex items-center text-green-400 text-sm">
                <FiImage className="mr-2 h-4 w-4" />
                <span>Selected: {selectedFile.name}</span>
                <span className="ml-2 text-gray-400">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Position</label>
            <select
              value={formData.position}
              onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value as any }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            >
                {positionOptions
                  .filter(option => !existingImages.some(img => img.position === option.value))
                  .map(option => (
                <option key={option.value} value={option.value}>{option.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Rotation</label>
            <select
              value={formData.rotation}
              onChange={(e) => setFormData(prev => ({ ...prev, rotation: e.target.value }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            >
              {rotationOptions.map(option => (
                <option key={option.value} value={option.value}>{option.name}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Alt Text (English) *</label>
            <input
              type="text"
              value={formData.alt.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                alt: { ...prev.alt, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              placeholder="Describe the image in English"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Alt Text (Arabic)</label>
            <input
              type="text"
              value={formData.alt.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                alt: { ...prev.alt, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              placeholder="وصف الصورة بالعربية"
            />
          </div>
        </div>

        {/* Preview Info */}
        {previewUrl && (
          <div className="bg-gray-700 p-3 rounded border border-gray-600">
            <div className="flex items-center justify-between text-xs text-gray-400">
              <span>Position: {positionOptions.find(p => p.value === formData.position)?.name}</span>
              <span>Rotation: {rotationOptions.find(r => r.value === formData.rotation)?.name}</span>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            <FiSave className="mr-2 h-4 w-4 inline" />
            Add Image
          </button>
        </div>
      </form>
      )}
    </div>
  );
} 