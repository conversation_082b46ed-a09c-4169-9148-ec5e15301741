"use client";

import React from "react";
import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { motion, useScroll, useTransform } from "framer-motion";
import { notFound } from "next/navigation";
import { FiCalendar, FiTag, FiShare2, FiBookmark, FiChevronsUp, FiChevronRight, FiUser, FiChevronDown } from "react-icons/fi";

// Mock article data
const articles = [
  {
    id: 1,
    title: "The Future of Urban Development in the UAE",
    excerpt: "Exploring innovative approaches to urban planning and sustainable development in the UAE's growing cities.",
    date: "June 20, 2023",
    category: "Urban Planning",
    image: "/images/article-1.jpg",
    slug: "future-urban-development-uae",
    author: {
      name: "<PERSON>",
      role: "Urban Planning Specialist",
      avatar: "/images/avatar-1.jpg"
    },
    readTime: "8 min read",
    content: `
      <h1>The Future of Urban Development in the UAE</h1>
      <p>The United Arab Emirates has emerged as a global leader in urban development, with cities like Dubai and Abu Dhabi showcasing innovative approaches to architecture, sustainability, and smart city initiatives.</p>
      
      <h2>Sustainable Urban Planning</h2>
      <p>Sustainability has become a cornerstone of urban planning in the UAE. From energy-efficient buildings to water conservation systems, developers are increasingly prioritizing environmentally conscious designs.</p>
      
      <h3>Green Building Standards</h3>
      <p>The UAE's Vision 2030 includes ambitious goals for reducing carbon footprints and implementing renewable energy solutions across urban centers. This forward-thinking approach has positioned the country as a pioneer in sustainable urban development.</p>
      
      <h3>Water Conservation Initiatives</h3>
      <p>In a region where water is scarce, innovative water conservation techniques are being implemented in new developments, including gray water recycling, efficient irrigation, and smart monitoring systems.</p>
      
      <h2>Smart Cities and Technology Integration</h2>
      <p>Technology plays a pivotal role in shaping the future of UAE's urban landscapes. Smart city initiatives are being implemented to enhance the quality of life for residents while optimizing resource management.</p>
      
      <h3>IoT and Urban Monitoring</h3>
      <p>From IoT sensors that monitor air quality to AI-powered traffic management systems, technology is revolutionizing how cities operate and respond to the needs of their inhabitants.</p>
      
      <h4>Traffic Management Systems</h4>
      <p>Advanced AI algorithms are being used to optimize traffic flow in major urban centers, reducing congestion and emissions while improving commute times.</p>
      
      <h4>Public Safety Networks</h4>
      <p>Integrated camera systems and emergency response networks ensure public spaces remain safe while respecting privacy concerns.</p>
      
      <h2>Mixed-Use Developments</h2>
      <p>The concept of mixed-use developments has gained significant traction in the UAE's urban planning strategies. These developments integrate residential, commercial, and recreational spaces, creating self-sufficient communities that reduce the need for long commutes.</p>
      
      <h3>Community-Centric Design</h3>
      <p>By fostering a sense of community and promoting walkability, mixed-use developments contribute to more sustainable and livable urban environments.</p>
      
      <h2>Future Outlook</h2>
      <p>As the UAE continues to evolve, urban development will remain a key focus area. With a commitment to innovation, sustainability, and enhancing quality of life, the country is well-positioned to set new standards in urban planning and development.</p>
      
      <p>Investors and stakeholders in the real estate sector have much to look forward to as these urban development trends shape the future of the UAE's cities.</p>
    `,
    relatedArticles: [2, 3, 6]
  },
  {
    id: 2,
    title: "Investment Opportunities in Dubai's Real Estate Market",
    excerpt: "A comprehensive analysis of current trends and future projections for Dubai's dynamic real estate market.",
    date: "May 15, 2023",
    category: "Investment",
    image: "/images/article-2.jpg",
    slug: "investment-opportunities-dubai-real-estate",
    author: {
      name: "Mohammed Al Farsi",
      role: "Investment Advisor",
      avatar: "/images/avatar-2.jpg"
    },
    readTime: "10 min read",
    content: `
      <h1>Investment Opportunities in Dubai's Real Estate Market</h1>
      <p>Dubai's real estate market continues to offer compelling investment opportunities, attracting both local and international investors seeking strong returns and capital appreciation.</p>
      
      <h2>Market Overview</h2>
      <p>After a period of adjustment, Dubai's real estate market has shown remarkable resilience and is now experiencing steady growth across various segments. The combination of government initiatives, infrastructure development, and international events has created a favorable environment for real estate investment.</p>
      
      <h2>Prime Residential Properties</h2>
      <p>Luxury residential properties in prime locations such as Palm Jumeirah, Downtown Dubai, and Dubai Marina remain highly sought after by investors. These areas consistently deliver strong rental yields and potential for capital appreciation.</p>
      
      <h3>Beachfront Developments</h3>
      <p>The introduction of new visa regulations and residency options has further boosted demand for high-end residential properties, particularly among international investors looking for second homes or income-generating assets.</p>
      
      <h3>Downtown Living</h3>
      <p>The heart of Dubai continues to attract premium investments with its proximity to business districts and iconic landmarks.</p>
      
      <h4>Burj Khalifa Area</h4>
      <p>Properties near the world's tallest building command premium prices but offer exceptional returns and prestige.</p>
      
      <h4>Dubai Opera District</h4>
      <p>Cultural amenities and luxury shopping make this area particularly attractive to sophisticated investors.</p>
      
      <h2>Commercial Real Estate</h2>
      <p>The commercial real estate sector in Dubai presents attractive opportunities for investors seeking stable long-term returns. Office spaces in business districts like Business Bay and DIFC continue to attract multinational corporations and startups alike.</p>
      
      <h3>Retail Opportunities</h3>
      <p>Retail spaces, especially those integrated within mixed-use developments, offer promising investment potential as Dubai strengthens its position as a global shopping destination.</p>
      
      <h2>Emerging Areas</h2>
      <p>While established areas command premium prices, emerging neighborhoods like Dubai South, Jumeirah Village Circle, and Dubai Hills Estate offer more accessible entry points for investors with significant growth potential.</p>
      
      <h3>Infrastructure Development</h3>
      <p>These developing areas benefit from infrastructure improvements, proximity to key attractions, and the expansion of Dubai's urban footprint.</p>
      
      <h2>Conclusion</h2>
      <p>Dubai's real estate market continues to evolve, presenting diverse investment opportunities across various segments. With the government's commitment to economic diversification and infrastructure development, the long-term outlook for real estate investment in Dubai remains positive.</p>
    `,
    relatedArticles: [1, 5, 6]
  },
  {
    id: 3,
    title: "Sustainable Architecture Trends in Commercial Buildings",
    excerpt: "How sustainability is reshaping commercial architecture and creating value for investors and occupants.",
    date: "April 8, 2023",
    category: "Architecture",
    image: "/images/article-3.jpg",
    slug: "sustainable-architecture-trends-commercial",
    author: {
      name: "Layla Hamdan",
      role: "Sustainable Design Architect",
      avatar: "/images/avatar-3.jpg"
    },
    readTime: "12 min read",
    content: `
      <h1>Sustainable Architecture Trends in Commercial Buildings</h1>
      <p>Sustainable architecture has evolved from a niche concept to a mainstream approach in commercial building design, driven by environmental concerns, regulatory requirements, and economic benefits.</p>
      
      <h2>Green Building Certifications</h2>
      <p>LEED, BREEAM, and Estidama certifications have become important benchmarks for sustainable commercial buildings in the UAE. These certifications not only validate a building's environmental performance but also enhance its market value and appeal to environmentally conscious tenants.</p>
      
      <h3>LEED Certification Process</h3>
      <p>The Leadership in Energy and Environmental Design (LEED) certification involves a comprehensive assessment of a building's environmental performance across multiple categories.</p>
      
      <h4>Energy Efficiency Requirements</h4>
      <p>Buildings must demonstrate significant reductions in energy consumption compared to baseline standards to achieve higher ratings.</p>
      
      <h4>Water Conservation Metrics</h4>
      <p>Water use reduction, recycling systems, and efficient fixtures are evaluated against stringent benchmarks.</p>
      
      <h3>Estidama and Local Standards</h3>
      <p>The UAE's own Estidama Pearl Rating System is tailored to the specific environmental challenges of the region and complements international standards.</p>
      
      <h2>Energy Efficiency</h2>
      <p>Energy-efficient design elements such as advanced HVAC systems, smart building controls, and high-performance building envelopes are becoming standard features in modern commercial architecture. These technologies significantly reduce operational costs and environmental impact.</p>
      
      <h3>Solar Integration</h3>
      <p>Solar power integration, particularly in the form of building-integrated photovoltaics, is increasingly common in commercial buildings across the UAE, leveraging the region's abundant sunshine to generate clean energy.</p>
      
      <h3>Smart Energy Management</h3>
      <p>AI-powered systems optimize building operations in real-time, balancing energy needs with occupancy patterns and external conditions.</p>
      
      <h2>Biophilic Design</h2>
      <p>Biophilic design, which incorporates natural elements into the built environment, is gaining popularity in commercial architecture. Features such as living walls, indoor gardens, and natural lighting not only reduce environmental impact but also enhance occupant wellbeing and productivity.</p>
      
      <h3>Psychological Benefits</h3>
      <p>Research shows that connection to nature in the workplace reduces stress, improves cognitive function, and increases productivity.</p>
      
      <h3>Implementation Strategies</h3>
      <p>From simple plant installations to complex ecosystem recreations, biophilic design can be implemented at various scales and budgets.</p>
      
      <h2>Water Conservation</h2>
      <p>In a region where water is a precious resource, commercial buildings are incorporating advanced water conservation systems. From greywater recycling to efficient irrigation systems for landscaping, sustainable water management is a key focus area.</p>
      
      <h2>Adaptive Reuse</h2>
      <p>Rather than demolishing existing structures, many developers are opting for adaptive reuse – repurposing existing buildings with sustainable upgrades. This approach reduces construction waste and preserves architectural heritage while creating modern, efficient commercial spaces.</p>
      
      <h2>Future Directions</h2>
      <p>The future of sustainable commercial architecture in the UAE will likely see further integration of renewable energy systems, AI-powered building management, and circular economy principles in material selection and waste management.</p>
      
      <p>As sustainability continues to drive innovation in commercial architecture, investors and developers who embrace these trends will be well-positioned to meet market demands and regulatory requirements.</p>
    `,
    relatedArticles: [1, 2, 5]
  },
  {
    id: 4,
    title: "Advanced Construction Technologies Transforming Real Estate",
    excerpt: "Exploring cutting-edge construction methods that are revolutionizing how buildings are designed, built, and maintained.",
    date: "March 3, 2023",
    category: "Technology",
    image: "/images/article-4.jpg",
    slug: "advanced-construction-technologies",
    author: {
      name: "Khalid Rahman",
      role: "Construction Technology Specialist",
      avatar: "/images/avatar-4.jpg"
    },
    readTime: "15 min read",
    content: `
      <h1>Advanced Construction Technologies Transforming Real Estate</h1>
      <p>The construction industry is experiencing an unprecedented technological revolution that is fundamentally changing how buildings are conceived, designed, constructed, and maintained throughout their lifecycle.</p>
      
      <h2>Building Information Modeling (BIM)</h2>
      <p>BIM has evolved from a design tool to an integral platform for project management and collaboration, enabling stakeholders to visualize, plan, and coordinate complex projects with unprecedented precision.</p>
      
      <h3>5D BIM Integration</h3>
      <p>Beyond 3D spatial models, modern BIM systems now incorporate time sequencing (4D) and cost information (5D) to provide comprehensive project oversight.</p>
      
      <h3>Digital Twins</h3>
      <p>Virtual replicas of physical buildings are enabling real-time monitoring, predictive maintenance, and optimization of building performance throughout the operational lifecycle.</p>
      
      <h4>Sensor Integration</h4>
      <p>IoT sensors feeding data to digital twins provide insights into occupancy patterns, energy usage, and structural health.</p>
      
      <h4>Predictive Analytics</h4>
      <p>AI algorithms analyze building data to forecast maintenance needs before failures occur, extending building lifespans and reducing operational costs.</p>
      
      <h2>Prefabrication and Modular Construction</h2>
      <p>Factory-based construction methods are achieving new levels of sophistication, reducing construction timelines by up to 50% while improving quality control and reducing waste.</p>
      
      <h3>Volumetric Modular Systems</h3>
      <p>Complete room units constructed off-site with finishes, fixtures, and MEP systems installed are transforming project delivery timelines.</p>
      
      <h3>Automated Manufacturing</h3>
      <p>Robotic production lines are bringing automotive-industry precision to building component manufacturing.</p>
      
      <h2>3D Printing in Construction</h2>
      <p>Once considered experimental, 3D printing technology is now producing commercial buildings in the UAE, with Dubai's stated goal of having 25% of new buildings 3D-printed by 2030.</p>
      
      <h3>Material Innovations</h3>
      <p>Specialized concrete and composite materials are being developed specifically for 3D printing applications, balancing structural performance with printability.</p>
      
      <h3>On-site Printing Systems</h3>
      <p>Gantry and robotic arm systems capable of printing building structures directly on site are reducing transportation needs and enabling complex geometries.</p>
      
      <h2>Robotics and Automation</h2>
      <p>Construction robotics is expanding beyond factory settings to perform increasingly complex tasks on construction sites.</p>
      
      <h3>Autonomous Equipment</h3>
      <p>Self-operating excavators, dozers, and material handling equipment are improving safety and efficiency on major projects.</p>
      
      <h3>Specialized Construction Robots</h3>
      <p>Task-specific robots for bricklaying, rebar tying, and interior finishing are working alongside human crews to accelerate project timelines.</p>
      
      <h2>Augmented and Virtual Reality</h2>
      <p>AR and VR technologies are revolutionizing how projects are visualized, planned, marketed, and executed.</p>
      
      <h3>Design Visualization</h3>
      <p>Clients can now experience spaces before construction begins, making more informed decisions and reducing expensive changes.</p>
      
      <h3>Construction Guidance</h3>
      <p>AR overlays providing installation instructions and quality control verification are empowering workers to execute complex tasks with precision.</p>
      
      <h2>Sustainable Materials</h2>
      <p>Innovative building materials are addressing environmental concerns while offering new aesthetic and functional possibilities.</p>
      
      <h3>Carbon-Sequestering Concrete</h3>
      <p>New formulations that absorb CO2 throughout their lifecycle are transforming concrete from an environmental liability to a potential carbon sink.</p>
      
      <h3>Mass Timber Construction</h3>
      <p>Engineered wood products are enabling the construction of tall buildings with a fraction of the embodied carbon of traditional materials.</p>
      
      <h2>Future Outlook</h2>
      <p>The convergence of these technologies is creating a fundamentally different approach to building delivery that promises to address the industry's long-standing challenges of productivity, sustainability, and performance.</p>
      
      <p>As these technologies mature and become more integrated, we can expect to see buildings that are more sustainable, adaptable, and responsive to human needs than ever before.</p>
    `,
    relatedArticles: [3, 5, 6]
  },
  {
    id: 5,
    title: "Navigating Regulatory Changes in UAE Real Estate",
    excerpt: "A guide to understanding recent regulatory developments and their implications for developers and investors.",
    date: "February 10, 2023",
    category: "Regulations",
    image: "/images/article-5.jpg",
    slug: "navigating-regulatory-changes-uae",
    content: `
      <p>The UAE's real estate regulatory landscape has undergone significant changes in recent years, aimed at enhancing market stability, transparency, and investor protection.</p>
      
      <h2>New Ownership Laws</h2>
      <p>Recent amendments to foreign ownership laws have expanded opportunities for international investors in the UAE real estate market. These changes allow for greater foreign ownership in designated areas and have stimulated increased international investment in the sector.</p>
      
      <h2>Escrow Account Regulations</h2>
      <p>The implementation of strict escrow account regulations for off-plan developments has strengthened investor protection and market confidence. These regulations ensure that developers use funds exclusively for the intended project, reducing the risk of delays or non-completion.</p>
      
      <h2>Registration Requirements</h2>
      <p>Enhanced registration requirements and processing have improved transparency in real estate transactions. The digitalization of property registration services has streamlined processes, reducing bureaucracy and facilitating smoother transactions.</p>
      
      <h2>Rental Regulations</h2>
      <p>Updates to rental regulations have created a more balanced environment for both landlords and tenants. Clear guidelines on rent increases, contract terms, and dispute resolution mechanisms have contributed to a more stable rental market.</p>
      
      <h2>Sustainability Requirements</h2>
      <p>New regulations mandating sustainability features in development projects reflect the UAE's commitment to environmental responsibility. Developers must now adhere to specific green building standards, which affect project design, materials, and operational systems.</p>
      
      <h2>Impact on Developers</h2>
      <p>For developers, these regulatory changes necessitate greater compliance measures and potentially higher initial costs. However, they also create a more structured and predictable operating environment, which supports long-term planning and investment.</p>
      
      <h2>Impact on Investors</h2>
      <p>From an investor perspective, the enhanced regulatory framework provides greater protection and reduced risk. While the due diligence process may be more comprehensive, the resulting transparency and security justify the additional effort.</p>
      
      <h2>Looking Ahead</h2>
      <p>The evolving regulatory landscape in UAE real estate signals the market's maturation and alignment with international standards. Stakeholders who understand and adapt to these changes will be better positioned to capitalize on the opportunities presented by one of the region's most dynamic real estate markets.</p>
    `,
  },
  {
    id: 6,
    title: "Mixed-Use Developments: Creating Vibrant Communities",
    excerpt: "How mixed-use projects are transforming urban landscapes and creating sustainable, livable communities.",
    date: "January 5, 2023",
    category: "Development",
    image: "/images/article-6.jpg",
    slug: "mixed-use-developments-vibrant-communities",
    content: `
      <p>Mixed-use developments have emerged as a defining trend in urban planning, offering a comprehensive solution to the challenges of modern city living through thoughtful integration of residential, commercial, retail, and recreational spaces.</p>
      
      <h2>The Evolution of Mixed-Use Concepts</h2>
      <p>The mixed-use development model has evolved significantly from its early iterations. Contemporary mixed-use projects emphasize creating cohesive communities rather than simply combining different uses in a single location. This holistic approach focuses on enhancing quality of life through thoughtful urban design and amenity curation.</p>
      
      <h2>Creating Walkable Communities</h2>
      <p>One of the primary benefits of mixed-use developments is the creation of walkable neighborhoods where residents can access essential services, entertainment, and workspaces without relying on vehicular transportation. This pedestrian-friendly approach reduces traffic congestion, lowers carbon emissions, and promotes healthier lifestyles.</p>
      
      <h2>Economic Benefits</h2>
      <p>Mixed-use developments generate significant economic advantages for investors, businesses, and local communities. By concentrating diverse activities in a single location, these projects create built-in customer bases for retail and service businesses while offering residents convenient access to amenities that enhance their daily lives.</p>
      
      <h2>Community Building</h2>
      <p>Beyond their physical attributes, successful mixed-use developments foster a sense of community through thoughtfully designed public spaces, community programming, and shared amenities. These social elements transform developments from mere buildings into vibrant, living communities with distinct identities and cultures.</p>
      
      <h2>Adaptability and Resilience</h2>
      <p>The diversified nature of mixed-use developments provides inherent economic resilience. During market fluctuations, different components can support each other, helping to maintain overall performance and value stability. This adaptability makes mixed-use projects particularly attractive for long-term investment strategies.</p>
      
      <h2>Sustainability Integration</h2>
      <p>Modern mixed-use developments increasingly incorporate sustainable design principles, from energy-efficient buildings to water conservation systems and green spaces. By reducing transportation needs and optimizing resource use, these projects align with global sustainability goals while meeting consumer demand for environmentally responsible living options.</p>
      
      <h2>The Future of Mixed-Use Development</h2>
      <p>Looking ahead, mixed-use developments will continue to evolve with emerging technologies, changing work patterns, and shifting consumer preferences. The most successful projects will balance innovation with timeless principles of good urban design, creating enduring value for investors, residents, and communities.</p>
    `,
  },
];

// Get related articles data
const getRelatedArticles = (articleId: number) => {
  const article = articles.find(a => a.id === articleId);
  if (!article) return [];
  
  // Check if relatedArticles exists before trying to map it
  if (!article.relatedArticles) return [];
  
  return article.relatedArticles.map(id => articles.find(a => a.id === id)).filter(Boolean);
};

// Estimate reading progress
const calculateReadingProgress = (currentScrollPos: number) => {
  // Get the article content element
  const contentElement = document.getElementById('article-content');
  if (!contentElement) return 0;
  
  // Get header height to account for offset
  const headerHeight = window.innerHeight * 0.7; // Matches hero section height (70vh)
  
  // Get content dimensions
  const contentTop = contentElement.getBoundingClientRect().top + window.scrollY - 100;
  const contentHeight = contentElement.offsetHeight;
  
  // Calculate where we are in the content
  const scrollPosition = window.scrollY;
  const scrollStart = contentTop - headerHeight;
  const scrollEnd = contentTop + contentHeight;
  
  // Calculate percentage
  if (scrollPosition < scrollStart) return 0;
  if (scrollPosition > scrollEnd) return 100;
  
  const progressPercentage = ((scrollPosition - scrollStart) / (scrollEnd - scrollStart)) * 100;
  return Math.min(100, Math.max(0, progressPercentage));
};

// Create a slug from heading text
const createSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9 ]/g, '')
    .replace(/\s+/g, '-');
};

export default function ArticlePage({ params }: { params: Promise<{ slug: string }> }) {
  const resolvedParams = React.use(params);
  const article = articles.find((article) => article.slug === resolvedParams.slug);
  const articleRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [readingProgress, setReadingProgress] = useState(0);
  const [activeSection, setActiveSection] = useState("");
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [toc, setToc] = useState<{id: string, title: string, level: number, key: string}[]>([]);
  const [collapsedSections, setCollapsedSections] = useState<{[key: string]: boolean}>({});
  const [contentRendered, setContentRendered] = useState(false);
  const [processedContent, setProcessedContent] = useState("");
  
  // If article not found, show 404 page
  if (!article) {
    notFound();
  }
  
  const relatedArticles = getRelatedArticles(article.id);
  
  // Parallax effects
  const { scrollYProgress } = useScroll({
    target: articleRef,
    offset: ["start start", "end start"],
  });
  
  const headerY = useTransform(scrollYProgress, [0, 0.3], [0, -60]);
  const imageScale = useTransform(scrollYProgress, [0, 0.3], [1, 1.2]);
  const imageOpacity = useTransform(scrollYProgress, [0, 0.3], [1, 0.7]);
  
  // Process content on the client side only
  useEffect(() => {
    if (!article?.content || typeof window === 'undefined') return;
    
    // Create a temporary div to parse the HTML content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = article.content;
    
    // Process headings for TOC and add IDs
    const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const headingsData: {id: string, title: string, level: number, key: string}[] = [];
    
    // Process each heading
    headings.forEach((heading, index) => {
      const text = heading.textContent || `Section ${index + 1}`;
      const level = parseInt(heading.tagName.substring(1));
      const slug = createSlug(text);
      const id = `heading-${index}-${slug}`;
      
      // Set the ID on the heading
      heading.id = id;
      
      // Store heading data
      headingsData.push({
        id,
        title: text,
        level,
        key: `toc-${index}`
      });
    });
    
    // Apply styles to different elements based on their tag names
    const applyStylesToElement = (element: Element) => {
      const tagName = element.tagName.toLowerCase();
      
      switch (tagName) {
        case 'h1':
          element.className = 'text-3xl md:text-4xl font-bold mt-12 mb-6 text-white pb-2 border-b border-[rgb(var(--color-primary))]/30';
          break;
        case 'h2':
          element.className = 'text-2xl md:text-3xl font-bold mt-10 mb-5 text-white pb-2 border-b border-[rgb(var(--color-primary))]/30';
          break;
        case 'h3':
          element.className = 'text-xl md:text-2xl font-bold mt-8 mb-4 text-white/90';
          break;
        case 'h4':
          element.className = 'text-lg md:text-xl font-semibold mt-6 mb-3 text-white/90';
          break;
        case 'h5':
          element.className = 'text-base md:text-lg font-semibold mt-4 mb-2 text-white/90';
          break;
        case 'h6':
          element.className = 'text-base font-medium mt-4 mb-2 text-white/90';
          break;
        case 'p':
          element.className = 'mb-6 text-white/80 leading-relaxed';
          break;
        case 'ul':
          element.className = 'list-disc pl-6 mb-6 text-white/80';
          break;
        case 'ol':
          element.className = 'list-decimal pl-6 mb-6 text-white/80';
          break;
        case 'li':
          element.className = 'mb-2';
          break;
        case 'blockquote':
          element.className = 'border-l-4 border-[rgb(var(--color-primary))]/70 pl-4 italic my-6 text-white/70';
          break;
        case 'a':
          element.className = 'text-[rgb(var(--color-primary))] hover:underline';
          break;
        case 'strong':
        case 'b':
          element.className = 'font-bold text-white';
          break;
        case 'em':
        case 'i':
          element.className = 'italic';
          break;
        case 'code':
          element.className = 'bg-white/10 rounded px-1.5 py-0.5 font-mono text-sm';
          break;
        case 'pre':
          element.className = 'bg-[#141b35] rounded-lg p-4 overflow-x-auto mb-6 font-mono text-sm text-white/90';
          break;
        // Add more element types as needed
      }
      
      // Process child elements recursively
      Array.from(element.children).forEach(child => {
        applyStylesToElement(child);
      });
    };
    
    // Apply styles to all direct children of the tempDiv
    Array.from(tempDiv.children).forEach(child => {
      applyStylesToElement(child);
    });
    
    // Update state with processed content and TOC
    setProcessedContent(tempDiv.innerHTML);
    setToc(headingsData);
    setContentRendered(true);
    console.log("Content processed and TOC generated with items:", headingsData);
  }, [article]);
  
  // Modify DOM elements after render to add IDs
  useEffect(() => {
    if (!contentRef.current || !contentRendered || toc.length === 0) return;
    
    // Add IDs to headings in the rendered content
    const headingElements = contentRef.current.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    headingElements.forEach((heading, index) => {
      if (index < toc.length) {
        heading.id = toc[index].id;
      }
    });
    
    console.log(`Added IDs to ${headingElements.length} headings in the DOM`);
  }, [contentRendered, toc]);
  
  // Scroll to section handler
  const scrollToSection = (id: string) => {
    console.log(`Attempting to scroll to ${id}`);
    
    const element = document.getElementById(id);
    if (element) {
      const offsetTop = element.getBoundingClientRect().top + window.scrollY;
      console.log(`Found element ${id}, scrolling to position ${offsetTop - 120}`);
      
      window.scrollTo({
        top: offsetTop - 120,
        behavior: "smooth"
      });
    } else {
      console.error(`Element with ID ${id} not found. Available IDs:`);
      document.querySelectorAll('#article-content h1, #article-content h2, #article-content h3, #article-content h4, #article-content h5, #article-content h6')
        .forEach(el => console.log(`- ${el.id}: ${el.textContent}`));
    }
  };
  
  // Update active section on scroll
  useEffect(() => {
    const handleScroll = () => {
      // Update reading progress
      setReadingProgress(calculateReadingProgress(window.scrollY));
      
      // Show/hide scroll to top button
      setShowScrollTop(window.scrollY > 500);
      
      // Update active section for table of contents
      if (toc.length > 0) {
        const headings = toc.map(item => document.getElementById(item.id)).filter(Boolean);
        
        for (let i = headings.length - 1; i >= 0; i--) {
          const heading = headings[i];
          if (!heading) continue;
          
          const rect = heading.getBoundingClientRect();
          if (rect.top <= 100) {
            setActiveSection(heading.id);
            break;
          }
        }
      }
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [toc]);
  
  // Scroll to top handler
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };
  
  // Check if author exists and provide fallbacks
  const authorName = article.author?.name || "Anonymous";
  const authorRole = article.author?.role || "Contributor";
  const authorAvatar = article.author?.avatar || "/images/default-avatar.jpg";
  const readTime = article.readTime || "5 min read";
  
  // Toggle section collapse state
  const toggleSection = (key: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent button click
    setCollapsedSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Check if an item has children
  const hasChildren = (item: {id: string, title: string, level: number, key: string}, index: number) => {
    if (index === toc.length - 1) return false;
    return toc[index + 1].level > item.level;
  };

  // Get direct children of an item
  const getChildren = (item: {id: string, title: string, level: number, key: string}, index: number) => {
    const children = [];
    let i = index + 1;
    
    while (i < toc.length && toc[i].level > item.level) {
      if (toc[i].level === item.level + 1) {
        children.push({item: toc[i], index: i});
      }
      i++;
    }
    
    return children;
  };

  // Render TOC item
  const renderTocItem = (item: { id: string, title: string, level: number, key: string }, index: number) => {
    const indentClass = `pl-${(item.level - 1) * 4}`;
    const hasChildItems = hasChildren(item, index);
    const isCollapsed = collapsedSections[item.key];
    const children = hasChildItems ? getChildren(item, index) : [];
    
    return (
      <React.Fragment key={item.key}>
        <button
          onClick={() => scrollToSection(item.id)}
          className={`flex items-center justify-between w-full text-left px-3 py-2 rounded-lg text-sm transition-colors duration-200 ${indentClass} ${
            activeSection === item.id 
              ? 'bg-[rgb(var(--color-primary))]/20 text-[rgb(var(--color-primary))] font-medium' 
              : 'text-white/80 hover:bg-white/5'
          }`}
        >
          <div className="flex items-center">
            {hasChildItems && (
              <button 
                onClick={(e) => toggleSection(item.key, e)} 
                className="mr-2 p-1 hover:bg-white/10 rounded-full transition-colors"
              >
                {isCollapsed ? 
                  <FiChevronRight size={14} /> : 
                  <FiChevronDown size={14} />
                }
              </button>
            )}
            <span>{item.title}</span>
          </div>
        </button>
        
        {hasChildItems && !isCollapsed && (
          <div className="ml-2">
            {children.map(({item, index}) => renderTocItem(item, index))}
          </div>
        )}
      </React.Fragment>
    );
  };
  
  // Render article content with nested structure handling
  const renderArticleContent = () => {
    return (
      <div 
        id="article-content"
        ref={contentRef}
        className="prose prose-lg max-w-none prose-invert"
        dangerouslySetInnerHTML={{ __html: processedContent || '' }}
      />
    );
  };
  
  return (
    <div className="pb-24 bg-[rgb(var(--color-background))]" ref={articleRef}>
      {/* Hero Section with Parallax */}
      <div className="relative h-[70vh] min-h-[600px] overflow-hidden">
        {/* Background Image with Parallax */}
        <motion.div 
          className="absolute inset-0 bg-[#0A0F23]"
          style={{ opacity: imageOpacity }}
        >
          <motion.div
            className="absolute inset-0 z-0"
            style={{ scale: imageScale }}
          >
            <Image
              src={article.image}
              alt={article.title}
              fill
              className="object-cover opacity-40"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-b from-[#0A0F23]/70 via-[#0A0F23]/80 to-[#0A0F23]"></div>
          </motion.div>
        </motion.div>
        
        {/* Floating Elements */}
        <div className="absolute inset-0 z-1 overflow-hidden">
          {/* Geometric Patterns */}
          <div className="absolute top-1/3 left-[5%] w-20 h-20 rounded-full border border-[rgb(var(--color-primary))]/30 opacity-70"></div>
          <div className="absolute top-1/3 left-[5%] w-20 h-20 rounded-full border border-[rgb(var(--color-primary))]/20 opacity-70 animate-pulse-slow"></div>
          
          <div className="absolute top-1/4 right-[10%] w-32 h-32 rounded-full border border-[rgb(var(--color-secondary))]/30 opacity-70"></div>
          <div className="absolute top-1/4 right-[10%] w-32 h-32 rounded-full border border-[rgb(var(--color-secondary))]/20 opacity-70 animate-pulse-slow"></div>
          
          <div className="absolute bottom-1/4 left-[15%] w-24 h-24 rounded-full bg-[rgb(var(--color-primary))]/5 backdrop-blur-sm"></div>
          <div className="absolute top-1/2 right-[20%] w-40 h-40 rounded-full bg-[rgb(var(--color-secondary))]/5 backdrop-blur-sm"></div>
        </div>
        
        {/* Content Container */}
        <motion.div 
          className="relative container mx-auto h-full flex items-center z-10"
          style={{ y: headerY }}
        >
          <div className="max-w-4xl mx-auto text-center text-white px-4">
            <motion.div 
              className="inline-block px-4 py-1 rounded-full bg-[rgb(var(--color-primary))]/20 backdrop-blur-sm text-[rgb(var(--color-primary))] text-sm font-medium mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {article.category}
            </motion.div>
            
            <motion.h1 
              className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6 text-balance text-white"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {article.title}
            </motion.h1>
            
            <motion.div
              className="flex flex-col md:flex-row items-center justify-center gap-4 md:gap-8 text-sm text-white/80"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="flex items-center">
                {article.author ? (
                  <Image 
                    src={authorAvatar} 
                    alt={authorName}
                    width={40} 
                    height={40} 
                    className="rounded-full mr-3"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-[#101736] flex items-center justify-center mr-3">
                    <FiUser size={20} className="text-white/60" />
                  </div>
                )}
                <div className="text-left">
                  <p className="font-medium text-white">{authorName}</p>
                  <p className="text-xs text-white/70">{authorRole}</p>
                </div>
              </div>
              
              <div className="flex gap-4 md:gap-6">
                <div className="flex items-center gap-1">
                  <FiCalendar size={16} />
                  <span>{article.date}</span>
                </div>
                <div className="flex items-center gap-1">
                  <FiTag size={16} />
                  <span>{article.category}</span>
                </div>
                <div className="flex items-center gap-1">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                  </svg>
                  <span>{readTime}</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
        
        {/* Reading Progress Bar - Fixed at top */}
        <div className="fixed top-0 left-0 right-0 h-1 bg-[rgb(var(--color-primary))]/20 z-50">
          <div 
            className="h-full bg-[rgb(var(--color-primary))]" 
            style={{ width: `${readingProgress}%` }}
          ></div>
        </div>
      </div>
      
      {/* Main Content Area with Sidebar */}
      <div className="container mx-auto px-4 -mt-16 relative z-20">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Article Content - 8 Columns on Desktop */}
          <main className="lg:col-span-8 bg-[#0A0F23] rounded-xl shadow-xl p-6 md:p-10 border border-white/5">
      {/* Article Content */}
            {renderArticleContent()}
            
            {/* Article Footer - Tags, Share, Save */}
            <div className="mt-12 pt-8 border-t border-white/10">
              <div className="flex flex-wrap justify-between items-center gap-4">
                <div className="flex flex-wrap gap-2">
                  <span className="text-sm font-medium text-white/60">Tags:</span>
                  <a href="#" className="text-sm px-3 py-1 rounded-full bg-[rgb(var(--color-primary))]/20 text-[rgb(var(--color-primary))]">
                    {article.category}
                  </a>
                  <a href="#" className="text-sm px-3 py-1 rounded-full bg-[rgb(var(--color-secondary))]/20 text-[rgb(var(--color-secondary))]">
                    Real Estate
                  </a>
                  <a href="#" className="text-sm px-3 py-1 rounded-full bg-[#1A1E30]/50 text-white/80">
                    UAE
                  </a>
                </div>
                
                <div className="flex gap-3">
                  <button className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white">
                    <FiShare2 size={18} />
                  </button>
                  <button className="p-2 rounded-full bg-white/5 hover:bg-white/10 transition-colors text-white">
                    <FiBookmark size={18} />
                  </button>
            </div>
          </div>
          </div>
          
            {/* Author Box */}
            {article.author && (
              <div className="mt-12 p-6 rounded-xl bg-[#101736] border border-white/10">
                <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
                  <Image 
                    src={authorAvatar} 
                    alt={authorName}
                    width={80} 
                    height={80} 
                    className="rounded-full"
                  />
                  
            <div>
                    <h3 className="text-xl font-bold mb-2 text-white">{authorName}</h3>
                    <p className="text-sm text-white/70 mb-3">{authorRole}</p>
                    <p className="text-white/80">
                      Expert in real estate development and investment strategies with over 10 years of experience in the UAE market.
                    </p>
                    
                    <div className="mt-4 flex gap-3">
                      <a href="#" className="text-sm font-medium text-[rgb(var(--color-primary))]">
                        View Profile
                      </a>
                      <a href="#" className="text-sm font-medium text-[rgb(var(--color-primary))]">
                        More Articles
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Related Articles */}
            {relatedArticles.length > 0 && (
              <div className="mt-12">
                <h3 className="text-2xl font-bold mb-6 text-white">Related Articles</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {relatedArticles.map((relatedArticle) => (
                    relatedArticle && (
              <Link
                        href={`/articles/${relatedArticle.slug}`} 
                        key={relatedArticle.id}
                        className="group"
                      >
                        <div className="bg-[#101736] hover:bg-[#171D3B] border border-white/10 rounded-lg overflow-hidden transition-all duration-300">
                          <div className="relative h-48 overflow-hidden">
                            <Image
                              src={relatedArticle.image}
                              alt={relatedArticle.title}
                              fill
                              className="object-cover transition-all duration-500 group-hover:scale-105 opacity-70"
                            />
                          </div>
                          <div className="p-4">
                            <span className="text-xs font-medium text-[rgb(var(--color-primary))]">
                              {relatedArticle.category}
                </span>
                            <h4 className="text-lg font-bold mt-2 mb-2 line-clamp-2 text-white">
                              {relatedArticle.title}
                            </h4>
                            <p className="text-sm text-white/70 line-clamp-2">
                              {relatedArticle.excerpt}
                            </p>
                          </div>
                        </div>
              </Link>
                    )
                  ))}
                </div>
              </div>
            )}
          </main>
          
          {/* Sidebar - 4 Columns on Desktop */}
          <aside className="lg:col-span-4 space-y-8">
            {/* Table of Contents */}
            <div className="bg-[#0A0F23] rounded-xl shadow-xl p-6 border border-white/5">
              <h3 className="text-xl font-bold mb-4 pb-4 border-b border-white/10 text-white">
                Table of Contents
              </h3>
              
              <nav className="space-y-1 mt-4">
                {toc.length > 0 ? (
                  toc.filter(item => item.level <= 2).map((item, index) => 
                    renderTocItem(item, toc.findIndex(i => i.key === item.key))
                  )
                ) : (
                  <p className="text-white/50 text-sm">Loading contents...</p>
                )}
              </nav>
            </div>
            
            {/* Newsletter signup */}
            <div className="bg-gradient-to-br from-[rgb(var(--color-primary))]/20 to-[rgb(var(--color-secondary))]/20 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-xl font-bold mb-3 text-white">Stay Updated</h3>
              <p className="text-sm text-white/80 mb-4">
                Subscribe to our newsletter for the latest insights and updates.
              </p>
              
              <form className="space-y-3">
                <div>
                  <input 
                    type="email" 
                    placeholder="Your email address" 
                    className="w-full px-4 py-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 focus:outline-none focus:ring-2 focus:ring-[rgb(var(--color-primary))]/50 text-white"
                  />
                </div>
                <button 
                  type="submit" 
                  className="w-full bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
                >
                  Subscribe
                </button>
              </form>
              
              <p className="text-xs text-white/60 mt-3">
                By subscribing, you agree to our Privacy Policy
              </p>
            </div>
          </aside>
        </div>
      </div>
      
      {/* Scroll to Top Button */}
      <button 
        onClick={scrollToTop}
        className={`fixed bottom-8 right-8 p-3 rounded-full bg-[rgb(var(--color-primary))] text-white shadow-lg z-50 transition-all duration-300 ${showScrollTop ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'}`}
      >
        <FiChevronsUp size={24} />
      </button>
    </div>
  );
} 