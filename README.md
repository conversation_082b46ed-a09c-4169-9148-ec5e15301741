# Advanced Rich Text Editor for Mazaya Website

A comprehensive, feature-rich text editor built with Slate.js for the Mazaya Website admin panel. This editor provides an extensive set of tools for creating rich, interactive content.

## Features

### Basic Formatting
- Text formatting (bold, italic, underline, strikethrough)
- Headings (H1, H2, H3)
- Lists (bulleted and numbered)
- Text alignment (left, center, right, justify)
- Block quotes

### Advanced Formatting
- Advanced text styling (colors, highlighting, font sizes, font families)
- Superscript and subscript

### Rich Content
- Tables with row/column management
- Code blocks with syntax highlighting
- Math equations with LaTeX support
- Media embedding (images, videos, files)
- Multi-column layouts
- Page breaks
- Table of contents

### Collaborative Features
- Real-time collaboration with multiple users
- User presence indicators and cursor tracking
- Comments and annotations on selected text
- Revision history with restore capabilities

### AI-Assisted Editing
- Writing improvement suggestions
- Content summarization
- Text expansion
- Grammar and style checks
- Translation support
- Tone adjustment

### Document Analysis
- Word and character count
- Reading time estimation
- Readability scoring
- Writing quality metrics
- Smart suggestions for improvement

### Import/Export
- Markdown support
- HTML import/export
- Word document export
- PDF export
- Plain text export
- JSON format for storage

### Productivity
- Auto-save functionality
- Keyboard shortcuts
- Content templates

## Technical Implementation

The editor is built using:

- **Slate.js** - A customizable framework for building rich text editors
- **React** - For the UI components
- **TypeScript** - For type safety and better development experience
- **TailwindCSS** - For styling the components

## Architecture

The editor is composed of:

1. **Core Components**
   - RichTextEditor - Main component that integrates all features
   - Element - Renders block elements (paragraphs, headings, etc.)
   - Leaf - Renders text-level formatting

2. **Toolbar Components**
   - Text formatting tools
   - Advanced content insertion tools
   - Utility tools (import/export, collaboration, AI)

3. **Panel Components**
   - CollaborationPanel - For multi-user editing
   - CommentsPanel - For managing comments and annotations
   - RevisionHistory - For tracking and restoring previous versions
   - DocumentAnalytics - For analyzing document metrics

4. **Utilities**
   - serializeToHtml - Converts editor content to HTML
   - deserializeHtml - Converts HTML to editor content
   - Import/export functions for various formats

## Usage

```tsx
import RichTextEditor from '@/components/RichTextEditor';
import { useState } from 'react';
import { CustomElement } from '@/components/RichTextEditor/types';

export default function Editor() {
  const [content, setContent] = useState<CustomElement[]>([{ 
    type: 'paragraph', 
    children: [{ text: 'Start typing here...' }] 
  }]);

  return (
    <RichTextEditor
      value={content}
      onChange={setContent}
      placeholder="Start writing..."
    />
  );
}
```

## Dependencies

See package.json for the complete list of dependencies.

## License

Private - Mazaya Website
