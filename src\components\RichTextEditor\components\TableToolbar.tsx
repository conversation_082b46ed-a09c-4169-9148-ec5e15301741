import React, { useState } from 'react';
import { Editor, Transforms, Element as SlateElement } from 'slate';
import { ReactEditor, useSlate } from 'slate-react';
import { FiGrid, FiPlus, FiMinus, FiTrash2 } from 'react-icons/fi';

interface TableToolbarProps {
  icon: React.ReactNode;
}

const TableToolbar = ({ icon }: TableToolbarProps) => {
  const editor = useSlate();
  const [showOptions, setShowOptions] = useState(false);

  const insertTable = () => {
    const rows = parseInt(prompt('Number of rows', '3') || '3');
    const cols = parseInt(prompt('Number of columns', '3') || '3');
    
    if (rows <= 0 || cols <= 0) return;
    
    const tableRows = Array.from({ length: rows }, (_, rowIndex) => ({
      type: 'table-row',
      children: Array.from({ length: cols }, (_, colIndex) => ({
        type: 'table-cell',
        children: [{ type: 'paragraph', children: [{ text: '' }] }]
      })),
    }));
    
    const table = {
      type: 'table',
      children: tableRows
    };
    
    Transforms.insertNodes(editor, table);
  };

  const insertRow = () => {
    const { selection } = editor;
    if (!selection) return;
    
    // Find the table and table row
    const [tableNode] = Editor.nodes(editor, {
      match: n => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'table',
    });
    
    if (!tableNode) return;
    
    const [table, tablePath] = tableNode;
    const tableRows = table.children;
    if (!tableRows.length) return;
    
    // Get the number of columns from the first row
    const columnCount = tableRows[0].children.length;
    
    // Create a new row with the same number of cells
    const newRow = {
      type: 'table-row',
      children: Array.from({ length: columnCount }, () => ({
        type: 'table-cell',
        children: [{ type: 'paragraph', children: [{ text: '' }] }]
      }))
    };
    
    // Insert the new row
    Transforms.insertNodes(editor, newRow, { at: [...tablePath, tableRows.length] });
  };

  const insertColumn = () => {
    const { selection } = editor;
    if (!selection) return;
    
    // Find the table
    const [tableNode] = Editor.nodes(editor, {
      match: n => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'table',
    });
    
    if (!tableNode) return;
    
    const [table, tablePath] = tableNode;
    const rows = table.children;
    
    // Add a new cell to each row
    rows.forEach((row, rowIndex) => {
      const newCell = {
        type: 'table-cell',
        children: [{ type: 'paragraph', children: [{ text: '' }] }]
      };
      
      Transforms.insertNodes(editor, newCell, { at: [...tablePath, rowIndex, row.children.length] });
    });
  };

  const deleteTable = () => {
    const { selection } = editor;
    if (!selection) return;
    
    // Find and delete the table
    const [tableNode] = Editor.nodes(editor, {
      match: n => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'table',
    });
    
    if (tableNode) {
      const [, tablePath] = tableNode;
      Transforms.removeNodes(editor, { at: tablePath });
    }
  };

  return (
    <div className="relative">
      <button
        type="button"
        className="px-3 py-1.5 bg-[#0A0F23] hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center"
        onMouseDown={(e) => {
          e.preventDefault();
          setShowOptions(!showOptions);
        }}
      >
        {icon}
      </button>
      
      {showOptions && (
        <div className="absolute top-full left-0 mt-1 bg-[#141b35] border border-white/10 rounded shadow-lg z-50 min-w-[150px]">
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              insertTable();
              setShowOptions(false);
            }}
          >
            <FiGrid size={16} />
            <span>Insert Table</span>
          </button>
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              insertRow();
              setShowOptions(false);
            }}
          >
            <FiPlus size={16} />
            <span>Add Row</span>
          </button>
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              insertColumn();
              setShowOptions(false);
            }}
          >
            <FiPlus size={16} />
            <span>Add Column</span>
          </button>
          <button
            className="flex items-center gap-2 w-full px-3 py-2 hover:bg-[#1a2349] text-white text-left"
            onMouseDown={(e) => {
              e.preventDefault();
              deleteTable();
              setShowOptions(false);
            }}
          >
            <FiTrash2 size={16} />
            <span>Delete Table</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default TableToolbar; 