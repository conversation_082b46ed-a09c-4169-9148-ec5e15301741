import React, { useState, useEffect } from 'react';
import { FiPlus, FiX, FiEdit, FiMove, FiSearch } from 'react-icons/fi';
import * as FcIcons from 'react-icons/fc';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';

interface ProjectAmenitiesSectionProps {
  project: any;
  language: 'en' | 'ar';
  getCurrentContent: () => any;
  setProject: React.Dispatch<React.SetStateAction<any>>;
  editingAmenityIndex: number | null;
  setEditingAmenityIndex: React.Dispatch<React.SetStateAction<number | null>>;
  editingAmenity: { icon: string; title: string; description: string };
  setEditingAmenity: React.Dispatch<React.SetStateAction<{ icon: string; title: string; description: string }>>;
  draggedAmenity: number | null;
  setDraggedAmenity: React.Dispatch<React.SetStateAction<number | null>>;
  showIconSelector: boolean;
  setShowIconSelector: React.Dispatch<React.SetStateAction<boolean>>;
  iconSearchTerm: string;
  setIconSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  showAmenityForm: boolean;
  setShowAmenityForm: React.Dispatch<React.SetStateAction<boolean>>;
  selectedIconSet: string;
  setSelectedIconSet: React.Dispatch<React.SetStateAction<any>>;
  hasMatchingIcons: (libraryId: string, searchTerm: string) => boolean;
  getIconComponent: (iconName: string) => JSX.Element | null;
}

// Add this before the component definition:
const scrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a2234;
    border-radius: 4px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #00C2FF;
    border-radius: 4px;
    border: 1px solid #1a2234;
  }
`;

const ProjectAmenitiesSection: React.FC<ProjectAmenitiesSectionProps> = ({
  project,
  language,
  getCurrentContent,
  setProject,
  editingAmenityIndex,
  setEditingAmenityIndex,
  editingAmenity,
  setEditingAmenity,
  draggedAmenity,
  setDraggedAmenity,
  showIconSelector,
  setShowIconSelector,
  iconSearchTerm,
  setIconSearchTerm,
  showAmenityForm,
  setShowAmenityForm,
  selectedIconSet,
  setSelectedIconSet,
  hasMatchingIcons,
  getIconComponent
}) => {
  const [newAmenity, setNewAmenity] = useState({ icon: 'FcAddressBook', title: '', description: '' });
  const [editingSectionInfo, setEditingSectionInfo] = useState(false);
  
  // Get current section info based on selected language
  const currentSectionTitle = getCurrentContent().amenitiesSection?.title || '';
  const currentSectionDescription = getCurrentContent().amenitiesSection?.description || '';
  
  // State for the current language's section info
  const [sectionTitle, setSectionTitle] = useState(currentSectionTitle);
  const [sectionDescription, setSectionDescription] = useState(currentSectionDescription);
  
  // Update the form values when language changes
  useEffect(() => {
    // Get the current language's section info
    const currentAmenitiesSection = getCurrentContent().amenitiesSection || {};
    setSectionTitle(currentAmenitiesSection.title || '');
    setSectionDescription(currentAmenitiesSection.description || '');
  }, [language, getCurrentContent]);
  
  // Handle manual updates to section title
  const handleSectionTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setSectionTitle(newTitle);
    
    // Immediately save to project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent[language],
          amenitiesSection: {
            ...(project.localizedContent[language].amenitiesSection || {}),
            title: newTitle,
            description: sectionDescription
          }
        }
      }
    });
  };
  
  // Handle manual updates to section description
  const handleSectionDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newDescription = e.target.value;
    setSectionDescription(newDescription);
    
    // Immediately save to project state
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent[language],
          amenitiesSection: {
            ...(project.localizedContent[language].amenitiesSection || {}),
            title: sectionTitle,
            description: newDescription
          }
        }
      }
    });
  };
  
  // All available icon libraries
  const iconSets = [
    { id: 'fc', label: 'Flat Color', total: Object.keys(FcIcons).length },
    { id: 'fa', label: 'Font Awesome', total: Object.keys(FaIcons).length },
    { id: 'fa6', label: 'Font Awesome 6', total: Object.keys(FaIconsSolid).length },
    { id: 'bs', label: 'Bootstrap', total: Object.keys(BsIcons).length },
    { id: 'ri', label: 'Remix', total: Object.keys(RiIcons).length },
    { id: 'gi', label: 'Game Icons', total: Object.keys(GiIcons).length },
    { id: 'tb', label: 'Tabler', total: Object.keys(TbIcons).length },
    { id: 'md', label: 'Material Design', total: Object.keys(MdIcons).length },
    { id: 'hi', label: 'Heroicons', total: Object.keys(HiIcons).length },
    { id: 'ai', label: 'Ant Design', total: Object.keys(AiIcons).length },
    { id: 'io', label: 'Ionicons 4', total: Object.keys(IoIcons).length },
    { id: 'io5', label: 'Ionicons 5', total: Object.keys(Io5Icons).length },
    { id: 'pi', label: 'Phosphor', total: Object.keys(PiIcons).length },
  ];

  // Add structured amenity
  const addStructuredAmenity = () => {
    if (!newAmenity.title.trim() || !newAmenity.icon) return;
    
    // Create the new amenity object
    const amenityToAdd = {
      icon: newAmenity.icon,
      title: newAmenity.title.trim(),
      description: newAmenity.description.trim()
    };
    
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent[language],
          amenities: [...project.localizedContent[language].amenities, amenityToAdd]
        }
      }
    });
    
    // Reset form
    setNewAmenity({ icon: 'FcAddressBook', title: '', description: '' });
    setShowIconSelector(false);
    setShowAmenityForm(false);
  };

  // Functions for amenities editing
  const startEditAmenity = (index: number) => {
    setEditingAmenityIndex(index);
    setEditingAmenity({
      icon: getCurrentContent().amenities[index].icon,
      title: getCurrentContent().amenities[index].title,
      description: getCurrentContent().amenities[index].description
    });
  };

  const saveAmenityEdit = () => {
    if (editingAmenityIndex !== null) {
      const updatedAmenities = [...getCurrentContent().amenities];
      updatedAmenities[editingAmenityIndex] = {
        ...updatedAmenities[editingAmenityIndex],
        icon: editingAmenity.icon,
        title: editingAmenity.title,
        description: editingAmenity.description
      };
      
      setProject({
        ...project,
        localizedContent: {
          ...project.localizedContent,
          [language]: {
            ...project.localizedContent[language],
            amenities: updatedAmenities
          }
        }
      });
      
      // Reset editing state
      setEditingAmenityIndex(null);
      setEditingAmenity({ icon: '', title: '', description: '' });
      setShowIconSelector(false);
    }
  };
  
  // Get icons for the current selected library filtered by search term
  const getFilteredIcons = () => {
    let library;
    switch (selectedIconSet) {
      case 'fc': library = FcIcons; break;
      case 'fa': library = FaIcons; break;
      case 'fa6': library = FaIconsSolid; break;
      case 'bs': library = BsIcons; break;
      case 'ri': library = RiIcons; break;
      case 'gi': library = GiIcons; break;
      case 'tb': library = TbIcons; break;
      case 'md': library = MdIcons; break;
      case 'hi': library = HiIcons; break;
      case 'ai': library = AiIcons; break;
      case 'io': library = IoIcons; break;
      case 'io5': library = Io5Icons; break;
      case 'pi': library = PiIcons; break;
      default: library = FcIcons;
    }
    
    // Filter out non-component entries (avoid empty icons)
    const icons = Object.keys(library).filter(key => {
      const Icon = library[key as keyof typeof library];
      return typeof Icon === 'function';
    });
    
    const trimmedSearchTerm = iconSearchTerm.trim();
    
    if (!trimmedSearchTerm) {
      return icons.slice(0, 120); // Show more icons by default
    }
    
    const filteredIcons = icons
      .filter(name => name.toLowerCase().includes(trimmedSearchTerm.toLowerCase()))
      .slice(0, 120); // Show more search results
    
    // If we have a search term but no results in the current library,
    // auto-switch to the first library that has results
    if (trimmedSearchTerm && filteredIcons.length === 0) {
      // Find the first library with matching results
      const firstLibWithResults = iconSets.find(set => 
        set.id !== selectedIconSet && hasMatchingIcons(set.id, trimmedSearchTerm)
      );
      
      if (firstLibWithResults) {
        // Use setTimeout to avoid state update during render
        setTimeout(() => {
          setSelectedIconSet(firstLibWithResults.id);
        }, 0);
      }
    }
    
    return filteredIcons;
  };
  
  // Render icon component directly
  const renderIcon = (iconName: string) => {
    if (!iconName) return null;
    
    let library;
    let iconKey = iconName;
    
    if (iconName.startsWith('Fc')) {
      library = FcIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Fa6')) {
      library = FaIconsSolid;
      iconKey = iconName.substring(3);
    }
    else if (iconName.startsWith('Fa')) {
      library = FaIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Bs')) {
      library = BsIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Ri')) {
      library = RiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Gi')) {
      library = GiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Tb')) {
      library = TbIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Md')) {
      library = MdIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Hi')) {
      library = HiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Ai')) {
      library = AiIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Io5')) {
      library = Io5Icons;
      iconKey = iconName.substring(3);
    }
    else if (iconName.startsWith('Io')) {
      library = IoIcons;
      iconKey = iconName.substring(2);
    }
    else if (iconName.startsWith('Pi')) {
      library = PiIcons;
      iconKey = iconName.substring(2);
    }
    else return null;
    
    // Use proper type checking for the icon component
    const IconComponent = library[iconKey as keyof typeof library] as React.ComponentType<any>;
    return IconComponent ? <IconComponent className="text-xl" style={{ display: 'block', fontSize: '1.5rem' }} /> : null;
  };
  
  // Handle selecting an icon
  const selectIcon = (iconName: string) => {
    if (editingAmenityIndex !== null) {
      setEditingAmenity({ ...editingAmenity, icon: iconName });
    } else {
      setNewAmenity({ ...newAmenity, icon: iconName });
    }
    setShowIconSelector(false);
  };
  
  // Functions for amenities drag and drop
  const handleAmenityDragStart = (index: number) => {
    setDraggedAmenity(index);
  };

  const handleAmenityDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };

  const handleAmenityDrop = (dropIndex: number) => {
    if (draggedAmenity === null || draggedAmenity === dropIndex) return;
    
    const updatedAmenities = [...getCurrentContent().amenities];
    const draggedAmenityContent = updatedAmenities[draggedAmenity];
    
    // Remove the dragged item
    updatedAmenities.splice(draggedAmenity, 1);
    
    // Add it at the new position
    updatedAmenities.splice(dropIndex, 0, draggedAmenityContent);
    
    // Update the project state with the new order
    setProject({
      ...project,
      localizedContent: {
        ...project.localizedContent,
        [language]: {
          ...project.localizedContent[language],
          amenities: updatedAmenities
        }
      }
    });
    
    setDraggedAmenity(null);
  };

  // Section for display in front-end
  const displaySectionTitle = sectionTitle || (language === 'en' ? 'World-Class Amenities' : 'مرافق عالمية');
  const displaySectionDescription = sectionDescription || (language === 'en' 
    ? 'Discover a lifestyle of luxury with our exceptional range of facilities and services.' 
    : 'اكتشف أسلوب حياة الرفاهية مع مجموعة استثنائية من المرافق والخدمات.');

  useEffect(() => {
    // Add the scrollbar styles to the document head
    const styleElement = document.createElement('style');
    styleElement.innerHTML = scrollbarStyles;
    document.head.appendChild(styleElement);
    
    // Clean up on unmount
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Project Amenities' : 'مرافق المشروع'}
      </h2>
      
      {/* Section Title and Description */}
      <div className="mb-6 border-b border-gray-700 pb-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Amenities Section Title' : 'عنوان قسم المرافق'}
            </label>
            <input
              type="text"
              value={sectionTitle}
              onChange={handleSectionTitleChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' ? "World-Class Amenities" : "مرافق عالمية"}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              {language === 'en' ? 'Amenities Section Subtitle' : 'العنوان الفرعي لقسم المرافق'}
            </label>
            <textarea
              value={sectionDescription}
              onChange={handleSectionDescriptionChange}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
              rows={3}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
              placeholder={language === 'en' 
                ? "Discover a lifestyle of luxury with our exceptional range of facilities and services." 
                : "اكتشف أسلوب حياة الرفاهية مع مجموعة استثنائية من المرافق والخدمات."}
            />
          </div>
        </div>
      </div>
      
      {/* Amenities Grid with Edit Forms and Add Card */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 mt-4">
        {/* Existing Amenity Cards */}
        {getCurrentContent().amenities.map((amenity: any, index: number) => (
          <div key={index}>
            {editingAmenityIndex === index ? (
              // Edit Amenity Form
              <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600 shadow-lg">
                <h3 className="text-md font-medium text-white mb-4">
                  {language === 'en' ? 'Edit Amenity' : 'تعديل الميزة'}
                </h3>
                
                <div className="flex flex-col items-center mb-4">
                  <div className="relative mb-4 flex flex-col items-center">
                    <button
                      type="button"
                      onClick={() => setShowIconSelector(!showIconSelector)}
                      className="w-16 h-16 rounded-full bg-[#00C2FF]/10 flex items-center justify-center text-[#00C2FF] hover:bg-[#00C2FF]/20"
                    >
                      {renderIcon(editingAmenity.icon)}
                    </button>
                    <p className="text-xs text-gray-400 text-center mt-1">
                      {language === 'en' ? 'Click to change icon' : 'انقر لتغيير الأيقونة'}
                    </p>
                  </div>
                  
                  {/* Icon Selector Dropdown */}
                  {showIconSelector && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
                      <div className="bg-[#1a2234] rounded-lg shadow-xl p-3 w-full max-w-md max-h-[70vh] flex flex-col border border-gray-700">
                        <div className="flex justify-between items-center mb-2 pb-2 border-b border-gray-700">
                          <h3 className="text-base font-medium text-white flex items-center">
                            <span className="text-[#00C2FF] mr-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-14a2 2 0 10-.001 0H10zm-.001 0a2 2 0 100 0H10zM16 4a2 2 0 00-2-2h-1a2 2 0 100 4h1a2 2 0 002-2zm0 11a3 3 0 10-6 0 3 3 0 006 0z" clipRule="evenodd" />
                              </svg>
                            </span>
                            Select an Icon
                          </h3>
                          <button 
                            className="text-gray-400 hover:text-white hover:bg-gray-700 p-1 rounded-full transition-colors"
                            onClick={() => setShowIconSelector(false)}
                          >
                            <FiX className="h-4 w-4" />
                          </button>
                        </div>
                        
                        {/* Search input */}
                        <div className="relative mb-2">
                          <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                            <FiSearch className="h-3.5 w-3.5 text-[#00C2FF]" />
                          </div>
                          <input
                            type="text"
                            value={iconSearchTerm}
                            onChange={(e) => setIconSearchTerm(e.target.value)}
                            className="bg-gray-800 border border-gray-700 rounded-md py-1.5 pl-7 pr-3 w-full text-xs text-white placeholder-gray-400 focus:ring-[#00C2FF] focus:border-[#00C2FF] transition-colors"
                            placeholder="Search icons..."
                          />
                        </div>
                        
                        {/* Library selector */}
                        <div className="mb-2 pb-2 border-b border-gray-700 overflow-hidden">
                          <div className="flex gap-1 overflow-x-auto pb-1" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                            {iconSets
                              .filter(set => !iconSearchTerm.trim() || hasMatchingIcons(set.id, iconSearchTerm.trim()))
                              .map(set => (
                                <button
                                  key={set.id}
                                  onClick={() => {
                                    setSelectedIconSet(set.id);
                                    // Don't clear search term when changing libraries
                                  }}
                                  className={`px-2 py-0.5 rounded-md text-xs whitespace-nowrap flex-shrink-0 transition-colors ${
                                    selectedIconSet === set.id 
                                      ? 'bg-[#00C2FF] text-white shadow-md' 
                                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  }`}
                                >
                                  {set.label} ({set.total})
                                </button>
                              ))}
                          </div>
                        </div>
                        
                        {/* Icons container with scroll */}
                        <div 
                          className="h-[300px] overflow-y-auto border border-gray-700 rounded mb-2 custom-scrollbar" 
                          style={{
                            scrollbarWidth: 'thin',
                            scrollbarColor: '#6b7280 #1a2234',
                          }}
                        >
                          <div className="grid grid-cols-5 gap-0.5 p-0.5">
                            {getFilteredIcons().length > 0 ? (
                              getFilteredIcons().map(iconName => {
                                // Directly render icons from the appropriate library
                                let Icon;
                                switch (selectedIconSet) {
                                  case 'fc': Icon = FcIcons[iconName as keyof typeof FcIcons]; break;
                                  case 'fa': Icon = FaIcons[iconName as keyof typeof FaIcons]; break;
                                  case 'fa6': Icon = FaIconsSolid[iconName as keyof typeof FaIconsSolid]; break;
                                  case 'bs': Icon = BsIcons[iconName as keyof typeof BsIcons]; break;
                                  case 'ri': Icon = RiIcons[iconName as keyof typeof RiIcons]; break;
                                  case 'gi': Icon = GiIcons[iconName as keyof typeof GiIcons]; break;
                                  case 'tb': Icon = TbIcons[iconName as keyof typeof TbIcons]; break;
                                  case 'md': Icon = MdIcons[iconName as keyof typeof MdIcons]; break;
                                  case 'hi': Icon = HiIcons[iconName as keyof typeof HiIcons]; break;
                                  case 'ai': Icon = AiIcons[iconName as keyof typeof AiIcons]; break;
                                  case 'io': Icon = IoIcons[iconName as keyof typeof IoIcons]; break;
                                  case 'io5': Icon = Io5Icons[iconName as keyof typeof Io5Icons]; break;
                                  case 'pi': Icon = PiIcons[iconName as keyof typeof PiIcons]; break;
                                  default: Icon = null;
                                }
                                
                                // Create the full icon name with library prefix
                                let prefix = '';
                                switch (selectedIconSet) {
                                  case 'fc': prefix = 'Fc'; break;
                                  case 'fa': prefix = 'Fa'; break;
                                  case 'fa6': prefix = 'Fa6'; break;
                                  case 'bs': prefix = 'Bs'; break;
                                  case 'ri': prefix = 'Ri'; break;
                                  case 'gi': prefix = 'Gi'; break;
                                  case 'tb': prefix = 'Tb'; break;
                                  case 'md': prefix = 'Md'; break;
                                  case 'hi': prefix = 'Hi'; break;
                                  case 'ai': prefix = 'Ai'; break;
                                  case 'io': prefix = 'Io'; break;
                                  case 'io5': prefix = 'Io5'; break;
                                  case 'pi': prefix = 'Pi'; break;
                                }
                                
                                const fullIconName = `${prefix}${iconName}`;
                                
                                return (
                                  <button
                                    key={iconName}
                                    onClick={() => selectIcon(fullIconName)}
                                    className="aspect-square p-1 flex items-center justify-center bg-[#151b29] hover:bg-[#00C2FF]/10 rounded transition-colors border border-gray-800 hover:border-[#00C2FF]/50"
                                    title={iconName}
                                  >
                                    {Icon ? <Icon className="text-xl" style={{ display: 'block' }} /> : <span className="text-xs text-gray-400">?</span>}
                                  </button>
                                );
                              })
                            ) : (
                              <div className="col-span-5 flex flex-col items-center justify-center py-10 text-center bg-[#151b29]/50 rounded-md">
                                <div className="w-16 h-16 mb-3 flex items-center justify-center rounded-full bg-[#1a2234] text-gray-400">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                  </svg>
                                </div>
                                <h3 className="text-base font-medium text-gray-300 mb-1">No icons found</h3>
                                <p className="text-sm text-gray-400 mb-3 max-w-[220px]">
                                  Try a different search term or browse another icon library
                                </p>
                                {iconSearchTerm.trim().length > 0 && (
                                  <button
                                    onClick={() => setIconSearchTerm('')}
                                    className="px-3 py-1.5 bg-[#00C2FF]/10 hover:bg-[#00C2FF]/20 text-[#00C2FF] text-xs rounded-md transition-colors flex items-center"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Clear search
                                  </button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Footer with info */}
                        <div className="mt-0 pt-1 border-t border-gray-700 flex justify-between items-center text-xs text-gray-400">
                          <div>
                            {getFilteredIcons().length} icons shown
                          </div>
                          <button 
                            onClick={() => setIconSearchTerm('')}
                            className={`text-xs px-2 py-0.5 rounded hover:bg-gray-700 transition-colors ${
                              iconSearchTerm ? 'text-[#00C2FF]' : 'text-gray-500 cursor-default'
                            }`}
                            disabled={!iconSearchTerm}
                          >
                            Clear search
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="mb-5 mt-4">
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    {language === 'en' ? 'Title' : 'العنوان'}
                  </label>
                  <input
                    type="text"
                    value={editingAmenity.title}
                    onChange={(e) => setEditingAmenity({ ...editingAmenity, title: e.target.value })}
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                </div>
                
                <div className="mb-5">
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    {language === 'en' ? 'Description' : 'الوصف'}
                  </label>
                  <textarea
                    value={editingAmenity.description}
                    onChange={(e) => setEditingAmenity({ ...editingAmenity, description: e.target.value })}
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                    rows={3}
                    dir={language === 'ar' ? 'rtl' : 'ltr'}
                  />
                </div>
                
                <div className="flex justify-end space-x-2 mt-5 gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      setEditingAmenityIndex(null);
                      setEditingAmenity({ icon: '', title: '', description: '' });
                      setShowIconSelector(false);
                    }}
                    className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
                  >
                    {language === 'en' ? 'Cancel' : 'إلغاء'}
                  </button>
                  <button
                    type="button"
                    onClick={saveAmenityEdit}
                    disabled={!editingAmenity.title.trim() || !editingAmenity.icon}
                    className={`px-4 py-2 text-sm font-medium text-white rounded ${
                      editingAmenity.title.trim() && editingAmenity.icon 
                        ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
                        : 'bg-gray-600 cursor-not-allowed'
                    }`}
                  >
                    {language === 'en' ? 'Save Changes' : 'حفظ التغييرات'}
                  </button>
                </div>
              </div>
            ) : (
              // Normal Amenity Card
              <div
                className={`bg-[#151b29] rounded-lg overflow-hidden border border-gray-700 p-5 relative ${draggedAmenity === index ? 'opacity-50' : 'opacity-100'}`}
                draggable={true}
                onDragStart={() => handleAmenityDragStart(index)}
                onDragOver={handleAmenityDragOver}
                onDrop={() => handleAmenityDrop(index)}
              >
                {/* Drag indicator */}
                {getCurrentContent().amenities.length > 1 && (
                  <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1.5 z-10 text-gray-300 cursor-move">
                    <FiMove className="h-3 w-3" />
                  </div>
                )}
                
                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 rounded-full bg-[#182035] flex items-center justify-center text-[#00C2FF] mb-5">
                    {renderIcon(amenity.icon)}
                  </div>
                  
                  <h3 className="text-white font-medium text-lg text-center mb-2">{amenity.title}</h3>
                  <p className="text-gray-400 text-sm text-center">{amenity.description}</p>
                </div>
                
                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-70 rounded-lg">
                  <div className="flex space-x-3 gap-2">
                    <button
                      type="button"
                      onClick={() => startEditAmenity(index)}
                      className="p-2 bg-blue-600 rounded-full text-white"
                      title={language === 'en' ? 'Edit amenity' : 'تعديل الميزة'}
                    >
                      <FiEdit className="h-5 w-5" />
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const updatedAmenities = [...getCurrentContent().amenities];
                        updatedAmenities.splice(index, 1);
                        
                        setProject({
                          ...project,
                          localizedContent: {
                            ...project.localizedContent,
                            [language]: {
                              ...project.localizedContent[language],
                              amenities: updatedAmenities
                            }
                          }
                        });
                      }}
                      className="p-2 bg-red-600 rounded-full text-white"
                      title={language === 'en' ? 'Delete amenity' : 'حذف الميزة'}
                    >
                      <FiX className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
        
        {/* Add New Amenity Card - only show when no form is visible and we're not editing */}
        {!showAmenityForm && !editingAmenityIndex && (
          <div 
            className="bg-[#151b29] rounded-lg overflow-hidden border border-gray-700 border-dashed p-5 flex flex-col items-center justify-center cursor-pointer hover:bg-[#182035] transition-colors"
            onClick={() => setShowAmenityForm(true)}
          >
            <div className="w-14 h-14 bg-[#182035] rounded-full flex items-center justify-center mb-3 text-[#00C2FF]">
              <FiPlus className="h-6 w-6" />
            </div>
            <span className="text-sm text-gray-300 text-center">
              {language === 'en' ? 'Add New Amenity' : 'إضافة ميزة جديدة'}
            </span>
          </div>
        )}
        
        {/* New Amenity Form */}
        {showAmenityForm && !editingAmenityIndex && (
          <div className="bg-[#1a2234] rounded-lg p-5 border border-gray-600 shadow-lg">
            <h3 className="text-md font-medium text-white mb-4">
              {language === 'en' ? 'Add New Amenity' : 'إضافة ميزة جديدة'}
            </h3>
            
            <div className="flex flex-col items-center mb-4">
              <div className="relative mb-4 flex flex-col items-center">
                <button
                  type="button"
                  onClick={() => setShowIconSelector(!showIconSelector)}
                  className="w-16 h-16 rounded-full bg-[#00C2FF]/10 flex items-center justify-center text-[#00C2FF] hover:bg-[#00C2FF]/20"
                >
                  {renderIcon(newAmenity.icon)}
                </button>
                <p className="text-xs text-gray-400 text-center mt-1">
                  {language === 'en' ? 'Click to change icon' : 'انقر لتغيير الأيقونة'}
                </p>
              </div>
              
              {/* Icon Selector Dropdown */}
              {showIconSelector && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
                  <div className="bg-[#1a2234] rounded-lg shadow-xl p-3 w-full max-w-md max-h-[70vh] flex flex-col border border-gray-700">
                    <div className="flex justify-between items-center mb-2 pb-2 border-b border-gray-700">
                      <h3 className="text-base font-medium text-white flex items-center">
                        <span className="text-[#00C2FF] mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-14a2 2 0 10-.001 0H10zm-.001 0a2 2 0 100 0H10zM16 4a2 2 0 00-2-2h-1a2 2 0 100 4h1a2 2 0 002-2zm0 11a3 3 0 10-6 0 3 3 0 006 0z" clipRule="evenodd" />
                          </svg>
                        </span>
                        Select an Icon
                      </h3>
                      <button 
                        className="text-gray-400 hover:text-white hover:bg-gray-700 p-1 rounded-full transition-colors"
                        onClick={() => setShowIconSelector(false)}
                      >
                        <FiX className="h-4 w-4" />
                      </button>
                    </div>
                    
                    {/* Search input */}
                    <div className="relative mb-2">
                      <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <FiSearch className="h-3.5 w-3.5 text-[#00C2FF]" />
                      </div>
                      <input
                        type="text"
                        value={iconSearchTerm}
                        onChange={(e) => setIconSearchTerm(e.target.value)}
                        className="bg-gray-800 border border-gray-700 rounded-md py-1.5 pl-7 pr-3 w-full text-xs text-white placeholder-gray-400 focus:ring-[#00C2FF] focus:border-[#00C2FF] transition-colors"
                        placeholder="Search icons..."
                      />
                    </div>
                    
                    {/* Library selector */}
                    <div className="mb-2 pb-2 border-b border-gray-700 overflow-hidden">
                      <div className="flex gap-1 overflow-x-auto pb-1" style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
                        {iconSets
                          .filter(set => !iconSearchTerm.trim() || hasMatchingIcons(set.id, iconSearchTerm.trim()))
                          .map(set => (
                            <button
                              key={set.id}
                              onClick={() => {
                                setSelectedIconSet(set.id);
                                // Don't clear search term when changing libraries
                              }}
                              className={`px-2 py-0.5 rounded-md text-xs whitespace-nowrap flex-shrink-0 transition-colors ${
                                selectedIconSet === set.id 
                                  ? 'bg-[#00C2FF] text-white shadow-md' 
                                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              {set.label} ({set.total})
                            </button>
                          ))}
                      </div>
                    </div>
                    
                    {/* Icons container with scroll */}
                    <div 
                      className="h-[300px] overflow-y-auto border border-gray-700 rounded mb-2 custom-scrollbar" 
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#6b7280 #1a2234',
                      }}
                    >
                      <div className="grid grid-cols-5 gap-0.5 p-0.5">
                        {getFilteredIcons().length > 0 ? (
                          getFilteredIcons().map(iconName => {
                            // Directly render icons from the appropriate library
                            let Icon;
                            switch (selectedIconSet) {
                              case 'fc': Icon = FcIcons[iconName as keyof typeof FcIcons]; break;
                              case 'fa': Icon = FaIcons[iconName as keyof typeof FaIcons]; break;
                              case 'fa6': Icon = FaIconsSolid[iconName as keyof typeof FaIconsSolid]; break;
                              case 'bs': Icon = BsIcons[iconName as keyof typeof BsIcons]; break;
                              case 'ri': Icon = RiIcons[iconName as keyof typeof RiIcons]; break;
                              case 'gi': Icon = GiIcons[iconName as keyof typeof GiIcons]; break;
                              case 'tb': Icon = TbIcons[iconName as keyof typeof TbIcons]; break;
                              case 'md': Icon = MdIcons[iconName as keyof typeof MdIcons]; break;
                              case 'hi': Icon = HiIcons[iconName as keyof typeof HiIcons]; break;
                              case 'ai': Icon = AiIcons[iconName as keyof typeof AiIcons]; break;
                              case 'io': Icon = IoIcons[iconName as keyof typeof IoIcons]; break;
                              case 'io5': Icon = Io5Icons[iconName as keyof typeof Io5Icons]; break;
                              case 'pi': Icon = PiIcons[iconName as keyof typeof PiIcons]; break;
                              default: Icon = null;
                            }
                            
                            // Create the full icon name with library prefix
                            let prefix = '';
                            switch (selectedIconSet) {
                              case 'fc': prefix = 'Fc'; break;
                              case 'fa': prefix = 'Fa'; break;
                              case 'fa6': prefix = 'Fa6'; break;
                              case 'bs': prefix = 'Bs'; break;
                              case 'ri': prefix = 'Ri'; break;
                              case 'gi': prefix = 'Gi'; break;
                              case 'tb': prefix = 'Tb'; break;
                              case 'md': prefix = 'Md'; break;
                              case 'hi': prefix = 'Hi'; break;
                              case 'ai': prefix = 'Ai'; break;
                              case 'io': prefix = 'Io'; break;
                              case 'io5': prefix = 'Io5'; break;
                              case 'pi': prefix = 'Pi'; break;
                            }
                            
                            const fullIconName = `${prefix}${iconName}`;
                            
                            return (
                              <button
                                key={iconName}
                                onClick={() => selectIcon(fullIconName)}
                                className="aspect-square p-1 flex items-center justify-center bg-[#151b29] hover:bg-[#00C2FF]/10 rounded transition-colors border border-gray-800 hover:border-[#00C2FF]/50"
                                title={iconName}
                              >
                                {Icon ? <Icon className="text-xl" style={{ display: 'block' }} /> : <span className="text-xs text-gray-400">?</span>}
                              </button>
                            );
                          })
                        ) : (
                          <div className="col-span-5 flex flex-col items-center justify-center py-10 text-center bg-[#151b29]/50 rounded-md">
                            <div className="w-16 h-16 mb-3 flex items-center justify-center rounded-full bg-[#1a2234] text-gray-400">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                              </svg>
                            </div>
                            <h3 className="text-base font-medium text-gray-300 mb-1">No icons found</h3>
                            <p className="text-sm text-gray-400 mb-3 max-w-[220px]">
                              Try a different search term or browse another icon library
                            </p>
                            {iconSearchTerm.trim().length > 0 && (
                              <button
                                onClick={() => setIconSearchTerm('')}
                                className="px-3 py-1.5 bg-[#00C2FF]/10 hover:bg-[#00C2FF]/20 text-[#00C2FF] text-xs rounded-md transition-colors flex items-center"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                Clear search
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Footer with info */}
                    <div className="mt-0 pt-1 border-t border-gray-700 flex justify-between items-center text-xs text-gray-400">
                      <div>
                        {getFilteredIcons().length} icons shown
                      </div>
                      <button 
                        onClick={() => setIconSearchTerm('')}
                        className={`text-xs px-2 py-0.5 rounded hover:bg-gray-700 transition-colors ${
                          iconSearchTerm ? 'text-[#00C2FF]' : 'text-gray-500 cursor-default'
                        }`}
                        disabled={!iconSearchTerm}
                      >
                        Clear search
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Title' : 'العنوان'}
              </label>
              <input
                type="text"
                value={newAmenity.title}
                onChange={(e) => setNewAmenity({ ...newAmenity, title: e.target.value })}
                placeholder={language === 'en' ? "e.g. Infinity Pool" : "مثال: مسبح لا نهائي"}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {language === 'en' ? 'Description' : 'الوصف'}
              </label>
              <textarea
                value={newAmenity.description}
                onChange={(e) => setNewAmenity({ ...newAmenity, description: e.target.value })}
                placeholder={language === 'en' ? "Enjoy breathtaking views from our stunning infinity pool that seemingly merges with the horizon." : "استمتع بإطلالات خلابة من حوض السباحة اللانهائي المذهل الذي يندمج على ما يبدو مع الأفق."}
                className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white px-3 py-2"
                rows={3}
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              />
            </div>
            
            <div className="flex justify-end space-x-2 mt-5 gap-2">
              <button
                type="button"
                onClick={() => {
                  setNewAmenity({ icon: 'FcAddressBook', title: '', description: '' });
                  setShowAmenityForm(false);
                  setShowIconSelector(false);
                }}
                className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
              >
                {language === 'en' ? 'Cancel' : 'إلغاء'}
              </button>
              <button
                type="button"
                onClick={addStructuredAmenity}
                disabled={!newAmenity.title.trim() || !newAmenity.icon}
                className={`px-4 py-2 text-sm font-medium text-white rounded ${
                  newAmenity.title.trim() && newAmenity.icon 
                    ? 'bg-[#00C2FF] hover:bg-[#009DB5]' 
                    : 'bg-gray-600 cursor-not-allowed'
                }`}
              >
                {language === 'en' ? 'Add Amenity' : 'إضافة ميزة'}
              </button>
            </div>
          </div>
        )}
      </div>
      
      {getCurrentContent().amenities.length > 1 && !editingAmenityIndex && !showAmenityForm && (
        <p className="mt-4 text-xs text-gray-400">
          {language === 'en' ? 'Drag amenity cards to reorder them.' : 'اسحب بطاقات المرافق لإعادة ترتيبها.'}
        </p>
      )}
    </div>
  );
};

export default ProjectAmenitiesSection; 