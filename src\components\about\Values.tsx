"use client";

import React, { useEffect, useRef, useState } from "react";
import { FaHandshake, FaLightbulb, FaLeaf, FaStar, FaUsers, FaPeopleCarry } from "react-icons/fa";
import { motion, useScroll, useTransform, useSpring, useMotionValue } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

// 3D tilt effect hook
const useMouseTilt = (ref: React.RefObject<HTMLElement>) => {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);
  
  useEffect(() => {
    const element = ref.current;
    if (!element) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;
      
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      
      const xPct = mouseX / width - 0.5;
      const yPct = mouseY / height - 0.5;
      
      x.set(xPct * 3); // Max rotation in degrees
      y.set(yPct * 3);
    };
    
    const handleMouseLeave = () => {
      x.set(0);
      y.set(0);
    };
    
    element.addEventListener("mousemove", handleMouseMove);
    element.addEventListener("mouseleave", handleMouseLeave);
    
    return () => {
      element.removeEventListener("mousemove", handleMouseMove);
      element.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, [ref, x, y]);
  
  return { mouseXSpring, mouseYSpring };
};

const Values = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const valueItemsRef = useRef<Array<HTMLDivElement | null>>([]);
  const titleRef = useRef<HTMLDivElement>(null);
  
  // Add language context
  const { locale } = useLanguage();
  
  // Intersection observer for entrance animation
  const [isInView, setIsInView] = useState(false);
  
  // For parallax scrolling effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  // Parallax animation values for different elements
  const titleOpacity = useTransform(scrollYProgress, [0, 0.1], [0, 1]);
  const titleY = useTransform(scrollYProgress, [0, 0.1], [30, 0]);
  
  // Background element animations
  const floatY1 = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const floatY2 = useTransform(scrollYProgress, [0, 1], [0, -30]);
  const floatY3 = useTransform(scrollYProgress, [0, 1], [0, -20]);
  const rotate1 = useTransform(scrollYProgress, [0, 1], [0, 15]);
  const rotate2 = useTransform(scrollYProgress, [0, 1], [0, -10]);
  
  // Apply 3D tilt effect to the title
  const titleTilt = useMouseTilt(titleRef);
  
  useEffect(() => {
    // Initialize the array with the correct number of refs
    valueItemsRef.current = valueItemsRef.current.slice(0, 6);
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in");
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: "0px 0px -100px 0px"
      }
    );
    
    // Observe each value card
    valueItemsRef.current.forEach((ref) => {
      if (ref) {
        observer.observe(ref);
      }
    });
    
    // Observe section for overall animation
    const sectionObserver = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          entry.target.classList.add("section-reveal");
        }
      },
      {
        threshold: 0.15
      }
    );
    
    if (sectionRef.current) {
      sectionObserver.observe(sectionRef.current);
    }
    
    return () => {
      observer.disconnect();
      sectionObserver.disconnect();
    };
  }, []);

  const values = [
    {
      icon: <FaHandshake className="h-6 w-6" />,
      title: t('values.integrity.title', locale),
      description: t('values.integrity.description', locale),
    },
    {
      icon: <FaStar className="h-6 w-6" />,
      title: t('values.excellence.title', locale),
      description: t('values.excellence.description', locale),
    },
    {
      icon: <FaLightbulb className="h-6 w-6" />,
      title: t('values.innovation.title', locale),
      description: t('values.innovation.description', locale),
    },
    {
      icon: <FaLeaf className="h-6 w-6" />,
      title: t('values.sustainability.title', locale),
      description: t('values.sustainability.description', locale),
    },
    {
      icon: <FaUsers className="h-6 w-6" />,
      title: t('values.community.title', locale),
      description: t('values.community.description', locale),
    },
    {
      icon: <FaPeopleCarry className="h-6 w-6" />,
      title: t('values.teamwork.title', locale),
      description: t('values.teamwork.description', locale),
    },
  ];

  return (
    <section 
      ref={sectionRef}
      className="relative py-24 md:py-32 overflow-hidden opacity-0 transform translate-y-10 transition-all duration-1000 ease-out"
    >
      {/* Advanced Gradient Background */}
      <div className="absolute inset-0 bg-[rgb(var(--color-background))] z-0 overflow-hidden">
        <div 
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: "radial-gradient(circle at 80% 20%, rgba(var(--color-primary), 0.15), transparent 25%), radial-gradient(circle at 20% 80%, rgba(var(--color-secondary), 0.15), transparent 25%)"
          }}
        ></div>
        
        <motion.div 
          className="absolute top-0 start-0 w-full h-full opacity-20"
          style={{
            backgroundImage: "linear-gradient(135deg, rgba(var(--color-primary), 0.1) 0%, transparent 50%, rgba(var(--color-secondary), 0.05) 100%)",
            y: useTransform(scrollYProgress, [0, 1], [0, 50])
          }}
        ></motion.div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-[0.03]" 
          style={{ 
            backgroundImage: "radial-gradient(rgba(var(--color-text), 0.8) 1px, transparent 1px)",
            backgroundSize: "40px 40px" 
          }}>
        </div>
      </div>
      
      {/* Animated Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Floating element 1 */}
        <motion.div 
          className="absolute top-[10%] end-[10%] w-64 h-64 rounded-full bg-[rgb(var(--color-primary))]/5 blur-3xl opacity-60"
          style={{ 
            y: floatY1,
            rotate: rotate1
          }}
        ></motion.div>
        
        {/* Floating element 2 */}
        <motion.div 
          className="absolute bottom-[15%] start-[10%] w-72 h-72 rounded-full bg-[rgb(var(--color-secondary))]/5 blur-3xl opacity-60"
          style={{ 
            y: floatY2,
            rotate: rotate2
          }}
        ></motion.div>
        
        {/* Floating element 3 */}
        <motion.div 
          className="absolute top-[40%] start-[5%] w-48 h-48 rounded-full bg-[rgb(var(--color-primary))]/10 blur-xl opacity-50"
          style={{ y: floatY3 }}
        ></motion.div>
        
        {/* Ornamental lines */}
        <svg className="absolute inset-0 w-full h-full opacity-[0.03]" preserveAspectRatio="none">
          <motion.path 
            d="M0,100 Q300,120 600,100 T1200,100" 
            stroke="rgb(var(--color-primary))" 
            fill="none" 
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
            transition={{ duration: 2, ease: "easeOut", delay: 0.5 }}
          />
          <motion.path 
            d="M0,140 Q300,160 600,140 T1200,140" 
            stroke="rgb(var(--color-secondary))" 
            fill="none" 
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={isInView ? { pathLength: 1 } : { pathLength: 0 }}
            transition={{ duration: 2, ease: "easeOut", delay: 0.8 }}
          />
        </svg>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Title with 3D effect */}
        <motion.div 
          ref={titleRef}
          className="max-w-3xl mx-auto text-center mb-20"
          style={{ 
            opacity: titleOpacity, 
            y: titleY,
            rotateX: titleTilt.mouseYSpring,
            rotateY: titleTilt.mouseXSpring,
            transformPerspective: "1000px"
          }}
        >
          <div className="perspective-1000 relative">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-[rgb(var(--color-text))]">
              {t('values.title', locale).includes('Values') || t('values.title', locale).includes('قيمنا') ? (
                <>
                  {t('values.title', locale).split(' ').slice(0, -1).join(' ')} <span className="text-[rgb(var(--color-primary))]">{t('values.title', locale).split(' ').slice(-1)[0]}</span>
                </>
              ) : (
                t('values.title', locale)
              )}
            </h2>
            <div className="w-32 h-1.5 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] mx-auto mb-8 rounded-full"></div>
            <p className="text-lg md:text-xl text-[rgb(var(--color-text-secondary))]">
              {t('values.description', locale)}
            </p>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 max-w-5xl mx-auto">
          {values.map((value, index) => (
            <div
              key={index}
              ref={(el) => {
                valueItemsRef.current[index] = el;
              }}
              className="opacity-0 translate-y-8 h-full perspective-1000"
              style={{ 
                transitionDelay: `${index * 100}ms`,
              }}
            >
              <div 
                className="bg-[rgba(var(--color-background),0.8)] backdrop-blur-sm h-full rounded-xl border border-[rgba(var(--color-text),0.08)] shadow-lg hover:shadow-xl overflow-hidden transition-all duration-500 hover:translate-y-[-4px] flex flex-col group"
                style={{ transformStyle: "preserve-3d" }}
              >
                <div className="relative p-4 border-b border-[rgba(var(--color-text),0.06)] flex items-center transform transition-transform duration-300 group-hover:translate-z-4">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] flex items-center justify-center text-white me-3 shadow-md transform group-hover:scale-110 transition-all duration-300">
                    {value.icon}
                    <div className="absolute inset-0 rounded-lg bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                  </div>
                  <h3 className="text-lg font-bold text-[rgb(var(--color-text))] group-hover:text-[rgb(var(--color-primary))] transition-colors duration-300">{value.title}</h3>
                </div>
                <div className="p-4 flex-grow relative">
                  {/* Subtle decorative element */}
                  <div className="absolute top-0 end-0 w-16 h-16 opacity-[0.04] rounded-full bg-gradient-to-br from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] blur-lg transform -translate-y-1/2 translate-x-1/2"></div>
                  
                  <p className="text-[rgb(var(--color-text-secondary))] text-sm relative z-10">
                    {value.description}
                  </p>
                </div>
                <div className="px-4 pb-4">
                  <div className="w-8 h-0.5 bg-gradient-to-r from-[rgb(var(--color-primary))] to-transparent rounded-full"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <style jsx>{`
        .animate-in {
          opacity: 1 !important;
          transform: translateY(0) !important;
          transition: all 800ms cubic-bezier(0.2, 0.8, 0.2, 1);
        }
        
        .section-reveal {
          opacity: 1 !important;
          transform: translateY(0) !important;
        }
        
        .perspective-1000 {
          perspective: 1000px;
        }
      `}</style>
    </section>
  );
};

export default Values; 