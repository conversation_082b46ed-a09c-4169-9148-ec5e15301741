"use client";

import { useEffect, useRef } from "react";

// Define types outside useEffect to avoid scope issues
type BuildingWindow = { x: number; y: number; size: number; lit: boolean };
type Star = { x: number; y: number; size: number; opacity: number; twinkleSpeed: number; twinklePhase: number };

const CompanyIntroBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    let animationFrameId: number;
    let starPhase = 0;
    let pixelRatio = window.devicePixelRatio || 1;

    // Define classes first before using them
    class Building {
      x: number;
      width: number;
      height: number;
      speed: number;
      color: string;
      opacity: number;
      windows: BuildingWindow[];

      constructor(x: number, width: number, height: number) {
        this.x = x;
        this.width = width;
        this.height = height;
        this.speed = 0.05;
        this.opacity = 0.05 + Math.random() * 0.1;
        this.color = Math.random() > 0.5 
          ? `rgba(14, 198, 224, ${this.opacity})` // Light blue
          : `rgba(151, 71, 255, ${this.opacity})`; // Purple
        
        // Create windows
        this.windows = [];
        const windowSize = Math.min(5, width / 10);
        const windowSpacing = windowSize * 1.5;
        const windowsPerFloor = Math.max(1, Math.floor((width - 10) / windowSpacing));
        const floors = Math.max(1, Math.floor((height - 20) / windowSpacing));
        
        for (let floor = 0; floor < floors; floor++) {
          for (let i = 0; i < windowsPerFloor; i++) {
            this.windows.push({
              x: this.x + 5 + i * windowSpacing,
              y: canvas!.height - this.height + 10 + floor * windowSpacing,
              size: windowSize,
              lit: Math.random() > 0.5
            });
          }
        }
      }

      draw() {
        if (!ctx) return;
        
        // Draw building
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, canvas!.height - this.height, this.width, this.height);
        
        // Draw windows
        this.windows.forEach(window => {
          if (window.lit) {
            ctx.fillStyle = "rgba(255, 255, 200, 0.7)";
            ctx.fillRect(window.x, window.y, window.size, window.size);
          } else {
            ctx.fillStyle = "rgba(5, 10, 30, 0.3)";
            ctx.fillRect(window.x, window.y, window.size, window.size);
          }
        });
      }

      update() {
        // Slowly animate windows (occasional blinking)
        if (Math.random() > 0.995) {
          const randomWindow = Math.floor(Math.random() * this.windows.length);
          if (this.windows[randomWindow]) {
            this.windows[randomWindow].lit = !this.windows[randomWindow].lit;
          }
        }
      }
    }

    class Particle {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      color: string;
      opacity: number;

      constructor() {
        this.x = Math.random() * canvas!.width;
        this.y = Math.random() * canvas!.height;
        this.size = Math.random() * 1.5;
        this.speedX = Math.random() * 0.2 - 0.1;
        this.speedY = Math.random() * 0.1 - 0.05;
        this.opacity = Math.random() * 0.3;
        this.color = Math.random() > 0.5
          ? `rgba(14, 198, 224, ${this.opacity})` // Light blue
          : `rgba(151, 71, 255, ${this.opacity})`; // Purple
      }

      draw() {
        if (!ctx) return;
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
      }

      update() {
        this.x += this.speedX;
        this.y += this.speedY;

        // Reset particles when they move off-screen
        if (this.x < 0 || this.x > canvas!.width || this.y < 0 || this.y > canvas!.height) {
          this.x = Math.random() * canvas!.width;
          this.y = Math.random() * canvas!.height;
        }
      }
    }

    // After defining classes, initialize collections
    let buildings: Building[] = [];
    let particles: Particle[] = [];
    let stars: Star[] = [];
    let moonX: number;
    let moonY: number;
    let moonSize: number;
    let moonGlow: number;

    // Set canvas dimensions and handle high DPI displays
    const handleResize = () => {
      pixelRatio = window.devicePixelRatio || 1;
      
      // Get actual container dimensions
      const rect = container.getBoundingClientRect();
      const displayWidth = rect.width;
      const displayHeight = rect.height;
      
      // Set canvas size for high DPI displays
      canvas.width = displayWidth * pixelRatio;
      canvas.height = displayHeight * pixelRatio;
      
      // Scale all drawing operations
      ctx.scale(pixelRatio, pixelRatio);
      
      // Set canvas CSS size
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;
      
      initBuildings();
      initParticles();
      initStars();
      initMoon();
    };

    // Handle window resizing
    window.addEventListener("resize", handleResize);
    handleResize();

    function initBuildings() {
      buildings = [];
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      const buildingCount = Math.max(5, Math.floor(displayWidth / 150));
      let currentX = -50;

      while (currentX < displayWidth + 100) {
        const width = 30 + Math.random() * 100;
        const height = 100 + Math.random() * (displayHeight * 0.6);
        buildings.push(new Building(currentX, width, height));
        currentX += width - 10 - Math.random() * 20; // Slightly overlapping buildings
      }
    }

    function initParticles() {
      particles = [];
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      const particleCount = Math.min(100, Math.floor(displayWidth * displayHeight / 15000));
      for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
      }
    }

    function initStars() {
      stars = [];
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      const starCount = Math.min(150, Math.floor(displayWidth * displayHeight / 6000));
      
      for (let i = 0; i < starCount; i++) {
        // Distribute stars more densely in the top portion of the canvas
        const yFactor = Math.pow(Math.random(), 2); // Square to bias towards smaller values
        stars.push({
          x: Math.random() * displayWidth,
          y: yFactor * (displayHeight * 0.7), // Stars only in top 70% of screen
          size: 0.5 + Math.random() * 1.5,
          opacity: 0.3 + Math.random() * 0.7,
          twinkleSpeed: 0.01 + Math.random() * 0.05,
          twinklePhase: Math.random() * Math.PI * 2
        });
      }
    }

    function initMoon() {
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      // Position the moon in the upper portion of the canvas
      moonX = displayWidth * (0.7 + Math.random() * 0.2); // Position in end side
      moonY = displayHeight * (0.15 + Math.random() * 0.15); // Position in top portion
      
      // Size moon proportionally to the smaller dimension of the canvas to maintain aspect ratio
      const minDimension = Math.min(displayWidth, displayHeight);
      moonSize = minDimension * 0.05; // Moon size relative to canvas size
      moonGlow = moonSize * 2.5; // Glow radius around moon
    }

    function drawStars() {
      if (!ctx) return;
      
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      starPhase += 0.01; // Increment the global phase
      
      stars.forEach(star => {
        // Calculate current twinkle value (0 to 1)
        const twinkle = 0.5 + 0.5 * Math.sin(starPhase * star.twinkleSpeed + star.twinklePhase);
        
        // Draw star with varying opacity based on twinkle
        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity * twinkle})`;
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
        ctx.fill();
        
        // Add subtle glow for brighter stars
        if (star.size > 1) {
          const gradient = ctx.createRadialGradient(
            star.x, star.y, 0,
            star.x, star.y, star.size * 4
          );
          gradient.addColorStop(0, `rgba(255, 255, 255, ${0.3 * star.opacity * twinkle})`);
          gradient.addColorStop(1, "rgba(255, 255, 255, 0)");
          
          ctx.fillStyle = gradient;
          ctx.beginPath();
          ctx.arc(star.x, star.y, star.size * 4, 0, Math.PI * 2);
          ctx.fill();
        }
      });
    }

    function drawMoon() {
      if (!ctx) return;
      
      // Save the current context state
      ctx.save();
      
      // Draw moon glow
      const glowGradient = ctx.createRadialGradient(
        moonX, moonY, moonSize * 0.8,
        moonX, moonY, moonGlow
      );
      glowGradient.addColorStop(0, "rgba(200, 220, 255, 0.3)");
      glowGradient.addColorStop(1, "rgba(200, 220, 255, 0)");
      
      ctx.fillStyle = glowGradient;
      ctx.beginPath();
      ctx.arc(moonX, moonY, moonGlow, 0, Math.PI * 2);
      ctx.fill();
      
      // Draw moon
      const moonGradient = ctx.createRadialGradient(
        moonX - moonSize * 0.2, moonY - moonSize * 0.2, 0,
        moonX, moonY, moonSize
      );
      moonGradient.addColorStop(0, "rgba(240, 240, 245, 1)");
      moonGradient.addColorStop(0.8, "rgba(210, 220, 240, 1)");
      moonGradient.addColorStop(1, "rgba(180, 190, 210, 1)");
      
      ctx.fillStyle = moonGradient;
      ctx.beginPath();
      ctx.arc(moonX, moonY, moonSize, 0, Math.PI * 2);
      ctx.fill();
      
      // Draw subtle moon craters
      ctx.fillStyle = "rgba(150, 160, 180, 0.2)";
      
      // First crater
      const crater1X = moonX - moonSize * 0.3;
      const crater1Y = moonY + moonSize * 0.1;
      const crater1Size = moonSize * 0.2;
      
      ctx.beginPath();
      ctx.arc(crater1X, crater1Y, crater1Size, 0, Math.PI * 2);
      ctx.fill();
      
      // Second crater
      const crater2X = moonX + moonSize * 0.2;
      const crater2Y = moonY - moonSize * 0.3;
      const crater2Size = moonSize * 0.15;
      
      ctx.beginPath();
      ctx.arc(crater2X, crater2Y, crater2Size, 0, Math.PI * 2);
      ctx.fill();
      
      // Third crater
      const crater3X = moonX + moonSize * 0.1;
      const crater3Y = moonY + moonSize * 0.4;
      const crater3Size = moonSize * 0.1;
      
      ctx.beginPath();
      ctx.arc(crater3X, crater3Y, crater3Size, 0, Math.PI * 2);
      ctx.fill();
      
      // Restore the context state
      ctx.restore();
    }

    function drawGrid() {
      if (!ctx) return;
      
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      ctx.strokeStyle = 'rgba(120, 120, 180, 0.05)';
      ctx.lineWidth = 0.5;
      
      // Draw vertical lines
      const gridSize = 40;
      for (let x = 0; x < displayWidth; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, displayHeight);
        ctx.stroke();
      }
      
      // Draw horizontal lines
      for (let y = 0; y < displayHeight; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(displayWidth, y);
        ctx.stroke();
      }
    }

    function drawBlueprint() {
      if (!ctx) return;
      
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      // Create blueprint-style elements
      const numCircles = 3;
      const numLines = 15;
      
      // Draw circles
      for (let i = 0; i < numCircles; i++) {
        const x = Math.random() * displayWidth;
        const y = Math.random() * displayHeight;
        const radius = 20 + Math.random() * 100;
        
        ctx.strokeStyle = i % 2 === 0 
          ? 'rgba(14, 198, 224, 0.04)' 
          : 'rgba(151, 71, 255, 0.04)';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.stroke();
        
        // Add crosshair
        ctx.beginPath();
        ctx.moveTo(x - 10, y);
        ctx.lineTo(x + 10, y);
        ctx.moveTo(x, y - 10);
        ctx.lineTo(x, y + 10);
        ctx.stroke();
      }
      
      // Draw architectural lines
      for (let i = 0; i < numLines; i++) {
        const x1 = Math.random() * displayWidth;
        const y1 = Math.random() * displayHeight;
        const x2 = x1 + (Math.random() * 200 - 100);
        const y2 = y1 + (Math.random() * 200 - 100);
        
        ctx.strokeStyle = i % 2 === 0 
          ? 'rgba(14, 198, 224, 0.03)' 
          : 'rgba(151, 71, 255, 0.03)';
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
      }
    }

    function animate() {
      if (!ctx) return;
      
      const displayWidth = canvas!.width / pixelRatio;
      const displayHeight = canvas!.height / pixelRatio;
      
      // Clear with proper dimensions
      ctx.clearRect(0, 0, displayWidth, displayHeight);
      
      // Draw background with gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, displayHeight);
      gradient.addColorStop(0, 'rgba(10, 15, 35, 0.05)');
      gradient.addColorStop(1, 'rgba(10, 15, 35, 0.1)');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, displayWidth, displayHeight);
      
      // Draw stars and moon in the sky (behind buildings)
      drawStars();
      drawMoon();
      
      // Draw grid and blueprint elements
      drawGrid();
      drawBlueprint();
      
      // Draw skyline
      buildings.forEach(building => {
        building.update();
        building.draw();
      });
      
      // Draw particles
      particles.forEach(particle => {
        particle.update();
        particle.draw();
      });
      
      animationFrameId = requestAnimationFrame(animate);
    }

    animate();

    return () => {
      window.removeEventListener("resize", handleResize);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <div ref={containerRef} className="absolute inset-0 w-full h-full overflow-hidden">
      <canvas
        ref={canvasRef}
        className="pointer-events-none opacity-70"
      />
    </div>
  );
};

export default CompanyIntroBackground; 