"use client";

import React from 'react';
import { 
  FiTrash2, FiDownload, FiX, FiUser<PERSON>heck, FiUserX
} from 'react-icons/fi';

interface BulkActionsBarProps {
  selectedCount: number;
  onActivateAll: () => void;
  onSuspendAll: () => void;
  onDeleteAll: () => void;
  onExportSelected: () => void;
  onClearSelection: () => void;
  onSelectAll: () => void;
  totalCount: number;
}

export default function BulkActionsBar({
  selectedCount,
  onActivateAll,
  onSuspendAll,
  onDeleteAll,
  onExportSelected,
  onClearSelection,
  onSelectAll,
  totalCount
}: BulkActionsBarProps) {
  if (selectedCount === 0) return null;

  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-white font-medium">
              {selectedCount} of {totalCount} selected
            </span>
            <button
              onClick={onSelectAll}
              className="text-[#00C2FF] hover:text-[#00C2FF]/80 text-sm"
            >
              Select all
            </button>
            <button
              onClick={onClearSelection}
              className="text-gray-400 hover:text-white text-sm"
            >
              Clear selection
            </button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={onActivateAll}
            className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 transition-colors flex items-center text-sm"
            title="Activate all selected users"
          >
            <FiUserCheck className="mr-2 h-4 w-4" />
            Activate
          </button>

          <button
            onClick={onSuspendAll}
            className="px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-500 transition-colors flex items-center text-sm"
            title="Suspend all selected users"
          >
            <FiUserX className="mr-2 h-4 w-4" />
            Suspend
          </button>

          <button
            onClick={onExportSelected}
            className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors flex items-center text-sm"
            title="Export selected users"
          >
            <FiDownload className="mr-2 h-4 w-4" />
            Export
          </button>

          <button
            onClick={() => {
              if (confirm(`Are you sure you want to delete ${selectedCount} users? This action cannot be undone.`)) {
                onDeleteAll();
              }
            }}
            className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors flex items-center text-sm"
            title="Delete all selected users"
          >
            <FiTrash2 className="mr-2 h-4 w-4" />
            Delete
          </button>

          <button
            onClick={onClearSelection}
            className="p-2 text-gray-400 hover:text-white transition-colors"
            title="Clear selection"
          >
            <FiX className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
} 