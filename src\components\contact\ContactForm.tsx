"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

// Background component with architectural design
const ArchitecturalBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden opacity-8 pointer-events-none z-0">
      <div className="absolute inset-0 bg-gradient-to-br from-background/90 via-background/70 to-background/95 z-0"></div>
      <svg 
        width="100%" 
        height="100%" 
        viewBox="0 0 1000 1000" 
        xmlns="http://www.w3.org/2000/svg"
        className="architectural-blueprint relative z-1"
        style={{ transform: 'scale(1.2)' }}
      >
        {/* Horizontal grid lines */}
        {Array.from({ length: 20 }).map((_, i) => (
          <line 
            key={`h-line-${i}`} 
            x1="0" 
            y1={i * 50} 
            x2="1000" 
            y2={i * 50} 
            className="grid-line"
          />
        ))}
        
        {/* Vertical grid lines */}
        {Array.from({ length: 20 }).map((_, i) => (
          <line 
            key={`v-line-${i}`} 
            x1={i * 50} 
            y1="0" 
            x2={i * 50} 
            y2="1000" 
            className="grid-line"
          />
        ))}
        
        {/* Architectural elements - Room outlines */}
        <rect x="100" y="100" width="300" height="200" className="room-outline" />
        <rect x="500" y="150" width="400" height="300" className="room-outline" />
        <rect x="200" y="400" width="250" height="200" className="room-outline" />
        <rect x="600" y="500" width="300" height="250" className="room-outline" />
        
        {/* Floor plan details */}
        <path d="M100,100 L100,300 L400,300 L400,100 Z" className="floor-detail" />
        <path d="M500,150 L500,450 L900,450 L900,150 Z" className="floor-detail" />
        <path d="M200,400 L200,600 L450,600 L450,400 Z" className="floor-detail" />
        <path d="M600,500 L600,750 L900,750 L900,500 Z" className="floor-detail" />
        
        {/* Interior walls */}
        <line x1="250" y1="100" x2="250" y2="300" className="interior-wall" />
        <line x1="700" y1="150" x2="700" y2="450" className="interior-wall" />
        <line x1="650" y1="500" x2="650" y2="750" className="interior-wall" />
        <line x1="600" y1="600" x2="900" y2="600" className="interior-wall" />
        
        {/* Architectural details - Doors */}
        <path d="M100,200 A50,50 0 0,1 150,150" className="detail-line" />
        <path d="M400,200 A50,50 0 0,0 350,150" className="detail-line" />
        <path d="M500,300 A60,60 0 0,1 560,240" className="detail-line" />
        <path d="M450,400 A40,40 0 0,0 410,360" className="detail-line" />
        <path d="M650,550 A30,30 0 0,1 680,520" className="detail-line" />
        
        {/* Architectural details - Windows */}
        <line x1="150" y1="100" x2="230" y2="100" className="window-line" />
        <line x1="600" y1="150" x2="680" y2="150" className="window-line" />
        <line x1="600" y1="450" x2="680" y2="450" className="window-line" />
        <line x1="200" y1="600" x2="280" y2="600" className="window-line" />
        <line x1="750" y1="500" x2="830" y2="500" className="window-line" />
        
        {/* Architectural details - Dimensions */}
        <line x1="100" y1="350" x2="400" y2="350" className="dimension-line" />
        <line x1="500" y1="500" x2="900" y2="500" className="dimension-line" />
        <line x1="150" y1="400" x2="150" y2="600" className="dimension-line" />
        <line x1="550" y1="150" x2="550" y2="450" className="dimension-line" />
        
        {/* Furniture and fixtures */}
        <rect x="320" y="120" width="60" height="40" className="furniture" />
        <rect x="150" y="150" width="80" height="40" className="furniture" />
        <circle cx="600" cy="250" r="30" className="furniture" />
        <circle cx="800" cy="350" r="30" className="furniture" />
        <rect x="700" y="650" width="80" height="60" className="furniture" />
        
        {/* Blueprint annotations */}
        <path d="M250,50 L300,50 L320,70 L340,50 L380,50" className="annotation" />
        <path d="M650,100 L700,100 L720,80 L740,100 L780,100" className="annotation" />
        <path d="M450,550 L500,550 L520,570 L540,550 L580,550" className="annotation" />
        
        {/* Circles for specific points */}
        <circle cx="100" cy="100" r="4" className="point-marker" />
        <circle cx="400" cy="100" r="4" className="point-marker" />
        <circle cx="100" cy="300" r="4" className="point-marker" />
        <circle cx="400" cy="300" r="4" className="point-marker" />
        <circle cx="500" cy="150" r="4" className="point-marker" />
        <circle cx="900" cy="150" r="4" className="point-marker" />
        <circle cx="500" cy="450" r="4" className="point-marker" />
        <circle cx="900" cy="450" r="4" className="point-marker" />
        
        {/* Animated Light Points - These will be animated with CSS */}
        <circle className="light-point light-1" cx="0" cy="0" r="3">
          <animateMotion 
            path="M100,100 L400,100 L400,300 L100,300 L100,100 M250,100 L250,300 M150,100 L230,100" 
            dur="15s" 
            repeatCount="indefinite"
          />
        </circle>
        
        <circle className="light-point light-2" cx="0" cy="0" r="3">
          <animateMotion 
            path="M500,150 L900,150 L900,450 L500,450 L500,150 M700,150 L700,450 M600,150 L680,150 M600,450 L680,450" 
            dur="18s" 
            repeatCount="indefinite"
          />
        </circle>
        
        <circle className="light-point light-3" cx="0" cy="0" r="2">
          <animateMotion 
            path="M0,0 L1000,0 L1000,1000 L0,1000 L0,0" 
            dur="30s" 
            repeatCount="indefinite"
          />
        </circle>
        
        <circle className="light-point light-4" cx="0" cy="0" r="2">
          <animateMotion 
            path="M200,400 L450,400 L450,600 L200,600 L200,400 M200,600 L280,600" 
            dur="12s" 
            repeatCount="indefinite"
          />
        </circle>
        
        <circle className="light-point light-5" cx="0" cy="0" r="2">
          <animateMotion 
            path="M0,500 L1000,500 M600,500 L900,500 L900,750 L600,750 L600,500 M600,600 L900,600 M650,500 L650,750 M750,500 L830,500" 
            dur="22s" 
            repeatCount="indefinite"
          />
        </circle>
        
        <circle className="light-point light-6" cx="0" cy="0" r="2.5">
          <animateMotion 
            path="M100,350 L400,350 M150,400 L150,600 M550,150 L550,450 M450,550 L580,550" 
            dur="20s" 
            repeatCount="indefinite"
          />
        </circle>
        
        <circle className="light-point light-7" cx="0" cy="0" r="1.5">
          <animateMotion 
            path="M650,100 L780,100 M250,50 L380,50 M320,120 L380,120 L380,160 L320,160 L320,120 M150,150 L230,150 L230,190 L150,190 L150,150" 
            dur="16s" 
            repeatCount="indefinite"
          />
        </circle>
      </svg>
      
      {/* Add CSS for the background */}
      <style jsx>{`
        .architectural-blueprint {
          filter: blur(0.5px);
          opacity: 0.7;
        }
        
        @keyframes glowEffect {
          0% { filter: blur(2px) drop-shadow(0 0 6px rgb(var(--color-primary))); opacity: 0.8; }
          50% { filter: blur(4px) drop-shadow(0 0 12px rgb(var(--color-primary))); opacity: 1; }
          100% { filter: blur(2px) drop-shadow(0 0 6px rgb(var(--color-primary))); opacity: 0.8; }
        }
        
        @keyframes glowEffectSecondary {
          0% { filter: blur(2px) drop-shadow(0 0 6px rgb(var(--color-secondary))); opacity: 0.7; }
          50% { filter: blur(4px) drop-shadow(0 0 12px rgb(var(--color-secondary))); opacity: 0.9; }
          100% { filter: blur(2px) drop-shadow(0 0 6px rgb(var(--color-secondary))); opacity: 0.7; }
        }
        
        @keyframes pulseThin {
          0% { stroke-opacity: 0.1; }
          50% { stroke-opacity: 0.15; }
          100% { stroke-opacity: 0.1; }
        }
        
        @keyframes pulseMedium {
          0% { stroke-opacity: 0.15; }
          50% { stroke-opacity: 0.25; }
          100% { stroke-opacity: 0.15; }
        }
        
        @keyframes pulseThick {
          0% { stroke-opacity: 0.25; }
          50% { stroke-opacity: 0.4; }
          100% { stroke-opacity: 0.25; }
        }
        
        @keyframes pulseFill {
          0% { fill-opacity: 0.03; }
          50% { fill-opacity: 0.06; }
          100% { fill-opacity: 0.03; }
        }
        
        .grid-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 0.5;
          stroke-opacity: 0.1;
          animation: pulseThin 8s infinite ease-in-out;
        }
        
        .room-outline {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 1.5;
          stroke-opacity: 0.25;
          animation: pulseThick 12s infinite ease-in-out;
        }
        
        .floor-detail {
          fill: rgb(var(--color-primary));
          fill-opacity: 0.03;
          stroke: rgb(var(--color-primary));
          stroke-width: 0.8;
          stroke-opacity: 0.2;
          animation: pulseFill 10s infinite ease-in-out, pulseMedium 10s infinite ease-in-out;
        }
        
        .interior-wall {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.2;
          stroke-opacity: 0.15;
          stroke-dasharray: 1, 1;
          animation: pulseMedium 15s infinite ease-in-out;
        }
        
        .detail-line {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 1.2;
          stroke-opacity: 0.2;
          animation: pulseMedium 12s infinite ease-in-out;
        }
        
        .window-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 2;
          stroke-opacity: 0.3;
          animation: pulseThick 10s infinite ease-in-out;
        }
        
        .dimension-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 0.8;
          stroke-opacity: 0.15;
          stroke-dasharray: 5, 3;
          animation: pulseMedium 18s infinite ease-in-out;
        }
        
        .furniture {
          fill: rgb(var(--color-primary));
          fill-opacity: 0.08;
          stroke: rgb(var(--color-primary));
          stroke-width: 0.8;
          stroke-opacity: 0.2;
          animation: pulseFill 14s infinite ease-in-out, pulseMedium 14s infinite ease-in-out;
        }
        
        .annotation {
          fill: none;
          stroke: rgb(var(--color-secondary));
          stroke-width: 0.8;
          stroke-opacity: 0.2;
          animation: pulseMedium 16s infinite ease-in-out;
        }
        
        .point-marker {
          fill: rgb(var(--color-secondary));
          fill-opacity: 0.3;
          stroke: none;
          animation: glowEffectSecondary 10s infinite;
        }
        
        .light-point {
          fill: #fff;
          filter: blur(1px);
          opacity: 0.8;
        }
        
        .light-1 {
          fill: rgb(var(--color-primary));
          filter: blur(3px) drop-shadow(0 0 8px rgb(var(--color-primary)));
          animation: glowEffect 3s infinite;
        }
        
        .light-2 {
          fill: rgb(var(--color-secondary));
          filter: blur(3px) drop-shadow(0 0 8px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 4s infinite;
        }
        
        .light-3 {
          fill: #ffffff;
          filter: blur(2px) drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
        }
        
        .light-4 {
          fill: rgb(var(--color-primary));
          filter: blur(2px) drop-shadow(0 0 6px rgb(var(--color-primary)));
          animation: glowEffect 4s infinite;
        }
        
        .light-5 {
          fill: rgb(var(--color-secondary));
          filter: blur(2px) drop-shadow(0 0 6px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 3.5s infinite;
        }
        
        .light-6 {
          fill: rgb(var(--color-primary));
          filter: blur(4px) drop-shadow(0 0 10px rgb(var(--color-primary)));
          animation: glowEffect 5s infinite;
        }
        
        .light-7 {
          fill: rgb(var(--color-secondary));
          filter: blur(3px) drop-shadow(0 0 8px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 4.5s infinite;
        }
      `}</style>
    </div>
  );
};

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    interest: "",
  });

  const [touched, setTouched] = useState({
    name: false,
    email: false,
    phone: false,
    subject: false,
    message: false,
    interest: false,
  });

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    interest: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState("");
  const [formFilled, setFormFilled] = useState(0);
  
  // Custom dropdown state
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    }
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Interest options for dropdown
  const interestOptions = [
    { value: "investment", label: "Investment Opportunities" },
    { value: "residential", label: "Residential Properties" },
    { value: "commercial", label: "Commercial Properties" },
    { value: "consultation", label: "Real Estate Consultation" },
    { value: "other", label: "Other" },
  ];

  // Validate form data
  useEffect(() => {
    const newErrors = {
      name: !formData.name && touched.name ? "Name is required" : "",
      email: touched.email 
        ? !formData.email 
          ? "Email is required" 
          : !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email) 
            ? "Please enter a valid email" 
            : ""
        : "",
      phone: touched.phone && formData.phone && !/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/.test(formData.phone)
        ? "Please enter a valid phone number"
        : "",
      subject: !formData.subject && touched.subject ? "Subject is required" : "",
      message: !formData.message && touched.message ? "Message is required" : "",
      interest: !formData.interest && touched.interest ? "Please select an interest" : "",
    };
    
    setErrors(newErrors);
    
    // Calculate percentage of form filled
    const fields = Object.keys(formData);
    const requiredFields = fields.filter(field => field !== 'phone'); // Phone is optional
    const filledRequired = requiredFields.filter(field => formData[field as keyof typeof formData]).length;
    setFormFilled((filledRequired / requiredFields.length) * 100);
    
  }, [formData, touched]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name } = e.target;
    setTouched((prev) => ({ ...prev, [name]: true }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Touch all fields to show errors
    const touchedFields = Object.keys(touched).reduce((acc, field) => {
      acc[field as keyof typeof touched] = true;
      return acc;
    }, {} as typeof touched);
    
    setTouched(touchedFields);
    
    // Check if there are any errors
    const hasErrors = Object.values(errors).some(error => error);
    const requiredFieldsMissing = !formData.name || !formData.email || !formData.subject || !formData.message || !formData.interest;
    
    if (hasErrors || requiredFieldsMissing) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitError("");

    try {
      // In a real application, you would submit this data to your backend API
      // This is just a mock example of the submission process
      await new Promise((resolve) => setTimeout(resolve, 1500));
      
      console.log("Form submitted:", formData);
      setSubmitSuccess(true);
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
        interest: "",
      });
      
      // Reset touched state
      setTouched({
        name: false,
        email: false,
        phone: false,
        subject: false,
        message: false,
        interest: false,
      });
      
    } catch (error) {
      console.error("Error submitting form:", error);
      setSubmitError("There was an error submitting your form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle interest selection in custom dropdown
  const handleInterestSelect = (value: string) => {
    setFormData((prev) => ({ ...prev, interest: value }));
    setTouched((prev) => ({ ...prev, interest: true }));
    setDropdownOpen(false);
  };

  return (
    <div className="relative">
      {/* Progress bar */}
      <div className="absolute -top-2 start-0 w-full h-1 bg-background rounded-full overflow-hidden">
        <motion.div 
          className="h-full bg-brand-gradient"
          initial={{ width: 0 }}
          animate={{ width: `${formFilled}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      
      {/* Success/error messages */}
      <AnimatePresence>
      {submitSuccess && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-green-900/20 border border-green-500/30 text-green-400 rounded-lg p-6 mb-8 shadow-xl"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 me-3">
                <svg className="h-6 w-6 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">Message Sent Successfully!</h3>
                <p>Thank you for contacting us. We have received your message and will get back to you soon.</p>
              </div>
            </div>
            <div className="mt-4 flex justify-end">
              <button 
                onClick={() => setSubmitSuccess(false)} 
                className="text-sm bg-green-500/20 text-green-400 px-4 py-2 rounded hover:bg-green-500/30 transition-colors"
              >
                Dismiss
              </button>
        </div>
          </motion.div>
      )}

      {submitError && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-red-900/20 border border-red-500/30 text-red-400 rounded-lg p-6 mb-8 shadow-xl"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0 me-3">
                <svg className="h-6 w-6 text-red-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-2">Error</h3>
          <p>{submitError}</p>
        </div>
            </div>
            <div className="mt-4 flex justify-end">
              <button 
                onClick={() => setSubmitError("")} 
                className="text-sm bg-red-500/20 text-red-400 px-4 py-2 rounded hover:bg-red-500/30 transition-colors"
              >
                Dismiss
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
  
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
          <div className="relative">
            <label htmlFor="name" className="block text-text font-medium mb-2 flex items-center">
              Full Name 
              <span className="text-primary ms-1">*</span>
          </label>
            <div className="relative">
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
                onBlur={handleBlur}
                className={`w-full px-4 py-3 border ${errors.name ? 'border-red-500/50' : 'border-primary/20'} rounded-lg focus:ring-primary focus:border-primary outline-none transition bg-background/80 text-text pe-10`}
            required
          />
              <div className="absolute end-3 top-3 text-primary opacity-50">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            <AnimatePresence>
              {errors.name && (
                <motion.p 
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="text-red-400 text-sm mt-1 ms-1 absolute"
                >
                  {errors.name}
                </motion.p>
              )}
            </AnimatePresence>
        </div>

          <div className="relative">
            <label htmlFor="email" className="block text-text font-medium mb-2 flex items-center">
              Email Address
              <span className="text-primary ms-1">*</span>
          </label>
            <div className="relative">
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
                onBlur={handleBlur}
                className={`w-full px-4 py-3 border ${errors.email ? 'border-red-500/50' : 'border-primary/20'} rounded-lg focus:ring-primary focus:border-primary outline-none transition bg-background/80 text-text pe-10`}
            required
          />
              <div className="absolute end-3 top-3 text-primary opacity-50">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <AnimatePresence>
              {errors.email && (
                <motion.p 
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="text-red-400 text-sm mt-1 ms-1 absolute"
                >
                  {errors.email}
                </motion.p>
              )}
            </AnimatePresence>
        </div>

          <div className="relative">
            <label htmlFor="phone" className="block text-text font-medium mb-2">
              Phone Number <span className="text-text-secondary text-sm">(Optional)</span>
          </label>
            <div className="relative">
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
                onBlur={handleBlur}
                className={`w-full px-4 py-3 border ${errors.phone ? 'border-red-500/50' : 'border-primary/20'} rounded-lg focus:ring-primary focus:border-primary outline-none transition bg-background/80 text-text pe-10`}
              />
              <div className="absolute end-3 top-3 text-primary opacity-50">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            <AnimatePresence>
              {errors.phone && (
                <motion.p 
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="text-red-400 text-sm mt-1 ms-1 absolute"
                >
                  {errors.phone}
                </motion.p>
              )}
            </AnimatePresence>
        </div>

          <div className="relative">
            <label htmlFor="interest" className="block text-text font-medium mb-2 flex items-center">
              I am interested in
              <span className="text-primary ms-1">*</span>
          </label>
            <div ref={dropdownRef} className="relative">
              <div 
                className={`w-full px-4 py-3 border ${
                  errors.interest ? 'border-red-500/50' : 'border-primary/20'
                } rounded-lg transition bg-background/80 text-text flex justify-between items-center cursor-pointer ${
                  dropdownOpen ? 'ring-2 ring-primary/30 border-primary/40' : 'hover:border-primary/40 focus:ring-2 focus:ring-primary/30 focus:border-primary/40'
                }`}
                onClick={() => setDropdownOpen(!dropdownOpen)}
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    setDropdownOpen(!dropdownOpen);
                  }
                }}
                onBlur={() => {
                  // Delayed to allow click on options to register
                  setTimeout(() => {
                    if (!touched.interest) {
                      setTouched(prev => ({ ...prev, interest: true }));
                    }
                  }, 200);
                }}
                role="combobox"
                aria-expanded={dropdownOpen}
                aria-haspopup="listbox"
                aria-controls="interest-options"
              >
                <span className={formData.interest ? "text-text" : "text-text-secondary"}>
                  {formData.interest ? interestOptions.find(option => option.value === formData.interest)?.label : "Please select"}
                </span>
                <motion.div
                  animate={{ rotate: dropdownOpen ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                  className="text-primary opacity-50"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 9l-7 7-7-7" />
                  </svg>
                </motion.div>
              </div>
              
              {/* Dropdown options */}
              <AnimatePresence>
                {dropdownOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.15 }}
                    className="absolute z-10 mt-1 w-full bg-background border border-primary/20 rounded-lg shadow-xl overflow-hidden"
                    id="interest-options"
                    role="listbox"
                    aria-label="Interest options"
                  >
                    {interestOptions.map((option) => (
                      <div
                        key={option.value}
                        className={`px-4 py-3 cursor-pointer transition ${
                          formData.interest === option.value
                            ? "bg-primary/10 text-primary font-medium"
                            : "hover:bg-primary/5 text-text"
                        }`}
                        onClick={() => handleInterestSelect(option.value)}
                        role="option"
                        aria-selected={formData.interest === option.value}
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleInterestSelect(option.value);
                          }
                        }}
                      >
                        {option.label}
                      </div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
              
              {/* Hidden input for form submission */}
              <input
                type="hidden"
            name="interest"
            value={formData.interest}
            required
              />
            </div>
            <AnimatePresence>
              {errors.interest && (
                <motion.p 
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  className="text-red-400 text-sm mt-1 ms-1 absolute"
                >
                  {errors.interest}
                </motion.p>
              )}
            </AnimatePresence>
          </div>
      </div>

        <div className="relative">
          <label htmlFor="subject" className="block text-text font-medium mb-2 flex items-center">
            Subject
            <span className="text-primary ms-1">*</span>
        </label>
          <div className="relative">
        <input
          type="text"
          id="subject"
          name="subject"
          value={formData.subject}
          onChange={handleChange}
              onBlur={handleBlur}
              className={`w-full px-4 py-3 border ${errors.subject ? 'border-red-500/50' : 'border-primary/20'} rounded-lg focus:ring-primary focus:border-primary outline-none transition bg-background/80 text-text pe-10`}
          required
        />
            <div className="absolute end-3 top-3 text-primary opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
              </svg>
            </div>
          </div>
          <AnimatePresence>
            {errors.subject && (
              <motion.p 
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="text-red-400 text-sm mt-1 ms-1 absolute"
              >
                {errors.subject}
              </motion.p>
            )}
          </AnimatePresence>
      </div>

        <div className="relative">
          <label htmlFor="message" className="block text-text font-medium mb-2 flex items-center">
            Message
            <span className="text-primary ms-1">*</span>
        </label>
          <div className="relative">
        <textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
              onBlur={handleBlur}
          rows={6}
              className={`w-full px-4 py-3 border ${errors.message ? 'border-red-500/50' : 'border-primary/20'} rounded-lg focus:ring-primary focus:border-primary outline-none transition resize-none bg-background/80 text-text`}
          required
        ></textarea>
            <div className="absolute end-3 top-3 text-primary opacity-50">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
            </div>
          </div>
          <AnimatePresence>
            {errors.message && (
              <motion.p 
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="text-red-400 text-sm mt-1 ms-1 absolute"
              >
                {errors.message}
              </motion.p>
            )}
          </AnimatePresence>
          <p className="text-text-secondary text-sm mt-2">
            <span className="text-primary">*</span> Required fields
          </p>
      </div>

      <div className="mt-4">
          <motion.button
          type="submit"
          disabled={isSubmitting}
            className={`w-full bg-brand-gradient hover:opacity-90 text-white font-medium py-4 px-6 rounded-lg transition shadow-xl overflow-hidden relative group ${
            isSubmitting ? "opacity-50 cursor-not-allowed" : ""
          }`}
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.98 }}
          >
            <span className="absolute top-0 start-0 w-full h-full bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></span>
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ms-1 me-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <span>Send Message</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ms-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            )}
          </motion.button>
          
          <p className="text-xs text-text-secondary text-center mt-4">
            By submitting this form, you agree to our <a href="#" className="text-primary underline hover:text-primary-hover">Privacy Policy</a> and <a href="#" className="text-primary underline hover:text-primary-hover">Terms of Service</a>.
          </p>
      </div>
    </form>
    </div>
  );
};

export default ContactForm;