"use client";

import { useEffect, useRef, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion, useScroll, useTransform, AnimatePresence, useSpring, useMotionValue } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

// 3D tilt effect hook
const useMouseTilt = (ref: React.RefObject<HTMLElement>) => {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const mouseXSpring = useSpring(x);
  const mouseYSpring = useSpring(y);
  
  useEffect(() => {
    const element = ref.current;
    if (!element) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;
      
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      
      const xPct = mouseX / width - 0.5;
      const yPct = mouseY / height - 0.5;
      
      x.set(xPct * 5); // Max rotation in degrees
      y.set(yPct * 5);
    };
    
    const handleMouseLeave = () => {
      x.set(0);
      y.set(0);
    };
    
    element.addEventListener("mousemove", handleMouseMove);
    element.addEventListener("mouseleave", handleMouseLeave);
    
    return () => {
      element.removeEventListener("mousemove", handleMouseMove);
      element.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, [ref, x, y]);
  
  return { mouseXSpring, mouseYSpring };
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delayChildren: 0.3,
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.6, ease: [0.43, 0.13, 0.23, 0.96] }
  }
};

const MissionVision = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const missionCardRef = useRef<HTMLDivElement>(null);
  const visionCardRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState("mission");
  const [cardHeight, setCardHeight] = useState<number | null>(null);
  const [isRTL, setIsRTL] = useState(false);
  
  // Add language context
  const { locale, isRTL: contextIsRTL } = useLanguage();
  
  // 3D tilt effect
  const missionTilt = useMouseTilt(missionCardRef);
  const visionTilt = useMouseTilt(visionCardRef);
  
  // Intersection observer for entrance animation
  const [isInView, setIsInView] = useState(false);
  
  // Use RTL from context instead of manual detection
  useEffect(() => {
    setIsRTL(contextIsRTL);
  }, [contextIsRTL]);
  
  // For parallax scrolling effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  // Parallax animation values for different elements
  const leftGradientY = useTransform(scrollYProgress, [0, 0.4], [50, 0]);
  const rightGradientY = useTransform(scrollYProgress, [0, 0.4], [20, 0]);
  const contentOpacity = useTransform(scrollYProgress, [0, 0.2], [0, 1]);
  const contentY = useTransform(scrollYProgress, [0, 0.2], [30, 0]);
  
  // Floating animations for background elements
  const floatY1 = useTransform(
    scrollYProgress,
    [0, 1],
    [0, -50]
  );
  
  const floatY2 = useTransform(
    scrollYProgress,
    [0, 1],
    [0, -30]
  );
  
  const rotation = useTransform(
    scrollYProgress,
    [0, 1],
    [0, isRTL ? -15 : 15]
  );
  
  // Gradient background motion
  const gradientPosition = useTransform(
    scrollYProgress,
    [0, 1],
    [0, 50]
  );
  
  // Effect to maintain consistent heights between mission and vision sections
  useEffect(() => {
    const updateHeight = () => {
      const missionElement = missionCardRef.current;
      const visionElement = visionCardRef.current;
      
      if (missionElement && visionElement) {
        const missionHeight = missionElement.clientHeight;
        const visionHeight = visionElement.clientHeight;
        const maxHeight = Math.max(missionHeight, visionHeight);
        setCardHeight(maxHeight);
      }
    };
    
    // Update on tab change and window resize
    updateHeight();
    window.addEventListener('resize', updateHeight);
    
    return () => window.removeEventListener('resize', updateHeight);
  }, [activeTab]);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          entry.target.classList.add("reveal");
        }
      },
      {
        threshold: 0.15
      }
    );
    
    const section = sectionRef.current;
    if (section) {
      observer.observe(section);
    }
    
    return () => {
      if (section) {
        observer.unobserve(section);
      }
    };
  }, []);
  
  return (
    <section 
      ref={sectionRef}
      className="pt-16 pb-24 md:pb-32 opacity-0 transform translate-y-10 transition-all duration-1000 ease-out relative overflow-hidden"
      dir="auto" // Automatically detect text direction
    >
      {/* Advanced Gradient Background */}
      <div className="absolute inset-0 bg-[rgb(var(--color-background))] z-0 overflow-hidden">
        <div 
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: isRTL 
              ? "radial-gradient(circle at 80% 80%, rgba(var(--color-primary), 0.1), transparent 20%), radial-gradient(circle at 20% 20%, rgba(var(--color-secondary), 0.1), transparent 20%)"
              : "radial-gradient(circle at 20% 80%, rgba(var(--color-primary), 0.1), transparent 20%), radial-gradient(circle at 80% 20%, rgba(var(--color-secondary), 0.1), transparent 20%)"
          }}
        ></div>
        
        <motion.div 
          className="absolute top-0 inset-inline-0 w-full h-full opacity-20"
          style={{
            backgroundImage: isRTL
              ? "linear-gradient(-135deg, rgba(var(--color-primary), 0.1) 0%, transparent 50%, rgba(var(--color-secondary), 0.05) 100%)"
              : "linear-gradient(135deg, rgba(var(--color-primary), 0.1) 0%, transparent 50%, rgba(var(--color-secondary), 0.05) 100%)",
            y: gradientPosition
          }}
        ></motion.div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-[0.03]" 
          style={{ 
            backgroundImage: "radial-gradient(rgba(var(--color-text), 0.8) 1px, transparent 1px)",
            backgroundSize: "40px 40px" 
          }}>
        </div>
      </div>
      
      {/* Animated 3D Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Floating cube */}
        <motion.div 
          className="absolute top-[10%] inline-start-[15%] w-24 h-24 md:w-32 md:h-32"
          style={{ 
            y: floatY1,
            rotate: rotation
          }}
        >
          <div className="relative w-full h-full">
            <div className="absolute inset-0 bg-gradient-to-tr from-[rgba(var(--color-primary),0.2)] to-[rgba(var(--color-secondary),0.1)] backdrop-blur-xl rounded-xl transform rotate-12 scale-[0.85]"></div>
            <div className={`absolute inset-0 border border-[rgba(var(--color-primary),0.2)] rounded-xl transform ${isRTL ? 'rotate-3' : '-rotate-3'}`}></div>
          </div>
        </motion.div>
        
        {/* Floating sphere */}
        <motion.div 
          className="absolute bottom-[15%] inline-end-[15%] w-40 h-40 md:w-56 md:h-56"
          style={{ 
            y: floatY2,
          }}
        >
          <div className="relative w-full h-full">
            <div className="absolute inset-0 rounded-full bg-gradient-to-b from-[rgba(var(--color-secondary),0.1)] to-transparent"></div>
            <div className="absolute inset-[5%] rounded-full border border-[rgba(var(--color-primary),0.1)]"></div>
            <div className="absolute inset-[15%] rounded-full border border-[rgba(var(--color-primary),0.05)]"></div>
            <div className="absolute inset-[25%] rounded-full border border-[rgba(var(--color-primary),0.03)]"></div>
          </div>
        </motion.div>
        
        {/* Abstract decorative lines - RTL aware */}
        <svg className="absolute top-0 inline-start-0 w-full h-full opacity-10" viewBox="0 0 100 100" preserveAspectRatio="none">
          <motion.path 
            d={isRTL ? "M100,50 Q75,45 50,50 T0,50" : "M0,50 Q25,45 50,50 T100,50"}
            stroke="rgba(var(--color-primary), 0.5)" 
            fill="none" 
            strokeWidth="0.2"
            initial={{pathLength: 0}}
            animate={isInView ? {pathLength: 1} : {pathLength: 0}}
            transition={{duration: 2, ease: "easeOut", delay: 0.5}}
          />
          <motion.path 
            d={isRTL ? "M100,55 Q75,60 50,55 T0,55" : "M0,55 Q25,60 50,55 T100,55"}
            stroke="rgba(var(--color-secondary), 0.5)" 
            fill="none" 
            strokeWidth="0.2"
            initial={{pathLength: 0}}
            animate={isInView ? {pathLength: 1} : {pathLength: 0}}
            transition={{duration: 2, ease: "easeOut", delay: 0.8}}
          />
        </svg>
      </div>
      
      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Premium Tab Controls */}
        <motion.div 
          className="flex justify-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
        >
          <div className="inline-flex rounded-full bg-[rgb(var(--color-text))]/5 p-1.5 backdrop-blur-sm border border-[rgb(var(--color-text))]/10 shadow-lg">
            <button
              onClick={() => setActiveTab("mission")}
              className={`relative px-6 sm:px-8 py-2.5 sm:py-3 rounded-full text-sm sm:text-base font-medium transition-all duration-500 ${
                activeTab === "mission"
                ? "text-white" 
                : "text-[rgb(var(--color-text))]"
              }`}
            >
              {activeTab === "mission" && (
                <motion.div 
                  className="absolute inset-0 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))] rounded-full"
                  layoutId="activeTab"
                  transition={{ type: "spring", duration: 0.6, bounce: 0.2 }}
                />
              )}
              <span className="relative z-10 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4">
                  <path fillRule="evenodd" d="M10 1a.75.75 0 01.75.75v1.5a.75.75 0 01-1.5 0v-1.5A.75.75 0 0110 1zM5.05 3.05a.75.75 0 011.06 0l1.062 1.06a.75.75 0 11-1.061 1.061L5.05 4.11a.75.75 0 010-1.06zM14.95 3.05a.75.75 0 010 1.06l-1.06 1.062a.75.75 0 01-1.062-1.061l1.061-1.06a.75.75 0 011.06 0zM3 10a.75.75 0 01.75-.75h1.5a.75.75 0 010 1.5h-1.5A.75.75 0 013 10zM14.75 9.25a.75.75 0 000 1.5h1.5a.75.75 0 000-1.5h-1.5zM5.05 15.95a.75.75 0 010-1.06l1.06-1.062a.75.75 0 111.062 1.061l-1.061 1.06a.75.75 0 01-1.06 0zM14.95 15.95a.75.75 0 01-1.06 0l-1.062-1.06a.75.75 0 111.061-1.062l1.06 1.061a.75.75 0 010 1.06zM10 20a.75.75 0 01-.75-.75v-1.5a.75.75 0 011.5 0v1.5A.75.75 0 0110 20z" clipRule="evenodd" />
                </svg>
                {t('missionVision.mission', locale)}
              </span>
            </button>
            <button
              onClick={() => setActiveTab("vision")}
              className={`relative px-6 sm:px-8 py-2.5 sm:py-3 rounded-full text-sm sm:text-base font-medium transition-all duration-500 ${
                activeTab === "vision"
                ? "text-white" 
                : "text-[rgb(var(--color-text))]"
              }`}
            >
              {activeTab === "vision" && (
                <motion.div 
                  className="absolute inset-0 bg-gradient-to-r from-[rgb(var(--color-secondary))] to-[rgb(var(--color-primary))] rounded-full"
                  layoutId="activeTab"
                  transition={{ type: "spring", duration: 0.6, bounce: 0.2 }}
                />
              )}
              <span className="relative z-10 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4">
                  <path d="M10 12.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z" />
                  <path fillRule="evenodd" d="M.664 10.59a1.651 1.651 0 010-1.186A10.004 10.004 0 0110 3c4.257 0 7.893 2.66 9.336 6.41.147.381.146.804 0 1.186A10.004 10.004 0 0110 17c-4.257 0-7.893-2.66-9.336-6.41zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                </svg>
                {t('missionVision.vision', locale)}
              </span>
            </button>
          </div>
        </motion.div>        
        
        {/* Content Container with 3D Card Effect */}
        <motion.div 
          className="max-w-5xl mx-auto z-10 relative"
          style={{ opacity: contentOpacity, y: contentY }}
        >
          <AnimatePresence mode="wait">
            {/* Mission Content */}
            {activeTab === "mission" && (
              <motion.div 
                key="mission"
                initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: isRTL ? -20 : 20 }}
                transition={{ duration: 0.5 }}
                className="perspective-1000"
              >
                <motion.div 
                  ref={missionCardRef}
                  className="flex flex-col lg:flex-row relative z-10 rounded-3xl overflow-hidden shadow-[0_15px_50px_-15px_rgba(0,0,0,0.15)] backdrop-blur-sm"
                  style={{
                    rotateX: missionTilt.mouseYSpring,
                    rotateY: missionTilt.mouseXSpring,
                    transformStyle: "preserve-3d",
                    height: cardHeight ? `${cardHeight}px` : 'auto'
                  }}
                >
                  {/* Start Gradient Section with 3D layers */}
                  <motion.div 
                    className="lg:w-5/12 bg-gradient-to-br from-[rgb(var(--color-primary))] via-[rgb(var(--color-primary))]/90 to-[rgb(var(--color-secondary))]/80 text-white p-8 sm:p-10 lg:p-12 flex items-center relative overflow-hidden"
                    style={{ y: leftGradientY, translateZ: "20px" }}
                  >
                    {/* Background abstract pattern */}
                    <div className="absolute inset-0 opacity-10">
                      <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
                        <pattern id="grid-mission" width="8" height="8" patternUnits="userSpaceOnUse">
                          <path d="M 8 0 L 0 0 0 8" fill="none" stroke="white" strokeWidth="0.5" opacity="0.5" />
                        </pattern>
                        <rect width="100%" height="100%" fill="url(#grid-mission)" />
                      </svg>
                    </div>
                    
                    {/* 3D floating icon - RTL aware positioning */}
                    <motion.div 
                      className="absolute top-8 start-8 w-16 h-16 rounded-xl bg-white/10 backdrop-blur-[2px] border border-white/20 flex items-center justify-center shadow-lg"
                      style={{ translateZ: "40px" }}
                      animate={{ 
                        y: [0, -10, 0],
                        rotate: [0, isRTL ? -5 : 5, 0],
                      }}
                      transition={{ 
                        duration: 6, 
                        ease: "easeInOut",
                        repeat: Infinity,
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-7 h-7">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0 .5 1.5m-.5-1.5h-9.5m0 0-.5 1.5m.75-9 3-3 2.148 2.148A12.061 12.061 0 0 1 16.5 7.605" />
                      </svg>
                    </motion.div>
                    
                    {/* Light glow effect - RTL aware positioning */}
                    <div className="absolute -end-12 -top-12 w-48 h-48 rounded-full bg-white/10 filter blur-[40px]"></div>
                    
                    {/* Content */}
                    <motion.div 
                      className="pt-20 relative w-full"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      <motion.h2 variants={itemVariants} className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4">{t('missionVision.ourMission', locale)}</motion.h2>
                      <motion.div variants={itemVariants} className="w-16 h-1 bg-white/60 rounded-full mb-6 sm:mb-8"></motion.div>
                      <motion.p variants={itemVariants} className="text-lg sm:text-xl font-light leading-relaxed">
                        {t('missionVision.missionTagline', locale)}
                      </motion.p>
                      <motion.div 
                        variants={itemVariants}
                        className="mt-8 sm:mt-12 w-full h-[1px] bg-gradient-to-r from-white/0 via-white/30 to-white/0"
                      ></motion.div>
                      
                      {/* Decorative elements - RTL aware positioning */}
                      <div className="absolute bottom-2 end-2 opacity-20">
                        <svg width="60" height="60" viewBox="0 0 100 100" fill="none">
                          <circle cx="50" cy="50" r="40" stroke="white" strokeWidth="1" />
                          <circle cx="50" cy="50" r="30" stroke="white" strokeWidth="0.5" />
                          <circle cx="50" cy="50" r="20" stroke="white" strokeWidth="0.25" />
                        </svg>
                      </div>
                    </motion.div>
                  </motion.div>
                  
                  {/* End Content Section */}
                  <div className="lg:w-7/12 bg-[rgba(var(--color-background),0.8)] backdrop-blur-sm border-t lg:border-t-0 lg:border-s border-[rgb(var(--color-text))]/10 p-6 sm:p-8 lg:p-12">
                    <motion.div
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      <motion.h3 variants={itemVariants} className="text-[rgb(var(--color-primary))] text-xl sm:text-2xl font-medium mb-6 flex items-center gap-3">
                        <span className="w-6 h-6 rounded-full bg-[rgb(var(--color-primary))]/10 flex items-center justify-center">
                          <span className="w-2 h-2 rounded-full bg-[rgb(var(--color-primary))]"></span>
                        </span>
                        {t('missionVision.whatDrivesUs', locale)}
                      </motion.h3>
                      
                      <motion.div variants={itemVariants} className="text-[rgb(var(--color-text-secondary))] lg:text-lg">
                        <p className="leading-relaxed">
                          {t('missionVision.missionDescription', locale)}
                        </p>
                      </motion.div>
                      
                      <motion.div variants={itemVariants} className="mt-8 sm:mt-10">
                        <h4 className="text-[rgb(var(--color-text))] font-medium mb-4">{t('missionVision.ourCoreValues', locale)}</h4>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                          <motion.div 
                            className="flex flex-col group hover:bg-[rgb(var(--color-primary))]/5 p-4 rounded-2xl transition-all duration-300"
                            whileHover={{ scale: 1.02 }}
                          >
                            <div className="w-12 h-12 mb-4 rounded-2xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                                <path fillRule="evenodd" d="M11.484 2.17a.75.75 0 0 1 1.032 0 11.209 11.209 0 0 0 7.877 ********** 0 0 1 .722.515 12.74 12.74 0 0 1 .635 3.985c0 5.942-4.064 10.933-9.563 12.348a.749.749 0 0 1-.374 0C6.314 20.683 2.25 15.692 2.25 9.75c0-1.39.223-2.73.635-3.985a.75.75 0 0 1 .722-.516l.143.001c2.996 0 5.718-1.17 7.734-3.08ZM12 12.75a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <h5 className="font-semibold text-[rgb(var(--color-text))] mb-2">{t('missionVision.excellence.title', locale)}</h5>
                            <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.excellence.description', locale)}</p>
                          </motion.div>
                          
                          <motion.div 
                            className="flex flex-col group hover:bg-[rgb(var(--color-primary))]/5 p-4 rounded-2xl transition-all duration-300"
                            whileHover={{ scale: 1.02 }}
                          >
                            <div className="w-12 h-12 mb-4 rounded-2xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                                <path fillRule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72a.75.75 0 1 0-1.06-1.06L10.94 12l-1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <h5 className="font-semibold text-[rgb(var(--color-text))] mb-2">{t('missionVision.integrity.title', locale)}</h5>
                            <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.integrity.description', locale)}</p>
                          </motion.div>
                          
                          <motion.div 
                            className="flex flex-col group hover:bg-[rgb(var(--color-primary))]/5 p-4 rounded-2xl transition-all duration-300"
                            whileHover={{ scale: 1.02 }}
                          >
                            <div className="w-12 h-12 mb-4 rounded-2xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6">
                                <path d="M12 .75a8.25 8.25 0 0 0-4.135 15.39c.686.398 1.115 1.008 1.134 1.623a.75.75 0 0 0 .577.706c.352.083.71.148 1.074.195.323.041.6-.218.6-.544v-4.661a6.75 6.75 0 1 1 2.8 0v4.661c0 .326.277.585.6.544.364-.047.722-.112 1.074-.195a.75.75 0 0 0 .577-.706c.02-.615.448-1.225 1.134-1.623A8.25 8.25 0 0 0 12 .75Z" />
                                <path fillRule="evenodd" d="M9.013 19.9a.75.75 0 0 1 .877-.597 11.319 11.319 0 0 0 4.22 0 .75.75 0 1 1 .28 1.473 12.819 12.819 0 0 1-4.78 0 .75.75 0 0 1-.597-.876ZM9.754 22.344a.75.75 0 0 1 .824-.668 13.682 13.682 0 0 0 2.844 0 .75.75 0 1 1 .156 1.492 15.156 15.156 0 0 1-3.156 0 .75.75 0 0 1-.668-.824Z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <h5 className="font-semibold text-[rgb(var(--color-text))] mb-2">{t('missionVision.innovation.title', locale)}</h5>
                            <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.innovation.description', locale)}</p>
                          </motion.div>
                        </div>
                      </motion.div>
                    </motion.div>
                  </div>
                </motion.div>
              </motion.div>
            )}
            
            {/* Vision Content */}
            {activeTab === "vision" && (
              <motion.div 
                key="vision"
                initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
                transition={{ duration: 0.5 }}
                className="perspective-1000"
              >
                <motion.div 
                  ref={visionCardRef}
                  className="flex flex-col lg:flex-row-reverse relative z-10 rounded-3xl overflow-hidden shadow-[0_15px_50px_-15px_rgba(0,0,0,0.15)] backdrop-blur-sm"
                  style={{
                    rotateX: visionTilt.mouseYSpring,
                    rotateY: visionTilt.mouseXSpring,
                    transformStyle: "preserve-3d",
                    height: cardHeight ? `${cardHeight}px` : 'auto'
                  }}
                >
                  {/* Right Gradient Section with 3D layers */}
                  <motion.div 
                    className="lg:w-5/12 bg-gradient-to-bl from-[rgb(var(--color-secondary))]/90 via-[rgb(var(--color-primary))]/90 to-[rgb(var(--color-primary))]/80 text-white p-8 sm:p-10 lg:p-12 flex items-center relative overflow-hidden"
                    style={{ y: rightGradientY, translateZ: "20px" }}
                  >
                    {/* Background abstract pattern */}
                    <div className="absolute inset-0 opacity-10">
                      <svg width="100%" height="100%" viewBox="0 0 100 100">
                        <circle cx={isRTL ? "20" : "80"} cy="20" r="10" fill="none" stroke="white" strokeWidth="0.5" />
                        <circle cx={isRTL ? "20" : "80"} cy="20" r="20" fill="none" stroke="white" strokeWidth="0.3" />
                        <circle cx={isRTL ? "80" : "20"} cy="80" r="10" fill="none" stroke="white" strokeWidth="0.5" />
                        <circle cx={isRTL ? "80" : "20"} cy="80" r="20" fill="none" stroke="white" strokeWidth="0.3" />
                        <line x1="20" y1="0" x2="20" y2="100" stroke="white" strokeWidth="0.2" />
                        <line x1="80" y1="0" x2="80" y2="100" stroke="white" strokeWidth="0.2" />
                        <line x1="0" y1="20" x2="100" y2="20" stroke="white" strokeWidth="0.2" />
                        <line x1="0" y1="80" x2="100" y2="80" stroke="white" strokeWidth="0.2" />
                      </svg>
                    </div>
                    
                    {/* 3D floating icon - RTL aware positioning */}
                    <motion.div 
                      className="absolute top-8 start-8 w-16 h-16 rounded-xl bg-white/10 backdrop-blur-[2px] border border-white/20 flex items-center justify-center shadow-lg"
                      style={{ translateZ: "40px" }}
                      animate={{ 
                        y: [0, -10, 0],
                        rotate: [0, isRTL ? 5 : -5, 0],
                      }}
                      transition={{ 
                        duration: 6, 
                        ease: "easeInOut",
                        repeat: Infinity,
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-7 h-7">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z" />
                        <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                      </svg>
                    </motion.div>
                    
                    {/* Light glow effect - RTL aware positioning */}
                    <div className="absolute -start-12 -top-12 w-48 h-48 rounded-full bg-white/10 filter blur-[40px]"></div>
                    
                    {/* Content */}
                    <motion.div 
                      className="pt-20 relative w-full"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                    >
                      <motion.h2 variants={itemVariants} className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4">{t('missionVision.ourVision', locale)}</motion.h2>
                      <motion.div variants={itemVariants} className="w-16 h-1 bg-white/60 rounded-full mb-6 sm:mb-8"></motion.div>
                      <motion.p variants={itemVariants} className="text-lg sm:text-xl font-light leading-relaxed">
                        {t('missionVision.visionTagline', locale)}
                      </motion.p>
                      <motion.div 
                        variants={itemVariants}
                        className="mt-8 sm:mt-12 w-full h-[1px] bg-gradient-to-r from-white/0 via-white/30 to-white/0"
                      ></motion.div>
                      
                      {/* Decorative elements - RTL aware positioning */}
                      <div className="absolute bottom-4 start-4 opacity-20">
                        <svg width="60" height="60" viewBox="0 0 100 100" fill="none">
                          <rect x="10" y="10" width="80" height="80" stroke="white" strokeWidth="1" fill="none" />
                          <rect x="25" y="25" width="50" height="50" stroke="white" strokeWidth="0.5" fill="none" />
                          <rect x="40" y="40" width="20" height="20" stroke="white" strokeWidth="0.25" fill="none" />
                        </svg>
                      </div>
                    </motion.div>
                  </motion.div>
                  
                  {/* Left Content Section */}
                  <div className="lg:w-7/12 bg-[rgba(var(--color-background),0.8)] backdrop-blur-sm border-b lg:border-b-0 lg:border-e border-[rgb(var(--color-text))]/10 p-6 sm:p-8 lg:p-12">
                    <motion.div
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      className="h-full"
                    >
                      <motion.h3 variants={itemVariants} className="text-[rgb(var(--color-primary))] text-xl sm:text-2xl font-medium mb-6 flex items-center gap-3">
                        <span className="w-6 h-6 rounded-full bg-[rgb(var(--color-primary))]/10 flex items-center justify-center">
                          <span className="w-2 h-2 rounded-full bg-[rgb(var(--color-primary))]"></span>
                        </span>
                        {t('missionVision.whereWereHeaded', locale)}
                      </motion.h3>
                      
                      <motion.div variants={itemVariants} className="text-[rgb(var(--color-text-secondary))] lg:text-lg">
                        <p className="leading-relaxed">
                          {t('missionVision.visionDescription', locale)}
                        </p>
                      </motion.div>
                      
                      <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mt-8 sm:mt-10">
                        <motion.div 
                          className="p-4 sm:p-6 rounded-2xl border border-[rgb(var(--color-text))]/10 bg-gradient-to-br from-[rgb(var(--color-background))] to-[rgb(var(--color-text))]/5 hover:shadow-lg transition-all duration-300 group hover:border-[rgb(var(--color-primary))]/20 flex flex-col h-full"
                          whileHover={{ y: -5, transition: { duration: 0.2 } }}
                        >
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] mb-3 sm:mb-4 group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                            </svg>
                          </div>
                          <h5 className="font-semibold text-base sm:text-lg mb-2 text-[rgb(var(--color-text))]">{t('missionVision.trust.title', locale)}</h5>
                          <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.trust.description', locale)}</p>
                        </motion.div>
                        
                        <motion.div 
                          className="p-4 sm:p-6 rounded-2xl border border-[rgb(var(--color-text))]/10 bg-gradient-to-br from-[rgb(var(--color-background))] to-[rgb(var(--color-text))]/5 hover:shadow-lg transition-all duration-300 group hover:border-[rgb(var(--color-primary))]/20 flex flex-col h-full"
                          whileHover={{ y: -5, transition: { duration: 0.2 } }}
                        >
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] mb-3 sm:mb-4 group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                            </svg>
                          </div>
                          <h5 className="font-semibold text-base sm:text-lg mb-2 text-[rgb(var(--color-text))]">{t('missionVision.innovation.title', locale)}</h5>
                          <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.innovation.description', locale)}</p>
                        </motion.div>
                        
                        <motion.div 
                          className="p-4 sm:p-6 rounded-2xl border border-[rgb(var(--color-text))]/10 bg-gradient-to-br from-[rgb(var(--color-background))] to-[rgb(var(--color-text))]/5 hover:shadow-lg transition-all duration-300 group hover:border-[rgb(var(--color-primary))]/20 flex flex-col h-full"
                          whileHover={{ y: -5, transition: { duration: 0.2 } }}
                        >
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] mb-3 sm:mb-4 group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18" />
                            </svg>
                          </div>
                          <h5 className="font-semibold text-base sm:text-lg mb-2 text-[rgb(var(--color-text))]">{t('missionVision.sustainability.title', locale)}</h5>
                          <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.sustainability.description', locale)}</p>
                        </motion.div>
                        
                        <motion.div 
                          className="p-4 sm:p-6 rounded-2xl border border-[rgb(var(--color-text))]/10 bg-gradient-to-br from-[rgb(var(--color-background))] to-[rgb(var(--color-text))]/5 hover:shadow-lg transition-all duration-300 group hover:border-[rgb(var(--color-primary))]/20 flex flex-col h-full"
                          whileHover={{ y: -5, transition: { duration: 0.2 } }}
                        >
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-[rgb(var(--color-primary))]/10 flex items-center justify-center text-[rgb(var(--color-primary))] mb-3 sm:mb-4 group-hover:bg-[rgb(var(--color-primary))]/20 transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 sm:w-6 sm:h-6">
                              <path strokeLinecap="round" strokeLinejoin="round" d="M15.182 15.182a4.5 4.5 0 0 1-6.364 0M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z" />
                            </svg>
                          </div>
                          <h5 className="font-semibold text-base sm:text-lg mb-2 text-[rgb(var(--color-text))]">{t('missionVision.experience.title', locale)}</h5>
                          <p className="text-sm text-[rgb(var(--color-text-secondary))]">{t('missionVision.experience.description', locale)}</p>
                        </motion.div>
                      </motion.div>
                    </motion.div>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
      
      <style jsx>{`
        .reveal {
          opacity: 1;
          transform: translateY(0);
        }
        .perspective-1000 {
          perspective: 1000px;
        }
      `}</style>
    </section>
  );
};

export default MissionVision; 
