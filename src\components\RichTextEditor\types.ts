// Define typescript types for the RichTextEditor component
export type CustomElement = {
  type: 'paragraph' | 'heading-one' | 'heading-two' | 'heading-three' | 'block-quote' | 
        'bulleted-list' | 'numbered-list' | 'list-item' | 'table' | 'table-row' | 'table-cell' | 
        'code-block' | 'image' | 'video' | 'file-embed' | 'footnote' | 'endnote' | 'comment' | 
        'annotation' | 'toc' | 'page-break' | 'column-layout' | 'math-equation' | 'diagram' |
        'timeline' | 'chart' | 'template' | 'social-embed' | 'map-embed' | 'poll';
  align?: 'left' | 'center' | 'right' | 'justify';
  url?: string; // For images, videos, embeds
  alt?: string; // For images
  language?: string; // For code blocks
  color?: string; // For text color
  bgColor?: string; // For background color
  children: CustomText[] | CustomElement[];
  caption?: string; // For images, tables
  width?: number | string; // For images, tables
  height?: number | string; // For images, tables
  colspan?: number; // For table cells
  rowspan?: number; // For table cells
  id?: string; // Unique identifier for elements (for comments, annotations, etc.)
  footnoteId?: string; // For footnotes
  authorId?: string; // For collaborative editing
  dateCreated?: string; // For tracking when elements were added
  dateModified?: string; // For tracking when elements were modified
  version?: number; // For revision history
  commentIds?: string[]; // References to comments
  equation?: string; // For math equations (LaTeX)
  diagramData?: string; // For storing diagram data
  chartData?: any; // For chart data
  columns?: number; // For column layouts
  template?: string; // Template identifier
  embedding?: { // For advanced embeds
    type: string;
    provider: string;
    html: string;
    data: any;
  };
  commentThread?: CommentThread; // For comment threads
  aiSuggestions?: AISuggestion[]; // For AI suggestions
};

export type CustomText = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  code?: boolean;
  underline?: boolean;
  strikethrough?: boolean;
  color?: string;
  bgColor?: string;
  fontSize?: string;
  fontFamily?: string;
  superscript?: boolean;
  subscript?: boolean;
  highlight?: boolean;
  translationSource?: string; // For translations
  translationData?: { [lang: string]: string }; // Translations to other languages
  authorId?: string; // For collaborative editing
  dateModified?: string; // For revision history
  commentIds?: string[]; // Reference to comments
  suggestions?: AISuggestion[]; // For AI grammar/style suggestions
};

// Comment and annotation types
export type Comment = {
  id: string;
  author: string;
  authorId: string;
  content: string;
  dateCreated: string;
  dateModified?: string;
  resolved?: boolean;
  reactions?: Reaction[];
};

export type CommentThread = {
  id: string;
  comments: Comment[];
  resolved: boolean;
};

export type Reaction = {
  emoji: string;
  count: number;
  users: string[];
};

// Revision history types
export type Revision = {
  id: string;
  author: string;
  authorId: string;
  date: string;
  content: CustomElement[];
  description?: string;
};

// Template type
export type Template = {
  id: string;
  name: string;
  description?: string;
  content: CustomElement[];
  thumbnail?: string;
};

// AI suggestion type
export type AISuggestion = {
  id: string;
  type: 'grammar' | 'style' | 'content' | 'summary' | 'translation'; 
  description: string;
  suggestion: string;
  confidence: number;
  accepted?: boolean;
  rejected?: boolean;
};

// Collaborative editing types
export type CollaborationUser = {
  id: string;
  name: string;
  color: string;
  cursor?: {
    path: number[];
    offset: number;
  };
  selection?: {
    anchor: { path: number[], offset: number };
    focus: { path: number[], offset: number };
  };
};

// Document analytics
export type DocumentAnalytics = {
  wordCount: number;
  charCount: number;
  readingTime: number; // in minutes
  readabilityScore: number;
  paragraphCount: number;
  sentenceCount: number;
  averageSentenceLength: number;
  complexWordCount: number;
};

// Export options
export type ExportFormat = 'pdf' | 'docx' | 'html' | 'markdown' | 'txt' | 'json'; 