"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { FiGrid, FiFileText, FiMail, FiEye, FiTrendingUp } from 'react-icons/fi';
import NoSSR from "@/components/NoSSR";

function AdminDashboardContent() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [isMounted, setIsMounted] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  console.log('Dashboard render:', { user: user?.email, isAuthenticated, isLoading, isMounted });

  // Always show loading on server-side and until mounted
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center min-h-screen" suppressHydrationWarning>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF]"></div>
      </div>
    );
  }

  // Only check authentication after mounting
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF]"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Not Authenticated</h1>
          <p className="text-gray-400">Please log in to access the admin panel.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6" suppressHydrationWarning>
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Dashboard</h1>
        <div className="text-sm text-gray-400">
          Welcome back, {user?.first_name} {user?.last_name}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Total Projects</p>
              <p className="text-3xl font-bold text-white">8</p>
            </div>
            <div className="h-12 w-12 bg-[#00C2FF]/10 rounded-lg flex items-center justify-center">
              <FiGrid className="h-6 w-6 text-[#00C2FF]" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-green-400 text-sm font-medium">↗ 7.8%</span>
            <span className="text-gray-400 text-sm ml-2">View all</span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Articles</p>
              <p className="text-3xl font-bold text-white">24</p>
            </div>
            <div className="h-12 w-12 bg-purple-500/10 rounded-lg flex items-center justify-center">
              <FiFileText className="h-6 w-6 text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-green-400 text-sm font-medium">↗ 12.5%</span>
            <span className="text-gray-400 text-sm ml-2">View all</span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Inquiries</p>
              <p className="text-3xl font-bold text-white">42</p>
            </div>
            <div className="h-12 w-12 bg-blue-500/10 rounded-lg flex items-center justify-center">
              <FiMail className="h-6 w-6 text-blue-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-green-400 text-sm font-medium">↗ 24.3%</span>
            <span className="text-gray-400 text-sm ml-2">View all</span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-400">Page Views</p>
              <p className="text-3xl font-bold text-white">12,348</p>
            </div>
            <div className="h-12 w-12 bg-green-500/10 rounded-lg flex items-center justify-center">
              <FiEye className="h-6 w-6 text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-green-400 text-sm font-medium">↗ 18.2%</span>
            <span className="text-gray-400 text-sm ml-2">View details</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Recent Activity</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-4 p-4 bg-gray-700/50 rounded-lg">
            <div className="h-10 w-10 bg-[#00C2FF]/10 rounded-lg flex items-center justify-center">
              <FiGrid className="h-5 w-5 text-[#00C2FF]" />
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">New project "Mazaya Gardens" added</p>
              <p className="text-gray-400 text-sm">Added by Admin on Sept 12, 2023</p>
            </div>
            <button className="text-[#00C2FF] hover:text-[#00C2FF]/80 text-sm font-medium">
              View
            </button>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-gray-700/50 rounded-lg">
            <div className="h-10 w-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
              <FiFileText className="h-5 w-5 text-purple-400" />
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">New article "Investment Opportunities in Dubai 2023" published</p>
              <p className="text-gray-400 text-sm">Published by Editor on Sept 10, 2023</p>
            </div>
            <button className="text-[#00C2FF] hover:text-[#00C2FF]/80 text-sm font-medium">
              View
            </button>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-gray-700/50 rounded-lg">
            <div className="h-10 w-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
              <FiMail className="h-5 w-5 text-blue-400" />
            </div>
            <div className="flex-1">
              <p className="text-white font-medium">New inquiry from John Doe about Mazaya Waterfront</p>
              <p className="text-gray-400 text-sm">Received on Sept 8, 2023</p>
            </div>
            <button className="text-[#00C2FF] hover:text-[#00C2FF]/80 text-sm font-medium">
              View
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AdminDashboard() {
  return (
    <NoSSR>
      <AdminDashboardContent />
    </NoSSR>
  );
} 