"use client";

import React, { useState } from 'react';
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiMove, FiUsers, <PERSON>Filter, FiUpload } from 'react-icons/fi';

interface Partner {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  category: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  logoUrl?: string;
  order: number;
}

interface PartnerCategory {
  id: string;
  label: {
    en: string;
    ar: string;
  };
}

interface PartnersSection {
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  ctaText: {
    en: string;
    ar: string;
  };
  ctaButton: {
    en: string;
    ar: string;
  };
  categories: PartnerCategory[];
  partners: Partner[];
}

export default function PartnersManagementPage() {
  // Initial data based on current partners
  const [partnersData, setPartnersData] = useState<PartnersSection>({
    title: {
      en: "Our Partners",
      ar: "شركاؤنا"
    },
    description: {
      en: "Working together with industry leaders to deliver exceptional real estate solutions and create lasting value for our clients.",
      ar: "نعمل معًا مع قادة الصناعة لتقديم حلول عقارية استثنائية وخلق قيمة دائمة لعملائنا."
    },
    ctaText: {
      en: "Interested in partnering with us?",
      ar: "مهتم بالشراكة معنا؟"
    },
    ctaButton: {
      en: "Get in Touch",
      ar: "تواصل معنا"
    },
    categories: [
      { id: "all", label: { en: "All Partners", ar: "جميع الشركاء" } },
      { id: "investment", label: { en: "Investment", ar: "الاستثمار" } },
      { id: "development", label: { en: "Development", ar: "التطوير" } },
      { id: "property-management", label: { en: "Property Management", ar: "إدارة الممتلكات" } },
      { id: "financial-services", label: { en: "Financial Services", ar: "الخدمات المالية" } },
      { id: "construction", label: { en: "Construction", ar: "البناء" } },
      { id: "real-estate", label: { en: "Real Estate", ar: "العقارات" } },
      { id: "architecture", label: { en: "Architecture", ar: "الهندسة المعمارية" } },
    ],
    partners: [
      {
        id: "global-investments",
        name: { en: "Global Investments Ltd.", ar: "الاستثمارات العالمية المحدودة" },
        category: { en: "Investment", ar: "الاستثمار" },
        description: {
          en: "Leading investment firm specializing in real estate and commercial properties.",
          ar: "شركة استثمار رائدة متخصصة في العقارات والممتلكات التجارية."
        },
        order: 1
      },
      {
        id: "urban-horizon",
        name: { en: "Urban Horizon Development", ar: "تطوير الأفق الحضري" },
        category: { en: "Development", ar: "التطوير" },
        description: {
          en: "Innovative urban development company creating modern living spaces.",
          ar: "شركة تطوير حضري مبتكرة تخلق مساحات معيشة حديثة."
        },
        order: 2
      },
      {
        id: "summit-property",
        name: { en: "Summit Property Group", ar: "مجموعة القمة العقارية" },
        category: { en: "Property Management", ar: "إدارة الممتلكات" },
        description: {
          en: "Professional property management services for residential and commercial properties.",
          ar: "خدمات إدارة الممتلكات المهنية للعقارات السكنية والتجارية."
        },
        order: 3
      },
      {
        id: "cornerstone-financial",
        name: { en: "Cornerstone Financial", ar: "حجر الأساس المالي" },
        category: { en: "Financial Services", ar: "الخدمات المالية" },
        description: {
          en: "Comprehensive financial services including real estate financing and investment advisory.",
          ar: "خدمات مالية شاملة تشمل التمويل العقاري والاستشارات الاستثمارية."
        },
        order: 4
      },
      {
        id: "premier-construction",
        name: { en: "Premier Construction", ar: "البناء الأول" },
        category: { en: "Construction", ar: "البناء" },
        description: {
          en: "High-quality construction services for residential and commercial projects.",
          ar: "خدمات البناء عالية الجودة للمشاريع السكنية والتجارية."
        },
        order: 5
      },
      {
        id: "meridian-real-estate",
        name: { en: "Meridian Real Estate", ar: "ميريديان العقارية" },
        category: { en: "Real Estate", ar: "العقارات" },
        description: {
          en: "Full-service real estate brokerage with extensive market knowledge.",
          ar: "وساطة عقارية كاملة الخدمات مع معرفة واسعة بالسوق."
        },
        order: 6
      },
      {
        id: "vantage-capital",
        name: { en: "Vantage Capital Partners", ar: "شركاء رأس المال المتقدم" },
        category: { en: "Investment", ar: "الاستثمار" },
        description: {
          en: "Strategic capital investment firm focused on real estate opportunities.",
          ar: "شركة استثمار رأس مال استراتيجي تركز على الفرص العقارية."
        },
        order: 7
      },
      {
        id: "elite-architecture",
        name: { en: "Elite Architecture Group", ar: "مجموعة النخبة المعمارية" },
        category: { en: "Architecture", ar: "الهندسة المعمارية" },
        description: {
          en: "Award-winning architectural design firm specializing in luxury developments.",
          ar: "شركة تصميم معماري حائزة على جوائز متخصصة في التطوير الفاخر."
        },
        order: 8
      }
    ]
  });

  const [editingSection, setEditingSection] = useState(false);
  const [editingPartner, setEditingPartner] = useState<string | null>(null);
  const [showAddPartnerForm, setShowAddPartnerForm] = useState(false);
  const [showAddCategoryForm, setShowAddCategoryForm] = useState(false);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState("all");

  const handleSectionSave = (newData: Partial<PartnersSection>) => {
    setPartnersData(prev => ({ ...prev, ...newData }));
    setEditingSection(false);
  };

  const handlePartnerSave = (partnerId: string, newPartner: Partner) => {
    setPartnersData(prev => ({
      ...prev,
      partners: prev.partners.map(p => p.id === partnerId ? newPartner : p)
    }));
    setEditingPartner(null);
  };

  const handlePartnerDelete = (partnerId: string) => {
    if (confirm('Are you sure you want to delete this partner?')) {
      setPartnersData(prev => ({
        ...prev,
        partners: prev.partners.filter(p => p.id !== partnerId)
      }));
    }
  };

  const handlePartnerAdd = (newPartner: Partner) => {
    const maxOrder = Math.max(...partnersData.partners.map(p => p.order), 0);
    const partnerWithOrder = { ...newPartner, order: maxOrder + 1 };
    setPartnersData(prev => ({
      ...prev,
      partners: [...prev.partners, partnerWithOrder]
    }));
    setShowAddPartnerForm(false);
  };

  const handleCategoryAdd = (newCategory: PartnerCategory) => {
    setPartnersData(prev => ({
      ...prev,
      categories: [...prev.categories, newCategory]
    }));
    setShowAddCategoryForm(false);
  };

  const handleCategoryDelete = (categoryId: string) => {
    if (categoryId === 'all') {
      alert('Cannot delete the "All" category');
      return;
    }

    if (confirm('Are you sure you want to delete this category? Partners in this category will need to be reassigned.')) {
      setPartnersData(prev => ({
        ...prev,
        categories: prev.categories.filter(c => c.id !== categoryId)
      }));
    }
  };

  const handleDragStart = (partnerId: string) => {
    setDraggedItem(partnerId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (!draggedItem || draggedItem === targetId) return;

    const draggedIndex = partnersData.partners.findIndex(p => p.id === draggedItem);
    const targetIndex = partnersData.partners.findIndex(p => p.id === targetId);

    const newPartners = [...partnersData.partners];
    const [draggedPartner] = newPartners.splice(draggedIndex, 1);
    newPartners.splice(targetIndex, 0, draggedPartner);

    // Update order values
    const updatedPartners = newPartners.map((partner, index) => ({
      ...partner,
      order: index + 1
    }));

    setPartnersData(prev => ({ ...prev, partners: updatedPartners }));
    setDraggedItem(null);
  };

  const handleSaveAll = () => {
    // Here you would typically send the data to your backend
    console.log('Saving partners data:', partnersData);
    alert('Partners data saved successfully!');
  };

  const filteredPartners = activeFilter === "all" 
    ? partnersData.partners 
    : partnersData.partners.filter(p => {
        const categoryEn = p.category.en.toLowerCase().replace(/\s+/g, '-');
        return categoryEn === activeFilter || p.category.en.toLowerCase().includes(activeFilter.replace('-', ' '));
      });

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">About Us - Partners Section</h1>
          <p className="text-gray-400 mt-1">Manage the partners section of the about page</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Section Header</h2>
            <button
              onClick={() => setEditingSection(!editingSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection ? (
            <SectionEditForm
              data={partnersData}
              onSave={handleSectionSave}
              onCancel={() => setEditingSection(false)}
            />
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                <p className="text-white">{partnersData.title.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                <p className="text-white">{partnersData.title.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                <p className="text-gray-300 text-sm">{partnersData.description.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                <p className="text-gray-300 text-sm">{partnersData.description.ar}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">CTA Text (English)</label>
                  <p className="text-gray-300 text-sm">{partnersData.ctaText.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">CTA Text (Arabic)</label>
                  <p className="text-gray-300 text-sm">{partnersData.ctaText.ar}</p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">CTA Button (English)</label>
                  <p className="text-gray-300 text-sm">{partnersData.ctaButton.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">CTA Button (Arabic)</label>
                  <p className="text-gray-300 text-sm">{partnersData.ctaButton.ar}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Categories Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">Partner Categories ({partnersData.categories.length})</h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowAddCategoryForm(true)}
                className="inline-flex items-center px-3 py-1 text-sm bg-[#00C2FF] text-white rounded hover:bg-[#00C2FF]/90"
              >
                <FiPlus className="mr-1 h-3 w-3" />
                Add Category
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 mb-4">
            {partnersData.categories.map((category) => (
              <div key={category.id} className="bg-gray-700 rounded-lg p-3 border border-gray-600">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-white font-medium">{category.label.en}</p>
                    <p className="text-gray-400 text-sm">{category.label.ar}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {partnersData.partners.filter(p => {
                        const categoryEn = p.category.en.toLowerCase().replace(/\s+/g, '-');
                        return categoryEn === category.id || p.category.en.toLowerCase().includes(category.id.replace('-', ' '));
                      }).length} partners
                    </p>
                  </div>
                  {category.id !== 'all' && (
                    <button
                      onClick={() => handleCategoryDelete(category.id)}
                      className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {showAddCategoryForm && (
            <div className="p-4 bg-gray-700 rounded-lg border border-gray-600">
              <CategoryAddForm
                onSave={handleCategoryAdd}
                onCancel={() => setShowAddCategoryForm(false)}
              />
            </div>
          )}
        </div>

        {/* Filter Controls */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <FiFilter className="mr-2 h-5 w-5" />
              Filter Partners
            </h3>
            <span className="text-gray-400 text-sm">
              Showing {filteredPartners.length} of {partnersData.partners.length} partners
            </span>
          </div>
          <div className="flex flex-wrap gap-2">
            {partnersData.categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  activeFilter === category.id
                    ? 'bg-[#00C2FF] text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {category.label.en}
              </button>
            ))}
          </div>
        </div>

        {/* Partners Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">
              Partners ({filteredPartners.length})
            </h2>
            <button
              onClick={() => setShowAddPartnerForm(true)}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add New Partner
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredPartners
              .sort((a, b) => a.order - b.order)
              .map((partner) => (
                <div
                  key={partner.id}
                  draggable
                  onDragStart={() => handleDragStart(partner.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, partner.id)}
                  className={`bg-gray-700 rounded-lg border border-gray-600 transition-all hover:border-gray-500 cursor-move ${
                    draggedItem === partner.id ? 'opacity-50' : ''
                  }`}
                >
                  {/* Partner Card Header */}
                  <div className="h-1 bg-gradient-to-r from-[#00C2FF] to-[#9747FF]"></div>
                  <div className="border-b border-gray-600 p-3 flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="bg-[#00C2FF]/20 w-8 h-8 rounded-full flex items-center justify-center text-[#00C2FF] mr-3">
                        {partner.logoUrl ? (
                          <img 
                            src={partner.logoUrl} 
                            alt={partner.name.en}
                            className="w-6 h-6 object-contain rounded"
                            onError={(e) => {
                              const target = e.currentTarget as HTMLElement;
                              target.style.display = 'none';
                              const nextSibling = target.nextElementSibling as HTMLElement;
                              nextSibling.style.display = 'flex';
                            }}
                          />
                        ) : null}
                        <span className="text-sm" style={{ display: partner.logoUrl ? 'none' : 'flex' }}>
                          🏢
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-bold text-white">{partner.name.en}</div>
                        <div className="flex items-center">
                          <FiMove className="h-3 w-3 text-gray-500 mr-1" />
                          <span className="text-xs text-gray-400">Order: {partner.order}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingPartner(partner.id)}
                        className="p-1 text-gray-400 hover:text-[#00C2FF] transition-colors"
                      >
                        <FiEdit3 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handlePartnerDelete(partner.id)}
                        className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {editingPartner === partner.id ? (
                    <div className="p-4">
                      <PartnerEditForm
                        partner={partner}
                        categories={partnersData.categories}
                        onSave={(newPartner) => handlePartnerSave(partner.id, newPartner)}
                        onCancel={() => setEditingPartner(null)}
                      />
                    </div>
                  ) : (
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-bold text-white text-sm">{partner.name.en}</h3>
                        <span className="text-xs px-2 py-1 bg-gray-600 rounded-full text-gray-300">
                          {partner.category.en}
                        </span>
                      </div>
                      <p className="text-gray-400 text-xs mb-2">{partner.name.ar}</p>
                      {partner.description && (
                        <p className="text-gray-300 text-xs line-clamp-3">{partner.description.en}</p>
                      )}
                      
                      {/* Accent line */}
                      <div className="mt-3">
                        <div className="w-8 h-0.5 bg-[#00C2FF]/40"></div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
          </div>

          {showAddPartnerForm && (
            <div className="mt-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <PartnerAddForm
                categories={partnersData.categories}
                onSave={handlePartnerAdd}
                onCancel={() => setShowAddPartnerForm(false)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Section Edit Form Component
function SectionEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: PartnersSection;
  onSave: (data: Partial<PartnersSection>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
        <textarea
          value={formData.description.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, en: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
        <textarea
          value={formData.description.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, ar: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">CTA Text (English)</label>
          <input
            type="text"
            value={formData.ctaText.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              ctaText: { ...prev.ctaText, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">CTA Text (Arabic)</label>
          <input
            type="text"
            value={formData.ctaText.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              ctaText: { ...prev.ctaText, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">CTA Button (English)</label>
          <input
            type="text"
            value={formData.ctaButton.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              ctaButton: { ...prev.ctaButton, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">CTA Button (Arabic)</label>
          <input
            type="text"
            value={formData.ctaButton.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              ctaButton: { ...prev.ctaButton, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Partner Edit Form Component
function PartnerEditForm({ 
  partner, 
  categories,
  onSave, 
  onCancel 
}: { 
  partner: Partner;
  categories: PartnerCategory[];
  onSave: (partner: Partner) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(partner);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(partner.logoUrl || '');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the logo persists
      setFormData(prev => ({ ...prev, logoUrl: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById(`logo-upload-edit-${partner.id}`)?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {/* Hidden File Input */}
      <input
        id={`logo-upload-edit-${partner.id}`}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Name (English)"
          value={formData.name.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            name: { ...prev.name, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Name (Arabic)"
          value={formData.name.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            name: { ...prev.name, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Category (English)"
          value={formData.category.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            category: { ...prev.category, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Category (Arabic)"
          value={formData.category.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            category: { ...prev.category, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      {/* Clickable Logo Upload Area */}
      <div>
        <label className="block text-sm font-medium text-gray-400 mb-1">Partner Logo</label>
        <div 
          onClick={handleImageClick}
          className="w-full h-12 bg-gray-600 rounded cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
        >
          {previewUrl ? (
            <div className="relative w-full h-full">
              <img 
                src={previewUrl} 
                alt="Logo Preview"
                className="w-full h-full object-contain rounded p-1"
                onError={() => setPreviewUrl('')}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                <FiUpload className="text-white opacity-0 group-hover:opacity-100 h-4 w-4" />
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
              <div className="text-center">
                <FiUpload className="h-4 w-4 mx-auto mb-1" />
                <p className="text-xs">Click to upload logo</p>
              </div>
            </div>
          )}
        </div>
        {selectedFile && (
          <p className="text-xs text-gray-400 mt-1">
            {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </p>
        )}
      </div>

      <textarea
        placeholder="Description (English)"
        value={formData.description?.en || ''}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          description: { ...prev.description, en: e.target.value } as any
        }))}
        rows={2}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      />

      <textarea
        placeholder="Description (Arabic)"
        value={formData.description?.ar || ''}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          description: { ...prev.description, ar: e.target.value } as any
        }))}
        rows={2}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      />

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save
        </button>
      </div>
    </form>
  );
}

// Partner Add Form Component
function PartnerAddForm({ 
  categories,
  onSave, 
  onCancel 
}: { 
  categories: PartnerCategory[];
  onSave: (partner: Partner) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<Partner>({
    id: '',
    name: { en: '', ar: '' },
    category: { en: '', ar: '' },
    description: { en: '', ar: '' },
    logoUrl: '',
    order: 0
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the logo persists
      setFormData(prev => ({ ...prev, logoUrl: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('logo-upload-add-partner')?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.en || !formData.category.en) {
      alert('Please fill in all required fields');
      return;
    }

    const newPartner = {
      ...formData,
      id: formData.name.en.toLowerCase().replace(/\s+/g, '-')
    };

    onSave(newPartner);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Partner</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Hidden File Input */}
        <input
          id="logo-upload-add-partner"
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Name (English) *</label>
            <input
              type="text"
              value={formData.name.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                name: { ...prev.name, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Name (Arabic)</label>
            <input
              type="text"
              value={formData.name.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                name: { ...prev.name, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Category (English) *</label>
            <input
              type="text"
              value={formData.category.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                category: { ...prev.category, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Category (Arabic)</label>
            <input
              type="text"
              value={formData.category.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                category: { ...prev.category, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        {/* Logo Upload Section */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Partner Logo</label>
          <div 
            onClick={handleImageClick}
            className="w-full h-20 bg-gray-600 rounded cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
          >
            {previewUrl ? (
              <div className="relative w-full h-full">
                <img 
                  src={previewUrl} 
                  alt="Logo Preview"
                  className="w-full h-full object-contain rounded p-2"
                  onError={() => setPreviewUrl('')}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                  <FiUpload className="text-white opacity-0 group-hover:opacity-100 h-6 w-6" />
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                <div className="text-center">
                  <FiUpload className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm font-medium">Click to upload partner logo</p>
                  <p className="text-xs">PNG, JPG or SVG recommended</p>
                </div>
              </div>
            )}
          </div>
          {selectedFile && (
            <p className="text-xs text-gray-400 mt-2">
              Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description?.en || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value } as any
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description?.ar || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value } as any
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            Add Partner
          </button>
        </div>
      </form>
    </div>
  );
}

// Category Add Form Component
function CategoryAddForm({ 
  onSave, 
  onCancel 
}: { 
  onSave: (category: PartnerCategory) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<PartnerCategory>({
    id: '',
    label: { en: '', ar: '' }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.label.en) {
      alert('Please fill in the English label');
      return;
    }

    const newCategory = {
      ...formData,
      id: formData.label.en.toLowerCase().replace(/\s+/g, '-')
    };

    onSave(newCategory);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Category</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Label (English) *</label>
            <input
              type="text"
              value={formData.label.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                label: { ...prev.label, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Label (Arabic)</label>
            <input
              type="text"
              value={formData.label.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                label: { ...prev.label, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            Add Category
          </button>
        </div>
      </form>
    </div>
  );
} 