import React, { useState, useRef, useEffect } from 'react';
import { useEditor, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import FontFamily from '@tiptap/extension-font-family';
import Image from '@tiptap/extension-image';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import CharacterCount from '@tiptap/extension-character-count';
import Typography from '@tiptap/extension-typography';
import Focus from '@tiptap/extension-focus';
import Dropcursor from '@tiptap/extension-dropcursor';
import Gapcursor from '@tiptap/extension-gapcursor';
import Youtube from '@tiptap/extension-youtube';
import Placeholder from '@tiptap/extension-placeholder';
import HardBreak from '@tiptap/extension-hard-break';
// Additional powerful extensions
import BubbleMenuExtension from '@tiptap/extension-bubble-menu';
import FloatingMenuExtension from '@tiptap/extension-floating-menu';
import Mention from '@tiptap/extension-mention';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { 
  FiBold, 
  FiItalic, 
  FiUnderline, 
  FiCode,
  FiList,
  FiLink,
  FiImage,
  FiAlignLeft,
  FiAlignCenter,
  FiAlignRight,
  FiAlignJustify,
  FiMessageSquare,
  FiTable,
  FiCheckSquare,
  FiMinus,
  FiRotateCcw,
  FiRotateCw,
  FiUpload,
  FiType,
  FiRefreshCw,
  FiMaximize,
  FiMinimize,
  FiDownload,
  FiPrinter,
  FiCopy,
  FiSearch,
  FiSmile,
  FiYoutube,
  FiEdit,
  FiSettings,
  FiFileText,
  FiZap,
  FiClock,
  FiBookOpen,
  FiActivity,
  FiAtSign,
  FiMenu
} from 'react-icons/fi';

// Custom Resizable Image Component
const ResizableImage = ({ node, updateAttributes, editor }: any) => {
  const [isResizing, setIsResizing] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleResize = (direction: string, event: React.MouseEvent) => {
    event.preventDefault();
    setIsResizing(true);
    
    const startX = event.clientX;
    const startY = event.clientY;
    const startWidth = parseInt(node.attrs.width || 300);
    const startHeight = parseInt(node.attrs.height || 200);

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;
      
      let newWidth = startWidth;
      let newHeight = startHeight;

      if (direction.includes('right')) newWidth = Math.max(100, startWidth + deltaX);
      if (direction.includes('left')) newWidth = Math.max(100, startWidth - deltaX);
      if (direction.includes('bottom')) newHeight = Math.max(50, startHeight + deltaY);
      if (direction.includes('top')) newHeight = Math.max(50, startHeight - deltaY);

      updateAttributes({ width: newWidth, height: newHeight });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (
    <div 
      className="resizable-image-container relative inline-block group"
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => !isResizing && setShowControls(false)}
    >
      <img
        ref={imageRef}
        src={node.attrs.src}
        alt={node.attrs.alt}
        width={node.attrs.width || 300}
        height={node.attrs.height || 200}
        className="max-w-full h-auto rounded-lg cursor-pointer"
        style={{ width: node.attrs.width, height: node.attrs.height }}
      />
      
      {showControls && (
        <>
          {/* Resize handles */}
          <div className="absolute inset-0 border-2 border-blue-500 pointer-events-none"></div>
          
          {/* Corner handles */}
          <div 
            className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 cursor-nw-resize"
            onMouseDown={(e) => handleResize('top-left', e)}
          ></div>
          <div 
            className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 cursor-ne-resize"
            onMouseDown={(e) => handleResize('top-right', e)}
          ></div>
          <div 
            className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 cursor-sw-resize"
            onMouseDown={(e) => handleResize('bottom-left', e)}
          ></div>
          <div 
            className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 cursor-se-resize"
            onMouseDown={(e) => handleResize('bottom-right', e)}
          ></div>

          {/* Image controls overlay */}
          <div className="absolute top-2 right-2 flex gap-1 bg-black bg-opacity-75 rounded p-1">
            <button
              onClick={() => updateAttributes({ width: null, height: null })}
              className="text-white text-xs px-2 py-1 hover:bg-gray-700 rounded"
              title="Reset size"
            >
              Reset
            </button>
            <button
              onClick={() => {
                const newSrc = prompt('Enter new image URL:', node.attrs.src);
                if (newSrc) updateAttributes({ src: newSrc });
              }}
              className="text-white text-xs px-2 py-1 hover:bg-gray-700 rounded"
              title="Change image"
            >
              Edit
            </button>
            <button
              onClick={() => editor.commands.deleteSelection()}
              className="text-red-400 text-xs px-2 py-1 hover:bg-red-700 rounded"
              title="Delete image"
            >
              ×
            </button>
          </div>

          {/* Size display */}
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {node.attrs.width || 300} × {node.attrs.height || 200}
          </div>
        </>
      )}
    </div>
  );
};

// Custom Image Extension with Resizing
const ResizableImageExtension = Image.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      width: {
        default: null,
        parseHTML: element => element.getAttribute('width'),
        renderHTML: attributes => {
          if (!attributes.width) return {};
          return { width: attributes.width };
        },
      },
      height: {
        default: null,
        parseHTML: element => element.getAttribute('height'),
        renderHTML: attributes => {
          if (!attributes.height) return {};
          return { height: attributes.height };
        },
      },
    };
  },
  addNodeView() {
    return ReactNodeViewRenderer(ResizableImage);
  },
});

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  direction?: 'ltr' | 'rtl';
  maxCharacters?: number;
  showWordCount?: boolean;
  showCharCount?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content,
  onChange,
  placeholder = "Start writing...",
  className = "",
  direction = 'ltr',
  maxCharacters = 50000,
  showWordCount = true,
  showCharCount = true,
}) => {
  // Simplified state management
  const [activePanel, setActivePanel] = useState<string | null>(null);
  const [fontSize, setFontSize] = useState(16);
  const [lineHeight, setLineHeight] = useState(1.6);
  const [isFullscreen, setIsFullscreen] = useState(false);
  // Advanced powerful features state
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  
  const editorRef = useRef<HTMLDivElement>(null);

  // Close panels when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activePanel && editorRef.current && event.target instanceof Element && !editorRef.current.contains(event.target)) {
        setActivePanel(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [activePanel]);

  // Simplified editor without problematic extensions
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: { depth: 100 },
      }),
      Underline,
      TextStyle,
      Color,
      FontFamily.configure({ types: ['textStyle'] }),
      TextAlign.configure({ types: ['heading', 'paragraph'] }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: { class: 'text-blue-400 underline hover:text-blue-300 cursor-pointer' },
      }),
      Image.configure({
        HTMLAttributes: { class: 'max-w-full h-auto rounded-lg my-2 cursor-pointer hover:shadow-lg transition-shadow' },
        allowBase64: true,
        inline: false,
      }),
      Table.configure({ resizable: true, handleWidth: 5, cellMinWidth: 25 }),
      TableRow,
      TableHeader,
      TableCell,
      Highlight.configure({ multicolor: true }),
      Subscript,
      Superscript,
      TaskList,
      TaskItem.configure({
        nested: true,
        HTMLAttributes: { class: 'flex items-start my-2' },
      }),
      HorizontalRule,
      CharacterCount.configure({ limit: maxCharacters }),
      Typography,
      Focus.configure({ className: 'has-focus', mode: 'all' }),
      Dropcursor.configure({ color: '#00C2FF', width: 3 }),
      Gapcursor,
      Youtube.configure({ controls: false, nocookie: true, width: 640, height: 480 }),
      Placeholder.configure({
        placeholder,
        showOnlyWhenEditable: true,
        showOnlyCurrent: false,
      }),
      HardBreak,
      // New powerful extensions
      Mention.configure({
        HTMLAttributes: {
          class: 'mention bg-blue-100 text-blue-600 px-1 py-0.5 rounded font-medium',
        },
      }),
    ],
    content,
    onUpdate: ({ editor }) => onChange(editor.getHTML()),
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4 prose-invert',
        dir: direction,
        style: `font-size: ${fontSize}px; line-height: ${lineHeight};`,
      },
    },
  });

  if (!editor) return null;

  // Color palette
  const colors = [
    '#FFFFFF', '#F3F4F6', '#E5E7EB', '#D1D5DB', '#9CA3AF', '#6B7280', '#374151', '#111827',
    '#FEF2F2', '#FEE2E2', '#FECACA', '#F87171', '#EF4444', '#DC2626', '#B91C1C', '#991B1B',
    '#FFF7ED', '#FFEDD5', '#FED7AA', '#FB923C', '#F97316', '#EA580C', '#C2410C', '#9A3412',
    '#FFFBEB', '#FEF3C7', '#FDE68A', '#FBBF24', '#F59E0B', '#D97706', '#B45309', '#92400E',
    '#F7FEE7', '#ECFCCB', '#D9F99D', '#A3E635', '#65A30D', '#4D7C0F', '#365314', '#1A2E05',
    '#F0FDF4', '#DCFCE7', '#BBF7D0', '#4ADE80', '#22C55E', '#16A34A', '#15803D', '#14532D',
    '#ECFEFF', '#CFFAFE', '#A5F3FC', '#22D3EE', '#06B6D4', '#0891B2', '#0E7490', '#164E63',
    '#EFF6FF', '#DBEAFE', '#BFDBFE', '#3B82F6', '#2563EB', '#1D4ED8', '#1E3A8A', '#1E40AF',
    '#F5F3FF', '#EDE9FE', '#DDD6FE', '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6', '#4C1D95',
    '#FDF4FF', '#FAE8FF', '#F3E8FF', '#C084FC', '#A855F7', '#9333EA', '#7E22CE', '#6B21A8',
  ];

  const fonts = [
    { name: 'Default', value: '' },
    { name: 'Arial', value: 'Arial, sans-serif' },
    { name: 'Helvetica', value: 'Helvetica, sans-serif' },
    { name: 'Times New Roman', value: 'Times New Roman, serif' },
    { name: 'Courier New', value: 'Courier New, monospace' },
    { name: 'Georgia', value: 'Georgia, serif' },
    { name: 'Verdana', value: 'Verdana, sans-serif' },
    { name: 'Comic Sans MS', value: 'Comic Sans MS, cursive' },
    { name: 'Impact', value: 'Impact, sans-serif' },
    { name: 'Trebuchet MS', value: 'Trebuchet MS, sans-serif' },
    { name: 'Palatino', value: 'Palatino, serif' },
    { name: 'Garamond', value: 'Garamond, serif' },
  ];

  const fontSizes = [8, 9, 10, 11, 12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 64, 72];

  // Helper functions
  const togglePanel = (panelName: string) => {
    setActivePanel(activePanel === panelName ? null : panelName);
  };

  const addImage = () => {
    const url = window.prompt('Enter image URL:');
    if (url) editor.chain().focus().setImage({ src: url }).run();
  };

  const uploadImage = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = () => editor.chain().focus().setImage({ src: reader.result as string }).run();
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const addLink = () => {
    const url = window.prompt('Enter URL:');
    if (url) editor.chain().focus().setLink({ href: url }).run();
  };

  const removeLink = () => editor.chain().focus().unsetLink().run();

  const addYoutube = () => {
    const url = window.prompt('Enter YouTube URL:');
    if (url) editor.commands.setYoutubeVideo({ src: url });
  };

  const insertTable = () => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();

  const setColor = (color: string) => {
    editor.chain().focus().setColor(color).run();
    setActivePanel(null);
  };

  const setFontFamily = (font: string) => {
    if (font) editor.chain().focus().setFontFamily(font).run();
    else editor.chain().focus().unsetFontFamily().run();
    setActivePanel(null);
  };

  const clearFormatting = () => editor.chain().focus().clearNodes().unsetAllMarks().run();

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) document.documentElement.requestFullscreen?.();
    else document.exitFullscreen?.();
  };

  const getWordCount = () => {
    const text = editor.getText();
    return text.trim() ? text.trim().split(/\s+/).length : 0;
  };

  const getCharacterCount = () => editor.storage.characterCount?.characters() || 0;
  const getReadingTime = () => Math.ceil(getWordCount() / 200);

  // Advanced powerful features
  const findAndReplace = (searchText: string, replaceText: string) => {
    if (!searchText) return;
    const { from, to } = editor.state.selection;
    const content = editor.getHTML();
    const newContent = content.replace(new RegExp(searchText, 'gi'), replaceText);
    editor.commands.setContent(newContent);
  };

  // Export functions
  const exportToMarkdown = () => {
    const content = editor.getText();
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.md';
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportToHTML = () => {
    const content = editor.getHTML();
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.html';
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportToJSON = () => {
    const content = editor.getJSON();
    const blob = new Blob([JSON.stringify(content, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  // Print functionality
  const printDocument = () => {
    const printWindow = window.open('', '', 'height=600,width=800');
    if (printWindow) {
      printWindow.document.write('<html><head><title>Print Document</title>');
      printWindow.document.write('<style>body{font-family:Arial,sans-serif;line-height:1.6;max-width:800px;margin:0 auto;padding:20px;}</style>');
      printWindow.document.write('</head><body>');
      printWindow.document.write(editor.getHTML());
      printWindow.document.write('</body></html>');
      printWindow.document.close();
      printWindow.print();
    }
  };

  // Content templates
  const templates = [
    {
      name: 'Blog Post',
      content: '<h1>Blog Post Title</h1><p>Introduction paragraph...</p><h2>Main Content</h2><p>Your main content goes here...</p><h2>Conclusion</h2><p>Wrap up your thoughts...</p>'
    },
    {
      name: 'Article',
      content: '<h1>Article Title</h1><p><em>By Author Name</em></p><p><strong>Abstract:</strong> Brief summary of the article...</p><h2>Introduction</h2><p>Article content...</p>'
    },
    {
      name: 'Meeting Notes',
      content: '<h1>Meeting Notes</h1><p><strong>Date:</strong> [Date]</p><p><strong>Attendees:</strong> [Names]</p><h2>Agenda</h2><ul><li>Item 1</li><li>Item 2</li></ul><h2>Action Items</h2><ul><li>[ ] Action 1</li><li>[ ] Action 2</li></ul>'
    },
    {
      name: 'Report',
      content: '<h1>Report Title</h1><p><strong>Executive Summary</strong></p><p>Brief overview...</p><h2>Methodology</h2><p>How the work was done...</p><h2>Results</h2><p>What was found...</p><h2>Recommendations</h2><p>What should be done...</p>'
    }
  ];

  // Image gallery with common stock images
  const stockImages = [
    'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1515378960530-7c0da6231fb1?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=800&h=600&fit=crop'
  ];

  // Advanced text analysis
  const getAdvancedStats = () => {
    const text = editor.getText();
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
    const avgWordsPerSentence = sentences > 0 ? Math.round(getWordCount() / sentences) : 0;
    const avgSentencesPerParagraph = paragraphs > 0 ? Math.round(sentences / paragraphs) : 0;
    
    return {
      sentences,
      paragraphs,
      avgWordsPerSentence,
      avgSentencesPerParagraph,
      readabilityScore: avgWordsPerSentence < 15 ? 'Easy' : avgWordsPerSentence < 20 ? 'Medium' : 'Hard'
    };
  };

  // Smart paste with format detection
  const handleSmartPaste = (event: ClipboardEvent) => {
    const clipboardData = event.clipboardData;
    if (!clipboardData) return;

    const htmlData = clipboardData.getData('text/html');
    const textData = clipboardData.getData('text/plain');

    if (htmlData) {
      event.preventDefault();
      // Clean and insert HTML
      const cleanHTML = htmlData.replace(/<style[^>]*>.*?<\/style>/gi, '').replace(/class="[^"]*"/gi, '');
      editor.commands.insertContent(cleanHTML);
    } else if (textData.startsWith('http')) {
      event.preventDefault();
      // Auto-create link for URLs
      editor.chain().focus().setLink({ href: textData }).insertContent(textData).run();
    }
  };

  return (
    <div ref={editorRef} className={`border border-gray-600 rounded-lg bg-gray-800 ${isFullscreen ? 'fixed inset-0 z-50 rounded-none' : className}`}>
      {/* Simplified Toolbar */}
      <div className="border-b border-gray-600 p-3 bg-gray-700">
        <div className="flex flex-wrap gap-2 mb-2">
          
          {/* File Operations */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.commands.setContent('')} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="New Document">
              <FiFileText className="w-4 h-4" />
            </button>
            <button onClick={() => document.getElementById('file-upload')?.click()} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Import">
              <FiUpload className="w-4 h-4" />
            </button>
            <input id="file-upload" type="file" accept=".html,.md,.txt" className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (e) => editor.commands.setContent(e.target?.result as string);
                  reader.readAsText(file);
                }
              }}
            />
          </div>

          {/* History */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.chain().focus().undo().run()} disabled={!editor.can().undo()}
              className={`p-2 rounded hover:bg-gray-600 ${!editor.can().undo() ? 'opacity-50 cursor-not-allowed' : 'text-gray-300 hover:text-white'}`} title="Undo">
              <FiRotateCcw className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().redo().run()} disabled={!editor.can().redo()}
              className={`p-2 rounded hover:bg-gray-600 ${!editor.can().redo() ? 'opacity-50 cursor-not-allowed' : 'text-gray-300 hover:text-white'}`} title="Redo">
              <FiRotateCw className="w-4 h-4" />
            </button>
            <button onClick={clearFormatting} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Clear Formatting">
              <FiRefreshCw className="w-4 h-4" />
            </button>
          </div>

          {/* Font Controls */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => togglePanel('fonts')} className={`p-2 rounded hover:bg-gray-600 ${activePanel === 'fonts' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Font Family">
              <FiType className="w-4 h-4" />
            </button>
            <select value={fontSize} onChange={(e) => setFontSize(Number(e.target.value))} className="px-2 py-1 text-xs bg-gray-800 border border-gray-600 rounded text-gray-300">
              {fontSizes.map(size => <option key={size} value={size}>{size}px</option>)}
            </select>
          </div>

          {/* Text Formatting */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.chain().focus().toggleBold().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('bold') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Bold">
              <FiBold className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().toggleItalic().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('italic') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Italic">
              <FiItalic className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().toggleUnderline().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('underline') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Underline">
              <FiUnderline className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().toggleStrike().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('strike') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Strikethrough">
              <span className="text-sm font-bold">S̶</span>
            </button>
            <button onClick={() => editor.chain().focus().toggleHighlight().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('highlight') ? 'bg-yellow-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Highlight">
              <span className="text-sm font-bold bg-yellow-500 text-black px-1 rounded">H</span>
            </button>
          </div>

          {/* Super/Subscript */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.chain().focus().toggleSubscript().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('subscript') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Subscript">
              <span className="text-xs">X₂</span>
            </button>
            <button onClick={() => editor.chain().focus().toggleSuperscript().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('superscript') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Superscript">
              <span className="text-xs">X²</span>
            </button>
          </div>

          {/* Color Picker */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => togglePanel('colors')} className={`p-2 rounded hover:bg-gray-600 ${activePanel === 'colors' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Text Color">
              <div className="w-4 h-4 bg-current border border-gray-500 rounded"></div>
            </button>
          </div>

          {/* Headings */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <select onChange={(e) => {
              const level = parseInt(e.target.value);
              if (level === 0) editor.chain().focus().setParagraph().run();
              else editor.chain().focus().toggleHeading({ level: level as 1 | 2 | 3 | 4 | 5 | 6 }).run();
            }} className="px-2 py-1 text-sm border border-gray-600 rounded bg-gray-800 text-gray-300"
            value={
              editor.isActive('heading', { level: 1 }) ? 1 :
              editor.isActive('heading', { level: 2 }) ? 2 :
              editor.isActive('heading', { level: 3 }) ? 3 :
              editor.isActive('heading', { level: 4 }) ? 4 :
              editor.isActive('heading', { level: 5 }) ? 5 :
              editor.isActive('heading', { level: 6 }) ? 6 : 0
            }>
              <option value={0}>Paragraph</option>
              <option value={1}>H1</option>
              <option value={2}>H2</option>
              <option value={3}>H3</option>
              <option value={4}>H4</option>
              <option value={5}>H5</option>
              <option value={6}>H6</option>
            </select>
          </div>

          {/* Lists */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.chain().focus().toggleBulletList().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('bulletList') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Bullet List">
              <FiList className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().toggleOrderedList().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('orderedList') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Numbered List">
              <span className="text-xs font-bold">1.</span>
            </button>
            <button onClick={() => editor.chain().focus().toggleTaskList().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('taskList') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Task List">
              <FiCheckSquare className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().toggleBlockquote().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('blockquote') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Quote">
              <FiMessageSquare className="w-4 h-4" />
            </button>
          </div>

          {/* Alignment */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.chain().focus().setTextAlign('left').run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive({ textAlign: 'left' }) ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Align Left">
              <FiAlignLeft className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().setTextAlign('center').run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive({ textAlign: 'center' }) ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Align Center">
              <FiAlignCenter className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().setTextAlign('right').run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive({ textAlign: 'right' }) ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Align Right">
              <FiAlignRight className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().setTextAlign('justify').run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive({ textAlign: 'justify' }) ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Justify">
              <FiAlignJustify className="w-4 h-4" />
            </button>
          </div>

          {/* Media & Links */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={addLink} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('link') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Add Link">
              <FiLink className="w-4 h-4" />
            </button>
            {editor.isActive('link') && (
              <button onClick={removeLink} className="p-2 rounded hover:bg-red-600 text-red-400 hover:text-white" title="Remove Link">×</button>
            )}
            <button onClick={addImage} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Add Image URL">
              <FiImage className="w-4 h-4" />
            </button>
            <button onClick={uploadImage} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Upload Image">
              <FiUpload className="w-4 h-4" />
            </button>
            <button onClick={addYoutube} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Add YouTube Video">
              <FiYoutube className="w-4 h-4" />
            </button>
          </div>

          {/* Table */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={insertTable} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Insert Table">
              <FiTable className="w-4 h-4" />
            </button>
            {editor.isActive('table') && (
              <>
                <button onClick={() => editor.chain().focus().addColumnBefore().run()} className="p-1 text-xs rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Add Column">+C</button>
                <button onClick={() => editor.chain().focus().addRowBefore().run()} className="p-1 text-xs rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Add Row">+R</button>
                <button onClick={() => editor.chain().focus().deleteTable().run()} className="p-1 text-xs rounded hover:bg-red-600 text-red-400 hover:text-white" title="Delete Table">×T</button>
              </>
            )}
          </div>

          {/* Code & Special */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button onClick={() => editor.chain().focus().toggleCode().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('code') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Inline Code">
              <FiCode className="w-4 h-4" />
            </button>
            <button onClick={() => editor.chain().focus().toggleCodeBlock().run()} className={`p-2 rounded hover:bg-gray-600 ${editor.isActive('codeBlock') ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Code Block">
              <span className="text-xs font-mono">{"{}"}</span>
            </button>
            <button onClick={() => editor.chain().focus().setHorizontalRule().run()} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Horizontal Rule">
              <FiMinus className="w-4 h-4" />
            </button>
          </div>

          {/* Collaboration & Mentions */}
          <div className="flex items-center gap-1 border-r border-gray-600 pr-2 mr-2">
            <button 
              onClick={() => {
                // Insert @ symbol to trigger mention
                editor.chain().focus().insertContent('@').run();
              }} 
              className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" 
              title="Add Mention (@)"
            >
              <FiAtSign className="w-4 h-4" />
            </button>
            <button 
              onClick={() => togglePanel('bubbleMenu')} 
              className={`p-2 rounded hover:bg-gray-600 ${activePanel === 'bubbleMenu' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} 
              title="Toggle Bubble Menu"
            >
              <FiMenu className="w-4 h-4" />
            </button>
          </div>

          {/* Tools */}
          <div className="flex items-center gap-1">
            <button onClick={() => setIsSearchVisible(!isSearchVisible)} className={`p-2 rounded hover:bg-gray-600 ${isSearchVisible ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Find & Replace">
              <FiSearch className="w-4 h-4" />
            </button>
            <button onClick={() => setShowTemplates(!showTemplates)} className={`p-2 rounded hover:bg-gray-600 ${showTemplates ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Content Templates">
              <FiFileText className="w-4 h-4" />
            </button>
            <button onClick={() => setShowImageGallery(!showImageGallery)} className={`p-2 rounded hover:bg-gray-600 ${showImageGallery ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Image Gallery">
              <FiImage className="w-4 h-4" />
            </button>
            <button onClick={printDocument} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Print Document">
              <FiPrinter className="w-4 h-4" />
            </button>
            <button onClick={() => togglePanel('export')} className={`p-2 rounded hover:bg-gray-600 ${activePanel === 'export' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Export Options">
              <FiDownload className="w-4 h-4" />
            </button>
            <button onClick={() => togglePanel('stats')} className={`p-2 rounded hover:bg-gray-600 ${activePanel === 'stats' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Document Statistics">
              <FiActivity className="w-4 h-4" />
            </button>
            <button onClick={toggleFullscreen} className="p-2 rounded hover:bg-gray-600 text-gray-300 hover:text-white" title="Toggle Fullscreen">
              {isFullscreen ? <FiMinimize className="w-4 h-4" /> : <FiMaximize className="w-4 h-4" />}
            </button>
            <button onClick={() => togglePanel('settings')} className={`p-2 rounded hover:bg-gray-600 ${activePanel === 'settings' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'}`} title="Settings">
              <FiSettings className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Stats */}
        {(showWordCount || showCharCount) && (
          <div className="flex items-center gap-4 text-xs text-gray-400 pt-2 border-t border-gray-600">
            {showWordCount && <span>Words: {getWordCount()}</span>}
            {showCharCount && (
              <span className={getCharacterCount() > maxCharacters * 0.9 ? 'text-red-400' : ''}>
                Characters: {getCharacterCount()}/{maxCharacters}
              </span>
            )}
            <span>Reading time: {getReadingTime()} min</span>
          </div>
        )}
      </div>

      {/* Conditionally rendered panels - Better positioning within editor bounds */}
      {activePanel === 'colors' && (
        <div key="colors" className="absolute top-20 left-4 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-lg p-3 max-w-md mx-auto">
          <div className="grid grid-cols-8 gap-1">
            {colors.map((color) => (
              <button key={color} onClick={() => setColor(color)} className="w-6 h-6 rounded border border-gray-500 hover:scale-110 transition-transform" style={{ backgroundColor: color }} title={color} />
            ))}
          </div>
        </div>
      )}

      {activePanel === 'fonts' && (
        <div key="fonts" className="absolute top-20 left-4 right-4 z-50 bg-gray-800 border border-gray-600 rounded shadow-lg max-w-md mx-auto max-h-48 overflow-y-auto">
          {fonts.map((font) => (
            <button key={font.value} onClick={() => setFontFamily(font.value)} className="block w-full text-left px-3 py-2 hover:bg-gray-700 text-sm text-gray-300 hover:text-white" style={{ fontFamily: font.value }}>
              {font.name}
            </button>
          ))}
        </div>
      )}

      {activePanel === 'stats' && (
        <div key="stats" className="absolute top-20 left-4 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-4 max-w-md mx-auto">
          <h3 className="text-white text-sm font-medium mb-3">📊 Document Analytics</h3>
          <div className="space-y-2 text-sm text-gray-300">
            <div className="flex justify-between"><span>Words:</span><span className="font-medium">{getWordCount()}</span></div>
            <div className="flex justify-between"><span>Characters:</span><span className="font-medium">{getCharacterCount()}</span></div>
            <div className="flex justify-between"><span>Characters (no spaces):</span><span className="font-medium">{editor.getText().replace(/\s/g, '').length}</span></div>
            <div className="flex justify-between"><span>Reading time:</span><span className="font-medium">{getReadingTime()} min</span></div>
            <div className="flex justify-between"><span>Lines:</span><span className="font-medium">{editor.getText().split('\n').length}</span></div>
            
            {/* Advanced Stats */}
            <hr className="border-gray-600 my-3" />
            <div className="space-y-2">
              <div className="flex justify-between"><span>Sentences:</span><span className="font-medium">{getAdvancedStats().sentences}</span></div>
              <div className="flex justify-between"><span>Paragraphs:</span><span className="font-medium">{getAdvancedStats().paragraphs}</span></div>
              <div className="flex justify-between"><span>Avg words/sentence:</span><span className="font-medium">{getAdvancedStats().avgWordsPerSentence}</span></div>
              <div className="flex justify-between">
                <span>Readability:</span>
                <span className={`font-medium ${getAdvancedStats().readabilityScore === 'Easy' ? 'text-green-400' : getAdvancedStats().readabilityScore === 'Medium' ? 'text-yellow-400' : 'text-red-400'}`}>
                  {getAdvancedStats().readabilityScore}
                </span>
              </div>
            </div>
          </div>
          <button onClick={() => setActivePanel(null)} className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {activePanel === 'settings' && (
        <div key="settings" className="absolute top-20 left-4 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-4 max-w-md mx-auto">
          <h3 className="text-white text-sm font-medium mb-3">Editor Settings</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-xs text-gray-300 mb-1">Line Height</label>
              <input type="range" min="1" max="3" step="0.1" value={lineHeight} onChange={(e) => setLineHeight(Number(e.target.value))} className="w-full" />
              <span className="text-xs text-gray-400">{lineHeight}</span>
            </div>
            <div>
              <label className="block text-xs text-gray-300 mb-1">Base Font Size</label>
              <select value={fontSize} onChange={(e) => setFontSize(Number(e.target.value))} className="w-full px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm">
                {fontSizes.map(size => <option key={size} value={size}>{size}px</option>)}
              </select>
            </div>
            <div className="flex items-center gap-2">
              <input type="checkbox" id="spellcheck" className="rounded" />
              <label htmlFor="spellcheck" className="text-xs text-gray-300">Enable spellcheck</label>
            </div>
          </div>
          <button onClick={() => setActivePanel(null)} className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {activePanel === 'bubbleMenu' && (
        <div key="bubbleMenu" className="absolute top-20 left-4 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-4 max-w-md mx-auto">
          <h3 className="text-white text-sm font-medium mb-3">Advanced Features Active</h3>
          <div className="space-y-3 text-sm text-gray-300">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>✨ <strong>Bubble Menu:</strong> Select text to see formatting options</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>🎯 <strong>Floating Menu:</strong> Appears on empty lines for quick blocks</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>👥 <strong>Mentions:</strong> Type @ to mention users or teams</span>
            </div>
            <div className="bg-gray-700 p-3 rounded text-xs">
              <strong>💡 Pro Tips:</strong>
              <ul className="mt-2 space-y-1">
                <li>• Select text to see the bubble menu</li>
                <li>• Click on empty line to see floating menu</li>
                <li>• Type @username to mention someone</li>
                <li>• Use keyboard shortcuts for faster editing</li>
              </ul>
            </div>
          </div>
          <button onClick={() => setActivePanel(null)} className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {/* Export Panel */}
      {activePanel === 'export' && (
        <div key="export" className="absolute top-20 left-4 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-4 max-w-md mx-auto">
          <h3 className="text-white text-sm font-medium mb-3">Export Options</h3>
          <div className="space-y-2">
            <button onClick={exportToHTML} className="w-full text-left px-3 py-2 hover:bg-gray-700 text-sm text-gray-300 hover:text-white rounded flex items-center gap-2">
              <FiFileText className="w-4 h-4" />
              Export as HTML
            </button>
            <button onClick={exportToMarkdown} className="w-full text-left px-3 py-2 hover:bg-gray-700 text-sm text-gray-300 hover:text-white rounded flex items-center gap-2">
              <FiDownload className="w-4 h-4" />
              Export as Markdown
            </button>
            <button onClick={exportToJSON} className="w-full text-left px-3 py-2 hover:bg-gray-700 text-sm text-gray-300 hover:text-white rounded flex items-center gap-2">
              <FiCode className="w-4 h-4" />
              Export as JSON
            </button>
            <button onClick={() => {navigator.clipboard.writeText(editor.getHTML()); setActivePanel(null);}} className="w-full text-left px-3 py-2 hover:bg-gray-700 text-sm text-gray-300 hover:text-white rounded flex items-center gap-2">
              <FiCopy className="w-4 h-4" />
              Copy HTML to Clipboard
            </button>
          </div>
          <button onClick={() => setActivePanel(null)} className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {/* Search Panel */}
      {isSearchVisible && (
        <div className="absolute top-16 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-3 w-80">
          <h3 className="text-white text-sm font-medium mb-2">Find & Replace</h3>
          <div className="space-y-2">
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-sm"
            />
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Replace with..."
                id="replace-input"
                className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-sm"
              />
              <button
                onClick={() => {
                  const replaceText = (document.getElementById('replace-input') as HTMLInputElement)?.value || '';
                  findAndReplace(searchTerm, replaceText);
                }}
                className="px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Replace All
              </button>
            </div>
          </div>
          <button onClick={() => setIsSearchVisible(false)} className="mt-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {/* Templates Panel */}
      {showTemplates && (
        <div className="absolute top-16 left-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-3 w-80 max-h-96 overflow-y-auto">
          <h3 className="text-white text-sm font-medium mb-3">Content Templates</h3>
          <div className="space-y-2">
            {templates.map((template) => (
              <button
                key={template.name}
                onClick={() => {
                  editor.commands.setContent(template.content);
                  setShowTemplates(false);
                }}
                className="w-full text-left px-3 py-2 hover:bg-gray-700 text-sm text-gray-300 hover:text-white rounded"
              >
                <div className="font-medium">{template.name}</div>
                <div className="text-xs text-gray-500 mt-1 truncate">
                  {template.content.replace(/<[^>]*>/g, '').substring(0, 60)}...
                </div>
              </button>
            ))}
          </div>
          <button onClick={() => setShowTemplates(false)} className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {/* Image Gallery Panel */}
      {showImageGallery && (
        <div className="absolute top-16 right-4 z-50 bg-gray-800 border border-gray-600 rounded-lg p-3 w-80 max-h-96 overflow-y-auto">
          <h3 className="text-white text-sm font-medium mb-3">Stock Images</h3>
          <div className="grid grid-cols-2 gap-2">
            {stockImages.map((imageUrl, index) => (
              <button
                key={index}
                onClick={() => {
                  editor.chain().focus().setImage({ src: imageUrl }).run();
                  setShowImageGallery(false);
                }}
                className="relative group overflow-hidden rounded hover:ring-2 hover:ring-blue-500"
              >
                <img
                  src={imageUrl}
                  alt={`Stock image ${index + 1}`}
                  className="w-full h-20 object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <span className="text-white text-xs">Insert</span>
                </div>
              </button>
            ))}
          </div>
          <button onClick={() => setShowImageGallery(false)} className="mt-3 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700">Close</button>
        </div>
      )}

      {/* Editor Content */}
      <div className="min-h-[300px] max-h-[500px] overflow-y-auto bg-gray-800 relative">
        <EditorContent editor={editor} className="prose-editor-dark" />
        {!content && (
          <div className="absolute top-6 left-6 text-gray-500 pointer-events-none flex items-center gap-2">
            <FiEdit className="w-4 h-4" />
            {placeholder}
          </div>
        )}
        
        {/* Bubble Menu - appears when text is selected */}
        {editor && (
          <BubbleMenu 
            editor={editor} 
            tippyOptions={{ duration: 100 }}
            className="bubble-menu bg-gray-900 border border-gray-600 rounded-lg shadow-lg p-2 flex items-center gap-1"
          >
            <button
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={`p-2 rounded hover:bg-gray-700 ${editor.isActive('bold') ? 'bg-blue-600 text-white' : 'text-gray-300'}`}
              title="Bold"
            >
              <FiBold className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={`p-2 rounded hover:bg-gray-700 ${editor.isActive('italic') ? 'bg-blue-600 text-white' : 'text-gray-300'}`}
              title="Italic"
            >
              <FiItalic className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={`p-2 rounded hover:bg-gray-700 ${editor.isActive('underline') ? 'bg-blue-600 text-white' : 'text-gray-300'}`}
              title="Underline"
            >
              <FiUnderline className="w-4 h-4" />
            </button>
            <div className="w-px h-6 bg-gray-600 mx-1"></div>
            <button
              onClick={() => editor.chain().focus().toggleHighlight().run()}
              className={`p-2 rounded hover:bg-gray-700 ${editor.isActive('highlight') ? 'bg-yellow-600 text-white' : 'text-gray-300'}`}
              title="Highlight"
            >
              <span className="text-xs font-bold bg-yellow-500 text-black px-1 rounded">H</span>
            </button>
            <button
              onClick={addLink}
              className={`p-2 rounded hover:bg-gray-700 ${editor.isActive('link') ? 'bg-blue-600 text-white' : 'text-gray-300'}`}
              title="Add Link"
            >
              <FiLink className="w-4 h-4" />
            </button>
          </BubbleMenu>
        )}

        {/* Floating Menu - appears on empty lines */}
        {editor && (
          <FloatingMenu 
            editor={editor} 
            tippyOptions={{ duration: 100 }}
            className="floating-menu bg-gray-900 border border-gray-600 rounded-lg shadow-lg p-2 flex items-center gap-1"
          >
            <button
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              className="p-2 rounded hover:bg-gray-700 text-gray-300 hover:text-white text-sm font-bold"
              title="Heading 1"
            >
              H1
            </button>
            <button
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              className="p-2 rounded hover:bg-gray-700 text-gray-300 hover:text-white text-sm font-bold"
              title="Heading 2"
            >
              H2
            </button>
            <button
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className="p-2 rounded hover:bg-gray-700 text-gray-300 hover:text-white"
              title="Bullet List"
            >
              <FiList className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className="p-2 rounded hover:bg-gray-700 text-gray-300 hover:text-white"
              title="Quote"
            >
              <FiMessageSquare className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleCodeBlock().run()}
              className="p-2 rounded hover:bg-gray-700 text-gray-300 hover:text-white"
              title="Code Block"
            >
              <FiCode className="w-4 h-4" />
            </button>
          </FloatingMenu>
        )}
      </div>

      {/* Status Bar */}
      <div className="border-t border-gray-600 px-4 py-2 bg-gray-700 text-xs text-gray-400 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <span className="flex items-center gap-1">
            <FiActivity className="w-3 h-3" />
            Live editing
          </span>
          <span className="flex items-center gap-1">
            <FiZap className="w-3 h-3" />
            Auto-save
          </span>
        </div>
        <div className="flex items-center gap-4">
          <span>Last saved: {new Date().toLocaleTimeString()}</span>
          <span className="flex items-center gap-1">
            <FiClock className="w-3 h-3" />
            {getReadingTime()} min read
          </span>
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor; 