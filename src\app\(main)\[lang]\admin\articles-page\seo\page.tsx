"use client";

import { useState } from 'react';
import { FiSave, FiEye, FiX, FiInfo, FiGlobe } from 'react-icons/fi';

// Interface for SEO data
interface ArticlesSEOData {
  title: {
    english: string;
    arabic: string;
  };
  description: {
    english: string;
    arabic: string;
  };
  keywords: {
    english: string[];
    arabic: string[];
  };
  ogTitle: {
    english: string;
    arabic: string;
  };
  ogDescription: {
    english: string;
    arabic: string;
  };
  ogImage: string;
  canonical: string;
  robots: {
    index: boolean;
    follow: boolean;
    archive: boolean;
    snippet: boolean;
  };
  schema: {
    type: string;
    enabled: boolean;
  };
  hreflang: {
    enabled: boolean;
    alternates: {
      lang: string;
      url: string;
    }[];
  };
}

export default function ArticlesSEOPage() {
  const [seoData, setSeoData] = useState<ArticlesSEOData>({
    title: {
      english: "Latest Articles & Real Estate Insights | Mazaya Capital",
      arabic: "أحدث المقالات ورؤى العقارات | مزايا كابيتال"
    },
    description: {
      english: "Discover the latest articles, market insights, and expert analysis in real estate investment. Stay informed with Mazaya Capital's comprehensive article collection covering industry trends, investment tips, and market developments.",
      arabic: "اكتشف أحدث المقالات ورؤى السوق والتحليلات المتخصصة في الاستثمار العقاري. ابق على اطلاع مع مجموعة مقالات مزايا كابيتال الشاملة التي تغطي اتجاهات الصناعة ونصائح الاستثمار وتطورات السوق."
    },
    keywords: {
      english: ["real estate articles", "property investment", "market analysis", "investment tips", "real estate news", "property guides", "market trends", "real estate insights", "property market", "investment strategies"],
      arabic: ["مقالات عقارية", "استثمار عقاري", "تحليل السوق", "نصائح استثمارية", "أخبار العقارات", "أدلة العقارات", "اتجاهات السوق", "رؤى عقارية", "سوق العقارات", "استراتيجيات الاستثمار"]
    },
    ogTitle: {
      english: "Real Estate Articles & Market Insights - Mazaya Capital",
      arabic: "مقالات العقارات ورؤى السوق - مزايا كابيتال"
    },
    ogDescription: {
      english: "Stay updated with the latest real estate articles, market insights, and investment analysis from Mazaya Capital's expert team.",
      arabic: "ابق على اطلاع بأحدث مقالات العقارات ورؤى السوق وتحليلات الاستثمار من فريق خبراء مزايا كابيتال."
    },
    ogImage: "/images/og/articles-page-og.jpg",
    canonical: "https://mazayacapital.com/articles",
    robots: {
      index: true,
      follow: true,
      archive: true,
      snippet: true
    },
    schema: {
      type: "CollectionPage",
      enabled: true
    },
    hreflang: {
      enabled: true,
      alternates: [
        { lang: "en", url: "https://mazayacapital.com/en/articles" },
        { lang: "ar", url: "https://mazayacapital.com/ar/articles" }
      ]
    }
  });

  const [showPreview, setShowPreview] = useState(false);
  const [newKeyword, setNewKeyword] = useState({ english: '', arabic: '' });

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean, language?: 'english' | 'arabic') => {
    if (language) {
      setSeoData(prev => ({
        ...prev,
        [field]: {
          ...prev[field as keyof ArticlesSEOData] as any,
          [language]: value
        }
      }));
    } else {
      setSeoData(prev => ({ ...prev, [field]: value }));
    }
  };

  // Handle robots settings
  const handleRobotsChange = (setting: keyof ArticlesSEOData['robots'], value: boolean) => {
    setSeoData(prev => ({
      ...prev,
      robots: {
        ...prev.robots,
        [setting]: value
      }
    }));
  };

  // Handle schema settings
  const handleSchemaChange = (field: keyof ArticlesSEOData['schema'], value: string | boolean) => {
    setSeoData(prev => ({
      ...prev,
      schema: {
        ...prev.schema,
        [field]: value
      }
    }));
  };

  // Add keyword
  const addKeyword = (language: 'english' | 'arabic') => {
    const keyword = newKeyword[language].trim();
    if (keyword && !seoData.keywords[language].includes(keyword)) {
      setSeoData(prev => ({
        ...prev,
        keywords: {
          ...prev.keywords,
          [language]: [...prev.keywords[language], keyword]
        }
      }));
      setNewKeyword(prev => ({ ...prev, [language]: '' }));
    }
  };

  // Remove keyword
  const removeKeyword = (language: 'english' | 'arabic', keyword: string) => {
    setSeoData(prev => ({
      ...prev,
      keywords: {
        ...prev.keywords,
        [language]: prev.keywords[language].filter(k => k !== keyword)
      }
    }));
  };

  const handleSave = () => {
    // Here you would typically save to your backend
    console.log('Saving articles page SEO data:', seoData);
    alert('Articles page SEO settings saved successfully!');
  };

  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Articles Page SEO Settings</h1>
          <p className="mt-2 text-sm text-gray-400">
            Configure SEO settings for the articles page to improve search engine visibility
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 hover:text-white hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiEye className="-ml-1 mr-2 h-5 w-5" />
            {showPreview ? 'Hide Preview' : 'Preview'}
          </button>
          <button
            onClick={handleSave}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
          >
            <FiSave className="-ml-1 mr-2 h-5 w-5" />
            Save Settings
          </button>
        </div>
      </div>

      {/* Preview Modal */}
      {showPreview && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">SEO Preview</h3>
                  <button
                    onClick={() => setShowPreview(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                
                {/* Google Search Result Preview */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Google Search Result Preview</h4>
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="text-sm text-green-700 mb-1">https://mazayacapital.com/articles</div>
                    <div className="text-xl text-blue-600 hover:underline cursor-pointer mb-1">
                      {seoData.title.english}
                    </div>
                    <div className="text-sm text-gray-600 leading-relaxed">
                      {seoData.description.english}
                    </div>
                  </div>
                </div>

                {/* Social Media Preview */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Social Media Preview</h4>
                  <div className="border rounded-lg overflow-hidden bg-gray-50">
                    <div className="h-48 bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-500">OG Image Preview</span>
                    </div>
                    <div className="p-4">
                      <div className="font-medium text-gray-900 mb-1">
                        {seoData.ogTitle.english}
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        {seoData.ogDescription.english}
                      </div>
                      <div className="text-xs text-gray-500">mazayacapital.com</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* English SEO */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4">English SEO</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">Meta Title</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.title.english}
                onChange={e => handleInputChange('title', e.target.value, 'english')}
                placeholder="Enter meta title"
              />
              <div className="mt-1 text-xs text-gray-400">
                {seoData.title.english.length}/60 characters
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300">Meta Description</label>
              <textarea
                rows={3}
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.description.english}
                onChange={e => handleInputChange('description', e.target.value, 'english')}
                placeholder="Enter meta description"
              />
              <div className="mt-1 text-xs text-gray-400">
                {seoData.description.english.length}/160 characters
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Keywords</label>
              <div className="mt-1 flex">
                <input
                  type="text"
                  className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={newKeyword.english}
                  onChange={e => setNewKeyword(prev => ({ ...prev, english: e.target.value }))}
                  placeholder="Add keyword"
                  onKeyPress={e => e.key === 'Enter' && addKeyword('english')}
                />
                <button
                  onClick={() => addKeyword('english')}
                  className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                >
                  Add
                </button>
              </div>
              <div className="mt-2 flex flex-wrap gap-2">
                {seoData.keywords.english.map(keyword => (
                  <span
                    key={keyword}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                  >
                    {keyword}
                    <button
                      onClick={() => removeKeyword('english', keyword)}
                      className="ml-1 text-gray-400 hover:text-white"
                    >
                      <FiX className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Open Graph Title</label>
              <input
                type="text"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.ogTitle.english}
                onChange={e => handleInputChange('ogTitle', e.target.value, 'english')}
                placeholder="Enter OG title"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Open Graph Description</label>
              <textarea
                rows={2}
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.ogDescription.english}
                onChange={e => handleInputChange('ogDescription', e.target.value, 'english')}
                placeholder="Enter OG description"
              />
            </div>
          </div>
        </div>

        {/* Arabic SEO */}
        <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-medium text-white mb-4">Arabic SEO</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">عنوان الميتا</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.title.arabic}
                onChange={e => handleInputChange('title', e.target.value, 'arabic')}
                placeholder="أدخل عنوان الميتا"
              />
              <div className="mt-1 text-xs text-gray-400">
                {seoData.title.arabic.length}/60 حرف
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300">وصف الميتا</label>
              <textarea
                rows={3}
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.description.arabic}
                onChange={e => handleInputChange('description', e.target.value, 'arabic')}
                placeholder="أدخل وصف الميتا"
              />
              <div className="mt-1 text-xs text-gray-400">
                {seoData.description.arabic.length}/160 حرف
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">الكلمات المفتاحية</label>
              <div className="mt-1 flex">
                <input
                  type="text"
                  dir="rtl"
                  className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                  value={newKeyword.arabic}
                  onChange={e => setNewKeyword(prev => ({ ...prev, arabic: e.target.value }))}
                  placeholder="أضف كلمة مفتاحية"
                  onKeyPress={e => e.key === 'Enter' && addKeyword('arabic')}
                />
                <button
                  onClick={() => addKeyword('arabic')}
                  className="px-3 py-2 bg-[#00C2FF] text-white rounded-r-md hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"
                >
                  إضافة
                </button>
              </div>
              <div className="mt-2 flex flex-wrap gap-2">
                {seoData.keywords.arabic.map(keyword => (
                  <span
                    key={keyword}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300"
                    dir="rtl"
                  >
                    {keyword}
                    <button
                      onClick={() => removeKeyword('arabic', keyword)}
                      className="mr-1 text-gray-400 hover:text-white"
                    >
                      <FiX className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">عنوان Open Graph</label>
              <input
                type="text"
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.ogTitle.arabic}
                onChange={e => handleInputChange('ogTitle', e.target.value, 'arabic')}
                placeholder="أدخل عنوان OG"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">وصف Open Graph</label>
              <textarea
                rows={2}
                dir="rtl"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.ogDescription.arabic}
                onChange={e => handleInputChange('ogDescription', e.target.value, 'arabic')}
                placeholder="أدخل وصف OG"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-4">Advanced SEO Settings</h3>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300">Canonical URL</label>
              <input
                type="url"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.canonical}
                onChange={e => handleInputChange('canonical', e.target.value)}
                placeholder="https://mazayacapital.com/articles"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Open Graph Image</label>
              <input
                type="url"
                className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                value={seoData.ogImage}
                onChange={e => handleInputChange('ogImage', e.target.value)}
                placeholder="/images/og/articles-page-og.jpg"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Schema Type</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-[#00C2FF] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2FF]"
                    checked={seoData.schema.enabled}
                    onChange={e => handleSchemaChange('enabled', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-300">Enable Schema Markup</span>
                </label>
                {seoData.schema.enabled && (
                  <select
                    className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={seoData.schema.type}
                    onChange={e => handleSchemaChange('type', e.target.value)}
                  >
                    <option value="CollectionPage">Collection Page</option>
                    <option value="WebPage">Web Page</option>
                    <option value="Blog">Blog</option>
                  </select>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Robots Meta</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-[#00C2FF] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2FF]"
                    checked={seoData.robots.index}
                    onChange={e => handleRobotsChange('index', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-300">Index</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-[#00C2FF] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2FF]"
                    checked={seoData.robots.follow}
                    onChange={e => handleRobotsChange('follow', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-300">Follow</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-[#00C2FF] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2FF]"
                    checked={seoData.robots.archive}
                    onChange={e => handleRobotsChange('archive', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-300">Archive</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-[#00C2FF] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2FF]"
                    checked={seoData.robots.snippet}
                    onChange={e => handleRobotsChange('snippet', e.target.checked)}
                  />
                  <span className="ml-2 text-sm text-gray-300">Snippet</span>
                </label>
              </div>
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-5 w-5 text-[#00C2FF] bg-gray-700 border-gray-600 rounded focus:ring-[#00C2FF]"
                  checked={seoData.hreflang.enabled}
                  onChange={e => setSeoData(prev => ({
                    ...prev,
                    hreflang: { ...prev.hreflang, enabled: e.target.checked }
                  }))}
                />
                <span className="ml-2 text-sm font-medium text-gray-300">Enable Hreflang</span>
              </label>
              <div className="mt-1 text-xs text-gray-400">
                <FiInfo className="inline h-3 w-3 mr-1" />
                Automatically generates hreflang tags for multilingual content
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SEO Tips */}
      <div className="mt-6 bg-blue-900/20 border border-blue-700 rounded-lg p-4">
        <div className="flex">
          <FiInfo className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-blue-200">SEO Optimization Tips</h3>
            <div className="mt-2 text-sm text-blue-100">
              <ul className="list-disc list-inside space-y-1">
                <li>Keep meta titles under 60 characters for optimal display</li>
                <li>Write compelling meta descriptions between 120-160 characters</li>
                <li>Use relevant keywords naturally in your content</li>
                <li>Ensure your Open Graph image is at least 1200x630 pixels</li>
                <li>Use descriptive, keyword-rich URLs</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 