"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Fi<PERSON>ser, FiMail, FiShield, FiSave, FiCamera, FiEdit3, FiCheckCircle, FiAlertCircle } from 'react-icons/fi';
import Image from 'next/image';
import { getMediaUrl } from '@/utils/api';
import { useToast } from '@/contexts/ToastContext';
import { createToast, toastMessages } from '@/utils/toast';

export default function AdminProfilePage() {
  const { user, isLoading, updateProfile, uploadAvatar } = useAuth();
  const { showToast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    role: ''
  });

  useEffect(() => {
    if (user) {
      setFormData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        role: user.role || ''
      });
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async () => {
    if (!updateProfile) return;
    
    setIsSaving(true);
    
    try {
      const result = await updateProfile({
        first_name: formData.first_name,
        last_name: formData.last_name
      });

      if (result.success) {
        showToast(createToast.success(result.message || toastMessages.success.profileUpdated));
        setIsEditing(false);
        // Update form data with the latest user data
        if (result.user) {
          setFormData({
            first_name: result.user.first_name || '',
            last_name: result.user.last_name || '',
            email: result.user.email || '',
            role: result.user.role || ''
          });
        }
      } else {
        showToast(createToast.error(result.message || toastMessages.error.saveFailed));
      }
    } catch (error) {
      console.error('Profile update error:', error);
      showToast(createToast.error(toastMessages.error.networkError));
    } finally {
      setIsSaving(false);
    }
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      showToast(createToast.error('Please select a valid image file (JPEG, PNG, or GIF)'));
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      showToast(createToast.error(toastMessages.error.fileTooLarge, 'Maximum file size is 5MB'));
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setAvatarPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload to server using the useAuth hook
    setIsUploadingAvatar(true);

    try {
      if (!uploadAvatar) {
        showToast(createToast.error(toastMessages.error.authRequired));
        return;
      }

      const result = await uploadAvatar(file);

      if (result.success) {
        showToast(createToast.success(result.message || toastMessages.success.avatarUpdated));
        setAvatarPreview(null); // Clear preview since the real avatar is now updated
      } else {
        showToast(createToast.error(result.message || toastMessages.error.uploadFailed));
        setAvatarPreview(null);
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      showToast(createToast.error(toastMessages.error.uploadFailed));
      setAvatarPreview(null);
    } finally {
      setIsUploadingAvatar(false);
      // Clear the file input
      event.target.value = '';
    }
  };

  const getUserInitials = () => {
    console.log('🔍 User data for initials:', {
      first_name: user?.first_name,
      last_name: user?.last_name,
      email: user?.email,
      avatar: user?.avatar
    });
    
    // Priority 1: Use first_name and last_name if both exist
    if (user?.first_name && user?.last_name) {
      const initials = `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
      console.log('✅ Using first + last name initials:', initials);
      return initials;
    }
    
    // Priority 2: Use first_name only if last_name doesn't exist
    if (user?.first_name) {
      const initials = user.first_name.substring(0, 2).toUpperCase();
      console.log('✅ Using first name initials:', initials);
      return initials;
    }
    
    // Priority 3: Use last_name only if first_name doesn't exist
    if (user?.last_name) {
      const initials = user.last_name.substring(0, 2).toUpperCase();
      console.log('✅ Using last name initials:', initials);
      return initials;
    }
    
    // Priority 4: Use email if no names exist
    if (user?.email) {
      const emailParts = user.email.split('@')[0];
      if (emailParts.length >= 2) {
        const initials = emailParts.substring(0, 2).toUpperCase();
        console.log('✅ Using email initials (2 chars):', initials);
        return initials;
      }
      const initials = emailParts[0].toUpperCase();
      console.log('✅ Using email initials (1 char):', initials);
      return initials;
    }
    
    // Fallback: Default initials
    console.log('⚠️ Using fallback initials: AD');
    return 'AD'; // Admin Default
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF]"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">No user data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Profile Settings</h1>
          <p className="text-gray-400 mt-1">Manage your account information and preferences</p>
        </div>
        <button
          onClick={() => setIsEditing(!isEditing)}
          className="flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiEdit3 className="mr-2 h-4 w-4" />
          {isEditing ? 'Cancel' : 'Edit Profile'}
        </button>
      </div>

      {/* Profile Card */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        {/* Profile Header */}
        <div className="bg-gradient-to-r from-[#00C2FF]/20 to-purple-600/20 px-6 py-8">
          <div className="flex items-center space-x-6">
            {/* Avatar */}
            <div className="relative">
              <div className="h-24 w-24 bg-gradient-to-br from-[#00C2FF] to-[#0099CC] rounded-full flex items-center justify-center overflow-hidden border-2 border-gray-600">
                {/* Always show initials as background */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white text-2xl font-bold tracking-wider drop-shadow-lg">
                    {getUserInitials()}
                  </span>
                </div>
                
                {/* Show preview image on top if exists */}
                {avatarPreview && (
                  <Image
                    src={avatarPreview}
                    alt="Avatar Preview"
                    width={96}
                    height={96}
                    className="rounded-full object-cover w-full h-full relative z-10"
                  />
                )}
                
                {/* Show user avatar on top if exists and no preview */}
                {!avatarPreview && user.avatar && (
                  <Image
                    key={user.avatar}
                    src={getMediaUrl(user.avatar)}
                    alt="User Avatar"
                    width={96}
                    height={96}
                    className="rounded-full object-cover w-full h-full relative z-10"
                    onError={(e) => {
                      // If image fails to load, hide it to show initials
                      console.error('❌ Avatar failed to load:', user.avatar ? getMediaUrl(user.avatar) : 'No avatar');
                      e.currentTarget.style.display = 'none';
                    }}
                    onLoad={() => {
                      console.log('✅ Avatar loaded successfully:', user.avatar ? getMediaUrl(user.avatar) : 'No avatar');
                    }}
                  />
                )}
                
                {/* Loading overlay */}
                {isUploadingAvatar && (
                  <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center z-20">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
              
              {/* Hidden file input */}
              <input
                type="file"
                id="avatar-upload"
                accept="image/*"
                onChange={handleAvatarUpload}
                className="hidden"
                disabled={isUploadingAvatar}
              />
              
              {/* Camera button - always visible */}
              <button 
                onClick={() => document.getElementById('avatar-upload')?.click()}
                disabled={isUploadingAvatar}
                className="absolute bottom-0 right-0 h-8 w-8 bg-gray-700 rounded-full flex items-center justify-center border-2 border-gray-800 hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed z-10"
                title="Upload new avatar"
              >
                <FiCamera className="h-4 w-4 text-gray-300" />
              </button>
            </div>

            {/* User Info */}
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-white">
                {user.first_name} {user.last_name}
              </h2>
              <p className="text-[#00C2FF] font-medium">{user.role}</p>
              <p className="text-gray-400 mt-1">{user.email}</p>
              {user.last_login && (
                <p className="text-gray-500 text-sm mt-2">
                  Last login: {new Date(user.last_login).toLocaleString()}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Profile Form */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                First Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                />
              ) : (
                <div className="flex items-center px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white">
                  <FiUser className="mr-2 h-4 w-4 text-gray-400" />
                  {user.first_name}
                </div>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Last Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                />
              ) : (
                <div className="flex items-center px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white">
                  <FiUser className="mr-2 h-4 w-4 text-gray-400" />
                  {user.last_name}
                </div>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Email Address
              </label>
              <div className="flex items-center px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white">
                <FiMail className="mr-2 h-4 w-4 text-gray-400" />
                {user.email}
              </div>
              <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
            </div>

            {/* Role */}
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Role
              </label>
              <div className="flex items-center px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white">
                <FiShield className="mr-2 h-4 w-4 text-gray-400" />
                {user.role}
              </div>
              <p className="text-xs text-gray-500 mt-1">Role is managed by administrators</p>
            </div>
          </div>

          {/* Permissions */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-400 mb-3">
              Permissions
            </label>
            <div className="flex flex-wrap gap-2">
              {user.permissions?.map((permission, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-[#00C2FF]/20 text-[#00C2FF] rounded-full text-sm border border-[#00C2FF]/30"
                >
                  {permission.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              ))}
            </div>
          </div>

          {/* Save Button */}
          {isEditing && (
            <div className="mt-6 flex justify-end">
              <button
                onClick={handleSave}
                disabled={isSaving}
                className={`flex items-center px-6 py-2 rounded-lg transition-colors ${
                  isSaving
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-[#00C2FF] text-white hover:bg-[#00C2FF]/90'
                }`}
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <FiSave className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Account Information */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Account Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">User ID:</span>
            <span className="text-white ml-2">#{user.id}</span>
          </div>
          <div>
            <span className="text-gray-400">Account Type:</span>
            <span className="text-white ml-2">Admin</span>
          </div>
          {user.last_login && (
            <div>
              <span className="text-gray-400">Last Login:</span>
              <span className="text-white ml-2">
                {new Date(user.last_login).toLocaleDateString()}
              </span>
            </div>
          )}
          <div>
            <span className="text-gray-400">Status:</span>
            <span className="text-green-400 ml-2">Active</span>
          </div>
        </div>
      </div>
    </div>
  );
} 