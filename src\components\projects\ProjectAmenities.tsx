import { forwardRef } from "react";

interface ProjectAmenitiesProps {
  project: any;
}

// Define amenities data with proper icons and descriptions
const amenitiesData = [
  {
    id: 1,
    name: "Infinity Pool",
    description: "Enjoy breathtaking views from our stunning infinity pool that seemingly merges with the horizon.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
      </svg>
    )
  },
  {
    id: 2,
    name: "State-of-the-art Gym",
    description: "Stay fit and active with our fully equipped fitness center featuring the latest exercise equipment.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    id: 3,
    name: "Sauna & Steam Room",
    description: "Relax and rejuvenate in our luxurious spa facilities, designed for ultimate relaxation.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    )
  },
  {
    id: 4,
    name: "Business Center",
    description: "Work from home in our dedicated business center with meeting rooms and high-speed internet.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
      </svg>
    )
  },
  {
    id: 5,
    name: "Indoor/Outdoor Lounges",
    description: "Socialize in beautifully designed lounge spaces featuring premium furnishings and ambiance.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
      </svg>
    )
  },
  {
    id: 6,
    name: "Landscaped Gardens",
    description: "Stroll through meticulously maintained gardens featuring exotic plants and peaceful sitting areas.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
      </svg>
    )
  },
  {
    id: 7,
    name: "BBQ Area",
    description: "Host outdoor gatherings with family and friends in our dedicated barbecue and entertainment areas.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    )
  },
  {
    id: 8,
    name: "Visitor Parking",
    description: "Dedicated secure parking for your guests ensures convenient access when entertaining.",
    icon: (
      <svg className="h-8 w-8 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
      </svg>
    )
  }
];

const ProjectAmenities = forwardRef<HTMLElement, ProjectAmenitiesProps>(({ project }, ref) => {
  return (
    <section ref={ref} id="amenities" className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">World-Class Amenities</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Discover a lifestyle of luxury with our exceptional range of facilities and services.</p>
      </div>
      
      {/* Main Amenities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {amenitiesData.map((amenity) => (
          <div key={amenity.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-300">
            <div className="w-16 h-16 rounded-full bg-[#00C2FF]/10 flex items-center justify-center mb-6">
              {amenity.icon}
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">{amenity.name}</h3>
            <p className="text-gray-600">{amenity.description}</p>
          </div>
        ))}
      </div>
      
      {/* Additional amenities if any */}
      {project.amenities && project.amenities.length > 8 && (
        <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Additional Amenities</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {project.amenities.slice(8).map((amenity: string, index: number) => (
              <div key={index} className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-[#00C2FF]/10 flex items-center justify-center me-2">
                  <svg className="h-3 w-3 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <span className="text-gray-700">{amenity}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </section>
  );
});

ProjectAmenities.displayName = "ProjectAmenities";

export default ProjectAmenities; 