import React, { useEffect, useRef } from 'react';

interface ProjectBasicInfoProps {
  project: any;
  language: 'en' | 'ar';
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  getCurrentContent: () => any;
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectBasicInfo: React.FC<ProjectBasicInfoProps> = ({ 
  project, 
  language, 
  handleInputChange,
  getCurrentContent,
  setProject
}) => {
  // References to track if slugs have been manually edited
  const slugManuallyEditedRef = useRef(false);
  const slugArManuallyEditedRef = useRef(false);

  // Helper function to generate slug from title
  const generateSlug = (title: string, isArabic: boolean = false) => {
    if (!title) return '';

    if (isArabic) {
      // For Arabic, we need special handling
      // Replace spaces with hyphens and remove special characters
      return title
        .trim()
        .replace(/\s+/g, '-')
        .replace(/[^\u0600-\u06FF\u0750-\u077F\-]/g, ''); // Keep Arabic Unicode range
    } else {
      // For English
      return title
        .trim()
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '') // Remove all non-word chars
        .replace(/\-\-+/g, '-'); // Replace multiple dashes with single dash
    }
  };

  // Auto-generate English slug when English title changes
  useEffect(() => {
    if (project.title && !slugManuallyEditedRef.current) {
      const newSlug = generateSlug(project.title);
      if (newSlug !== project.slug) {
        setProject((prevProject: any) => ({
          ...prevProject,
          slug: newSlug
        }));
      }
    }
  }, [project.title, setProject]);

  // Auto-generate Arabic slug when Arabic title changes
  useEffect(() => {
    if (project.titleAr && !slugArManuallyEditedRef.current) {
      const newSlugAr = generateSlug(project.titleAr, true);
      if (newSlugAr !== project.slugAr) {
        setProject((prevProject: any) => ({
          ...prevProject,
          slugAr: newSlugAr
        }));
      }
    }
  }, [project.titleAr, setProject]);

  // Handle manual slug changes
  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === 'slug') {
      slugManuallyEditedRef.current = true;
    } else if (name === 'slugAr') {
      slugArManuallyEditedRef.current = true;
    }
    handleInputChange(e);
  };

  // Category options with translations
  const categoryOptions = [
    { value: "Residential", en: "Residential", ar: "سكني" },
    { value: "Commercial", en: "Commercial", ar: "تجاري" },
    { value: "Mixed-Use", en: "Mixed-Use", ar: "متعدد الاستخدامات" }
  ];

  // Status options with translations
  const statusOptions = [
    { value: "Planned", en: "Planned", ar: "مخطط" },
    { value: "In Progress", en: "In Progress", ar: "قيد الإنشاء" },
    { value: "Completed", en: "Completed", ar: "مكتمل" }
  ];

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Project Basic Information' : 'معلومات المشروع الأساسية'}
      </h2>
      
      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
        <div className="sm:col-span-3">
          <label htmlFor="title" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Project Title *' : 'عنوان المشروع *'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="title"
              id="title"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.title : project.titleAr}
              onChange={handleInputChange}
              required
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>
        
        <div className="sm:col-span-3">
          <label htmlFor={language === 'en' ? 'slug' : 'slugAr'} className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'URL Slug *' : 'اسم-الرابط بالعربية *'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name={language === 'en' ? 'slug' : 'slugAr'}
              id={language === 'en' ? 'slug' : 'slugAr'}
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.slug : project.slugAr}
              onChange={handleSlugChange}
              required
              dir="ltr" // Always LTR for URL slugs
            />
          </div>
          <p className="mt-1 text-xs text-gray-400">
            {language === 'en' 
              ? `Used in the URL: /projects/${project.slug || 'your-project-slug'}`
              : `يستخدم في الرابط العربي: /ar/projects/${project.slugAr || 'اسم-المشروع'}`}
          </p>
          <p className="mt-1 text-xs text-[#00C2FF]">
            {language === 'en' 
              ? 'Auto-generated from title. Edit manually if needed.'
              : 'تم إنشاؤه تلقائيًا من العنوان. قم بالتعديل يدويًا إذا لزم الأمر.'}
          </p>
        </div>
        
        <div className="sm:col-span-3">
          <label htmlFor="location" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Location *' : 'الموقع *'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="location"
              id="location"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.location : project.locationAr || ''}
              onChange={handleInputChange}
              required
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>
        
        <div className="sm:col-span-3">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-300">
                {language === 'en' ? 'Category *' : 'الفئة *'}
              </label>
              <div className="mt-1 relative">
                <select
                  id="category"
                  name="category"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3 pr-10 appearance-none"
                  value={getCurrentContent().category}
                  onChange={handleInputChange}
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                >
                  {categoryOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {language === 'en' ? option.en : option.ar}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-300">
                {language === 'en' ? 'Status *' : 'الحالة *'}
              </label>
              <div className="mt-1 relative">
                <select
                  id="status"
                  name="status"
                  className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3 pr-10 appearance-none"
                  value={getCurrentContent().status}
                  onChange={handleInputChange}
                  required
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                >
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {language === 'en' ? option.en : option.ar}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="sm:col-span-6">
          <label htmlFor="description" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Short Description *' : 'وصف مختصر *'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="description"
              id="description"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={getCurrentContent().description}
              onChange={handleInputChange}
              required
              placeholder={language === 'en' ? "Brief description of the project (100 chars max)" : "وصف موجز للمشروع (100 حرف كحد أقصى)"}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
          <p className="mt-1 text-xs text-gray-400">
            {language === 'en' ? 'Brief description used in listings (max 100 chars)' : 'وصف موجز يستخدم في القوائم (100 حرف كحد أقصى)'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProjectBasicInfo; 