"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

const AnimatedBackground = () => {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Generate floating elements with different properties
  const generateFloatingElements = (count: number) => {
    const elements = [];
    
    for (let i = 0; i < count; i++) {
      const size = Math.floor(Math.random() * 140) + 60; // Random size between 60-200px
      const xPos = Math.floor(Math.random() * 100); // Random x position 0-100%
      const yPos = Math.floor(Math.random() * 100); // Random y position 0-100%
      const duration = Math.floor(Math.random() * 20) + 15; // Animation duration 15-35s
      const delay = Math.floor(Math.random() * 10); // Random delay 0-10s
      
      elements.push(
        <motion.div
          key={i}
          className="absolute rounded-full bg-gradient-to-r from-[rgb(var(--color-primary))/5] to-[rgb(var(--color-secondary))/10] backdrop-blur-xl"
          style={{
            width: size,
            height: size,
            left: `${xPos}%`,
            top: `${yPos}%`,
          }}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: [0.3, 0.5, 0.3],
            scale: [0.8, 1, 0.8],
            x: [0, 30, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: duration,
            repeat: Infinity,
            delay: delay,
            ease: "easeInOut",
          }}
        />
      );
    }
    
    return elements;
  };
  
  // Generate light beams
  const generateLightBeams = (count: number) => {
    const beams = [];
    
    for (let i = 0; i < count; i++) {
      const width = Math.floor(Math.random() * 3) + 1; // Width between 1-4px
      const length = Math.floor(Math.random() * 40) + 10; // Length between 10-50%
      const xPos = Math.floor(Math.random() * 90) + 5; // X position between 5-95%
      const rotate = Math.floor(Math.random() * 360); // Rotation 0-360 degrees
      const duration = Math.floor(Math.random() * 8) + 5; // Animation duration 5-13s
      const delay = Math.floor(Math.random() * 3); // Delay 0-3s
      
      beams.push(
        <motion.div
          key={`beam-${i}`}
          className="absolute bg-[rgb(var(--color-primary))]/10"
          style={{
            width: `${width}px`,
            height: `${length}%`,
            left: `${xPos}%`,
            transform: `rotate(${rotate}deg)`,
            transformOrigin: "center",
          }}
          initial={{ opacity: 0 }}
          animate={{ 
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: duration,
            repeat: Infinity,
            delay: delay,
            ease: "easeInOut",
          }}
        />
      );
    }
    
    return beams;
  };
  
  // Generate pulsing dots
  const generatePulsingDots = (count: number) => {
    const dots = [];
    
    for (let i = 0; i < count; i++) {
      const size = Math.floor(Math.random() * 6) + 2; // Size between 2-8px
      const xPos = Math.floor(Math.random() * 90) + 5; // X position between 5-95%
      const yPos = Math.floor(Math.random() * 90) + 5; // Y position between 5-95%
      const duration = Math.floor(Math.random() * 3) + 1; // Animation duration 1-4s
      const delay = Math.random() * 2; // Delay 0-2s
      const isPrimary = Math.random() > 0.5; // Randomly choose primary or secondary color
      
      dots.push(
        <motion.div
          key={`dot-${i}`}
          className={`absolute ${isPrimary ? 'bg-[rgb(var(--color-primary))]' : 'bg-[rgb(var(--color-secondary))]'} rounded-full`}
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${xPos}%`,
            top: `${yPos}%`,
          }}
          animate={{ 
            opacity: [0.2, 0.7, 0.2],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: duration,
            repeat: Infinity,
            delay: delay,
            ease: "easeInOut",
          }}
        />
      );
    }
    
    return dots;
  };
  
  // Generate perspective grid
  const generateGrid = () => {
    const gridLines = [];
    const lineCount = 6; // Number of lines in each direction
    
    // Horizontal lines
    for (let i = 1; i <= lineCount; i++) {
      const yPos = i * (100 / (lineCount + 1)); // Distribute evenly
      
      gridLines.push(
        <motion.div
          key={`h-${i}`}
          className="absolute start-0 end-0 h-[1px] bg-[rgb(var(--color-primary))]/10"
          style={{
            top: `${yPos}%`,
          }}
          initial={{ scaleX: 0, opacity: 0 }}
          animate={{ 
            scaleX: 1, 
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            delay: i * 0.2,
            ease: "easeInOut",
          }}
        />
      );
    }
    
    // Vertical lines
    for (let i = 1; i <= lineCount; i++) {
      const xPos = i * (100 / (lineCount + 1)); // Distribute evenly
      
      gridLines.push(
        <motion.div
          key={`v-${i}`}
          className="absolute top-0 bottom-0 w-[1px] bg-[rgb(var(--color-secondary))]/10"
          style={{
            left: `${xPos}%`,
          }}
          initial={{ scaleY: 0, opacity: 0 }}
          animate={{ 
            scaleY: 1, 
            opacity: [0.1, 0.2, 0.1],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            delay: i * 0.3,
            ease: "easeInOut",
          }}
        />
      );
    }
    
    return gridLines;
  };
  
  // Generate spotlight effect
  const Spotlight = () => (
    <motion.div 
      className="absolute w-full h-full bg-gradient-radial from-[rgb(var(--color-primary))]/5 via-transparent to-transparent"
      style={{
        width: '80%',
        height: '80%',
        left: '10%',
        top: '10%',
      }}
      animate={{
        opacity: [0.3, 0.5, 0.3],
      }}
      transition={{
        duration: 10,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    />
  );
  
  if (!mounted) return null;
  
  return (
    <div className="absolute inset-0 -z-10 overflow-hidden pointer-events-none">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-radial from-[rgb(var(--color-primary))]/5 to-transparent opacity-20" />
      
      {/* Spotlight effect */}
      <Spotlight />
      
      {/* Grid lines */}
      <div className="absolute inset-0 opacity-30">
        {generateGrid()}
      </div>
      
      {/* Floating gradient elements */}
      {generateFloatingElements(6)}
      
      {/* Light beams */}
      {generateLightBeams(12)}
      
      {/* Pulsing dots */}
      {generatePulsingDots(30)}
      
      {/* Bottom glow */}
      <div className="absolute bottom-0 start-0 end-0 h-40 bg-gradient-to-t from-[rgb(var(--color-primary))]/10 to-transparent" />
      
      {/* Top blur layer */}
      <div className="absolute inset-0 backdrop-blur-[100px] opacity-10" />
      
      {/* Digital circuit pattern */}
      <svg className="absolute inset-0 w-full h-full opacity-5" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="circuit" patternUnits="userSpaceOnUse" width="100" height="100">
            <path d="M0 0h100v100H0z" fill="none" />
            <path d="M30 10v30m0 0h40m0 0v30m0 0h-40m0 0v30" stroke="rgb(var(--color-primary))" strokeWidth="1" fill="none" />
            <circle cx="30" cy="40" r="3" fill="rgb(var(--color-primary))" />
            <circle cx="70" cy="70" r="3" fill="rgb(var(--color-secondary))" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#circuit)" />
      </svg>
    </div>
  );
};

export default AnimatedBackground; 