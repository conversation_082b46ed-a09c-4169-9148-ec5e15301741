import { forwardRef, useRef, useEffect, useState } from "react";

interface ProjectLocationProps {
  project: any;
}

const ProjectLocation = forwardRef<HTMLElement, ProjectLocationProps>(({ project }, ref) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isScrollable, setIsScrollable] = useState(false);
  const [hasScrolled, setHasScrolled] = useState(false);

  // Check if the table is scrollable
  useEffect(() => {
    const checkScroll = () => {
      const container = scrollContainerRef.current;
      if (container) {
        setIsScrollable(container.scrollWidth > container.clientWidth);
      }
    };

    // Check on initial load and window resize
    checkScroll();
    window.addEventListener('resize', checkScroll);
    
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  // Add scroll listener to hide indicator after user scrolls
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      if (!hasScrolled) {
        setHasScrolled(true);
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasScrolled]);

  return (
    <section ref={ref} id="location" className="mb-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Prime Location</h2>
        <div className="w-20 h-1 bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">Strategically located in the heart of Dubai with easy access to key landmarks and amenities.</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        {/* Map Placeholder */}
        <div className="bg-gray-200 rounded-xl overflow-hidden h-[400px] flex items-center justify-center">
          <p className="text-gray-500">Interactive Map would be integrated here</p>
        </div>

        <div>
          <h3 className="text-2xl font-bold text-gray-900 mb-6">A Strategic Location in Downtown Dubai</h3>
          <p className="text-gray-700 mb-6">
            Located in the prestigious Downtown Dubai district, {project.title} offers residents unparalleled accessibility to the city's finest attractions and amenities. The strategic location ensures that residents are just minutes away from business hubs, shopping centers, fine dining restaurants, and entertainment venues.
          </p>
          
          <h4 className="text-xl font-semibold text-gray-900 mb-4">Nearby Attractions</h4>
          <div className="relative mb-8">
            <div 
              ref={scrollContainerRef}
              className="overflow-x-auto"
              style={{ scrollbarWidth: 'thin' }}
            >
              <table className="w-full">
                <tbody className="divide-y divide-gray-100">
                  {project.locationDetails.nearbyAttractions.map((attraction: any, index: number) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-3 pe-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full bg-[#00C2FF]/10 flex items-center justify-center me-4 flex-shrink-0">
                            <svg className="h-5 w-5 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </div>
                          <span className="text-gray-900 font-medium whitespace-nowrap">{attraction.name}</span>
                        </div>
                      </td>
                      <td className="py-3 ps-4 text-end whitespace-nowrap">
                        <span className="text-[#00C2FF] font-medium">{attraction.distance}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Scroll indicator - only shows when scrollable and user hasn't scrolled yet */}
            {isScrollable && !hasScrolled && (
              <div className="absolute top-0 end-0 bottom-0 pointer-events-none flex items-center">
                <div className="h-full w-12 bg-gradient-to-s from-[#f9fafb] to-transparent"></div>
                <div className="me-2 w-6 h-6 rounded-full bg-[#00C2FF]/10 flex items-center justify-center animate-pulse">
                  <svg className="h-4 w-4 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            )}
          </div>
          
          <div className="bg-[#00C2FF]/5 rounded-xl p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">Transportation</h4>
            <ul className="space-y-2 text-gray-700">
              <li className="flex items-start">
                <svg className="h-5 w-5 me-2 text-[#00C2FF] mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Easy access to Sheikh Zayed Road and Al Khail Road
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 me-2 text-[#00C2FF] mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                5-minute walk to the Dubai Metro station
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 me-2 text-[#00C2FF] mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Regular bus services to key destinations
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 me-2 text-[#00C2FF] mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                20 minutes from Dubai International Airport
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
});

ProjectLocation.displayName = "ProjectLocation";

export default ProjectLocation; 