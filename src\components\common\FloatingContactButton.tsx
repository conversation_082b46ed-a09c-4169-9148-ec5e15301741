import React from 'react';
import ContactOptionButton from './ContactOptionButton';

interface FloatingContactButtonProps {
  isOpen: boolean;
  toggleOpen: () => void;
  projectTitle: string;
  projectSlug: string;
}

export default function FloatingContactButton({
  isOpen,
  toggleOpen,
  projectTitle,
  projectSlug
}: FloatingContactButtonProps) {
  return (
    <div className="fixed bottom-6 end-6 md:hidden z-40 flex items-end justify-end pointer-events-none">
      <div className="relative flex flex-col items-end pointer-events-auto">
        {/* Contact options */}
        <div
          className={`flex flex-col items-end gap-3 mb-2 transition-all duration-500 ease-in-out origin-bottom-end
            ${isOpen ? "opacity-100 scale-100 translate-y-0 pointer-events-auto" : "opacity-0 scale-90 translate-y-4 pointer-events-none"}
          `}
          style={{
            maxHeight: "calc(100vh - 120px)",
            overflowY: "auto",
          }}
        >
          {/* Email */}
          <ContactOptionButton
            href={`mailto:<EMAIL>?subject=Inquiry about ${projectTitle}`}
            ariaLabel="Send email inquiry"
            icon={
              <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            }
          />
          
          {/* Phone */}
          <ContactOptionButton
            href="tel:+971123456789"
            ariaLabel="Call us"
            icon={
              <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            }
          />
          
          {/* WhatsApp */}
          <ContactOptionButton
            href="https://wa.me/971123456789"
            target="_blank"
            rel="noopener noreferrer"
            ariaLabel="Contact us on WhatsApp"
            icon={
              <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            }
          />
          
          {/* Contact Form */}
          <ContactOptionButton
            href={`/contact?project=${projectSlug}`}
            ariaLabel="Contact us through form"
            icon={
              <svg className="h-6 w-6 text-[#00C2FF]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
            }
          />
        </div>
        
        {/* Main floating button */}
        <button
          onClick={toggleOpen}
          className={`w-14 h-14 rounded-full bg-gradient-to-r from-[#00C2FF] to-[#7B61FF] flex items-center justify-center shadow-lg hover:shadow-xl transition-transform duration-500 ease-in-out ${
            isOpen ? 'rotate-45' : ''
          }`}
          aria-label="Open contact options"
        >
          <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
    </div>
  );
} 