"use client";

import { Inter } from "next/font/google";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { FiHome, FiGrid, FiFileText, FiMail, FiSettings, FiLogOut, FiPlus, FiInfo, FiUsers, FiHeart, FiAward, FiGlobe, FiSun, FiClock, FiEye, FiChevronDown, FiChevronRight, FiSearch, FiMonitor, FiStar, FiTrendingUp, FiMessageSquare, FiLayout, FiTag, FiMapPin, FiPhone, FiShield, FiKey, FiUser } from "react-icons/fi";
import { getMediaUrl } from "@/utils/api";
import { ToastProvider } from "@/contexts/ToastContext";

// Configure the Inter font
const inter = Inter({ 
  subsets: ["latin"],
  display: 'swap',
});

function AdminLayoutContent({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { user, isLoading, isAuthenticated, logout, forceUpdate } = useAuth();
  const [isAboutUsExpanded, setIsAboutUsExpanded] = useState(false);
  const [isHomePageExpanded, setIsHomePageExpanded] = useState(false);
  const [isProjectsPageExpanded, setIsProjectsPageExpanded] = useState(false);
  const [isArticlesPageExpanded, setIsArticlesPageExpanded] = useState(false);
  const [isContactPageExpanded, setIsContactPageExpanded] = useState(false);
  const [isAuthorizationExpanded, setIsAuthorizationExpanded] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle authentication redirect (moved to useEffect to prevent render-time router calls)
  useEffect(() => {
    console.log('Layout auth check:', { isMounted, isLoading, isAuthenticated, user: user?.email });
    
    if (isMounted && !isLoading && !isAuthenticated) {
      const currentPath = window.location.pathname;
      const isAuthPage = currentPath.includes('/login') || currentPath.includes('/forgot-password');
      
      if (!isAuthPage) {
        console.log('Redirecting to login from:', currentPath);
        router.push('/en/login');
      }
    }
  }, [isMounted, isLoading, isAuthenticated, router, user, forceUpdate]);

  // Debug user avatar changes
  useEffect(() => {
    if (user) {
      console.log('🔄 Admin Layout - User state updated:', {
        email: user.email,
        avatar: user.avatar,
        forceUpdate
      });
    }
  }, [user, forceUpdate]);

  // Don't render layout for auth pages - just return children directly
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';
  const isAuthPage = currentPath.includes('/login') || currentPath.includes('/forgot-password');
  
  if (isAuthPage) {
    return <div className={inter.className} suppressHydrationWarning>{children}</div>;
  }

  // Always show loading until mounted to prevent hydration mismatch
  if (!isMounted) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center ${inter.className}`} suppressHydrationWarning>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // Only check authentication after mounting to prevent hydration mismatch
  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center ${inter.className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Show loading while redirecting to login (only after mounting and auth check)
  if (!isAuthenticated) {
    return (
      <div className={`min-h-screen bg-gray-900 flex items-center justify-center ${inter.className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#00C2FF] mx-auto mb-4"></div>
          <p className="text-gray-400">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  const handleLogout = async () => {
    if (confirm('Are you sure you want to logout?')) {
      await logout();
      router.push('/en/login');
    }
  };

  const getUserDisplayName = () => {
    if (user?.first_name && user?.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user?.email || 'Admin User';
  };

  const getUserInitials = () => {
    // Priority 1: Use first_name and last_name if both exist
    if (user?.first_name && user?.last_name) {
      return `${user.first_name[0]}${user.last_name[0]}`.toUpperCase();
    }
    
    // Priority 2: Use first_name only if last_name doesn't exist
    if (user?.first_name) {
      return user.first_name.substring(0, 2).toUpperCase();
    }
    
    // Priority 3: Use last_name only if first_name doesn't exist
    if (user?.last_name) {
      return user.last_name.substring(0, 2).toUpperCase();
    }
    
    // Priority 4: Use email if no names exist
    if (user?.email) {
      const emailParts = user.email.split('@')[0];
      if (emailParts.length >= 2) {
        return emailParts.substring(0, 2).toUpperCase();
      }
      return emailParts[0].toUpperCase();
    }
    
    // Fallback: Default initials
    return 'AD'; // Admin Default
  };

  return (
    <div className={`flex h-screen bg-gray-900 text-white ${inter.className}`}>
      {/* Sidebar */}
      <aside className="w-64 bg-gray-800 shadow-md flex flex-col h-screen">
        <div className="p-4 border-b border-gray-700 flex-shrink-0 flex justify-center items-center gap-2">
          <Link href="/en/admin" className="flex items-center gap-2 justify-center">
            <div className="h-10 w-10 relative">
              <Image
                src="/images/logo/mazaya-logo-mark.svg"
                alt="Mazaya Capital Logo"
                className="w-full h-full object-cover"
                width={60}
                height={60}
                priority
              />
            </div>
            <span className="text-lg font-semibold text-white mt-3">Website Panel</span>
          </Link>
        </div>
        
        {/* User Info */}
        {user && (
          <div className="p-4 border-b border-gray-700">
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <div className="!h-8 !w-8 min-w-8 bg-gradient-to-br from-[#00C2FF] to-[#0099CC] rounded-full flex items-center justify-center border border-gray-600 relative overflow-hidden">
                  {/* Always show initials as background */}
                  <span className="text-white text-xs font-bold tracking-wide absolute inset-0 flex items-center justify-center">
                    {getUserInitials()}
                  </span>
                  
                  {/* Show avatar image on top if exists */}
                  {user.avatar && (
                    <Image
                      key={user.avatar}
                      src={getMediaUrl(user.avatar)}
                      alt="User Avatar"
                      width={32}
                      height={32}
                      className="rounded-full object-cover w-full h-full relative z-10"
                      onError={(e) => {
                        // If image fails to load, hide it to show initials
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  )}
                </div>
                <div className="flex-1 text-left pe-4">
                  <p className="text-sm font-medium text-white truncate">
                    {getUserDisplayName().length > 20 
                      ? getUserDisplayName().substring(0, 20) + '...' 
                      : getUserDisplayName()
                    }
                  </p>
                  <p className="text-xs text-gray-400">{user.role}</p>
                </div>
                <FiChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${showUserMenu ? 'rotate-180' : ''}`} />
              </button>

              {/* User Dropdown Menu */}
              {showUserMenu && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-gray-700 rounded-lg border border-gray-600 shadow-lg z-50">
                  <div className="p-2">
                    <button
                      onClick={() => {
                        setShowUserMenu(false);
                        router.push('/en/admin/profile');
                      }}
                      className="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-600 rounded-md transition-colors"
                    >
                      <FiUser className="mr-2 h-4 w-4" />
                      Profile Settings
                    </button>
                    <button
                      onClick={() => {
                        setShowUserMenu(false);
                        handleLogout();
                      }}
                      className="w-full flex items-center px-3 py-2 text-sm text-gray-300 hover:text-red-400 hover:bg-gray-600 rounded-md transition-colors"
                    >
                      <FiLogOut className="mr-2 h-4 w-4" />
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        <nav className="flex-1 overflow-y-auto mt-6 px-4 pb-6 admin-scrollbar">
          <div className="space-y-2">
            <p className="px-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Main
            </p>
            <Link href="/en/admin" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
              <FiHome className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
              Dashboard
            </Link>
          
            <Link href="/en/admin/inquiries" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
              <FiMail className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
              Inquiries
            </Link>

            <p className="mt-6 px-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Pages
            </p>

            <div className="space-y-1">
              <button
                onClick={() => setIsHomePageExpanded(!isHomePageExpanded)}
                className="group flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center">
                  <FiMonitor className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  Home Page
                </div>
                {isHomePageExpanded ? (
                  <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                ) : (
                  <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                )}
              </button>
              
              {/* Collapsible Home Page Subsections */}
              <div className={`ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${
                isHomePageExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <Link href="/admin/home-page/hero" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiEye className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Hero
                </Link>
                <Link href="/admin/home-page/about-us" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiInfo className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  About Us
                </Link>
                <Link href="/admin/home-page/featured-projects" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiStar className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Featured Projects
                </Link>
                <Link href="/admin/home-page/achievements" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiTrendingUp className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Statistics
                </Link>
                <Link href="/admin/home-page/latest-articles" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiFileText className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Latest Articles
                </Link>
                <Link href="/admin/home-page/testimonials" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiMessageSquare className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Testimonials
                </Link>
                <Link href="/admin/home-page/seo" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiSearch className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  SEO
                </Link>
              </div>
            </div>

            <div className="space-y-1">
              <button
                onClick={() => setIsAboutUsExpanded(!isAboutUsExpanded)}
                className="group flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center">
                  <FiInfo className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  About Us
                </div>
                {isAboutUsExpanded ? (
                  <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                ) : (
                  <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                )}
              </button>
              
              {/* Collapsible About Us Subsections */}
              <div className={`ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${
                isAboutUsExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <Link href="/admin/about-us/about-hero" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiEye className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  About Hero
                </Link>
                <Link href="/admin/about-us/company-history" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiClock className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Company History
                </Link>
                <Link href="/admin/about-us/mission-vision" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiSun className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Mission & Vision
                </Link>
                <Link href="/admin/about-us/values" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiHeart className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Values
                </Link>
                <Link href="/admin/about-us/team" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiUsers className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Team
                </Link>
                <Link href="/admin/about-us/achievements" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiAward className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Achievements
                </Link>
                <Link href="/admin/about-us/partners" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiGlobe className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Partners
                </Link>
                <Link href="/admin/about-us/seo" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiSearch className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  SEO Settings
                </Link>
              </div>
            </div>

            <div className="space-y-1">
              <button
                onClick={() => setIsProjectsPageExpanded(!isProjectsPageExpanded)}
                className="group flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center">
                  <FiGrid className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  Projects Page
                </div>
                {isProjectsPageExpanded ? (
                  <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                ) : (
                  <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                )}
              </button>
              
              {/* Collapsible Projects Page Subsections */}
              <div className={`ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${
                isProjectsPageExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <Link href="/admin/projects-page/project-hero" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiEye className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Project Hero
                </Link>
                <Link href="/admin/projects-page/categories" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiTag className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Projects Categories
                </Link>
                <Link href="/admin/projects-page/projects" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiGrid className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Projects List
                </Link>
                <Link href="/admin/projects-page/seo" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiSearch className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  SEO
                </Link>
              </div>
            </div>

            <div className="space-y-1">
              <button
                onClick={() => setIsArticlesPageExpanded(!isArticlesPageExpanded)}
                className="group flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center">
                  <FiFileText className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  Articles Page
                </div>
                {isArticlesPageExpanded ? (
                  <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                ) : (
                  <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                )}
              </button>
              
              {/* Collapsible Articles Page Subsections */}
              <div className={`ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${
                isArticlesPageExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <Link href="/admin/articles-page/articles-hero" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiEye className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Articles Hero
                </Link>
                <Link href="/admin/articles-page/categories" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiTag className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Articles Categories
                </Link>
                <Link href="/admin/articles-page/articles" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiFileText className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  Articles List
                </Link>
                <Link href="/admin/articles-page/seo" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiSearch className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  SEO
                </Link>
              </div>
            </div>

            <div className="space-y-1">
              <button
                onClick={() => setIsContactPageExpanded(!isContactPageExpanded)}
                className="group flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center">
                  <FiPhone className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  Contact Us Page
                </div>
                {isContactPageExpanded ? (
                  <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                ) : (
                  <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                )}
              </button>
              
              {/* Collapsible Contact Page Subsections */}
              <div className={`ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${
                isContactPageExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <Link href="/admin/contact-page/contact-hero" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiEye className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Contact Hero
                </Link>
                <Link href="/admin/contact-page/contact-body" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiMessageSquare className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Contact Body
                </Link>
                <Link href="/admin/contact-page/our-location" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiMapPin className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Our Location
                </Link>
                <Link href="/admin/contact-page/seo" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiSearch className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  SEO
                </Link>
              </div>
            </div>
            
            <p className="mt-6 px-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              Settings
            </p>
            <Link href="/admin/settings" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
              <FiSettings className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
              Settings
            </Link>
            <Link href="/admin/users" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
              <FiUsers className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
              Users
            </Link>

            {/* Authorization Dropdown */}
            <div className="space-y-1">
              <button
                onClick={() => setIsAuthorizationExpanded(!isAuthorizationExpanded)}
                className="group flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center">
                  <FiShield className="mr-3 h-5 w-5 text-gray-500 group-hover:text-[#00C2FF]" />
                  Authorization
                </div>
                {isAuthorizationExpanded ? (
                  <FiChevronDown className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                ) : (
                  <FiChevronRight className="h-4 w-4 text-gray-500 group-hover:text-[#00C2FF] transition-transform" />
                )}
              </button>
              
              {/* Collapsible Authorization Subsections */}
              <div className={`ml-8 space-y-1 overflow-hidden transition-all duration-300 ease-in-out ${
                isAuthorizationExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              }`}>
                <Link href="/admin/authorization/roles" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiUsers className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Roles
                </Link>
                <Link href="/admin/authorization/permissions" className="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-300 hover:text-[#00C2FF] hover:bg-gray-700">
                  <FiKey className="mr-2 h-4 w-4 text-gray-500 group-hover:text-[#00C2FF]" />
                  Role Permissions
                </Link>
              </div>
            </div>
          </div>
        </nav>
      </aside>
      
      {/* Main Content */}
      <main className="flex-1 overflow-auto bg-gray-900">
        <div className="py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return <ToastProvider><AdminLayoutContent>{children}</AdminLayoutContent></ToastProvider>;
}