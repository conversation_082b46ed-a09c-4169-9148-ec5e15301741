"use client";

import React, { useState, useEffect, useRef } from 'react';
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiMove, FiClock, FiImage, FiUpload, FiSearch, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import * as FaIcons from 'react-icons/fa';
import * as FaIconsSolid from 'react-icons/fa6';
import * as BsIcons from 'react-icons/bs';
import * as RiIcons from 'react-icons/ri';
import * as GiIcons from 'react-icons/gi';
import * as TbIcons from 'react-icons/tb';
import * as MdIcons from 'react-icons/md';
import * as HiIcons from 'react-icons/hi';
import * as AiIcons from 'react-icons/ai';
import * as IoIcons from 'react-icons/io';
import * as Io5Icons from 'react-icons/io5';
import * as PiIcons from 'react-icons/pi';
import * as FcIcons from 'react-icons/fc';

interface HistoryItem {
  id: string;
  year: number;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  icon: string;
  image: string;
  color: string;
  order: number;
}

interface CompanyHistorySection {
  header: {
    badge: {
      en: string;
      ar: string;
    };
    title: {
      en: string;
      ar: string;
    };
    subtitle: {
      en: string;
      ar: string;
    };
    description: {
      en: string;
      ar: string;
    };
  };
  timeline: HistoryItem[];
}

const colorOptions = [
  // Cool Gradients
  { name: 'Ocean Blue', value: 'from-blue-500 to-cyan-500', category: 'Cool' },
  { name: 'Deep Ocean', value: 'from-blue-600 to-blue-800', category: 'Cool' },
  { name: 'Arctic Ice', value: 'from-cyan-400 to-blue-600', category: 'Cool' },
  { name: 'Emerald Sea', value: 'from-emerald-500 to-teal-500', category: 'Cool' },
  { name: 'Forest Green', value: 'from-green-500 to-emerald-500', category: 'Cool' },
  { name: 'Mint Fresh', value: 'from-green-400 to-cyan-400', category: 'Cool' },
  { name: 'Teal Wave', value: 'from-teal-400 to-teal-600', category: 'Cool' },
  { name: 'Winter Sky', value: 'from-indigo-500 to-blue-500', category: 'Cool' },
  
  // Warm Gradients
  { name: 'Sunset Orange', value: 'from-orange-500 to-amber-500', category: 'Warm' },
  { name: 'Fire Glow', value: 'from-red-500 to-orange-500', category: 'Warm' },
  { name: 'Golden Hour', value: 'from-yellow-400 to-orange-500', category: 'Warm' },
  { name: 'Amber Light', value: 'from-amber-400 to-yellow-500', category: 'Warm' },
  { name: 'Coral Reef', value: 'from-orange-400 to-pink-500', category: 'Warm' },
  { name: 'Warm Embrace', value: 'from-red-400 to-orange-600', category: 'Warm' },
  
  // Vibrant Gradients
  { name: 'Rose Garden', value: 'from-rose-500 to-pink-500', category: 'Vibrant' },
  { name: 'Purple Dream', value: 'from-purple-500 to-violet-500', category: 'Vibrant' },
  { name: 'Magenta Blast', value: 'from-purple-600 to-pink-600', category: 'Vibrant' },
  { name: 'Royal Purple', value: 'from-indigo-600 to-purple-600', category: 'Vibrant' },
  { name: 'Pink Sunset', value: 'from-pink-400 to-rose-600', category: 'Vibrant' },
  { name: 'Neon Glow', value: 'from-purple-400 to-pink-400', category: 'Vibrant' },
  
  // Modern Gradients
  { name: 'Dark Elegance', value: 'from-gray-700 to-gray-900', category: 'Modern' },
  { name: 'Silver Shine', value: 'from-gray-400 to-gray-600', category: 'Modern' },
  { name: 'Carbon Fiber', value: 'from-slate-600 to-slate-800', category: 'Modern' },
  { name: 'Steel Blue', value: 'from-slate-500 to-blue-600', category: 'Modern' },
  { name: 'Urban Night', value: 'from-gray-600 to-indigo-700', category: 'Modern' },
  
  // Rainbow Gradients
  { name: 'Rainbow Spectrum', value: 'from-red-500 via-yellow-500 to-blue-500', category: 'Rainbow' },
  { name: 'Pride Colors', value: 'from-purple-500 via-pink-500 to-red-500', category: 'Rainbow' },
  { name: 'Tropical Paradise', value: 'from-green-400 via-blue-500 to-purple-600', category: 'Rainbow' },
  { name: 'Aurora Borealis', value: 'from-cyan-400 via-purple-500 to-pink-500', category: 'Rainbow' },
];

export default function CompanyHistoryManagementPage() {
  // Add custom CSS for hiding scrollbars
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Initial data based on current CompanyHistory component
  const [companyHistoryData, setCompanyHistoryData] = useState<CompanyHistorySection>({
    header: {
      badge: {
        en: "Company Timeline",
        ar: "الجدول الزمني للشركة"
      },
      title: {
        en: "Our Journey of",
        ar: "رحلتنا في"
      },
      subtitle: {
        en: "Growth",
        ar: "النمو"
      },
      description: {
        en: "Discover the key milestones that have shaped Mazaya Capital into a leading real estate development company.",
        ar: "اكتشف المعالم الرئيسية التي شكلت مزايا كابيتال إلى شركة تطوير عقاري رائدة."
      }
    },
    timeline: [
      {
        id: "foundation-2005",
        year: 2005,
        title: {
          en: "Foundation",
          ar: "التأسيس"
        },
        description: {
          en: "Mazaya Capital was founded with a vision to transform the real estate landscape through innovative development projects.",
          ar: "تأسست مزايا كابيتال برؤية لتحويل المشهد العقاري من خلال مشاريع التطوير المبتكرة."
        },
        icon: "FaBuilding",
        image: "https://via.placeholder.com/800x450/1f2937/00C2FF?text=Foundation+2005",
        color: "from-blue-500 to-cyan-500",
        order: 1
      },
      {
        id: "first-project-2008",
        year: 2008,
        title: {
          en: "First Major Project",
          ar: "أول مشروع كبير"
        },
        description: {
          en: "Completed our first major residential development, establishing our reputation for quality and reliability.",
          ar: "أكملنا أول تطوير سكني كبير لنا، مما رسخ سمعتنا في الجودة والموثوقية."
        },
        icon: "FaHome",
        image: "https://via.placeholder.com/800x450/374151/00C2FF?text=First+Project+2008",
        color: "from-emerald-500 to-teal-500",
        order: 2
      },
      {
        id: "commercial-expansion-2012",
        year: 2012,
        title: {
          en: "Expansion into Commercial",
          ar: "التوسع في التجاري"
        },
        description: {
          en: "Expanded our portfolio to include commercial properties, diversifying our investment opportunities.",
          ar: "وسعنا محفظتنا لتشمل العقارات التجارية، مما نوع فرص الاستثمار لدينا."
        },
        icon: "FaCity",
        image: "https://via.placeholder.com/800x450/4b5563/00C2FF?text=Commercial+2012",
        color: "from-purple-500 to-violet-500",
        order: 3
      },
      {
        id: "international-recognition-2015",
        year: 2015,
        title: {
          en: "International Recognition",
          ar: "الاعتراف الدولي"
        },
        description: {
          en: "Received international recognition for architectural excellence and sustainable design practices.",
          ar: "حصلنا على اعتراف دولي للتميز المعماري وممارسات التصميم المستدام."
        },
        icon: "FaGlobe",
        image: "https://via.placeholder.com/800x450/6b7280/00C2FF?text=Recognition+2015",
        color: "from-orange-500 to-amber-500",
        order: 4
      },
      {
        id: "tech-innovation-2018",
        year: 2018,
        title: {
          en: "Technological Innovation",
          ar: "الابتكار التكنولوجي"
        },
        description: {
          en: "Pioneered the use of smart home technology in all residential developments, setting new industry standards.",
          ar: "ريادة استخدام تقنية المنزل الذكي في جميع التطويرات السكنية، مما وضع معايير جديدة للصناعة."
        },
        icon: "FaMicrochip",
        image: "https://via.placeholder.com/800x450/9ca3af/1f2937?text=Innovation+2018",
        color: "from-rose-500 to-pink-500",
        order: 5
      },
      {
        id: "sustainability-focus-2021",
        year: 2021,
        title: {
          en: "Sustainability Focus",
          ar: "التركيز على الاستدامة"
        },
        description: {
          en: "Committed to sustainable development with implementation of green building practices across all projects.",
          ar: "التزمنا بالتنمية المستدامة مع تطبيق ممارسات البناء الأخضر عبر جميع المشاريع."
        },
        icon: "FaLeaf",
        image: "https://via.placeholder.com/800x450/d1d5db/1f2937?text=Sustainability+2021",
        color: "from-green-500 to-emerald-500",
        order: 6
      },
      {
        id: "portfolio-expansion-2023",
        year: 2023,
        title: {
          en: "Portfolio Expansion",
          ar: "توسع المحفظة"
        },
        description: {
          en: "Expanded our investment portfolio to include hospitality and retail sectors, creating integrated lifestyle destinations.",
          ar: "وسعنا محفظة الاستثمار لتشمل قطاعي الضيافة والتجزئة، مما خلق وجهات نمط حياة متكاملة."
        },
        icon: "FaChartBar",
        image: "https://via.placeholder.com/800x450/f3f4f6/1f2937?text=Expansion+2023",
        color: "from-indigo-500 to-blue-500",
        order: 7
      },
      {
        id: "future-vision-2024",
        year: 2024,
        title: {
          en: "Future Vision",
          ar: "الرؤية المستقبلية"
        },
        description: {
          en: "Setting ambitious goals for the next decade with focus on innovative urban solutions and community development.",
          ar: "وضع أهداف طموحة للعقد القادم مع التركيز على الحلول الحضرية المبتكرة وتنمية المجتمع."
        },
        icon: "FaTrophy",
        image: "https://via.placeholder.com/800x450/e5e7eb/1f2937?text=Future+2024",
        color: "from-red-500 to-orange-500",
        order: 8
      }
    ]
  });

  const [editingHeader, setEditingHeader] = useState(false);
  const [editingTimelineItem, setEditingTimelineItem] = useState<string | null>(null);
  const [showAddTimelineForm, setShowAddTimelineForm] = useState(false);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);

  const handleHeaderSave = (newData: Partial<CompanyHistorySection['header']>) => {
    setCompanyHistoryData(prev => ({
      ...prev,
      header: { ...prev.header, ...newData }
    }));
    setEditingHeader(false);
  };

  const handleTimelineItemSave = (itemId: string, newItem: HistoryItem) => {
    setCompanyHistoryData(prev => ({
      ...prev,
      timeline: prev.timeline.map(item => item.id === itemId ? newItem : item)
    }));
    setEditingTimelineItem(null);
  };

  const handleTimelineItemDelete = (itemId: string) => {
    if (confirm('Are you sure you want to delete this timeline item?')) {
      setCompanyHistoryData(prev => ({
        ...prev,
        timeline: prev.timeline.filter(item => item.id !== itemId)
      }));
    }
  };

  const handleTimelineItemAdd = (newItem: HistoryItem) => {
    const maxOrder = Math.max(...companyHistoryData.timeline.map(item => item.order), 0);
    const itemWithOrder = { ...newItem, order: maxOrder + 1 };
    setCompanyHistoryData(prev => ({
      ...prev,
      timeline: [...prev.timeline, itemWithOrder]
    }));
    setShowAddTimelineForm(false);
  };

  const handleDragStart = (itemId: string) => {
    setDraggedItem(itemId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    if (!draggedItem || draggedItem === targetId) return;

    const draggedIndex = companyHistoryData.timeline.findIndex(item => item.id === draggedItem);
    const targetIndex = companyHistoryData.timeline.findIndex(item => item.id === targetId);

    const newTimeline = [...companyHistoryData.timeline];
    const [draggedTimelineItem] = newTimeline.splice(draggedIndex, 1);
    newTimeline.splice(targetIndex, 0, draggedTimelineItem);

    // Update order values
    const updatedTimeline = newTimeline.map((item, index) => ({
      ...item,
      order: index + 1
    }));

    setCompanyHistoryData(prev => ({ ...prev, timeline: updatedTimeline }));
    setDraggedItem(null);
  };

  const handleSaveAll = () => {
    // Here you would typically send the data to your backend
    console.log('Saving company history data:', companyHistoryData);
    alert('Company History data saved successfully!');
  };

  // Carousel scroll state
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const scrollLeft = () => {
    if (carouselRef.current) {
      const scrollAmount = window.innerWidth < 640 ? 304 : 336; // Responsive: mobile (288px + 16px) or desktop (320px + 16px)
      carouselRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (carouselRef.current) {
      const scrollAmount = window.innerWidth < 640 ? 304 : 336; // Responsive: mobile (288px + 16px) or desktop (320px + 16px)
      carouselRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  // Check if scrolling is possible
  const checkScrollPosition = () => {
    if (carouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    const carousel = carouselRef.current;
    if (carousel) {
      carousel.addEventListener('scroll', checkScrollPosition);
      checkScrollPosition(); // Initial check
      
      return () => {
        carousel.removeEventListener('scroll', checkScrollPosition);
      };
    }
  }, [companyHistoryData.timeline]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target === document.body || (e.target as Element)?.closest('.carousel-container')) {
        if (e.key === 'ArrowLeft' && canScrollLeft) {
          e.preventDefault();
          scrollLeft();
        } else if (e.key === 'ArrowRight' && canScrollRight) {
          e.preventDefault();
          scrollRight();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canScrollLeft, canScrollRight]);

  // Function to determine if a library has any icons matching the search term
  const hasMatchingIcons = (libraryId: string, searchTerm: string) => {
    if (!searchTerm.trim()) return true;
    
    let library;
    switch (libraryId) {
      case 'fc': library = FcIcons; break;
      case 'fa': library = FaIcons; break;
      case 'fa6': library = FaIconsSolid; break;
      case 'bs': library = BsIcons; break;
      case 'ri': library = RiIcons; break;
      case 'gi': library = GiIcons; break;
      case 'tb': library = TbIcons; break;
      case 'md': library = MdIcons; break;
      case 'hi': library = HiIcons; break;
      case 'ai': library = AiIcons; break;
      case 'io': library = IoIcons; break;
      case 'io5': library = Io5Icons; break;
      case 'pi': library = PiIcons; break;
      default: return false;
    }
    
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).some(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    );
  };
  
  // Function to get the icon component based on the icon name
  const getIconComponent = (iconName: string): React.ReactElement | null => {
    if (!iconName) return null;
    
    // Determine which library the icon belongs to based on prefix
    if (iconName.startsWith('Fc')) {
        const IconFc = FcIcons[iconName as keyof typeof FcIcons];
        return IconFc ? <IconFc className="text-3xl" /> : null;
    } else if (iconName.startsWith('Fa6')) {
        const IconFa6 = FaIconsSolid[iconName as keyof typeof FaIconsSolid];
        return IconFa6 ? <IconFa6 className="text-3xl" /> : null;
    } else if (iconName.startsWith('Fa')) {
      const IconFa = FaIcons[iconName as keyof typeof FaIcons];
      return IconFa ? <IconFa className="text-3xl" /> : null;
    } else if (iconName.startsWith('Bs')) {
        const IconBs = BsIcons[iconName as keyof typeof BsIcons];
        return IconBs ? <IconBs className="text-3xl" /> : null;
    } else if (iconName.startsWith('Ri')) {
        const IconRi = RiIcons[iconName as keyof typeof RiIcons];
        return IconRi ? <IconRi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Gi')) {
        const IconGi = GiIcons[iconName as keyof typeof GiIcons];
        return IconGi ? <IconGi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Tb')) {
        const IconTb = TbIcons[iconName as keyof typeof TbIcons];
        return IconTb ? <IconTb className="text-3xl" /> : null;
    } else if (iconName.startsWith('Md')) {
        const IconMd = MdIcons[iconName as keyof typeof MdIcons];
        return IconMd ? <IconMd className="text-3xl" /> : null;
    } else if (iconName.startsWith('Hi')) {
        const IconHi = HiIcons[iconName as keyof typeof HiIcons];
        return IconHi ? <IconHi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Ai')) {
        const IconAi = AiIcons[iconName as keyof typeof AiIcons];
        return IconAi ? <IconAi className="text-3xl" /> : null;
    } else if (iconName.startsWith('Io5')) {
        const IconIo5 = Io5Icons[iconName as keyof typeof Io5Icons];
        return IconIo5 ? <IconIo5 className="text-3xl" /> : null;
    } else if (iconName.startsWith('Io')) {
      const IconIo = IoIcons[iconName as keyof typeof IoIcons];
      return IconIo ? <IconIo className="text-3xl" /> : null;
    } else if (iconName.startsWith('Pi')) {
        const IconPi = PiIcons[iconName as keyof typeof PiIcons];
        return IconPi ? <IconPi className="text-3xl" /> : null;
      }
    
        return null;
  };

  const iconLibraries = [
    { id: 'fc', name: 'Flat Color', library: FcIcons },
    { id: 'fa', name: 'Font Awesome', library: FaIcons },
    { id: 'fa6', name: 'Font Awesome 6', library: FaIconsSolid },
    { id: 'bs', name: 'Bootstrap', library: BsIcons },
    { id: 'ri', name: 'Remix', library: RiIcons },
    { id: 'gi', name: 'Game Icons', library: GiIcons },
    { id: 'tb', name: 'Tabler', library: TbIcons },
    { id: 'md', name: 'Material Design', library: MdIcons },
    { id: 'hi', name: 'Heroicons', library: HiIcons },
    { id: 'ai', name: 'Ant Design', library: AiIcons },
    { id: 'io', name: 'Ionicons 4', library: IoIcons },
    { id: 'io5', name: 'Ionicons 5', library: Io5Icons },
    { id: 'pi', name: 'Phosphor', library: PiIcons },
  ];

  const getFilteredIcons = (library: any, searchTerm: string) => {
    const normalizedSearch = searchTerm.trim().toLowerCase();
    return Object.keys(library).filter(iconName => 
      iconName.toLowerCase().includes(normalizedSearch)
    ).slice(0, 50); // Limit to 50 icons for performance
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">About Us - Company History Section</h1>
          <p className="text-gray-400 mt-1">Manage the company history timeline section of the about page</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Header Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiClock className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Header
            </h2>
            <button
              onClick={() => setEditingHeader(!editingHeader)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingHeader ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingHeader ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingHeader ? (
            <HeaderEditForm
              data={companyHistoryData.header}
              onSave={handleHeaderSave}
              onCancel={() => setEditingHeader(false)}
            />
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                  <p className="text-white">{companyHistoryData.header.badge.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                  <p className="text-white">{companyHistoryData.header.badge.ar}</p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <p className="text-white">{companyHistoryData.header.title.en} <span className="text-[#00C2FF]">{companyHistoryData.header.subtitle.en}</span></p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <p className="text-white">{companyHistoryData.header.title.ar} <span className="text-[#00C2FF]">{companyHistoryData.header.subtitle.ar}</span></p>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
                <p className="text-gray-300 text-sm">{companyHistoryData.header.description.en}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
                <p className="text-gray-300 text-sm">{companyHistoryData.header.description.ar}</p>
              </div>
            </div>
          )}
        </div>

        {/* Timeline Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">
              Company Timeline ({companyHistoryData.timeline.length})
            </h2>
            <button
              onClick={() => setShowAddTimelineForm(true)}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Timeline Item
            </button>
          </div>

          {/* Carousel Container */}
          <div className="relative carousel-container">
            {/* Carousel Navigation Buttons */}
            <div className="flex justify-between items-center mb-4">
              <button
                onClick={scrollLeft}
                disabled={!canScrollLeft}
                className={`p-2 rounded-full transition-all duration-200 shadow-lg ${
                  canScrollLeft 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white cursor-pointer hover:scale-105' 
                    : 'bg-gray-800 text-gray-500 cursor-not-allowed opacity-50'
                }`}
                aria-label="Scroll left"
              >
                <FiChevronLeft className="h-5 w-5" />
              </button>
              
              <div className="text-sm text-gray-400 flex items-center gap-2">
                <span>Scroll to view more timeline items</span>
                {companyHistoryData.timeline.length > 3 && (
                  <span className="text-xs bg-gray-700 px-2 py-1 rounded">
                    {companyHistoryData.timeline.length} items
                  </span>
                )}
              </div>
              
              <button
                onClick={scrollRight}
                disabled={!canScrollRight}
                className={`p-2 rounded-full transition-all duration-200 shadow-lg ${
                  canScrollRight 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white cursor-pointer hover:scale-105' 
                    : 'bg-gray-800 text-gray-500 cursor-not-allowed opacity-50'
                }`}
                aria-label="Scroll right"
              >
                <FiChevronRight className="h-5 w-5" />
              </button>
            </div>

            {/* Scrollable Timeline Items */}
            <div 
              ref={carouselRef}
              className="flex gap-4 overflow-x-auto scrollbar-hide pb-4"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              }}
            >
              {companyHistoryData.timeline
                .sort((a, b) => a.order - b.order)
                .map((item) => (
                <div
                  key={item.id}
                  draggable
                  onDragStart={() => handleDragStart(item.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, item.id)}
                  className={`bg-gray-700 rounded-lg border border-gray-600 transition-all hover:border-gray-500 cursor-move flex-shrink-0 w-72 sm:w-80 ${
                    draggedItem === item.id ? 'opacity-50' : ''
                  }`}
                >
                  {/* Timeline Item Card Header */}
                  <div className={`h-2 bg-gradient-to-r ${item.color}`}></div>
                  <div className="border-b border-gray-600 p-3 flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="bg-[#00C2FF]/20 w-16 h-16 rounded-full flex items-center justify-center text-[#00C2FF] mr-3">
                        <span className="text-sm" key={item.icon}>
                          {getIconComponent(item.icon) || <FaIcons.FaBuilding className="text-lg" />}
                        </span>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-[#00C2FF]">{item.year}</div>
                        <div className="flex items-center">
                          <FiMove className="h-3 w-3 text-gray-500 mr-1" />
                          <span className="text-xs text-gray-400">Order: {item.order}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingTimelineItem(item.id)}
                        className="p-1 text-gray-400 hover:text-[#00C2FF] transition-colors"
                      >
                        <FiEdit3 className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleTimelineItemDelete(item.id)}
                        className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {editingTimelineItem === item.id ? (
                    <div className="p-4">
                      <TimelineItemEditForm
                        item={item}
                        onSave={(newItem) => handleTimelineItemSave(item.id, newItem)}
                        onCancel={() => setEditingTimelineItem(null)}
                        getIconComponent={getIconComponent}
                        hasMatchingIcons={hasMatchingIcons}
                        iconLibraries={iconLibraries}
                        getFilteredIcons={getFilteredIcons}
                      />
                    </div>
                  ) : (
                    <div className="p-4">
                      <h3 className="font-bold text-white text-sm mb-2">{item.title.en}</h3>
                      <p className="text-gray-400 text-xs mb-2">{item.title.ar}</p>
                      <p className="text-gray-300 text-xs line-clamp-3 mb-3">{item.description.en}</p>
                      
                      {/* Image preview */}
                      <div className="w-full h-20 bg-gray-600 rounded-md overflow-hidden mb-3">
                        <img 
                          src={item.image} 
                          alt={item.title.en}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      
                      {/* Color indicator */}
                      <div className="flex items-center justify-between">
                        <div className={`w-16 h-3 rounded-full bg-gradient-to-r ${item.color}`}></div>
                        <span className="text-xs text-gray-500">{colorOptions.find(c => c.value === item.color)?.name}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {showAddTimelineForm && (
            <div className="mt-6 p-4 bg-gray-700 rounded-lg border border-gray-600">
              <TimelineItemAddForm
                onSave={handleTimelineItemAdd}
                onCancel={() => setShowAddTimelineForm(false)}
                getIconComponent={getIconComponent}
                hasMatchingIcons={hasMatchingIcons}
                iconLibraries={iconLibraries}
                getFilteredIcons={getFilteredIcons}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Header Edit Form Component
function HeaderEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: CompanyHistorySection['header'];
  onSave: (data: Partial<CompanyHistorySection['header']>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
          <input
            type="text"
            value={formData.badge.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              badge: { ...prev.badge, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
          <input
            type="text"
            value={formData.badge.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              badge: { ...prev.badge, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (English)</label>
          <input
            type="text"
            value={formData.subtitle.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              subtitle: { ...prev.subtitle, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (Arabic)</label>
          <input
            type="text"
            value={formData.subtitle.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              subtitle: { ...prev.subtitle, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
        <textarea
          value={formData.description.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, en: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
        <textarea
          value={formData.description.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, ar: e.target.value }
          }))}
          rows={3}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Timeline Item Edit Form Component
function TimelineItemEditForm({ 
  item, 
  onSave, 
  onCancel,
  getIconComponent,
  hasMatchingIcons,
  iconLibraries,
  getFilteredIcons
}: { 
  item: HistoryItem;
  onSave: (item: HistoryItem) => void;
  onCancel: () => void;
  getIconComponent: (iconName: string) => React.ReactElement | null;
  hasMatchingIcons: (libraryId: string, searchTerm: string) => boolean;
  iconLibraries: any[];
  getFilteredIcons: (library: any, searchTerm: string) => string[];
}) {
  const [formData, setFormData] = useState(item);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(item.image);
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconSet, setSelectedIconSet] = useState<
    'fc' | 'fa' | 'fa6' | 'bs' | 'ri' | 'gi' | 'tb' | 'md' | 'hi' | 'ai' | 'io' | 'io5' | 'pi'
  >('fa');
  
  // Icon library carousel refs and state
  const iconLibsCarouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLibsLeft, setCanScrollLibsLeft] = useState(false);
  const [canScrollLibsRight, setCanScrollLibsRight] = useState(true);

  // Icon library carousel scroll functions
  const scrollLibsLeft = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: -120,
        behavior: 'smooth'
      });
    }
  };

  const scrollLibsRight = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: 120,
        behavior: 'smooth'
      });
    }
  };

  // Check if libs carousel scrolling is possible
  const checkLibsScrollPosition = () => {
    if (iconLibsCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = iconLibsCarouselRef.current;
      setCanScrollLibsLeft(scrollLeft > 0);
      setCanScrollLibsRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // Icon libs carousel scroll effect
  useEffect(() => {
    const carousel = iconLibsCarouselRef.current;
    if (carousel && showIconSelector) {
      carousel.addEventListener('scroll', checkLibsScrollPosition);
      checkLibsScrollPosition(); // Initial check
      
      return () => {
        carousel.removeEventListener('scroll', checkLibsScrollPosition);
      };
    }
  }, [showIconSelector]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the image persists
      setFormData(prev => ({ ...prev, image: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById(`image-upload-edit-${item.id}`)?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      {/* Hidden File Input */}
      <input
        id={`image-upload-edit-${item.id}`}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="grid grid-cols-2 gap-2">
        <input
          type="number"
          placeholder="Year"
          value={formData.year}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            year: parseInt(e.target.value) || new Date().getFullYear()
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        
        {/* Icon Selector */}
        <div className="relative icon-selector-container">
          <button
            type="button"
            onClick={() => setShowIconSelector(!showIconSelector)}
            className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF] flex items-center justify-between"
          >
                        <span className="flex items-center min-w-0 flex-1">              <span key={formData.icon} className="flex-shrink-0">                {getIconComponent(formData.icon) || <FaIcons.FaBuilding className="text-lg" />}              </span>              <span className="ml-2 text-xs truncate min-w-0">{formData.icon || 'Select Icon'}</span>            </span>
            <FiSearch className="h-3 w-3" />
          </button>

          {showIconSelector && (
            <div 
              className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg max-h-80 overflow-y-auto min-w-[400px]" 
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: '#6b7280 transparent'
              }}
            >
              {/* Icon Library Tabs with Carousel */}
              <div className="relative border-b border-gray-600">
                {/* Navigation Buttons */}
                <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                  <button
                    type="button"
                    onClick={scrollLibsLeft}
                    disabled={!canScrollLibsLeft}
                    className={`p-1 rounded-r transition-colors ${
                      canScrollLibsLeft 
                        ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                        : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                    }`}
                    aria-label="Scroll library tabs left"
                  >
                    <FiChevronLeft className="h-3 w-3" />
                  </button>
                </div>
                
                <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                  <button
                    type="button"
                    onClick={scrollLibsRight}
                    disabled={!canScrollLibsRight}
                    className={`p-1 rounded-l transition-colors ${
                      canScrollLibsRight 
                        ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                        : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                    }`}
                    aria-label="Scroll library tabs right"
                  >
                    <FiChevronRight className="h-3 w-3" />
                  </button>
                </div>

                {/* Scrollable Tabs Container */}
                <div 
                  ref={iconLibsCarouselRef}
                  className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                  {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                    <button
                      key={lib.id}
                      type="button"
                      onClick={() => setSelectedIconSet(lib.id as any)}
                      className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                        selectedIconSet === lib.id
                          ? 'bg-[#00C2FF] text-white'
                          : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                      }`}
                    >
                      {lib.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Search Input */}
              <div className="p-2 border-b border-gray-600">
                <input
                  type="text"
                  placeholder="Search icons..."
                  value={iconSearchTerm}
                  onChange={(e) => setIconSearchTerm(e.target.value)}
                  className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-xs"
                />
              </div>

              {/* Icon Grid */}
              <div className="p-2">
                <div className="grid grid-cols-3 gap-1">
                  {(() => {
                    const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                    if (!currentLibrary) return null;

                    return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                      const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary];
                      
                      // Create the full icon name with library prefix
                      let prefix = '';
                      switch (selectedIconSet) {
                        case 'fc': prefix = 'Fc'; break;
                        case 'fa': prefix = 'Fa'; break;
                        case 'fa6': prefix = 'Fa6'; break;
                        case 'bs': prefix = 'Bs'; break;
                        case 'ri': prefix = 'Ri'; break;
                        case 'gi': prefix = 'Gi'; break;
                        case 'tb': prefix = 'Tb'; break;
                        case 'md': prefix = 'Md'; break;
                        case 'hi': prefix = 'Hi'; break;
                        case 'ai': prefix = 'Ai'; break;
                        case 'io': prefix = 'Io'; break;
                        case 'io5': prefix = 'Io5'; break;
                        case 'pi': prefix = 'Pi'; break;
                                            }                                            const fullIconName = iconName;                                            return (
                        <button
                          key={iconName}
                          type="button"
                          onClick={() => {
                            setFormData(prev => ({ ...prev, icon: fullIconName }));
                            setShowIconSelector(false);
                          }}
                          className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors"
                          title={iconName}
                        >
                          {IconComponent && <IconComponent className="text-lg text-white hover:text-[#00C2FF] transition-colors" />}
                        </button>
                      );
                    });
                  })()}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Title (English)"
          value={formData.title.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: { ...prev.title, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Title (Arabic)"
          value={formData.title.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: { ...prev.title, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <textarea
        placeholder="Description (English)"
        value={formData.description.en}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          description: { ...prev.description, en: e.target.value }
        }))}
        rows={2}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      />

      <textarea
        placeholder="Description (Arabic)"
        value={formData.description.ar}
        onChange={(e) => setFormData(prev => ({
          ...prev,
          description: { ...prev.description, ar: e.target.value }
        }))}
        rows={3}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      />

      {/* Clickable Image Upload Area */}
      <div>
        <label className="block text-sm font-medium text-gray-400 mb-1">Timeline Image</label>
        <div 
          onClick={handleImageClick}
          className="w-full h-20 bg-gray-600 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
        >
          {previewUrl ? (
            <div className="relative w-full h-full">
              <img 
                src={previewUrl} 
                alt="Preview"
                className="w-full h-full object-cover"
                onError={() => setPreviewUrl('https://via.placeholder.com/800x450/1f2937/00C2FF?text=Image+Error')}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                <FiUpload className="text-white opacity-0 group-hover:opacity-100 h-5 w-5" />
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
              <div className="text-center">
                <FiUpload className="h-5 w-5 mx-auto mb-1" />
                <p className="text-xs">Click to upload</p>
              </div>
            </div>
          )}
        </div>
        {selectedFile && (
          <p className="text-xs text-gray-400 mt-1">
            New: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
          </p>
        )}
      </div>

            {/* Visual Gradient Picker */}      <div className="space-y-3">        <div className="text-xs text-gray-400 mb-2">          Current: {colorOptions.find(opt => opt.value === formData.color)?.name || 'Custom'}        </div>        <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto scrollbar-hide">          {colorOptions.map((option) => (            <button              key={option.value}              type="button"              onClick={() => setFormData(prev => ({ ...prev, color: option.value }))}              className={`group relative h-8 rounded-md overflow-hidden transition-all ${                formData.color === option.value                  ? 'ring-2 ring-[#00C2FF] ring-offset-2 ring-offset-gray-700'                  : 'hover:ring-1 hover:ring-gray-400'              }`}              title={option.name}            >              <div className={`w-full h-full bg-gradient-to-r ${option.value}`}></div>              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">                <span className="text-white text-xs opacity-0 group-hover:opacity-100 font-medium text-center px-1">                  {option.name}                </span>              </div>            </button>          ))}        </div>      </div>

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save
        </button>
      </div>
    </form>
  );
}

// Timeline Item Add Form Component
function TimelineItemAddForm({ 
  onSave, 
  onCancel,
  getIconComponent,
  hasMatchingIcons,
  iconLibraries,
  getFilteredIcons
}: { 
  onSave: (item: HistoryItem) => void;
  onCancel: () => void;
  getIconComponent: (iconName: string) => React.ReactElement | null;
  hasMatchingIcons: (libraryId: string, searchTerm: string) => boolean;
  iconLibraries: any[];
  getFilteredIcons: (library: any, searchTerm: string) => string[];
}) {
  const [formData, setFormData] = useState<HistoryItem>({
    id: '',
    year: new Date().getFullYear(),
    title: { en: '', ar: '' },
    description: { en: '', ar: '' },
    icon: 'FaBuilding',
    image: '',
    color: 'from-blue-500 to-cyan-500',
    order: 0
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [iconSearchTerm, setIconSearchTerm] = useState('');
  const [selectedIconSet, setSelectedIconSet] = useState<
    'fc' | 'fa' | 'fa6' | 'bs' | 'ri' | 'gi' | 'tb' | 'md' | 'hi' | 'ai' | 'io' | 'io5' | 'pi'
  >('fa');

  // Icon library carousel refs and state
  const iconLibsCarouselRef = useRef<HTMLDivElement>(null);
  const [canScrollLibsLeft, setCanScrollLibsLeft] = useState(false);
  const [canScrollLibsRight, setCanScrollLibsRight] = useState(true);

  // Icon library carousel scroll functions
  const scrollLibsLeft = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: -120,
        behavior: 'smooth'
      });
    }
  };

  const scrollLibsRight = () => {
    if (iconLibsCarouselRef.current) {
      iconLibsCarouselRef.current.scrollBy({
        left: 120,
        behavior: 'smooth'
      });
    }
  };

  // Check if libs carousel scrolling is possible
  const checkLibsScrollPosition = () => {
    if (iconLibsCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = iconLibsCarouselRef.current;
      setCanScrollLibsLeft(scrollLeft > 0);
      setCanScrollLibsRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // Icon libs carousel scroll effect
  useEffect(() => {
    const carousel = iconLibsCarouselRef.current;
    if (carousel && showIconSelector) {
      carousel.addEventListener('scroll', checkLibsScrollPosition);
      checkLibsScrollPosition(); // Initial check
      
      return () => {
        carousel.removeEventListener('scroll', checkLibsScrollPosition);
      };
    }
  }, [showIconSelector]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Use the blob URL directly so the image persists
      setFormData(prev => ({ ...prev, image: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById('image-upload-add-timeline')?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.en || !formData.year) {
      alert('Please fill in the English title and year');
      return;
    }

    if (!selectedFile && !formData.image) {
      alert('Please upload an image for the timeline item');
      return;
    }

    const newItem = {
      ...formData,
      id: `${formData.title.en.toLowerCase().replace(/\s+/g, '-')}-${formData.year}`
    };

    onSave(newItem);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showIconSelector && !(event.target as Element).closest('.icon-selector-container')) {
        setShowIconSelector(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showIconSelector]);

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Timeline Item</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Hidden File Input */}
        <input
          id="image-upload-add-timeline"
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Year *</label>
            <input
              type="number"
              value={formData.year}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                year: parseInt(e.target.value) || new Date().getFullYear()
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
              min="1900"
              max="2100"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
            <div className="relative icon-selector-container">
              <button
                type="button"
                onClick={() => setShowIconSelector(!showIconSelector)}
                className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] flex items-center justify-between"
              >
                                <span className="flex items-center min-w-0 flex-1">                  <span className="flex-shrink-0">                    {getIconComponent(formData.icon) || <FaIcons.FaBuilding className="text-lg" />}                  </span>                  <span className="ml-2 text-sm truncate min-w-0">{formData.icon || 'Select Icon'}</span>                </span>
                <FiSearch className="h-4 w-4" />
              </button>

              {showIconSelector && (
                <div 
                  className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-700 border border-gray-600 rounded-lg shadow-lg max-h-80 overflow-y-auto min-w-[400px]" 
                      style={{
                    scrollbarWidth: 'thin',
                    scrollbarColor: '#6b7280 transparent'
                  }}
                >
              {/* Icon Library Tabs with Carousel */}
              <div className="relative border-b border-gray-600">
                {/* Navigation Buttons */}
                <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                  <button
                    type="button"
                    onClick={scrollLibsLeft}
                    disabled={!canScrollLibsLeft}
                    className={`p-1 rounded-r transition-colors ${
                      canScrollLibsLeft 
                        ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                        : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                    }`}
                    aria-label="Scroll library tabs left"
                  >
                    <FiChevronLeft className="h-3 w-3" />
                  </button>
                </div>
                
                <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                  <button
                    type="button"
                    onClick={scrollLibsRight}
                    disabled={!canScrollLibsRight}
                    className={`p-1 rounded-l transition-colors ${
                      canScrollLibsRight 
                        ? 'bg-gray-600 hover:bg-gray-500 text-white' 
                        : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                    }`}
                    aria-label="Scroll library tabs right"
                  >
                    <FiChevronRight className="h-3 w-3" />
                  </button>
                </div>

                {/* Scrollable Tabs Container */}
                <div 
                  ref={iconLibsCarouselRef}
                  className="flex gap-1 p-2 overflow-x-auto scrollbar-hide px-6"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                  {iconLibraries.filter(lib => hasMatchingIcons(lib.id, iconSearchTerm)).map((lib) => (
                    <button
                      key={lib.id}
                      type="button"
                      onClick={() => setSelectedIconSet(lib.id as any)}
                      className={`px-2 py-1 text-xs rounded transition-colors whitespace-nowrap flex-shrink-0 ${
                        selectedIconSet === lib.id
                          ? 'bg-[#00C2FF] text-white'
                          : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                      }`}
                    >
                      {lib.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Search Input */}
              <div className="p-2 border-b border-gray-600">
                <input
                  type="text"
                  placeholder="Search icons..."
                  value={iconSearchTerm}
                  onChange={(e) => setIconSearchTerm(e.target.value)}
                      className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm"
                />
              </div>

              {/* Icon Grid */}
              <div className="p-2">
                <div className="grid grid-cols-3 gap-1">
                  {(() => {
                    const currentLibrary = iconLibraries.find(lib => lib.id === selectedIconSet)?.library;
                    if (!currentLibrary) return null;

                    return getFilteredIcons(currentLibrary, iconSearchTerm).map((iconName) => {
                      const IconComponent = currentLibrary[iconName as keyof typeof currentLibrary];
                          
                          // Create the full icon name with library prefix
                          let prefix = '';
                          switch (selectedIconSet) {
                            case 'fc': prefix = 'Fc'; break;
                            case 'fa': prefix = 'Fa'; break;
                            case 'fa6': prefix = 'Fa6'; break;
                            case 'bs': prefix = 'Bs'; break;
                            case 'ri': prefix = 'Ri'; break;
                            case 'gi': prefix = 'Gi'; break;
                            case 'tb': prefix = 'Tb'; break;
                            case 'md': prefix = 'Md'; break;
                            case 'hi': prefix = 'Hi'; break;
                            case 'ai': prefix = 'Ai'; break;
                            case 'io': prefix = 'Io'; break;
                            case 'io5': prefix = 'Io5'; break;
                            case 'pi': prefix = 'Pi'; break;
                          }
                          
                          const fullIconName = iconName;
                          
                          return (
                            <button
                              key={iconName}
                              type="button"
                              onClick={() => {
                                setFormData(prev => ({ ...prev, icon: fullIconName }));
                                setShowIconSelector(false);
                              }}
                              className="p-2 bg-gray-600 hover:bg-gray-500 rounded flex items-center justify-center transition-colors"
                              title={iconName}
                            >
                              {IconComponent && <IconComponent className="text-lg text-white hover:text-[#00C2FF] transition-colors" />}
                            </button>
                          );
                        });
                      })()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
            <input
              type="text"
              value={formData.title.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
            <input
              type="text"
              value={formData.title.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>

        {/* Clickable Image Upload Area */}
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Timeline Image *</label>
          <div 
            onClick={handleImageClick}
            className="w-full h-48 bg-gray-600 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group"
          >
            {previewUrl ? (
              <div className="relative w-full h-full">
                <img 
                  src={previewUrl} 
                  alt="Preview"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = 'https://via.placeholder.com/800x450/1f2937/00C2FF?text=Image+Error';
                  }}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                  <div className="text-center text-white opacity-0 group-hover:opacity-100">
                    <FiUpload className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Click to change image</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                <div className="text-center">
                  <FiUpload className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium">Click to upload image</p>
                  <p className="text-sm">Select a timeline image</p>
                </div>
              </div>
            )}
          </div>
          
          {selectedFile && (
            <div className="mt-2 p-3 bg-gray-700 rounded border border-gray-600">
              <div className="flex items-center text-green-400 text-sm">
                <FiImage className="mr-2 h-4 w-4" />
                <span>Selected: {selectedFile.name}</span>
                <span className="ml-2 text-gray-400">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
              </div>
            </div>
          )}
        </div>

                <div>          <label className="block text-sm font-medium text-gray-400 mb-2">Color Scheme</label>          {/* Visual Gradient Picker */}          <div className="space-y-3">            <div className="text-sm text-gray-400 mb-2 p-2 bg-gray-700 rounded">              Selected: <span className="text-white">{colorOptions.find(opt => opt.value === formData.color)?.name || 'Custom'}</span>            </div>            <div className="grid grid-cols-3 gap-3 max-h-48 overflow-y-auto scrollbar-hide p-2 bg-gray-700 rounded border border-gray-600">              {colorOptions.map((option) => (                <button                  key={option.value}                  type="button"                  onClick={() => setFormData(prev => ({ ...prev, color: option.value }))}                  className={`group relative h-12 rounded-lg overflow-hidden transition-all ${                    formData.color === option.value                      ? 'ring-2 ring-[#00C2FF] ring-offset-2 ring-offset-gray-700 scale-105'                      : 'hover:ring-1 hover:ring-gray-400 hover:scale-102'                  }`}                  title={`${option.name} (${option.category})`}                >                  <div className={`w-full h-full bg-gradient-to-r ${option.value}`}></div>                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex flex-col items-center justify-center">                    <span className="text-white text-xs opacity-0 group-hover:opacity-100 font-medium text-center px-1 leading-tight">                      {option.name}                    </span>                    <span className="text-gray-300 text-xs opacity-0 group-hover:opacity-80 font-light">                      {option.category}                    </span>                  </div>                  {formData.color === option.value && (                    <div className="absolute top-1 right-1 w-3 h-3 bg-[#00C2FF] rounded-full flex items-center justify-center">                      <div className="w-1.5 h-1.5 bg-white rounded-full"></div>                    </div>                  )}                </button>              ))}            </div>          </div>        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            <FiSave className="mr-2 h-4 w-4 inline" />
            Add Timeline Item
          </button>
        </div>
      </form>
    </div>
  );
} 