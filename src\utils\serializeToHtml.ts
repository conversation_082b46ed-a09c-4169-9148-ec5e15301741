import { CustomElement, CustomText } from '@/components/RichTextEditor/types';

// Helper to serialize the Slate content to HTML
export const serializeToHtml = (nodes: CustomElement[]): string => {
  return nodes.map(node => serializeNodeToHtml(node)).join('');
};

const serializeNodeToHtml = (node: CustomElement | CustomText): string => {
  // Handle leaf nodes (text)
  if ('text' in node) {
    let text = escapeHtml(node.text);
    
    if (node.bold) {
      text = `<strong>${text}</strong>`;
    }
    
    if (node.italic) {
      text = `<em>${text}</em>`;
    }
    
    if (node.underline) {
      text = `<u>${text}</u>`;
    }
    
    if (node.code) {
      text = `<code>${text}</code>`;
    }
    
    if (node.strikethrough) {
      text = `<s>${text}</s>`;
    }
    
    if (node.superscript) {
      text = `<sup>${text}</sup>`;
    }
    
    if (node.subscript) {
      text = `<sub>${text}</sub>`;
    }
    
    if (node.highlight) {
      text = `<mark>${text}</mark>`;
    }
    
    if (node.color) {
      text = `<span style="color: ${node.color}">${text}</span>`;
    }
    
    if (node.bgColor) {
      text = `<span style="background-color: ${node.bgColor}">${text}</span>`;
    }
    
    if (node.fontSize) {
      text = `<span style="font-size: ${node.fontSize}">${text}</span>`;
    }
    
    if (node.fontFamily) {
      text = `<span style="font-family: ${node.fontFamily}">${text}</span>`;
    }
    
    return text;
  }

  // If children is not an array, handle it properly
  const children = Array.isArray(node.children) 
    ? node.children.map(child => serializeNodeToHtml(child)).join('') 
    : '';

  // Add style attribute for alignment if needed
  const style = node.align ? ` style="text-align: ${node.align}"` : '';

  // Handle different node types
  switch (node.type) {
    case 'paragraph':
      return `<p${style}>${children}</p>`;
    
    case 'heading-one':
      return `<h1${style}>${children}</h1>`;
    
    case 'heading-two':
      return `<h2${style}>${children}</h2>`;
    
    case 'heading-three':
      return `<h3${style}>${children}</h3>`;
    
    case 'block-quote':
      return `<blockquote${style}>${children}</blockquote>`;
    
    case 'bulleted-list':
      return `<ul${style}>${children}</ul>`;
    
    case 'numbered-list':
      return `<ol${style}>${children}</ol>`;
    
    case 'list-item':
      return `<li${style}>${children}</li>`;
    
    case 'image':
      return `<figure${style}>
        <img src="${escapeHtml(node.url || '')}" alt="${escapeHtml(node.alt || '')}" />
        ${node.caption ? `<figcaption>${escapeHtml(node.caption)}</figcaption>` : ''}
      </figure>`;
    
    case 'video':
      return `<figure${style}>
        <video src="${escapeHtml(node.url || '')}" controls></video>
        ${node.caption ? `<figcaption>${escapeHtml(node.caption)}</figcaption>` : ''}
      </figure>`;
    
    case 'file-embed':
      return `<p${style}><a href="${escapeHtml(node.url || '')}" download>${children || 'Download'}</a></p>`;
    
    case 'code-block':
      return `<pre><code class="language-${node.language || 'plaintext'}">${children}</code></pre>`;
    
    case 'table':
      return `<table${style}>${children}</table>`;
    
    case 'table-row':
      return `<tr>${children}</tr>`;
    
    case 'table-cell':
      return `<td${node.colspan ? ` colspan="${node.colspan}"` : ''}${node.rowspan ? ` rowspan="${node.rowspan}"` : ''}>${children}</td>`;
    
    case 'footnote':
      return `<sup id="footnote-ref-${node.footnoteId}">${children}</sup>`;
    
    case 'endnote':
      return `<div id="footnote-${node.footnoteId}" class="footnote">${children}</div>`;
    
    case 'comment':
    case 'annotation':
      // Comments and annotations typically aren't rendered in HTML output
      return children;
    
    case 'toc':
      return `<div class="table-of-contents">${children}</div>`;
    
    case 'page-break':
      return `<hr class="page-break" />`;
    
    case 'column-layout':
      return `<div class="column-container" style="column-count: ${node.columns || 2};">${children}</div>`;
    
    case 'math-equation':
      // In a real implementation, you would want to add MathJax or KaTeX rendering here
      return `<div class="math-equation" data-equation="${escapeHtml(node.equation || '')}">${node.equation}</div>`;
    
    default:
      return children;
  }
};

// Helper function to escape HTML special characters
const escapeHtml = (text: string): string => {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}; 