"use client";

import { FiPhone, FiMail, FiMapPin, FiPlus, FiTrash2 } from 'react-icons/fi';

interface ContactInfo {
  title: {
    english: string;
    arabic: string;
  };
  sections: {
    phone: {
      title: {
        english: string;
        arabic: string;
      };
      numbers: {
        id: string;
        number: string;
        label: {
          english: string;
          arabic: string;
        };
      }[];
    };
    email: {
      title: {
        english: string;
        arabic: string;
      };
      addresses: {
        id: string;
        address: string;
        label: {
          english: string;
          arabic: string;
        };
      }[];
    };
    address: {
      title: {
        english: string;
        arabic: string;
      };
      company: {
        english: string;
        arabic: string;
      };
      lines: {
        id: string;
        line: {
          english: string;
          arabic: string;
        };
      }[];
    };
  };
}

interface ContactInformationSectionProps {
  contactInfo: ContactInfo;
  onUpdate: (path: string[], value: string) => void;
  onAddItem: (section: 'phone' | 'email' | 'address') => void;
  onRemoveItem: (section: 'phone' | 'email' | 'address', id: string) => void;
}

export default function ContactInformationSection({ contactInfo, onUpdate, onAddItem, onRemoveItem }: ContactInformationSectionProps) {
  return (
    <div className="bg-gray-800 shadow rounded-lg p-6 border border-gray-700">
      <h3 className="text-lg font-medium text-white mb-4 flex items-center">
        <div className="w-6 h-6 bg-purple-500 rounded mr-2 flex items-center justify-center">
          <span className="text-white text-xs font-bold">3</span>
        </div>
        Contact Information
      </h3>
      <p className="text-gray-400 text-sm mb-6">Manage company contact details including section titles, contact methods, and all contact information</p>
      
      {/* Main Section Title */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-300">Main Section Title (English)</label>
          <input
            type="text"
            className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
            value={contactInfo.title.english}
            onChange={e => onUpdate(['title', 'english'], e.target.value)}
            placeholder="Contact Information"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300">Main Section Title (Arabic)</label>
          <input
            type="text"
            dir="rtl"
            className="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
            value={contactInfo.title.arabic}
            onChange={e => onUpdate(['title', 'arabic'], e.target.value)}
            placeholder="معلومات التواصل"
          />
        </div>
      </div>

      {/* Phone Section */}
      <div className="bg-gray-700 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-yellow-400 flex items-center">
            <FiPhone className="mr-2 h-4 w-4" />
            Phone Section Configuration
          </h4>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onAddItem('phone');
            }}
            className="inline-flex items-center px-3 py-1 border border-green-500 rounded-md text-sm font-medium text-green-400 hover:bg-green-500 hover:text-white transition-colors"
          >
            <FiPlus className="mr-1 h-4 w-4" />
            Add Phone
          </button>
        </div>
        
        {/* Phone Section Title */}
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300">Phone Section Title (English)</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.phone.title.english}
              onChange={e => onUpdate(['sections', 'phone', 'title', 'english'], e.target.value)}
              placeholder="Phone"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Phone Section Title (Arabic)</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.phone.title.arabic}
              onChange={e => onUpdate(['sections', 'phone', 'title', 'arabic'], e.target.value)}
              placeholder="الهاتف"
            />
          </div>
        </div>

        {/* Phone Numbers */}
        <div className="space-y-4">
          {contactInfo.sections.phone.numbers.map((phone, index) => (
            <div key={phone.id} className="bg-gray-600 rounded-lg p-3 border border-gray-500">
              <div className="flex items-center justify-between mb-3">
                <h5 className="text-sm font-medium text-gray-200">Phone #{index + 1}</h5>
                {contactInfo.sections.phone.numbers.length > 1 && (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveItem('phone', phone.id);
                    }}
                    className="inline-flex items-center px-2 py-1 border border-red-500 rounded text-xs font-medium text-red-400 hover:bg-red-500 hover:text-white transition-colors"
                  >
                    <FiTrash2 className="mr-1 h-3 w-3" />
                    Remove
                  </button>
                )}
              </div>
              <div className="grid grid-cols-1 gap-3 lg:grid-cols-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Phone Number</label>
                  <input
                    type="tel"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={phone.number}
                    onChange={e => onUpdate(['sections', 'phone', 'numbers', index.toString(), 'number'], e.target.value)}
                    placeholder="+971 12 345 6789"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Label (EN)</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={phone.label.english}
                    onChange={e => onUpdate(['sections', 'phone', 'numbers', index.toString(), 'label', 'english'], e.target.value)}
                    placeholder="Main"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Label (AR)</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={phone.label.arabic}
                    onChange={e => onUpdate(['sections', 'phone', 'numbers', index.toString(), 'label', 'arabic'], e.target.value)}
                    placeholder="الرئيسي"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Email Section */}
      <div className="bg-gray-700 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-purple-400 flex items-center">
            <FiMail className="mr-2 h-4 w-4" />
            Email Section Configuration
          </h4>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onAddItem('email');
            }}
            className="inline-flex items-center px-3 py-1 border border-green-500 rounded-md text-sm font-medium text-green-400 hover:bg-green-500 hover:text-white transition-colors"
          >
            <FiPlus className="mr-1 h-4 w-4" />
            Add Email
          </button>
        </div>
        
        {/* Email Section Title */}
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300">Email Section Title (English)</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.email.title.english}
              onChange={e => onUpdate(['sections', 'email', 'title', 'english'], e.target.value)}
              placeholder="Email"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Email Section Title (Arabic)</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.email.title.arabic}
              onChange={e => onUpdate(['sections', 'email', 'title', 'arabic'], e.target.value)}
              placeholder="البريد الإلكتروني"
            />
          </div>
        </div>

        {/* Email Addresses */}
        <div className="space-y-4">
          {contactInfo.sections.email.addresses.map((email, index) => (
            <div key={email.id} className="bg-gray-600 rounded-lg p-3 border border-gray-500">
              <div className="flex items-center justify-between mb-3">
                <h5 className="text-sm font-medium text-gray-200">Email #{index + 1}</h5>
                {contactInfo.sections.email.addresses.length > 1 && (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveItem('email', email.id);
                    }}
                    className="inline-flex items-center px-2 py-1 border border-red-500 rounded text-xs font-medium text-red-400 hover:bg-red-500 hover:text-white transition-colors"
                  >
                    <FiTrash2 className="mr-1 h-3 w-3" />
                    Remove
                  </button>
                )}
              </div>
              <div className="grid grid-cols-1 gap-3 lg:grid-cols-3">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Email Address</label>
                  <input
                    type="email"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={email.address}
                    onChange={e => onUpdate(['sections', 'email', 'addresses', index.toString(), 'address'], e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Label (EN)</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={email.label.english}
                    onChange={e => onUpdate(['sections', 'email', 'addresses', index.toString(), 'label', 'english'], e.target.value)}
                    placeholder="General Info"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Label (AR)</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={email.label.arabic}
                    onChange={e => onUpdate(['sections', 'email', 'addresses', index.toString(), 'label', 'arabic'], e.target.value)}
                    placeholder="معلومات عامة"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Address Section */}
      <div className="bg-gray-700 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-green-400 flex items-center">
            <FiMapPin className="mr-2 h-4 w-4" />
            Address Section Configuration
          </h4>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onAddItem('address');
            }}
            className="inline-flex items-center px-3 py-1 border border-green-500 rounded-md text-sm font-medium text-green-400 hover:bg-green-500 hover:text-white transition-colors"
          >
            <FiPlus className="mr-1 h-4 w-4" />
            Add Line
          </button>
        </div>
        
        {/* Address Section Title */}
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300">Address Section Title (English)</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.address.title.english}
              onChange={e => onUpdate(['sections', 'address', 'title', 'english'], e.target.value)}
              placeholder="Address"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">Address Section Title (Arabic)</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.address.title.arabic}
              onChange={e => onUpdate(['sections', 'address', 'title', 'arabic'], e.target.value)}
              placeholder="العنوان"
            />
          </div>
        </div>

        {/* Company Name */}
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300">Company Name (English)</label>
            <input
              type="text"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.address.company.english}
              onChange={e => onUpdate(['sections', 'address', 'company', 'english'], e.target.value)}
              placeholder="Company Name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300">اسم الشركة (عربي)</label>
            <input
              type="text"
              dir="rtl"
              className="mt-1 block w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
              value={contactInfo.sections.address.company.arabic}
              onChange={e => onUpdate(['sections', 'address', 'company', 'arabic'], e.target.value)}
              placeholder="اسم الشركة"
            />
          </div>
        </div>

        {/* Address Lines */}
        <div className="space-y-4">
          <h5 className="text-sm font-medium text-gray-300">Address Lines</h5>
          {contactInfo.sections.address.lines.map((line, index) => (
            <div key={line.id} className="bg-gray-600 rounded-lg p-3 border border-gray-500">
              <div className="flex items-center justify-between mb-3">
                <h6 className="text-sm font-medium text-gray-200">Line #{index + 1}</h6>
                {contactInfo.sections.address.lines.length > 1 && (
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveItem('address', line.id);
                    }}
                    className="inline-flex items-center px-2 py-1 border border-red-500 rounded text-xs font-medium text-red-400 hover:bg-red-500 hover:text-white transition-colors"
                  >
                    <FiTrash2 className="mr-1 h-3 w-3" />
                    Remove
                  </button>
                )}
              </div>
              <div className="grid grid-cols-1 gap-3 lg:grid-cols-2">
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">Address Line (English)</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={line.line.english}
                    onChange={e => onUpdate(['sections', 'address', 'lines', index.toString(), 'line', 'english'], e.target.value)}
                    placeholder="123 Business Avenue"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-300 mb-1">سطر العنوان (عربي)</label>
                  <input
                    type="text"
                    dir="rtl"
                    className="w-full px-3 py-2 bg-gray-500 border border-gray-400 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] focus:border-[#00C2FF]"
                    value={line.line.arabic}
                    onChange={e => onUpdate(['sections', 'address', 'lines', index.toString(), 'line', 'arabic'], e.target.value)}
                    placeholder="123 شارع الأعمال"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 