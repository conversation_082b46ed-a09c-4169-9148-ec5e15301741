/**
 * Cache management utilities for language switching
 */

// Clear localStorage items that might be language-specific
export const clearLanguageSpecificCache = () => {
  const keysToKeep = [
    'adminUser',
    'adminTokens',
    'adminAuth',
    'locale' // Keep the locale itself
  ];
  
  // Get all localStorage keys
  const allKeys = Object.keys(localStorage);
  
  // Remove keys that might contain language-specific content
  allKeys.forEach(key => {
    if (!keysToKeep.includes(key)) {
      // Remove cached API responses, content, etc.
      if (key.includes('cache') || 
          key.includes('content') || 
          key.includes('data') ||
          key.includes('hero') ||
          key.includes('about') ||
          key.includes('articles') ||
          key.includes('projects') ||
          key.includes('featured')) {
        localStorage.removeItem(key);
        console.log('🗑️ Cleared cache key:', key);
      }
    }
  });
};

// Clear sessionStorage
export const clearSessionCache = () => {
  // Clear all session storage except essential items
  const keysTo<PERSON>eep = ['debug', 'devtools'];
  
  const allKeys = Object.keys(sessionStorage);
  allKeys.forEach(key => {
    if (!keysToKeep.includes(key)) {
      sessionStorage.removeItem(key);
      console.log('🗑️ Cleared session key:', key);
    }
  });
};

// Force clear browser cache by adding cache-busting parameters
export const getCacheBustedUrl = (url: string): string => {
  const separator = url.includes('?') ? '&' : '?';
  const timestamp = Date.now();
  return `${url}${separator}_t=${timestamp}&_lang=${localStorage.getItem('locale') || 'en'}`;
};

// Clear Next.js router cache
export const clearNextJSCache = () => {
  // This will be used with router.refresh() in components
  if (typeof window !== 'undefined') {
    // Clear any cached fetch requests
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          if (cacheName.includes('next') || cacheName.includes('api')) {
            caches.delete(cacheName);
            console.log('🗑️ Cleared cache:', cacheName);
          }
        });
      });
    }
  }
};

// Clear component state by forcing re-mount
export const generateForceUpdateKey = (): string => {
  return `lang-switch-${Date.now()}`;
};

// Comprehensive cache clearing for language switch
export const clearAllLanguageCaches = async (): Promise<void> => {
  console.log('🧹 Starting comprehensive cache clear for language switch...');
  
  try {
    // 1. Clear localStorage
    clearLanguageSpecificCache();
    
    // 2. Clear sessionStorage  
    clearSessionCache();
    
    // 3. Clear Next.js/browser caches
    clearNextJSCache();
    
    // 4. Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
    
    console.log('✅ All language caches cleared successfully');
  } catch (error) {
    console.error('❌ Error clearing caches:', error);
  }
};

// Preload new language resources (optional optimization)
export const preloadLanguageResources = async (locale: string): Promise<void> => {
  console.log(`🔄 Preloading resources for ${locale}...`);
  
  // Preload critical API endpoints for the new language
  const criticalEndpoints = [
    `/api/admin/home-page/hero/?lang=${locale}`,
    `/api/admin/home-page/about-us/?lang=${locale}`,
    `/api/admin/home-page/featured-projects/?lang=${locale}`,
    `/api/admin/content/navigation/?lang=${locale}`
  ];
  
  // Fire and forget - don't wait for these
  criticalEndpoints.forEach(endpoint => {
    fetch(endpoint, { 
      method: 'HEAD',
      cache: 'no-cache' 
    }).catch(() => {
      // Ignore errors in preloading
    });
  });
}; 