"use client";

import { useState } from 'react';
import Link from 'next/link';
import { FiEdit2, FiTrash2, FiPlus, <PERSON>Search, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/fi';

// Mock project data - would normally come from an API or database
const MOCK_PROJECTS = [
  {
    id: 1,
    title: "Mazaya Heights",
    location: "Downtown Dubai",
    status: "Completed",
    category: "Residential",
    createdAt: "2023-05-15T09:00:00Z"
  },
  {
    id: 2,
    title: "Mazaya Business Park",
    location: "Business Bay",
    status: "Completed",
    category: "Commercial",
    createdAt: "2023-04-12T14:30:00Z"
  },
  {
    id: 3,
    title: "Mazaya Villas",
    location: "Palm Jumeirah",
    status: "Completed",
    category: "Residential",
    createdAt: "2023-03-28T11:15:00Z"
  },
  {
    id: 4,
    title: "Mazaya Retail Center",
    location: "Dubai Marina",
    status: "In Progress",
    category: "Commercial",
    createdAt: "2023-06-02T08:45:00Z"
  },
  {
    id: 5,
    title: "Mazaya Gardens",
    location: "Jumeirah Village Circle",
    status: "In Progress",
    category: "Residential",
    createdAt: "2023-07-20T13:00:00Z"
  },
  {
    id: 6,
    title: "Mazaya Mixed-Use Tower",
    location: "Sheikh Zayed Road",
    status: "Planned",
    category: "Mixed-Use",
    createdAt: "2023-08-05T10:30:00Z"
  },
  {
    id: 7,
    title: "Mazaya Waterfront",
    location: "Dubai Creek Harbour",
    status: "In Progress",
    category: "Residential",
    createdAt: "2023-06-18T09:20:00Z"
  },
  {
    id: 8,
    title: "Mazaya Tech Hub",
    location: "Dubai Internet City",
    status: "Planned",
    category: "Commercial",
    createdAt: "2023-08-24T16:45:00Z"
  }
];

export default function AdminProjects() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<number | null>(null);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Filter projects based on search and filters
  const filteredProjects = MOCK_PROJECTS.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.location.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || project.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });
  
  // Handle delete confirmation
  const handleDeleteClick = (projectId: number) => {
    setProjectToDelete(projectId);
    setShowDeleteModal(true);
  };
  
  const confirmDelete = () => {
    // In a real app, would make an API call to delete the project
    console.log(`Deleting project with ID: ${projectToDelete}`);
    setShowDeleteModal(false);
    setProjectToDelete(null);
    // Would then update the projects list or trigger a refetch
  };
  
  return (
    <div>
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Projects</h1>
          <p className="mt-2 text-sm text-gray-400">
            Manage your real estate development projects from here
          </p>
        </div>
                <Link          href="/admin/projects-page/projects/new"          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#00C2FF] hover:bg-[#009DB5] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#00C2FF]"        >
          <FiPlus className="-ml-1 mr-2 h-5 w-5" />
          New Project
        </Link>
      </div>

      {/* Filters */}
      <div className="mt-6 bg-gray-800 shadow rounded-lg p-4 sm:p-6 border border-gray-700">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-300">
              Search
            </label>
            <div className="relative rounded-md shadow-sm mt-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                name="search"
                id="search"
                className="bg-gray-700 focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full pl-10 py-2 sm:text-sm border-gray-600 rounded-md text-white h-10"
                placeholder="Search projects"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-300">
              Status
            </label>
            <select
              id="status"
              name="status"
              className="mt-1 block w-full pl-3 pr-10 py-2 sm:text-sm border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-[#00C2FF] focus:border-[#00C2FF] rounded-md h-10"
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
            >
              <option value="all">All</option>
              <option value="Completed">Completed</option>
              <option value="In Progress">In Progress</option>
              <option value="Planned">Planned</option>
            </select>
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-300">
              Category
            </label>
            <select
              id="category"
              name="category"
              className="mt-1 block w-full pl-3 pr-10 py-2 sm:text-sm border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-[#00C2FF] focus:border-[#00C2FF] rounded-md h-10"
              value={categoryFilter}
              onChange={e => setCategoryFilter(e.target.value)}
            >
              <option value="all">All</option>
              <option value="Residential">Residential</option>
              <option value="Commercial">Commercial</option>
              <option value="Mixed-Use">Mixed-Use</option>
            </select>
          </div>
        </div>
      </div>

      {/* Projects Table */}
      <div className="mt-6 flex flex-col">
        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div className="shadow overflow-hidden border-b border-gray-700 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-800">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Project
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Location
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Category
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Created
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-900 divide-y divide-gray-700">
                  {filteredProjects.map(project => (
                    <tr key={project.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">{project.title}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-400">{project.location}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            project.status === 'Completed' ? 'bg-green-900/50 text-green-400' : 
                            project.status === 'In Progress' ? 'bg-yellow-900/50 text-yellow-400' : 
                            'bg-blue-900/50 text-blue-400'
                          }`}
                        >
                          {project.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {project.category}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                        {formatDate(project.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <Link
                            href={`/projects/${project.id}`}
                            target="_blank"
                            className="text-gray-400 hover:text-gray-300"
                            title="View on site"
                          >
                            <FiEye className="h-5 w-5" />
                          </Link>
                                                    <Link                            href={`/admin/projects-page/projects/${project.id}`}                            className="text-indigo-400 hover:text-indigo-300"                            title="Edit"                          >
                            <FiEdit2 className="h-5 w-5" />
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(project.id)}
                            className="text-red-400 hover:text-red-300"
                            title="Delete"
                          >
                            <FiTrash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
              &#8203;
            </span>

            <div
              className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-gray-700"
              role="dialog"
              aria-modal="true"
              aria-labelledby="modal-headline"
            >
              <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-white" id="modal-headline">
                      Confirm Delete
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-300">
                        Are you sure you want to delete this project? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-gray-700">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={confirmDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteModal(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 