import React, { useState, useEffect } from 'react';
import { useSlate } from 'slate-react';
import { FiUsers, FiUserPlus, FiMessageCircle } from 'react-icons/fi';
import { CollaborationUser } from '../types';

// Mock data for collaborative users (in a real app, this would come from a real-time server)
const mockUsers: CollaborationUser[] = [
  {
    id: '1',
    name: '<PERSON>',
    color: '#FF5733',
    cursor: {
      path: [0, 0],
      offset: 5
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    color: '#33FF57',
    cursor: {
      path: [1, 0],
      offset: 2
    }
  }
];

interface CollaborationPanelProps {
  isOpen: boolean;
  togglePanel: () => void;
}

const CollaborationPanel: React.FC<CollaborationPanelProps> = ({ isOpen, togglePanel }) => {
  const editor = useSlate();
  const [users, setUsers] = useState<CollaborationUser[]>(mockUsers);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');

  // Mock function to invite a user
  const inviteUser = (email: string) => {
    console.log(`Inviting user with email: ${email}`);
    // In a real app, this would send an invitation via API
    const newUser: CollaborationUser = {
      id: `user-${Date.now()}`,
      name: email.split('@')[0],
      color: getRandomColor(),
    };
    
    setUsers([...users, newUser]);
    setInviteEmail('');
    setShowInviteForm(false);
  };

  // Generate a random color for new users
  const getRandomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  };

  // Simulate cursor movements (in a real app, this would use WebSockets)
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly move cursors for demo purposes
      setUsers(prevUsers => {
        return prevUsers.map(user => {
          if (Math.random() > 0.7) {
            return {
              ...user,
              cursor: {
                path: [Math.floor(Math.random() * 3), 0],
                offset: Math.floor(Math.random() * 10)
              }
            };
          }
          return user;
        });
      });
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Set up WebSocket connection in a real app
  useEffect(() => {
    // Connect to real-time collaboration server
    console.log('Connecting to collaboration server...');
    
    // Cleanup WebSocket on unmount
    return () => {
      console.log('Disconnecting from collaboration server...');
    };
  }, []);

  if (!isOpen) {
    return (
      <button
        className="p-2 bg-[#1a2349] rounded-full hover:bg-[#141b35] text-white"
        onClick={togglePanel}
        title="Collaboration"
      >
        <FiUsers size={18} />
      </button>
    );
  }

  return (
    <div className="absolute right-0 top-0 w-64 bg-[#141b35] border border-white/10 rounded-lg shadow-lg z-50 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-white font-medium">Collaborators</h3>
        <button 
          className="text-white/70 hover:text-white"
          onClick={togglePanel}
        >
          ×
        </button>
      </div>
      
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-white/70 text-sm">Online Users</span>
          <button
            className="text-xs bg-[#0A0F23] hover:bg-[#1a2349] text-white px-2 py-1 rounded-full flex items-center gap-1"
            onClick={() => setShowInviteForm(!showInviteForm)}
          >
            <FiUserPlus size={12} />
            <span>Invite</span>
          </button>
        </div>
        
        {showInviteForm && (
          <div className="mb-3 p-2 bg-[#0A0F23] rounded-lg">
            <input
              type="email"
              placeholder="Enter email"
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              className="w-full mb-2 p-1 text-sm bg-[#141b35] border border-white/10 rounded text-white"
            />
            <div className="flex justify-end gap-2">
              <button
                className="text-xs text-white/70 hover:text-white"
                onClick={() => setShowInviteForm(false)}
              >
                Cancel
              </button>
              <button
                className="text-xs bg-[rgb(var(--color-primary))] hover:bg-[rgb(var(--color-primary-hover))] text-white px-2 py-1 rounded"
                onClick={() => inviteUser(inviteEmail)}
              >
                Send
              </button>
            </div>
          </div>
        )}
        
        <div className="max-h-40 overflow-y-auto">
          {users.map(user => (
            <div 
              key={user.id} 
              className="flex items-center gap-2 p-2 hover:bg-[#0A0F23] rounded-lg"
            >
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: user.color }}
              ></div>
              <span className="text-white text-sm">{user.name}</span>
              <span className="ml-auto text-green-400 text-xs">Online</span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-4 pt-4 border-t border-white/10">
        <button className="w-full text-sm bg-[#0A0F23] hover:bg-[#1a2349] text-white px-3 py-2 rounded-lg flex items-center gap-2">
          <FiMessageCircle size={14} />
          <span>Open Chat</span>
        </button>
      </div>
    </div>
  );
};

export default CollaborationPanel; 