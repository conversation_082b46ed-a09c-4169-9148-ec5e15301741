import React from 'react';
import Link from 'next/link';

type ButtonProps = {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  href?: string;
  className?: string;
  onClick?: () => void;
};

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  href,
  className = '',
  onClick,
  ...props
}: ButtonProps & React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  // Base styles - Updated to match Hero button styles
  const baseStyles = "inline-flex items-center justify-center font-medium transition-all duration-300";
  
  // Variant styles - Updated to match Hero section buttons
  const variantStyles = {
    primary: "rounded-full border border-transparent bg-white text-gray-900 hover:scale-105",
    secondary: "rounded-full border border-white/30 bg-transparent text-white hover:bg-white/10", 
    outline: "rounded-full border border-white/30 bg-white/10 text-white hover:bg-white/20",
  };
  
  // Size styles
  const sizeStyles = {
    sm: "text-sm px-4 py-1.5",
    md: "px-6 py-3",
    lg: "text-lg px-8 py-3.5",
  };
  
  // Combined styles
  const buttonStyles = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;

  // Render as Link if href is provided
  if (href) {
    return (
      <Link href={href} className={buttonStyles} {...props}>
        {children}
      </Link>
    );
  }
  
  // Render as button otherwise
  return (
    <button className={buttonStyles} onClick={onClick} {...props}>
      {children}
    </button>
  );
};

export default Button;