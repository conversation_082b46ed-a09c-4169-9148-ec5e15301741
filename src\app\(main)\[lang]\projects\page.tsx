import { Metadata } from "next";
import ProjectsClient from "@/components/projects/ProjectsClient";
import ProjectsHero from "@/components/projects/ProjectsHero";
import ProjectsContainer from "@/components/projects/ProjectsContainer";

export const metadata: Metadata = {
  title: "Our Projects | Mazaya Capital",
  description: "Explore Mazaya Capital's diverse portfolio of residential, commercial, and mixed-use real estate development projects.",
};

// Project data (mock data)
const projects = [
  {
    id: 1,
    title: "Mazaya Heights",
    location: "Downtown Dubai",
    description: "Luxury residential tower with panoramic city views",
    image: "/images/project-1.jpg", // Replace with actual image paths
    slug: "mazaya-heights",
    category: "Residential",
    status: "Completed",
    features: ["Panoramic Views", "Smart Home", "Fitness Center", "Swimming Pool"],
  },
  {
    id: 2,
    title: "Mazaya Business Park",
    location: "Business Bay",
    description: "Premium office spaces designed for modern businesses",
    image: "/images/project-2.jpg", // Replace with actual image paths
    slug: "mazaya-business-park",
    category: "Commercial",
    status: "Completed",
    features: ["Conference Center", "24/7 Security", "Parking", "Retail Spaces"],
  },
  {
    id: 3,
    title: "Mazaya Villas",
    location: "Palm Jumeirah",
    description: "Exclusive beachfront villas with private pools",
    image: "/images/project-3.jpg", // Replace with actual image paths
    slug: "mazaya-villas",
    category: "Residential",
    status: "Completed",
    features: ["Beach Access", "Private Pool", "Smart Home", "Landscaped Gardens"],
  },
  {
    id: 4,
    title: "Mazaya Retail Center",
    location: "Dubai Marina",
    description: "Modern retail space in prime location",
    image: "/images/project-4.jpg", // Replace with actual image paths
    slug: "mazaya-retail-center",
    category: "Commercial",
    status: "In Progress",
    features: ["Prime Location", "High Foot Traffic", "Customizable Spaces", "Parking"],
  },
  {
    id: 5,
    title: "Mazaya Gardens",
    location: "Jumeirah Village Circle",
    description: "Family-friendly residential community with extensive gardens",
    image: "/images/project-5.jpg", // Replace with actual image paths
    slug: "mazaya-gardens",
    category: "Residential",
    status: "In Progress",
    features: ["Community Pool", "Children's Play Area", "Jogging Track", "BBQ Area"],
  },
  {
    id: 6,
    title: "Mazaya Mixed-Use Tower",
    location: "Sheikh Zayed Road",
    description: "Innovative mixed-use development combining residential, retail, and office spaces",
    image: "/images/project-6.jpg", // Replace with actual image paths
    slug: "mazaya-mixed-use-tower",
    category: "Mixed-Use",
    status: "Planned",
    features: ["Residential Units", "Office Spaces", "Retail Outlets", "Rooftop Amenities"],
  },
  {
    id: 7,
    title: "Mazaya Waterfront",
    location: "Dubai Creek Harbour",
    description: "Stunning waterfront apartments with creek and skyline views",
    image: "/images/project-7.jpg", // Replace with actual image paths
    slug: "mazaya-waterfront",
    category: "Residential",
    status: "In Progress",
    features: ["Waterfront Views", "Private Balconies", "Infinity Pool", "Yacht Club Access"],
  },
  {
    id: 8,
    title: "Mazaya Tech Hub",
    location: "Dubai Internet City",
    description: "Next-generation office spaces designed for tech companies",
    image: "/images/project-8.jpg", // Replace with actual image paths
    slug: "mazaya-tech-hub",
    category: "Commercial",
    status: "Planned",
    features: ["Smart Building", "Innovation Labs", "Co-Working Spaces", "Tech Lounge"],
  },
  {
    id: 9,
    title: "Mazaya Luxury Resort",
    location: "Bluewaters Island",
    description: "Premium mixed-use development with hotel, residences, and retail spaces",
    image: "/images/project-9.jpg", // Replace with actual image paths
    slug: "mazaya-luxury-resort",
    category: "Mixed-Use",
    status: "Planned",
    features: ["5-Star Hotel", "Beach Access", "Luxury Residences", "Gourmet Restaurants"],
  }
];

export default function ProjectsPage() {
  return (
    <div className="flex flex-col space-y-16 pb-16">
      <ProjectsHero />
      
      {/* Use the client component wrapper for projects section */}
      <ProjectsContainer>
        <ProjectsClient projects={projects} />
      </ProjectsContainer>
    </div>
  );
} 