import React, { useState } from 'react';
import { FiPlus, FiX, FiEdit, FiMove } from 'react-icons/fi';

interface ProjectGalleryProps {
  project: any;
  language: 'en' | 'ar';
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  setProject: React.Dispatch<React.SetStateAction<any>>;
}

const ProjectGallery: React.FC<ProjectGalleryProps> = ({ 
  project, 
  language, 
  handleInputChange,
  setProject
}) => {
  const [newGalleryImage, setNewGalleryImage] = useState<File | null>(null);
  const [newGalleryImageAltEn, setNewGalleryImageAltEn] = useState('');
  const [newGalleryImageAltAr, setNewGalleryImageAltAr] = useState('');
  const [editingGalleryIndex, setEditingGalleryIndex] = useState<number | null>(null);
  const [editingAltEn, setEditingAltEn] = useState('');
  const [editingAltAr, setEditingAltAr] = useState('');
  const [showNewImageForm, setShowNewImageForm] = useState(false);
  const [draggedItem, setDraggedItem] = useState<number | null>(null);

  // Handle file upload for gallery
  const handleGalleryImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setNewGalleryImage(e.target.files[0]);
    }
  };
  
  // Add gallery image
  const addGalleryImage = () => {
    if (newGalleryImage) {
      setProject({
        ...project,
        gallery: [...project.gallery, { 
          file: newGalleryImage, 
          altEn: newGalleryImageAltEn, 
          altAr: newGalleryImageAltAr 
        }]
      });
      setNewGalleryImage(null);
      setNewGalleryImageAltEn('');
      setNewGalleryImageAltAr('');
      setShowNewImageForm(false);
      
      // Reset the input by creating a temporary input and clearing it
      const fileInput = document.getElementById('new-gallery-image-input') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    }
  };
  
  // Drag and drop handlers for gallery reordering
  const handleDragStart = (index: number) => {
    setDraggedItem(index);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault(); // Allow drop
  };

  const handleDrop = (dropIndex: number) => {
    if (draggedItem === null || draggedItem === dropIndex) return;
    
    const newGallery = [...project.gallery];
    const draggedItemContent = newGallery[draggedItem];
    
    // Remove the dragged item
    newGallery.splice(draggedItem, 1);
    
    // Add it at the new position
    newGallery.splice(dropIndex, 0, draggedItemContent);
    
    // Update the project state with the new order
    setProject({
      ...project,
      gallery: newGallery
    });
    
    setDraggedItem(null);
  };
  
  // Handle starting to edit gallery image alt text
  const startEditGalleryItem = (index: number) => {
    setEditingGalleryIndex(index);
    setEditingAltEn(project.gallery[index].altEn);
    setEditingAltAr(project.gallery[index].altAr);
  };

  // Save edited gallery item alt text
  const saveGalleryItemEdit = () => {
    if (editingGalleryIndex !== null) {
      const updatedGallery = [...project.gallery];
      updatedGallery[editingGalleryIndex] = {
        ...updatedGallery[editingGalleryIndex],
        altEn: editingAltEn,
        altAr: editingAltAr
      };
      
      setProject({
        ...project,
        gallery: updatedGallery
      });
      
      // Reset editing state
      setEditingGalleryIndex(null);
      setEditingAltEn('');
      setEditingAltAr('');
    }
  };

  // Cancel editing gallery item
  const cancelGalleryItemEdit = () => {
    setEditingGalleryIndex(null);
    setEditingAltEn('');
    setEditingAltAr('');
  };

  return (
    <div className={`mt-6 bg-gray-800 shadow rounded-lg p-6 ${language === 'ar' ? 'text-right' : ''}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <h2 className="text-lg font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Project Gallery' : 'معرض صور المشروع'}
      </h2>
      
      <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6 mb-6">
        <div className="sm:col-span-6">
          <label htmlFor="galleryTitle" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Gallery Section Title' : 'عنوان قسم المعرض'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="galleryTitle"
              id="galleryTitle"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.galleryTitle : project.galleryTitleAr}
              onChange={handleInputChange}
              placeholder={language === 'en' ? "Project Gallery" : "معرض المشروع"}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>
        
        <div className="sm:col-span-6">
          <label htmlFor="gallerySubtitle" className="block text-sm font-medium text-gray-300">
            {language === 'en' ? 'Gallery Section Subtitle' : 'العنوان الفرعي لقسم المعرض'}
          </label>
          <div className="mt-1">
            <input
              type="text"
              name="gallerySubtitle"
              id="gallerySubtitle"
              className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
              value={language === 'en' ? project.gallerySubtitle : project.gallerySubtitleAr}
              onChange={handleInputChange}
              placeholder={language === 'en' ? "Experience the extraordinary design and premium finishes." : "استمتع بالتصميم الاستثنائي والتشطيبات الفاخرة."}
              dir={language === 'ar' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>
      </div>
      
      <h3 className="text-md font-medium text-gray-300 mb-4">
        {language === 'en' ? 'Gallery Images' : 'صور المعرض'}
      </h3>
      
      {/* Gallery Grid with Add Button and Drag-Drop */}
      <div className="mb-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {/* Existing Gallery Images */}
          {project.gallery.map((item: any, index: number) => (
            <div 
              key={index} 
              className={`bg-gray-700 rounded-lg overflow-hidden h-max flex flex-col relative ${draggedItem === index ? 'opacity-50' : 'opacity-100'}`}
              draggable={editingGalleryIndex !== index}
              onDragStart={() => handleDragStart(index)}
              onDragOver={handleDragOver}
              onDrop={() => handleDrop(index)}
            >
              {editingGalleryIndex === index ? (
                <>
                  <div className="h-48 bg-gray-800 overflow-hidden">
                    <div 
                      className="w-full h-full cursor-pointer group relative"
                      onClick={() => {
                        const fileInput = document.createElement('input');
                        fileInput.type = 'file';
                        fileInput.accept = 'image/*';
                        fileInput.onchange = (e) => {
                          const target = e.target as HTMLInputElement;
                          if (target.files && target.files[0]) {
                            const newFile = target.files[0];
                            // Update the gallery item with the new image but keep the alt text
                            const updatedGallery = [...project.gallery];
                            updatedGallery[editingGalleryIndex] = {
                              ...updatedGallery[editingGalleryIndex],
                              file: newFile
                            };
                            
                            setProject({
                              ...project,
                              gallery: updatedGallery
                            });
                          }
                        };
                        fileInput.click();
                      }}
                    >
                      <img 
                        src={URL.createObjectURL(item.file)} 
                        alt={item.altEn || `Gallery image ${index + 1}`}
                        className="object-cover w-full h-full" 
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center text-white">
                        <p className="text-sm font-medium">{language === 'en' ? 'Click to replace image' : 'انقر لاستبدال الصورة'}</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 flex-grow flex flex-col">
                    <div className="mb-3 mt-auto">
                      <label className="block text-xs font-medium text-gray-300 mb-1">
                        {language === 'en' ? 'English Alt Text' : 'النص البديل بالإنجليزية'}
                      </label>
                      <input
                        type="text"
                        value={editingAltEn}
                        onChange={(e) => setEditingAltEn(e.target.value)}
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-8 py-1 px-2"
                        dir="ltr"
                      />
                    </div>
                    
                    <div className="mb-3">
                      <label className="block text-xs font-medium text-gray-300 mb-1">
                        {language === 'en' ? 'Arabic Alt Text' : 'النص البديل بالعربية'}
                      </label>
                      <input
                        type="text"
                        value={editingAltAr}
                        onChange={(e) => setEditingAltAr(e.target.value)}
                        className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border-gray-600 rounded-md bg-gray-700 text-white h-8 py-1 px-2"
                        dir="rtl"
                      />
                    </div>
                    
                    <div className="flex justify-end space-x-2">
                      <button
                        type="button"
                        onClick={cancelGalleryItemEdit}
                        className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-500"
                      >
                        {language === 'en' ? 'Cancel' : 'إلغاء'}
                      </button>
                      <button
                        type="button"
                        onClick={saveGalleryItemEdit}
                        className="px-3 py-1 text-xs bg-[#00C2FF] text-white rounded hover:bg-[#009DB5]"
                      >
                        {language === 'en' ? 'Save' : 'حفظ'}
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="h-48 bg-gray-800 overflow-hidden relative group">
                    {project.gallery.length > 1 && (
                      <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded-full p-1.5 z-10 text-gray-300">
                        <FiMove className="h-4 w-4" />
                      </div>
                    )}
                    <img 
                      src={URL.createObjectURL(item.file)} 
                      alt={language === 'en' ? (item.altEn || `Gallery image ${index + 1}`) : (item.altAr || `صورة المعرض ${index + 1}`)} 
                      className="object-cover w-full h-full" 
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-50">
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          onClick={() => startEditGalleryItem(index)}
                          className="p-1 bg-blue-600 rounded-full text-white"
                          title={language === 'en' ? 'Edit alt text' : 'تحرير النص البديل'}
                        >
                          <FiEdit className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            const updatedGallery = [...project.gallery];
                            updatedGallery.splice(index, 1);
                            setProject({
                              ...project,
                              gallery: updatedGallery
                            });
                          }}
                          className="p-1 bg-red-600 rounded-full text-white"
                          title={language === 'en' ? 'Remove image' : 'إزالة الصورة'}
                        >
                          <FiX className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <p className="text-xs text-gray-400 truncate mb-1">{item.file.name}</p>
                    <div className="flex flex-col gap-0.5">
                      <p className="text-xs text-gray-300 truncate">
                        <span className="font-medium">EN:</span> {item.altEn || "(No alt text)"}
                      </p>
                      <p className="text-xs text-gray-300 truncate" dir="rtl">
                        <span className="font-medium">عربي:</span> {item.altAr || "(لا يوجد نص بديل)"}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>
          ))}
          
          {/* New Image Form or Add Button Card */}
          {showNewImageForm ? (
            <div className="bg-gray-700 rounded-lg overflow-hidden h-full flex flex-col relative border-2 border-gray-600 shadow-lg">
              <div className="h-48 bg-gray-800 flex items-center justify-center border-b border-gray-600">
                {newGalleryImage ? (
                  <div 
                    className="relative w-full h-full cursor-pointer group"
                    onClick={() => document.getElementById('new-gallery-image-input')?.click()}
                  >
                    <img 
                      src={URL.createObjectURL(newGalleryImage)} 
                      alt={language === 'en' ? "Gallery preview" : "معاينة المعرض"}
                      className="object-cover w-full h-full" 
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex flex-col items-center justify-center text-white">
                      <p className="text-sm font-medium mb-1">{language === 'en' ? 'Click to change image' : 'انقر لتغيير الصورة'}</p>
                      <p className="text-xs opacity-75">{newGalleryImage.name}</p>
                    </div>
                  </div>
                ) : (
                  <div 
                    className="w-full h-full flex items-center justify-center cursor-pointer bg-gray-750 hover:bg-gray-700"
                    onClick={() => document.getElementById('new-gallery-image-input')?.click()}
                  >
                    <div className="text-center">
                      <p className="text-gray-300 font-medium mb-2">
                        {language === 'en' ? 'Click to select image' : 'انقر لتحديد صورة'}
                      </p>
                      <div className="w-12 h-12 rounded-full bg-[#00C2FF] flex items-center justify-center mx-auto">
                        <FiPlus className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </div>
                )}
                <input
                  type="file"
                  id="new-gallery-image-input"
                  accept="image/*"
                  onChange={handleGalleryImageChange}
                  className="hidden"
                />
              </div>
              
              <div className="p-5 flex-grow flex flex-col bg-gray-750">
                <div className="mb-4">
                  <p className="block text-sm font-medium text-gray-300 mb-1">
                    {language === 'en' ? 'English Alt Text' : 'النص البديل بالإنجليزية'}
                  </p>
                  <input
                    type="text"
                    value={newGalleryImageAltEn}
                    onChange={(e) => setNewGalleryImageAltEn(e.target.value)}
                    placeholder="Describe this image"
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                    dir="ltr"
                  />
                </div>
                
                <div className="mb-5">
                  <p className="block text-sm font-medium text-gray-300 mb-1">
                    {language === 'en' ? 'Arabic Alt Text' : 'النص البديل بالعربية'}
                  </p>
                  <input
                    type="text"
                    value={newGalleryImageAltAr}
                    onChange={(e) => setNewGalleryImageAltAr(e.target.value)}
                    placeholder="وصف هذه الصورة"
                    className="shadow-sm focus:ring-[#00C2FF] focus:border-[#00C2FF] block w-full sm:text-sm border border-gray-600 rounded-md bg-gray-700 text-white h-10 py-2 px-3"
                    dir="rtl"
                  />
                </div>
                
                <div className="flex justify-end space-x-2 mt-auto">
                  <button
                    type="button"
                    onClick={() => {
                      setNewGalleryImage(null);
                      setNewGalleryImageAltEn('');
                      setNewGalleryImageAltAr('');
                      setShowNewImageForm(false);
                      const fileInput = document.getElementById('new-gallery-image-input') as HTMLInputElement;
                      if (fileInput) fileInput.value = '';
                    }}
                    className="px-4 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-500"
                  >
                    {language === 'en' ? 'Cancel' : 'إلغاء'}
                  </button>
                  <button
                    type="button"
                    onClick={addGalleryImage}
                    disabled={!newGalleryImage}
                    className={`px-4 py-2 text-sm font-medium text-white rounded ${
                      newGalleryImage ? 'bg-[#00C2FF] hover:bg-[#009DB5]' : 'bg-gray-600 cursor-not-allowed'
                    }`}
                  >
                    {language === 'en' ? 'Save' : 'حفظ'}
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div 
              className="bg-gray-750 rounded-lg overflow-hidden flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors border-2 border-dashed border-gray-600 h-48"
              onClick={() => setShowNewImageForm(true)}
            >
              <div className="text-center">
                <div className="w-16 h-16 rounded-full bg-[#00C2FF] flex items-center justify-center mx-auto mb-2">
                  <FiPlus className="h-8 w-8 text-white" />
                </div>
                <p className="text-gray-300 font-medium">
                  {language === 'en' ? 'Add New Image' : 'إضافة صورة جديدة'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <p className="mt-1 text-xs text-gray-400 mb-4">
        {language === 'en' ? 'Upload images for the project gallery with descriptive alt text for accessibility. Drag and drop to reorder.' : 'قم بتحميل صور لمعرض المشروع مع نص بديل وصفي للوصول. اسحب وأفلت لإعادة الترتيب.'}
      </p>
    </div>
  );
};

export default ProjectGallery; 