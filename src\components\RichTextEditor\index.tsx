import React, { useState, useCallback, useMemo, useEffect } from 'react';
import isHotkey from 'is-hotkey';
import { Editable, withReact, useSlate, Slate, ReactEditor } from 'slate-react';
import { Editor, Transforms, Element as SlateElement, createEditor, Node, Text, BaseEditor } from 'slate';
import { withHistory } from 'slate-history';
import { 
  FiBold, FiItalic, FiLink, FiList, FiAlignLeft, FiAlignCenter,
  FiAlignRight, FiType, FiCode, FiImage, FiFileText, FiAlignJustify,
  FiDownload, FiUpload, FiCpu, FiFunction, FiBarChart2, FiClock,
  FiMessageSquare, FiUsers, FiGrid, FiColumns, FiBookmark
} from 'react-icons/fi';
import { CustomElement, CustomText } from './types';

// Import our custom components
import TableToolbar from './components/TableToolbar';
import CodeBlockToolbar from './components/CodeBlockToolbar';
import TextFormatToolbar from './components/TextFormatToolbar';
import FileUploadToolbar from './components/FileUploadToolbar';
import MathEquationToolbar from './components/MathEquationToolbar';
import AIToolbar from './components/AIToolbar';
import ImportExportToolbar from './components/ImportExportToolbar';
import CollaborationPanel from './components/CollaborationPanel';
import CommentsPanel from './components/CommentsPanel';
import RevisionHistory from './components/RevisionHistory';
import DocumentAnalytics from './components/DocumentAnalytics';

declare module 'slate' {
  interface CustomTypes {
    Editor: BaseEditor & ReactEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

// Hotkeys for formatting shortcuts
const HOTKEYS = {
  'mod+b': 'bold',
  'mod+i': 'italic',
  'mod+u': 'underline',
  'mod+`': 'code',
  'mod+shift+1': 'heading-one',
  'mod+shift+2': 'heading-two',
  'mod+shift+3': 'heading-three',
  'mod+shift+l': 'bulleted-list',
  'mod+shift+n': 'numbered-list',
  'mod+shift+q': 'block-quote',
  'mod+shift+s': 'save',
  'mod+shift+c': 'comment',
};

// Define component props types
interface RichTextEditorProps {
  value?: CustomElement[];
  onChange: (value: CustomElement[]) => void;
  placeholder?: string;
  error?: string;
}

// Default initial value for the editor
const initialValue: CustomElement[] = [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

// Define rich text editor component
const RichTextEditor = ({ value, onChange, placeholder, error }: RichTextEditorProps) => {
  const renderElement = useCallback(props => <Element {...props} />, []);
  const renderLeaf = useCallback(props => <Leaf {...props} />, []);
  const editor = useMemo(() => withHistory(withReact(createEditor())), []);
  
  // Always ensure we have a valid array for Slate
  const editorValue = useMemo(() => {
    if (!value || !Array.isArray(value) || value.length === 0) {
      console.log('Using default editor value');
      return initialValue;
    }
    return value;
  }, [value]);
  
  const handleChange = (newValue: CustomElement[]) => {
    if (onChange && Array.isArray(newValue)) {
      onChange(newValue);
    }
  };

  // State for various panels
  const [showCollaborationPanel, setShowCollaborationPanel] = useState(false);
  const [showCommentsPanel, setShowCommentsPanel] = useState(false);
  const [showRevisionPanel, setShowRevisionPanel] = useState(false);
  const [showAnalyticsPanel, setShowAnalyticsPanel] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  // Update word count on content change
  useEffect(() => {
    if (editor) {
      const text = Node.string(editor);
      const words = text.trim().split(/\s+/).filter(Boolean);
      setWordCount(words.length);
    }
  }, [editor.children]);

  // Handle keyboard shortcuts for special actions
  const handleKeyDown = (event: React.KeyboardEvent) => {
    // Handle formatting hotkeys
    for (const hotkey in HOTKEYS) {
      if (isHotkey(hotkey, event)) {
        event.preventDefault();
        const mark = HOTKEYS[hotkey];
        
        // Special commands
        if (mark === 'save') {
          // We could trigger a save action here
          console.log('Save triggered via keyboard shortcut');
          return;
        }
        
        if (mark === 'comment') {
          // Toggle comments panel
          setShowCommentsPanel(!showCommentsPanel);
          return;
        }
        
        // Handle block formatting
        if (['heading-one', 'heading-two', 'heading-three', 'bulleted-list', 'numbered-list', 'block-quote'].includes(mark)) {
          toggleBlock(editor, mark);
          return;
        }
        
        // Handle mark formatting
        toggleMark(editor, mark);
      }
    }
  };

  return (
    <div className="rich-text-editor">
      <Slate editor={editor} initialValue={editorValue} onChange={handleChange}>
        <div className="mb-2 flex flex-wrap gap-2 bg-[#0A0F23] p-2 rounded-t-lg border-t border-l border-r border-white/10">
          {/* Formatting Toolbar */}
          <div className="flex flex-wrap gap-2">
            <MarkButton format="bold" icon={<FiBold />} />
            <MarkButton format="italic" icon={<FiItalic />} />
            <MarkButton format="underline" icon={<span className="underline">U</span>} />
            <MarkButton format="code" icon={<FiCode />} />
            <TextFormatToolbar icon={<FiType />} />
            <div className="border-l border-white/10 mx-1 h-8"></div>
            
            <BlockButton format="heading-one" icon={<span className="font-bold">H1</span>} />
            <BlockButton format="heading-two" icon={<span className="font-bold">H2</span>} />
            <BlockButton format="heading-three" icon={<span className="font-bold">H3</span>} />
            <BlockButton format="block-quote" icon={<FiFileText />} />
            <BlockButton format="numbered-list" icon={<span className="font-bold">1.</span>} />
            <BlockButton format="bulleted-list" icon={<FiList />} />
            <div className="border-l border-white/10 mx-1 h-8"></div>
            
            <AlignButton format="left" icon={<FiAlignLeft />} />
            <AlignButton format="center" icon={<FiAlignCenter />} />
            <AlignButton format="right" icon={<FiAlignRight />} />
            <AlignButton format="justify" icon={<FiAlignJustify />} />
            <div className="border-l border-white/10 mx-1 h-8"></div>
          </div>
          
          {/* Advanced Content Toolbar */}
          <div className="flex flex-wrap gap-2">
            <TableToolbar icon={<FiGrid />} />
            <FileUploadToolbar icon={<FiImage />} />
            <CodeBlockToolbar icon={<FiCode />} />
            <MathEquationToolbar icon={<FiFunction />} />
            <BlockButton format="column-layout" icon={<FiColumns />} />
            <BlockButton format="page-break" icon={<div className="w-4 h-0.5 bg-white"></div>} />
            <BlockButton format="toc" icon={<FiBookmark />} />
            <div className="border-l border-white/10 mx-1 h-8"></div>
          </div>
          
          {/* Utility Toolbar */}
          <div className="flex flex-wrap gap-2 ml-auto">
            <div className="text-white/50 text-xs flex items-center mr-2">
              {wordCount} words
            </div>
            
            <ImportExportToolbar icon={<FiDownload />} value={editorValue} />
            <AIToolbar icon={<FiCpu />} />
            
            <div className="relative">
              <DocumentAnalytics 
                isOpen={showAnalyticsPanel}
                togglePanel={() => setShowAnalyticsPanel(!showAnalyticsPanel)}
              />
            </div>
            
            <div className="relative">
              <RevisionHistory 
                isOpen={showRevisionPanel}
                togglePanel={() => setShowRevisionPanel(!showRevisionPanel)}
                value={editorValue}
                onChange={onChange}
              />
            </div>
            
            <div className="relative">
              <CommentsPanel 
                isOpen={showCommentsPanel}
                togglePanel={() => setShowCommentsPanel(!showCommentsPanel)}
              />
            </div>
            
            <div className="relative">
              <CollaborationPanel 
                isOpen={showCollaborationPanel}
                togglePanel={() => setShowCollaborationPanel(!showCollaborationPanel)}
              />
            </div>
          </div>
        </div>
        
        <div className={`p-3 bg-[#0A0F23] border ${error ? 'border-red-500' : 'border-white/10'} rounded-b-lg text-white focus-within:border-[rgb(var(--color-primary))]/50 focus-within:ring-1 focus-within:ring-[rgb(var(--color-primary))]/50 min-h-[300px]`}>
          <Editable
            renderElement={renderElement}
            renderLeaf={renderLeaf}
            placeholder={placeholder}
            spellCheck
            autoFocus
            onKeyDown={handleKeyDown}
            className="outline-none min-h-[280px]"
          />
        </div>
      </Slate>
      {error && <p className="mt-1 text-red-500 text-sm">{error}</p>}
    </div>
  );
};

// Define props for the Element component
interface ElementProps {
  attributes: any;
  children: React.ReactNode;
  element: CustomElement;
}

// Element component to render different block types
const Element = ({ attributes, children, element }: ElementProps) => {
  const style = { textAlign: element.align };
  
  switch (element.type) {
    case 'block-quote':
      return (
        <blockquote
          style={style}
          className="border-l-4 border-[rgb(var(--color-primary))]/50 pl-4 italic text-white/80"
          {...attributes}
        >
          {children}
        </blockquote>
      );
    case 'bulleted-list':
      return (
        <ul style={style} className="list-disc pl-10" {...attributes}>
          {children}
        </ul>
      );
    case 'heading-one':
      return (
        <h1 style={style} className="text-3xl font-bold my-4" {...attributes}>
          {children}
        </h1>
      );
    case 'heading-two':
      return (
        <h2 style={style} className="text-2xl font-bold my-3" {...attributes}>
          {children}
        </h2>
      );
    case 'heading-three':
      return (
        <h3 style={style} className="text-xl font-bold my-2" {...attributes}>
          {children}
        </h3>
      );
    case 'list-item':
      return (
        <li style={style} {...attributes}>
          {children}
        </li>
      );
    case 'numbered-list':
      return (
        <ol style={style} className="list-decimal pl-10" {...attributes}>
          {children}
        </ol>
      );
    case 'table':
      return (
        <div className="my-4 overflow-x-auto" {...attributes}>
          <table className="min-w-full border border-white/20 text-sm">
            <tbody>{children}</tbody>
          </table>
        </div>
      );
    case 'table-row':
      return (
        <tr className="border-b border-white/20" {...attributes}>
          {children}
        </tr>
      );
    case 'table-cell':
      return (
        <td 
          className="border-r border-white/20 p-2 last:border-r-0"
          colSpan={element.colspan}
          rowSpan={element.rowspan}
          {...attributes}
        >
          {children}
        </td>
      );
    case 'code-block':
      return (
        <div className="my-4 bg-[#0b1023] rounded-lg p-4 overflow-x-auto font-mono" {...attributes}>
          <pre className="text-sm" data-language={element.language || 'plaintext'}>
            <code>{children}</code>
          </pre>
        </div>
      );
    case 'image':
      return (
        <figure style={style} className="my-4" {...attributes}>
          <div contentEditable={false} className="flex justify-center">
            <img 
              src={element.url} 
              alt={element.alt || ''} 
              className="max-w-full rounded-lg"
              style={{
                width: element.width ? element.width : undefined,
                height: element.height ? element.height : undefined,
              }}
            />
          </div>
          {element.caption && (
            <figcaption className="text-center text-white/70 text-sm mt-2">
              {element.caption}
            </figcaption>
          )}
          {children}
        </figure>
      );
    case 'video':
      return (
        <figure style={style} className="my-4" {...attributes}>
          <div contentEditable={false} className="flex justify-center">
            <video 
              src={element.url} 
              controls
              className="max-w-full rounded-lg"
              style={{
                width: element.width ? element.width : undefined,
                height: element.height ? element.height : undefined,
              }}
            />
          </div>
          {element.caption && (
            <figcaption className="text-center text-white/70 text-sm mt-2">
              {element.caption}
            </figcaption>
          )}
          {children}
        </figure>
      );
    case 'file-embed':
      return (
        <div style={style} className="my-4 p-3 bg-[#1a2349] rounded-lg" {...attributes}>
          <a 
            href={element.url}
            download
            className="flex items-center gap-2 text-white hover:text-[rgb(var(--color-primary))]"
          >
            <FiFileText size={18} />
            <span>{children}</span>
          </a>
        </div>
      );
    case 'math-equation':
      return (
        <div 
          className="my-4 p-4 bg-[#1a2349] rounded-lg text-center" 
          data-equation={element.equation}
          {...attributes}
        >
          <div contentEditable={false} className="math-content">
            {/* In a real implementation, we would render this with KaTeX or MathJax */}
            <div className="text-white/80 font-mono">{element.equation}</div>
          </div>
          {children}
        </div>
      );
    case 'toc':
      return (
        <div className="my-4 p-4 bg-[#1a2349] rounded-lg" {...attributes}>
          <div className="text-white font-medium mb-2">Table of Contents</div>
          <div className="border-t border-white/10 pt-2">
            {/* In a real implementation, this would be dynamically generated */}
            <div className="text-white/70 text-sm">
              Generated table of contents would appear here
            </div>
          </div>
          {children}
        </div>
      );
    case 'column-layout':
      const columns = element.columns || 2;
      return (
        <div 
          className="my-4"
          style={{ 
            columnCount: columns, 
            columnGap: '2rem',
            columnRule: '1px solid rgba(255, 255, 255, 0.1)'
          }}
          {...attributes}
        >
          {children}
        </div>
      );
    case 'page-break':
      return (
        <div className="my-8 relative" {...attributes}>
          <hr className="border-t border-white/20 border-dashed" />
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#101736] px-4 text-white/50 text-xs">
            Page Break
          </div>
          {children}
        </div>
      );
    case 'footnote':
      return (
        <sup 
          id={`footnote-ref-${element.footnoteId}`}
          className="text-[rgb(var(--color-primary))]"
          {...attributes}
        >
          {children}
        </sup>
      );
    case 'endnote':
      return (
        <div 
          id={`footnote-${element.footnoteId}`}
          className="text-sm text-white/70 border-t border-white/10 pt-2 mt-8"
          {...attributes}
        >
          {children}
        </div>
      );
    default:
      return (
        <p style={style} className="mb-3" {...attributes}>
          {children}
        </p>
      );
  }
};

// Define props for the Leaf component
interface LeafProps {
  attributes: any;
  children: React.ReactNode;
  leaf: CustomText;
}

// Leaf component to render text formatting
const Leaf = ({ attributes, children, leaf }: LeafProps) => {
  let leafNode = <>{children}</>;

  if (leaf.bold) {
    leafNode = <strong>{leafNode}</strong>;
  }

  if (leaf.italic) {
    leafNode = <em>{leafNode}</em>;
  }

  if (leaf.underline) {
    leafNode = <u>{leafNode}</u>;
  }

  if (leaf.code) {
    leafNode = <code className="bg-white/10 px-1 py-0.5 rounded font-mono">{leafNode}</code>;
  }

  if (leaf.strikethrough) {
    leafNode = <s>{leafNode}</s>;
  }

  if (leaf.superscript) {
    leafNode = <sup>{leafNode}</sup>;
  }

  if (leaf.subscript) {
    leafNode = <sub>{leafNode}</sub>;
  }

  if (leaf.highlight) {
    leafNode = <mark className="bg-yellow-300 text-black">{leafNode}</mark>;
  }

  // Apply inline styles if present
  const style: React.CSSProperties = {};
  
  if (leaf.color) {
    style.color = leaf.color;
  }
  
  if (leaf.bgColor) {
    style.backgroundColor = leaf.bgColor;
  }
  
  if (leaf.fontSize) {
    style.fontSize = leaf.fontSize;
  }
  
  if (leaf.fontFamily) {
    style.fontFamily = leaf.fontFamily;
  }

  return (
    <span {...attributes} style={style}>
      {leafNode}
    </span>
  );
};

// Define props for the button components
interface ButtonProps {
  format: string;
  icon: React.ReactNode;
}

// Button component for text formatting
const MarkButton = ({ format, icon }: ButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className={`px-3 py-1.5 ${isMarkActive(editor, format) ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
      onMouseDown={event => {
        event.preventDefault();
        toggleMark(editor, format);
      }}
    >
      {icon}
    </button>
  );
};

// Button component for block formatting
const BlockButton = ({ format, icon }: ButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className={`px-3 py-1.5 ${isBlockActive(editor, format) ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
      onMouseDown={event => {
        event.preventDefault();
        toggleBlock(editor, format);
      }}
    >
      {icon}
    </button>
  );
};

// Button component for text alignment
const AlignButton = ({ format, icon }: ButtonProps) => {
  const editor = useSlate();
  
  return (
    <button
      type="button"
      className={`px-3 py-1.5 ${isAlignActive(editor, format) ? 'bg-[#1a2349]' : 'bg-[#0A0F23]'} hover:bg-[#141b35] text-white rounded border border-white/10 text-sm flex items-center justify-center`}
      onMouseDown={event => {
        event.preventDefault();
        toggleAlign(editor, format);
      }}
    >
      {icon}
    </button>
  );
};

// Helper to check if a mark is active
const isMarkActive = (editor, format) => {
  const marks = Editor.marks(editor);
  return marks ? marks[format] === true : false;
};

// Helper to toggle a mark
const toggleMark = (editor, format) => {
  const isActive = isMarkActive(editor, format);

  if (isActive) {
    Editor.removeMark(editor, format);
  } else {
    Editor.addMark(editor, format, true);
  }
};

// Helper to check if a block type is active
const isBlockActive = (editor, format) => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n.type === format,
    })
  );

  return !!match;
};

// Helper to check if alignment is active
const isAlignActive = (editor, format) => {
  const { selection } = editor;
  if (!selection) return false;

  const [match] = Array.from(
    Editor.nodes(editor, {
      at: Editor.unhangRange(editor, selection),
      match: n =>
        !Editor.isEditor(n) &&
        SlateElement.isElement(n) &&
        n.align === format,
    })
  );

  return !!match;
};

// Helper to toggle block type
const toggleBlock = (editor, format) => {
  const isActive = isBlockActive(editor, format);
  const isList = ['numbered-list', 'bulleted-list'].includes(format);

  Transforms.unwrapNodes(editor, {
    match: n =>
      !Editor.isEditor(n) &&
      SlateElement.isElement(n) &&
      ['numbered-list', 'bulleted-list'].includes(n.type),
    split: true,
  });

  // Special handling for column layout and page breaks
  if (format === 'column-layout' && !isActive) {
    Transforms.insertNodes(editor, {
      type: 'column-layout',
      columns: 2,
      children: [
        { type: 'paragraph', children: [{ text: 'Column content here...' }] }
      ]
    });
    return;
  }

  if (format === 'page-break' && !isActive) {
    Transforms.insertNodes(editor, {
      type: 'page-break',
      children: [{ text: '' }]
    });
    return;
  }

  if (format === 'toc' && !isActive) {
    Transforms.insertNodes(editor, {
      type: 'toc',
      children: [{ text: '' }]
    });
    return;
  }

  const newProperties: Partial<SlateElement> = {
    type: isActive ? 'paragraph' : isList ? 'list-item' : format,
  };
  
  Transforms.setNodes<SlateElement>(editor, newProperties);

  if (!isActive && isList) {
    const block = { type: format, children: [] };
    Transforms.wrapNodes(editor, block);
  }
};

// Helper to toggle alignment
const toggleAlign = (editor, format) => {
  const isActive = isAlignActive(editor, format);
  const newProperties: Partial<SlateElement> = {
    align: isActive ? undefined : format,
  };
  
  Transforms.setNodes<SlateElement>(editor, newProperties);
};

export default RichTextEditor; 