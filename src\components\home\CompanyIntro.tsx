"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { motion, useScroll, useTransform } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import dynamic from "next/dynamic";

// Global cache for failed images - prevents retries across all instances
const failedImageCache = new Set<string>();

// Dynamically import the architectural background with no SSR
const CompanyIntroBackground = dynamic(
  () => import("./CompanyIntroBackground"),
  { ssr: false }
);

// About Us content interface
interface AboutUsContent {
  id: number;
  badge: string;
  title: string;
  subtitle: string;
  description: string;
  imageOverlay: {
    badge: string;
    text: string;
  };
  mainImage: string;
  created_at: string;
  updated_at: string;
}

// Animated Background Component with particles and connections
const AnimatedBackground = () => {
  const [particles, setParticles] = useState<{ id: number; width: number; height: number; top: number; left: number; opacity: number; animation: string }[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);
  
  // Generate particles only on client-side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const newParticles = Array.from({ length: 75 }).map((_, i) => ({
        id: i,
        width: Math.random() * 2 + 0.5,
        height: Math.random() * 2 + 0.5,
        top: Math.random() * 100,
        left: Math.random() * 100,
        opacity: Math.random() * 0.5 + 0.2,
        animation: `float-particle ${Math.random() * 15 + 15}s linear infinite ${Math.random() * 5}s`
      }));
      
      setParticles(newParticles);
      setIsHydrated(true);
    }
  }, []);
  
  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Deep gradient background with multiple colors - updated to match Hero section */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a0e29] via-[#0b1233] to-[#121f52] z-0"></div>
      
      {/* Animated gradient mesh - colors aligned with project theme */}
      <div className="absolute inset-0 z-1 opacity-60">
        <div className="absolute top-0 end-0 w-full h-full bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-indigo-500/20 via-transparent to-transparent animate-pulse-slow"></div>
        <div className="absolute bottom-0 start-0 w-full h-full bg-[radial-gradient(ellipse_at_bottom_left,_var(--tw-gradient-stops))] from-purple-500/20 via-transparent to-transparent animate-pulse-slow"></div>
        <div className="absolute top-1/2 start-1/2 w-full h-full transform -translate-x-1/2 -translate-y-1/2 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-indigo-600/10 via-transparent to-transparent animate-pulse-slow"></div>
      </div>
      
      {/* Dynamic particles - small dots, styled like stars in other sections */}
      <div className="absolute inset-0 z-2">
        {isHydrated && particles.map((particle) => (
          <div 
            key={`particle-${particle.id}`}
            className="absolute rounded-full bg-white"
            style={{
              width: `${particle.width}px`,
              height: `${particle.height}px`,
              top: `${particle.top}%`,
              left: `${particle.left}%`,
              opacity: particle.opacity,
              animation: particle.animation,
              boxShadow: '0 0 4px rgba(255,255,255,0.5)'
            }}
          />
        ))}
      </div>
      
      {/* Animated building silhouettes at the bottom - matching Hero cityscape */}
      <div className="absolute bottom-0 start-0 w-full h-1/3 z-2 opacity-20">
        <svg viewBox="0 0 1440 320" className="w-full h-full" preserveAspectRatio="none">
          <path 
            fill="#0f1a4a" 
            d="M0,224L48,218.7C96,213,192,203,288,181.3C384,160,480,128,576,149.3C672,171,768,245,864,245.3C960,245,1056,171,1152,144C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
            className="animate-slow-pulse"
          ></path>
          <path 
            fill="#061130" 
            d="M0,64L80,96C160,128,320,192,480,192C640,192,800,128,960,112C1120,96,1280,128,1360,144L1440,160L1440,320L1360,320C1280,320,1120,320,960,320C800,320,640,320,480,320C320,320,160,320,80,320L0,320Z"
            className="animate-slow-pulse opacity-90" style={{ animationDelay: '2s' }}
          ></path>
        </svg>
      </div>
      
      {/* Blueprint grid overlay */}
      <div className="absolute inset-0 z-3 opacity-10">
        <div className="h-full w-full grid grid-cols-12 grid-rows-12">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={`col-${i}`} className="border-s border-white/5 h-full">
              {i === 0 && <div className="border-e border-white/5 h-full w-full"></div>}
            </div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={`row-${i}`} className="col-span-full border-t border-white/5 h-0"></div>
          ))}
        </div>
      </div>
      
      {/* Floating architectural elements */}
      <div className="absolute inset-0 z-3 opacity-20 overflow-hidden">
        <div className="absolute -top-[20%] -end-[10%] w-[40%] h-[40%] border-2 border-indigo-400/10 rounded-full rotate-45 animate-slow-spin"></div>
        <div className="absolute top-[60%] -start-[5%] w-[20%] h-[20%] border-2 border-purple-400/5 rounded-full animate-slow-spin-reverse"></div>
        <div className="absolute top-[30%] start-[20%] w-[15%] h-[15%] border border-indigo-400/10">
          <div className="absolute inset-0 border border-indigo-400/10 rotate-45"></div>
        </div>
      </div>
      
      {/* Noise texture overlay */}
      <div className="absolute inset-0 z-4 opacity-[0.03] pointer-events-none bg-[url('/images/noise.png')] bg-repeat"></div>
    </div>
  );
};

// Split text animation component from Hero
const SplitText = ({ text, className }: { text: string, className?: string }) => {
  // Check if text contains Arabic characters
  const isArabic = /[\u0600-\u06FF]/.test(text);
  
  if (isArabic) {
    // For Arabic text, split by words to preserve letter connections
    const words = text.split(" ");
    return (
      <span className={className}>
        {words.map((word, wordIndex) => (
          <motion.span
            key={wordIndex}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ 
              duration: 0.6, 
              delay: 0.1 * wordIndex,
              ease: [0.215, 0.61, 0.355, 1]
            }}
            className="inline-block"
          >
            {word}
            {wordIndex < words.length - 1 && "\u00A0"}
          </motion.span>
        ))}
      </span>
    );
  } else {
    // For English text, split by characters for the original effect
    return (
      <span className={className}>
        {text.split("").map((char, index) => (
          <motion.span
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ 
              duration: 0.6, 
              delay: 0.05 * index,
              ease: [0.215, 0.61, 0.355, 1]
            }}
            className="inline-block"
          >
            {char === " " ? "\u00A0" : char}
          </motion.span>
        ))}
      </span>
    );
  }
};

const CompanyIntro = () => {
  const { locale } = useLanguage();
  const [aboutUsContent, setAboutUsContent] = useState<AboutUsContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  const textY = useTransform(scrollYProgress, [0, 1], [50, -50]);
  const imageY = useTransform(scrollYProgress, [0, 1], [20, -20]);
  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.8, 1], [0, 1, 1, 0.8]);

  // Fetch About Us content from API
  useEffect(() => {
    const fetchAboutUsContent = async () => {
      try {
        setIsLoading(true);
        setError(null);
        setImageError(false); // Reset image error state
        setImageLoaded(false); // Reset image loaded state
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
        const endpoint = `/api/home-page/about-us/${locale}/`;
        
        console.log('🔄 Fetching About Us content from:', `${apiBaseUrl}${endpoint}`);
        
        const response = await fetch(`${apiBaseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();
        console.log('📥 About Us content response:', data);

        if (data.success && data.data) {
          setAboutUsContent(data.data);
        } else {
          console.error('Failed to fetch About Us content:', data.message);
          setError(data.message || 'Failed to load content');
          // Keep aboutUsContent as null to fall back to static content
        }
      } catch (error) {
        console.error('Error fetching About Us content:', error);
        setError('Network error occurred');
        // Keep aboutUsContent as null to fall back to static content
      } finally {
        setIsLoading(false);
      }
    };

    fetchAboutUsContent();
  }, [locale]);

  // Get content with fallback to static content
  const getContent = () => {
    if (aboutUsContent) {
      return {
        badge: aboutUsContent.badge,
        title: aboutUsContent.title,
        subtitle: aboutUsContent.subtitle,
        description: aboutUsContent.description,
        imageOverlay: {
          badge: aboutUsContent.imageOverlay.badge,
          text: aboutUsContent.imageOverlay.text
        },
        mainImage: aboutUsContent.mainImage
      };
    }
    
    // Fallback to static content
    return {
      badge: locale === 'ar' ? 'حول مزايا كابيتال' : 'About Mazaya Capital',
      title: locale === 'ar' ? 'مرحبا بك في' : 'Welcome to',
      subtitle: locale === 'ar' ? 'مزايا كابيتال' : 'Mazaya Capital',
      description: locale === 'ar' 
        ? 'مزايا كابيتال هي شركة تطوير عقاري رائدة تخصصت في إنشاء منازل ومنشآت تجارية ومشاريع متعددة الاستخدامات، تأتي بقيمة استثنائية للمستثمرين والمقيمين.'
        : 'A leading real estate development company specializing in premium properties across the UAE. With a focus on innovation, quality, and sustainability, we transform visions into exceptional developments.',
      imageOverlay: {
        badge: locale === 'ar' ? 'التميز المعماري' : 'Architectural Excellence',
        text: locale === 'ar' ? 'نشكل المعالم الأيقونية منذ عام ٢٠٠٥' : 'Shaping Iconic Landmarks Since 2005'
      },
      mainImage: '/images/home/<USER>'
    };
  };

  const content = getContent();
  
  // Helper function to get full image URL
  const getFullImageUrl = (imagePath: string): string => {
    if (!imagePath) return '';
    
    // If it's already a full URL, return as is
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }
    
    // If it's a relative path, prefix with API base URL
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    return `${apiBaseUrl}${imagePath}`;
  };

  // Get the image source with fallback logic
  const getImageSrc = () => {
    // If there was an image error or no API content, use fallback
    if (imageError || !aboutUsContent) {
      return '/images/home/<USER>';
    }
    
    // Get the full image URL
    const fullImageUrl = getFullImageUrl(content.mainImage);
    
    // Check if this image has already failed globally
    if (failedImageCache.has(fullImageUrl)) {
      console.warn('Image already in failed cache, using fallback:', fullImageUrl);
      return '/images/home/<USER>';
    }
    
    // Try to use the API image
    return fullImageUrl;
  };

  // Handle image error - prevent retries
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const failedUrl = e.currentTarget.src;
    console.warn('Image failed to load, adding to cache and falling back:', failedUrl);
    
    // Add to global failed cache
    failedImageCache.add(failedUrl);
    
    // Set local error state
    setImageError(true);
    
    // Immediately change the src to prevent retries
    e.currentTarget.src = '/images/home/<USER>';
  };

  // Handle image load success
  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  // Add random circles for the animated "float" effect
  useEffect(() => {
    // Add a custom keyframes animation for the floating particle effect
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes float-particle {
        0% { transform: translate(0, 0); }
        25% { transform: translate(${Math.random() * 30 - 15}px, ${Math.random() * 30 - 15}px); }
        50% { transform: translate(${Math.random() * 30 - 15}px, ${Math.random() * 30 - 15}px); }
        75% { transform: translate(${Math.random() * 30 - 15}px, ${Math.random() * 30 - 15}px); }
        100% { transform: translate(0, 0); }
      }
      @keyframes slow-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      @keyframes slow-spin-reverse {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(-360deg); }
      }
      @keyframes slow-pulse {
        0%, 100% { opacity: 0.1; }
        50% { opacity: 0.3; }
      }
      .animate-slow-spin {
        animation: slow-spin 120s linear infinite;
      }
      .animate-slow-spin-reverse {
        animation: slow-spin-reverse 90s linear infinite;
      }
      .animate-slow-pulse {
        animation: slow-pulse 10s ease-in-out infinite;
      }
    `;
    document.head.appendChild(styleElement);
    
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  
  return (
    <section 
      ref={containerRef}
      className="py-16 md:py-24 relative overflow-hidden"
    >
      {/* Modern Animated Background */}
      <AnimatedBackground />
      
      <div className="container mx-auto px-4 relative z-30">
        <motion.div style={{ opacity }} className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16">
          
          {/* Left Side - Text Content */}
          <motion.div 
            className="w-full lg:w-1/2 text-white"
            style={{ y: textY }}
          >
            {/* Loading state for badge */}
            {isLoading ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6"
              >
                <div className="h-4 w-32 bg-white/20 rounded animate-pulse"></div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-1.5 rounded-full bg-white/15 backdrop-blur-md border border-white/30 mb-6"
              >
                <span className="text-sm font-medium tracking-wide">{content.badge}</span>
              </motion.div>
            )}
            
            {/* Loading state for title */}
            {isLoading ? (
              <div className="space-y-4 mb-6">
                <div className="h-10 w-48 bg-white/20 rounded animate-pulse"></div>
                <div className="h-10 w-56 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded animate-pulse"></div>
              </div>
            ) : (
              <h2 className={`text-3xl md:text-4xl font-bold mb-6 leading-tight ${locale === 'ar' ? 'text-right' : ''}`}>
                <SplitText text={content.title} className="block text-white [text-shadow:0_2px_4px_rgba(0,0,0,0.8)]" />
                <span className="block h-2"></span>
                <SplitText text={content.subtitle} className="block bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500 [text-shadow:0_2px_4px_rgba(0,0,0,0.5)]" />
              </h2>
            )}
            
            {/* Loading state for description */}
            {isLoading ? (
              <div className="space-y-2 mb-6">
                <div className="h-4 w-full bg-white/20 rounded animate-pulse"></div>
                <div className="h-4 w-3/4 bg-white/20 rounded animate-pulse"></div>
                <div className="h-4 w-5/6 bg-white/20 rounded animate-pulse"></div>
              </div>
            ) : (
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className={`text-lg font-medium text-white mb-6 [text-shadow:0_1px_3px_rgba(0,0,0,0.9)] max-w-xl ${locale === 'ar' ? 'text-right' : ''}`}
              >
                {content.description}
              </motion.p>
            )}
            
            {/* Additional description - removed for API content */}
            
            {/* Loading state for buttons */}
            {isLoading ? (
              <div className="flex items-center gap-4">
                <div className="h-12 w-32 bg-white/20 rounded-full animate-pulse"></div>
                <div className="h-12 w-32 bg-white/10 border border-white/30 rounded-full animate-pulse"></div>
              </div>
            ) : (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.9 }}
                className="flex items-center gap-4"
              >
                <Link
                  href={`/${locale}/about`}
                  className="group relative inline-flex items-center justify-center overflow-hidden rounded-full border border-transparent bg-white px-6 py-3 text-base font-medium text-gray-900 transition hover:scale-105"
                >
                  <span>{locale === 'ar' ? 'اعرف المزيد' : 'Learn More'}</span>
                  <svg className={`w-4 h-4 transition-transform group-hover:translate-x-1 ${locale === 'ar' ? 'me-2 rotate-180' : 'ms-2'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
                
                <Link
                  href={`/${locale}/projects`}
                  className="relative inline-flex items-center justify-center overflow-hidden rounded-full border border-white/30 bg-white/10 backdrop-blur-sm px-6 py-3 text-base font-medium text-white transition hover:bg-white/15"
                >
                  <span>{locale === 'ar' ? 'عرض المشاريع' : 'View Projects'}</span>
                </Link>
              </motion.div>
            )}
          </motion.div>
          
          {/* Right Side - Image */}
          <motion.div 
            className="w-full lg:w-1/2"
            style={{ y: imageY }}
          >
            {/* Loading state for image */}
            {isLoading ? (
              <motion.div 
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="relative w-full h-[400px] rounded-xl overflow-hidden border border-white/30 shadow-lg shadow-black/20 bg-gray-700"
              >
                <div className="absolute inset-0 bg-gray-700 animate-pulse"></div>
                <div className="absolute bottom-0 start-0 end-0 p-6 bg-gradient-to-t from-blue-900/90 to-transparent pt-16">
                  <div className="inline-block px-3 py-1 rounded-full bg-white/15 backdrop-blur-md border border-white/30 mb-2">
                    <div className="h-3 w-24 bg-white/20 rounded animate-pulse"></div>
                  </div>
                  <div className="h-6 w-48 bg-white/20 rounded animate-pulse"></div>
                </div>
              </motion.div>
            ) : (
              <motion.div 
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="relative w-full h-[400px] rounded-xl overflow-hidden border border-white/30 shadow-lg shadow-black/20"
              >
                <img
                  src={getImageSrc()}
                  alt={locale === 'ar' ? 'منظر أفق المدينة من مبنى شاهق' : 'City skyline view from a high-rise building'}
                  className="absolute inset-0 w-full h-full object-cover object-center transition-transform duration-700 hover:scale-110 brightness-110 contrast-110"
                  onError={handleImageError}
                  onLoad={handleImageLoad}
                  loading="eager"
                />
                
                {/* Glass effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/80 via-indigo-900/50 to-transparent opacity-80"></div>
                
                {/* Caption */}
                <div className="absolute bottom-0 start-0 end-0 p-6 bg-gradient-to-t from-blue-900/90 to-transparent pt-16">
                  <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-white/15 backdrop-blur-md border border-white/30 text-white mb-2">
                    {content.imageOverlay.badge}
                  </span>
                  <h3 className={`text-white text-xl font-semibold [text-shadow:0_2px_4px_rgba(0,0,0,0.7)] ${locale === 'ar' ? 'text-right' : ''}`}>
                    {content.imageOverlay.text}
                  </h3>
                </div>
              </motion.div>
            )}
          </motion.div>
          
        </motion.div>
      </div>
    </section>
  );
};

export default CompanyIntro; 