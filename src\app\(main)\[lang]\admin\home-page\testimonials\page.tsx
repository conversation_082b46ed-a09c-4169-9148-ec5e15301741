"use client";

import { useState } from 'react';
import { FiSave, FiEdit3, FiX, FiPlus, FiTrash2, FiStar, FiEye, FiSettings, FiUsers, FiMessageSquare, FiImage } from 'react-icons/fi';

interface TestimonialData {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  content: {
    en: string;
    ar: string;
  };
  avatar: string;
  rating: number;
  initials: string;
}

interface SectionSettings {
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  subtitle: {
    en: string;
    ar: string;
  };
  backgroundEnabled: boolean;
  starsEnabled: boolean;
  geometricShapesEnabled: boolean;
  gridOverlayEnabled: boolean;
  quotationMarksEnabled: boolean;
}

export default function HomePageTestimonialsManagement() {
  const [sectionSettings, setSectionSettings] = useState<SectionSettings>({
    badge: {
      en: "Client Success Stories",
      ar: "قصص نجاح العملاء"
    },
    title: {
      en: "Client Testimonials",
      ar: "شهادات العملاء"
    },
    subtitle: {
      en: "Hear what our satisfied clients say about their experience with Mazaya Capital",
      ar: "اسمع ما يقوله عملاؤنا الراضون عن تجربتهم مع مزايا كابيتال"
    },
    backgroundEnabled: true,
    starsEnabled: true,
    geometricShapesEnabled: true,
    gridOverlayEnabled: true,
    quotationMarksEnabled: true
  });

  const [testimonials, setTestimonials] = useState<TestimonialData[]>([
    {
      id: '1',
      name: {
        en: 'Ahmed Al Mansouri',
        ar: 'أحمد المنصوري'
      },
      title: {
        en: 'Property Investor',
        ar: 'مستثمر عقاري'
      },
      content: {
        en: 'My investment with Mazaya Capital has consistently delivered above-market returns. Their professional team provides exceptional support and transparent communication throughout the investment process.',
        ar: 'استثماري مع مزايا كابيتال حقق عوائد تفوق السوق باستمرار. فريقهم المحترف يقدم دعماً استثنائياً وتواصلاً شفافاً طوال عملية الاستثمار.'
      },
      avatar: '',
      rating: 5,
      initials: 'AAM'
    },
    {
      id: '2',
      name: {
        en: 'Sarah Johnson',
        ar: 'سارة جونسون'
      },
      title: {
        en: 'Business Owner',
        ar: 'صاحبة أعمال'
      },
      content: {
        en: 'Mazaya Business Park exceeded all our expectations. The strategic location, modern facilities, and excellent management have significantly enhanced our business operations and employee satisfaction.',
        ar: 'تجاوز مزايا بيزنس بارك جميع توقعاتنا. الموقع الاستراتيجي والمرافق الحديثة والإدارة الممتازة عززت بشكل كبير عمليات أعمالنا ورضا الموظفين.'
      },
      avatar: '',
      rating: 5,
      initials: 'SJ'
    }
  ]);

  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editingTestimonial, setEditingTestimonial] = useState<string | null>(null);

  const handleSaveAll = () => {
    console.log('Saving testimonials data:', { sectionSettings, testimonials });
    alert('Testimonials settings saved successfully!');
  };

  const addTestimonial = () => {
    const newTestimonial: TestimonialData = {
      id: Date.now().toString(),
      name: { en: '', ar: '' },
      title: { en: '', ar: '' },
      content: { en: '', ar: '' },
      avatar: '',
      rating: 5,
      initials: ''
    };
    setTestimonials([...testimonials, newTestimonial]);
    setEditingTestimonial(newTestimonial.id);
  };

  const deleteTestimonial = (id: string) => {
    setTestimonials(testimonials.filter(t => t.id !== id));
  };

  const updateTestimonial = (id: string, updatedTestimonial: TestimonialData) => {
    setTestimonials(testimonials.map(t => t.id === id ? updatedTestimonial : t));
    setEditingTestimonial(null);
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Testimonials Section</h1>
          <p className="text-gray-400 mt-1">Manage the testimonials section content, design, and background effects</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Settings */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiSettings className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Settings
            </h2>
            <button
              onClick={() => setEditingSection(editingSection === 'settings' ? null : 'settings')}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection === 'settings' ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection === 'settings' ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection === 'settings' ? (
            <div className="space-y-6">
              {/* Content Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Content Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (English)</label>
                    <input
                      type="text"
                      value={sectionSettings.badge.en}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        badge: { ...prev.badge, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Badge Text (Arabic)</label>
                    <input
                      type="text"
                      value={sectionSettings.badge.ar}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        badge: { ...prev.badge, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                    <input
                      type="text"
                      value={sectionSettings.title.en}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        title: { ...prev.title, en: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                    <input
                      type="text"
                      value={sectionSettings.title.ar}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        title: { ...prev.title, ar: e.target.value }
                      }))}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (English)</label>
                    <textarea
                      value={sectionSettings.subtitle.en}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        subtitle: { ...prev.subtitle, en: e.target.value }
                      }))}
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">Subtitle (Arabic)</label>
                    <textarea
                      value={sectionSettings.subtitle.ar}
                      onChange={(e) => setSectionSettings(prev => ({
                        ...prev,
                        subtitle: { ...prev.subtitle, ar: e.target.value }
                      }))}
                      rows={3}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                    />
                  </div>
                </div>
              </div>

              

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setEditingSection(null)}
                  className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Current Badge</label>
                  <p className="text-white bg-gray-700 p-3 rounded">{sectionSettings.badge.en}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Current Title</label>
                  <p className="text-white bg-gray-700 p-3 rounded">{sectionSettings.title.en}</p>
                </div>
              </div>
              
            </div>
          )}
        </div>

        {/* Testimonials Management */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiUsers className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Testimonials Management
            </h2>
            <button
              onClick={addTestimonial}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Testimonial
            </button>
          </div>

          <div className="space-y-6">
            {testimonials.map((testimonial, index) => (
              <div key={testimonial.id} className="bg-gray-700 rounded-lg border border-gray-600 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <FiMessageSquare className="mr-2 h-4 w-4 text-[#00C2FF]" />
                    Testimonial #{index + 1}
                  </h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingTestimonial(editingTestimonial === testimonial.id ? null : testimonial.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-gray-600 text-gray-300 rounded hover:bg-gray-500"
                    >
                      {editingTestimonial === testimonial.id ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                      {editingTestimonial === testimonial.id ? 'Cancel' : 'Edit'}
                    </button>
                    <button
                      onClick={() => deleteTestimonial(testimonial.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-500"
                    >
                      <FiTrash2 className="mr-1 h-4 w-4" />
                      Delete
                    </button>
                  </div>
                </div>

                {editingTestimonial === testimonial.id ? (
                  <TestimonialForm
                    testimonial={testimonial}
                    onSave={(updatedTestimonial) => updateTestimonial(testimonial.id, updatedTestimonial)}
                    onCancel={() => setEditingTestimonial(null)}
                  />
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Name</label>
                        <p className="text-white">{testimonial.name.en}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Title</label>
                        <p className="text-gray-300">{testimonial.title.en}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-400 mb-1">Rating</label>
                        <div className="flex space-x-1">
                          {[...Array(5)].map((_, i) => (
                            <FiStar key={i} className={`h-4 w-4 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-500'}`} />
                          ))}
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Content</label>
                      <p className="text-gray-300 text-sm">{testimonial.content.en.substring(0, 150)}...</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Section Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-600">
                        <div className="text-center space-y-6">              {/* English Version */}              <div>                <div className="inline-block px-4 py-1 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm text-indigo-300 text-sm font-medium mb-4">                  {sectionSettings.badge.en}                </div>                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">                  <span className="relative inline-block">                    {sectionSettings.title.en}                    <span className="absolute -bottom-2 start-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500"></span>                  </span>                </h2>                <p className="text-indigo-200 max-w-2xl mx-auto">                  {sectionSettings.subtitle.en}                </p>              </div>                            {/* Arabic Version */}              <div dir="rtl">                <div className="inline-block px-4 py-1 rounded-full bg-gradient-to-r from-blue-500/20 to-cyan-500/20 backdrop-blur-sm text-blue-300 text-sm font-medium mb-4">                  {sectionSettings.badge.ar}                </div>                <h2 className="text-2xl md:text-3xl font-bold mb-4 text-blue-100">                  <span className="relative inline-block">                    {sectionSettings.title.ar}                    <span className="absolute -bottom-2 start-0 w-full h-1 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500"></span>                  </span>                </h2>                <p className="text-blue-200 max-w-2xl mx-auto">                  {sectionSettings.subtitle.ar}                </p>              </div>            </div>
                        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">              {testimonials.slice(0, 3).map((testimonial, index) => (                <div key={testimonial.id} className="bg-gray-800 p-4 rounded-lg border border-gray-700">                  <div className="flex items-center mb-3">                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-indigo-400 to-purple-600 flex items-center justify-center text-white font-bold mr-3">                      {testimonial.avatar ? (                        <img src={testimonial.avatar} alt={testimonial.name.en} className="w-full h-full rounded-full object-cover" />                      ) : (                        testimonial.initials                      )}                    </div>                    <div className="flex-1">                      <div className="space-y-1">                        <h4 className="text-white font-medium">{testimonial.name.en}</h4>                        <h4 className="text-blue-300 font-medium text-sm" dir="rtl">{testimonial.name.ar}</h4>                      </div>                      <div className="space-y-1">                        <p className="text-gray-400 text-sm">{testimonial.title.en}</p>                        <p className="text-gray-500 text-xs" dir="rtl">{testimonial.title.ar}</p>                      </div>                    </div>                  </div>                  <div className="flex mb-3">                    {[...Array(5)].map((_, i) => (                      <FiStar key={i} className={`h-3 w-3 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-500'}`} />                    ))}                  </div>                  <div className="space-y-2">                    <p className="text-gray-300 text-sm leading-relaxed">{testimonial.content.en.substring(0, 80)}...</p>                    <p className="text-blue-200 text-sm leading-relaxed" dir="rtl">{testimonial.content.ar.substring(0, 80)}...</p>                  </div>                </div>              ))}            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Testimonial Form Component
function TestimonialForm({ 
  testimonial, 
  onSave, 
  onCancel 
}: { 
  testimonial: TestimonialData;
  onSave: (testimonial: TestimonialData) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(testimonial);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>(testimonial.avatar);
  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      
      // Update form data with the new image URL
      setFormData(prev => ({ ...prev, avatar: objectUrl }));
    }
  };

  const handleImageClick = () => {
    document.getElementById(`avatar-upload-${formData.id}`)?.click();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Name (English)</label>
          <input
            type="text"
            value={formData.name.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              name: { ...prev.name, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Name (Arabic)</label>
          <input
            type="text"
            value={formData.name.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              name: { ...prev.name, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Content (English)</label>
          <textarea
            value={formData.content.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              content: { ...prev.content, en: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Content (Arabic)</label>
          <textarea
            value={formData.content.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              content: { ...prev.content, ar: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Initials</label>
          <input
            type="text"
            value={formData.initials}
            onChange={(e) => setFormData(prev => ({ ...prev, initials: e.target.value.toUpperCase() }))}
            maxLength={3}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Rating</label>
          <select
            value={formData.rating}
            onChange={(e) => setFormData(prev => ({ ...prev, rating: parseInt(e.target.value) }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          >
            {[1, 2, 3, 4, 5].map(rating => (
              <option key={rating} value={rating}>{rating} Star{rating > 1 ? 's' : ''}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Avatar Image</label>
          
          {/* Hidden File Input */}
          <input
            id={`avatar-upload-${formData.id}`}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          {/* Upload Method Toggle */}
          <div className="flex space-x-2 mb-3">
            <button
              type="button"
              onClick={() => setUploadMethod('upload')}
              className={`px-3 py-1 text-xs rounded transition-colors ${
                uploadMethod === 'upload' 
                  ? 'bg-[#00C2FF] text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Upload Image
            </button>
            <button
              type="button"
              onClick={() => setUploadMethod('url')}
              className={`px-3 py-1 text-xs rounded transition-colors ${
                uploadMethod === 'url' 
                  ? 'bg-[#00C2FF] text-white' 
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              Image URL
            </button>
          </div>

          {uploadMethod === 'upload' ? (
            // Image Upload Area
            <div 
              onClick={handleImageClick}
              className="w-full h-20 bg-gray-700 rounded-md overflow-hidden cursor-pointer border-2 border-dashed border-gray-500 hover:border-[#00C2FF] transition-colors group relative"
            >
              {previewUrl ? (
                <div className="relative w-full h-full">
                  <img 
                    src={previewUrl} 
                    alt="Avatar Preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center">
                    <div className="text-center text-white opacity-0 group-hover:opacity-100">
                      <FiImage className="h-4 w-4 mx-auto mb-1" />
                      <p className="text-xs">Click to change</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400 group-hover:text-[#00C2FF]">
                  <div className="text-center">
                    <FiImage className="h-6 w-6 mx-auto mb-1" />
                    <p className="text-xs font-medium">Upload avatar</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            // URL Input
            <input
              type="url"
              value={formData.avatar}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, avatar: e.target.value }));
                setPreviewUrl(e.target.value);
              }}
              placeholder="https://example.com/avatar.jpg"
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          )}

          {/* Upload Info */}
          {selectedFile && uploadMethod === 'upload' && (
            <div className="mt-2 p-2 bg-gray-700 rounded border border-gray-600">
              <div className="flex items-center text-green-400 text-xs">
                <FiImage className="mr-1 h-3 w-3" />
                <span>Selected: {selectedFile.name}</span>
              </div>
            </div>
          )}

          {/* Image Preview for URL */}
          {uploadMethod === 'url' && previewUrl && (
            <div className="mt-2">
              <img 
                src={previewUrl} 
                alt="Avatar Preview" 
                className="w-12 h-12 object-cover rounded-full border border-gray-600"
                onError={(e) => {
                  e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMzc0MTUxIi8+Cjx0ZXh0IHg9IjI0IiB5PSIyOCIgZmlsbD0iIzZCNzI4MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIj5ObyBJbWFnZTwvdGV4dD4KPHN2Zz4K';
                }}
              />
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Testimonial
        </button>
      </div>
    </form>
  );
} 