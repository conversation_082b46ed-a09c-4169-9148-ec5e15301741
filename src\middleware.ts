import { NextRequest, NextResponse } from 'next/server';

const locales = ['en', 'ar'];
const defaultLocale = 'en';

// Get the preferred locale, similar to above or using a different method
function getLocale(request: NextRequest) {
  // Check for locale in URL path
  const pathname = request.nextUrl.pathname;
  const pathnameLocale = locales.find(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (pathnameLocale) return pathnameLocale;
  
  // Check for locale in Accept-Language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const parsedLocales = acceptLanguage.split(',').map(l => l.split(';')[0].trim());
    for (const parsedLocale of parsedLocales) {
      const matchedLocale = locales.find(
        locale => locale === parsedLocale || parsedLocale.startsWith(`${locale}-`)
      );
      if (matchedLocale) return matchedLocale;
    }
  }
  
  // Check for stored locale in cookie
  const storedLocale = request.cookies.get('NEXT_LOCALE')?.value;
  if (storedLocale && locales.includes(storedLocale)) {
    return storedLocale;
  }
  
  return defaultLocale;
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip prefetch requests, API routes, static files, etc.
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/images') ||
    pathname.includes('.') // Files with extensions
  ) {
    return NextResponse.next();
  }
  
  // Check if the pathname already starts with a locale
  const pathnameIsMissingLocale = locales.every(
    locale => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );
  
  // Redirect if missing locale in the URL
  if (pathnameIsMissingLocale) {
    const locale = getLocale(request);
    const url = new URL(`/${locale}${pathname === '/' ? '' : pathname}`, request.url);
    
    // Preserve the search params and hash
    url.search = request.nextUrl.search;
    url.hash = request.nextUrl.hash;
    
    return NextResponse.redirect(url);
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|images|favicon.ico).*)'],
}; 