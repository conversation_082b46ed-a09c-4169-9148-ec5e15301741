/**
 * API Utility Functions for Django Backend Integration
 */

import { getCacheBustedUrl } from './cache';

// Get the Django API base URL
export const getApiBaseUrl = (): string => {
  return process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
};

// Convert relative Django media URLs to full URLs
export const getMediaUrl = (mediaPath: string): string => {
  // If it's already a full URL, return as is
  if (mediaPath.startsWith('http://') || mediaPath.startsWith('https://')) {
    return mediaPath;
  }
  
  // Use Next.js rewrite for better performance in development
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    return mediaPath; // Use the rewrite path directly
  }
  
  // Fallback: Direct Django API URL
  return `${getApiBaseUrl()}${mediaPath}`;
};

// Get full API endpoint URL with optional cache busting
export const getApiUrl = (endpoint: string, bustCache: boolean = false): string => {
  const baseUrl = getApiBaseUrl();
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const fullUrl = `${baseUrl}${cleanEndpoint}`;
  
  // Add cache busting if requested
  if (bustCache && typeof window !== 'undefined') {
    return getCacheBustedUrl(fullUrl);
  }
  
  return fullUrl;
};

// Add language parameter to API endpoints
export const addLanguageParam = (endpoint: string, locale?: string): string => {
  if (typeof window === 'undefined') return endpoint;
  
  const currentLocale = locale || localStorage.getItem('locale') || 'en';
  const separator = endpoint.includes('?') ? '&' : '?';
  return `${endpoint}${separator}lang=${currentLocale}`;
};

// API call helper with proper error handling
export const apiCall = async <T>(
  endpoint: string,
  options: RequestInit = {},
  bustCache: boolean = false
): Promise<{ success: boolean; data?: T; message?: string; errors?: Record<string, string[]> }> => {
  try {
    // Add language parameter to endpoint
    const endpointWithLang = addLanguageParam(endpoint);
    const url = getApiUrl(endpointWithLang, bustCache);
    
    console.log('🔄 Making API call to:', url);
    console.log('🌐 Cache busting:', bustCache);
    
    // Ensure Content-Type is always set for requests with body
    const defaultHeaders: Record<string, string> = {};
    if (options.body && typeof options.body === 'string') {
      defaultHeaders['Content-Type'] = 'application/json';
    }
    
    // Add cache control headers if busting cache
    if (bustCache) {
      defaultHeaders['Cache-Control'] = 'no-cache, no-store, must-revalidate';
      defaultHeaders['Pragma'] = 'no-cache';
      defaultHeaders['Expires'] = '0';
    }
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    console.log('📥 Response status:', response.status, response.statusText);
    
    const data = await response.json();
    console.log('📥 Response data:', data);
    
    return data;
  } catch (error) {
    console.error('❌ API call failed:', error);
    return {
      success: false,
      message: 'Network error occurred',
    };
  }
};

// Get authenticated headers
export const getAuthHeaders = (): Record<string, string> => {
  if (typeof window === 'undefined') return {};
  
  try {
    const tokensData = localStorage.getItem('adminTokens');
    const tokens = tokensData ? JSON.parse(tokensData) : null;
    
    if (tokens?.access) {
      return {
        'Authorization': `Bearer ${tokens.access}`,
      };
    }
  } catch (error) {
    console.error('Error getting auth headers:', error);
  }
  
  return {};
};

// Authenticated API call helper
export const authenticatedApiCall = async <T>(
  endpoint: string,
  options: RequestInit = {},
  bustCache: boolean = false
): Promise<{ success: boolean; data?: T; message?: string; errors?: Record<string, string[]> }> => {
  const authHeaders = getAuthHeaders();
  
  return apiCall<T>(endpoint, {
    ...options,
    headers: {
      ...authHeaders,
      ...options.headers,
    },
  }, bustCache);
}; 