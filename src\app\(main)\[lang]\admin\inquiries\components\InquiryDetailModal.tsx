"use client";

import React, { useState } from 'react';
import { 
  <PERSON>X, <PERSON><PERSON>ser, FiMail, FiPhone, FiMessageSquare, FiCalendar, 
  FiClock, FiStar, FiArchive, FiTrash2, FiSend, FiEdit3,
  FiTag, FiEye
} from 'react-icons/fi';

interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone?: string;
  interest: string;
  subject: string;
  message: string;
  isRead: boolean;
  isStarred: boolean;
  isArchived: boolean;
  createdAt: Date;
  lastViewedAt?: Date;
  priority: 'high' | 'medium' | 'low';
  status: 'new' | 'in-progress' | 'responded' | 'closed';
}

interface InquiryDetailModalProps {
  inquiry: Inquiry | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStatus: (id: string, status: Inquiry['status']) => void;
  onToggleStar: (id: string) => void;
  onArchive: (id: string) => void;
  onDelete: (id: string) => void;
  onMarkAsRead: (id: string) => void;
}

export default function InquiryDetailModal({
  inquiry,
  isOpen,
  onClose,
  onUpdateStatus,
  onToggleStar,
  onArchive,
  onDelete,
  onMarkAsRead
}: InquiryDetailModalProps) {
  const [isReplying, setIsReplying] = useState(false);
  const [replyMessage, setReplyMessage] = useState('');
  const [notes, setNotes] = useState('');

  if (!isOpen || !inquiry) return null;

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).format(date);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400 bg-red-400/10 border-red-400/20';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'low': return 'text-green-400 bg-green-400/10 border-green-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'in-progress': return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'responded': return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'closed': return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
      default: return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const handleReply = () => {
    // Here you would typically send the reply via email
    console.log('Sending reply:', replyMessage);
    setIsReplying(false);
    setReplyMessage('');
    onUpdateStatus(inquiry.id, 'responded');
  };

  const handleStatusChange = (newStatus: Inquiry['status']) => {
    onUpdateStatus(inquiry.id, newStatus);
  };

  const handleClose = () => {
    if (!inquiry.isRead) {
      onMarkAsRead(inquiry.id);
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-white">Inquiry Details</h2>
            {!inquiry.isRead && (
              <span className="inline-block w-2 h-2 bg-blue-400 rounded-full"></span>
            )}
            <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(inquiry.priority)}`}>
              {inquiry.priority.toUpperCase()} PRIORITY
            </span>
            <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(inquiry.status)}`}>
              {inquiry.status.replace('-', ' ').toUpperCase()}
            </span>
          </div>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="p-6 space-y-6">
            {/* Contact Information */}
            <div className="bg-gray-700/30 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                <FiUser className="mr-2 h-5 w-5 text-[#00C2FF]" />
                Contact Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <FiUser className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-400">Full Name</p>
                      <p className="text-white font-medium">{inquiry.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <FiMail className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-400">Email Address</p>
                      <a 
                        href={`mailto:${inquiry.email}`}
                        className="text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
                      >
                        {inquiry.email}
                      </a>
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  {inquiry.phone && (
                    <div className="flex items-center space-x-3">
                      <FiPhone className="h-4 w-4 text-gray-400" />
                      <div>
                        <p className="text-sm text-gray-400">Phone Number</p>
                        <a 
                          href={`tel:${inquiry.phone}`}
                          className="text-[#00C2FF] hover:text-[#00C2FF]/80 transition-colors"
                        >
                          {inquiry.phone}
                        </a>
                      </div>
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <FiTag className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-400">Interest</p>
                      <p className="text-white">{inquiry.interest}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Inquiry Details */}
            <div className="bg-gray-700/30 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                <FiMessageSquare className="mr-2 h-5 w-5 text-[#00C2FF]" />
                Inquiry Details
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-400 mb-2">Subject</p>
                  <p className="text-white font-medium">{inquiry.subject}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400 mb-2">Message</p>
                  <div className="bg-gray-800 rounded-lg p-4 border border-gray-600">
                    <p className="text-gray-300 whitespace-pre-wrap">{inquiry.message}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-gray-700/30 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                <FiClock className="mr-2 h-5 w-5 text-[#00C2FF]" />
                Timeline
              </h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <FiCalendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-400">Created</p>
                    <p className="text-white">{formatDate(inquiry.createdAt)}</p>
                  </div>
                </div>
                {inquiry.lastViewedAt && (
                  <div className="flex items-center space-x-3">
                    <FiEye className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-400">Last Viewed</p>
                      <p className="text-white">{formatDate(inquiry.lastViewedAt)}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Notes Section */}
            <div className="bg-gray-700/30 rounded-lg p-4">
              <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                <FiEdit3 className="mr-2 h-5 w-5 text-[#00C2FF]" />
                Internal Notes
              </h3>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Add internal notes about this inquiry..."
                rows={4}
                className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] resize-none"
              />
            </div>

            {/* Reply Section */}
            {isReplying ? (
              <div className="bg-gray-700/30 rounded-lg p-4">
                <h3 className="text-lg font-medium text-white mb-4 flex items-center">
                  <FiSend className="mr-2 h-5 w-5 text-[#00C2FF]" />
                  Reply to Inquiry
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">
                      Reply Message
                    </label>
                    <textarea
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                      placeholder={`Dear ${inquiry.name},\n\nThank you for your inquiry about ${inquiry.interest}. We appreciate your interest in our services.\n\nBest regards,\nMazaya Capital Team`}
                      rows={8}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF] resize-none"
                    />
                  </div>
                  <div className="flex space-x-3">
                    <button
                      onClick={handleReply}
                      className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
                    >
                      Send Reply
                    </button>
                    <button
                      onClick={() => setIsReplying(false)}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex justify-center">
                <button
                  onClick={() => setIsReplying(true)}
                  className="px-6 py-3 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors flex items-center"
                >
                  <FiSend className="mr-2 h-4 w-4" />
                  Reply to Inquiry
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700 bg-gray-800/50">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => onToggleStar(inquiry.id)}
              className={`p-2 rounded-lg transition-colors ${
                inquiry.isStarred 
                  ? 'text-yellow-400 bg-yellow-400/10' 
                  : 'text-gray-400 hover:text-yellow-400 hover:bg-yellow-400/10'
              }`}
              title={inquiry.isStarred ? 'Remove star' : 'Add star'}
            >
              <FiStar className={`h-5 w-5 ${inquiry.isStarred ? 'fill-current' : ''}`} />
            </button>
            
            <div className="relative group">
              <select
                value={inquiry.status}
                onChange={(e) => handleStatusChange(e.target.value as Inquiry['status'])}
                className="px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              >
                <option value="new">New</option>
                <option value="in-progress">In Progress</option>
                <option value="responded">Responded</option>
                <option value="closed">Closed</option>
              </select>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                onArchive(inquiry.id);
                onClose();
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors flex items-center"
            >
              <FiArchive className="mr-2 h-4 w-4" />
              Archive
            </button>
            
            <button
              onClick={() => {
                if (confirm('Are you sure you want to delete this inquiry? This action cannot be undone.')) {
                  onDelete(inquiry.id);
                  onClose();
                }
              }}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors flex items-center"
            >
              <FiTrash2 className="mr-2 h-4 w-4" />
              Delete
            </button>
            
            <button
              onClick={handleClose}
              className="px-6 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 