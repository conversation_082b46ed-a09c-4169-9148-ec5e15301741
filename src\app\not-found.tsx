"use client";

import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import { motion, useAnimationControls, useScroll, useTransform } from 'framer-motion';
import BackgroundEffects from '@/components/ui/BackgroundEffects';
import Button from '@/components/ui/Button';

export default function NotFound() {
  // State to handle responsive sizing
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimationControls();
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });
  
  // Create parallax effect on scroll
  const translateY = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0.8]);
  
  // Detect screen size changes
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkMobile();
    
    // Add event listener for window resize
    window.addEventListener('resize', checkMobile);
    
    // Sequence animations
    const sequenceAnimations = async () => {
      await controls.start("gridVisible");
      await controls.start("elementsVisible");
    };
    
    sequenceAnimations();
    
    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, [controls]);
  
  return (
    <div 
      ref={containerRef}
      className="relative w-full min-h-[100vh] flex flex-col items-center justify-center overflow-hidden"
    >
      {/* Star background effect */}
      <BackgroundEffects />
      
      {/* Architectural Blueprint Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Enhanced gradient background with more blur for text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-background/95 via-background/80 to-background/90 backdrop-blur-sm z-0"></div>
        
        {/* Blueprint Floor Plan */}
        <svg 
          width="100%" 
          height="100%" 
          viewBox="0 0 1000 1000" 
          xmlns="http://www.w3.org/2000/svg"
          className="architectural-blueprint relative z-1"
          style={{ 
            transform: isMobile ? 'scale(2.5)' : 'scale(2)', 
            transformOrigin: 'center center',
            opacity: isMobile ? 0.4 : 0.65
          }}
        >
          {/* Horizontal grid lines - fewer on mobile */}
          {Array.from({ length: isMobile ? 10 : 20 }).map((_, i) => (
            <line 
              key={`h-line-${i}`} 
              x1="0" 
              y1={i * (isMobile ? 100 : 50)} 
              x2="1000" 
              y2={i * (isMobile ? 100 : 50)} 
              className="grid-line"
            />
          ))}
          
          {/* Vertical grid lines - fewer on mobile */}
          {Array.from({ length: isMobile ? 10 : 20 }).map((_, i) => (
            <line 
              key={`v-line-${i}`} 
              x1={i * (isMobile ? 100 : 50)} 
              y1="0" 
              x2={i * (isMobile ? 100 : 50)} 
              y2="1000" 
              className="grid-line"
            />
          ))}
          
          {/* Main House Outline */}
          <rect x="200" y="200" width="600" height="500" className="house-outline" />
          
          {/* House Interior - Rooms */}
          {/* Living Room */}
          <rect x="200" y="200" width="300" height="200" className="room-outline" />
          <text x="300" y="300" className="room-label">LIVING ROOM</text>
          
          {/* Kitchen & Dining */}
          <rect x="500" y="200" width="300" height="200" className="room-outline" />
          <text x="620" y="300" className="room-label">KITCHEN & DINING</text>
          
          {/* Bedroom 1 */}
          <rect x="200" y="400" width="200" height="300" className="room-outline" />
          <text x="280" y="550" className="room-label">BEDROOM 1</text>
          
          {/* Bathroom */}
          <rect x="400" y="400" width="150" height="150" className="room-outline" />
          <text x="450" y="480" className="room-label">BATH</text>
          
          {/* Master Bedroom */}
          <rect x="550" y="400" width="250" height="300" className="room-outline" />
          <text x="650" y="550" className="room-label">MASTER BEDROOM</text>
          
          {/* Master Bathroom */}
          <rect x="400" y="550" width="150" height="150" className="room-outline" />
          <text x="450" y="630" className="room-label">MASTER BATH</text>
          
          {/* Doors */}
          {/* Front Door */}
          <path d="M350,200 A40,40 0 0,1 350,240" className="door" />
          <line x1="330" y1="200" x2="370" y2="200" className="door-line" />
          
          {/* Living Room to Kitchen */}
          <path d="M500,300 A30,30 0 0,0 500,340" className="door" />
          <line x1="500" y1="290" x2="500" y2="350" className="door-line" />
          
          {/* Living Room to Bedroom 1 */}
          <path d="M250,400 A30,30 0 0,1 280,400" className="door" />
          <line x1="240" y1="400" x2="290" y2="400" className="door-line" />
          
          {/* Bedroom 1 to Bathroom */}
          <path d="M400,470 A20,20 0 0,0 400,500" className="door" />
          <line x1="400" y1="460" x2="400" y2="510" className="door-line" />
          
          {/* Master Bedroom Door */}
          <path d="M600,400 A30,30 0 0,1 630,400" className="door" />
          <line x1="590" y1="400" x2="640" y2="400" className="door-line" />
          
          {/* Master Bedroom to Master Bath */}
          <path d="M550,600 A25,25 0 0,0 550,630" className="door" />
          <line x1="550" y1="590" x2="550" y2="640" className="door-line" />
          
          {/* Windows */}
          <line x1="250" y1="200" x2="320" y2="200" className="window-line" />
          <line x1="550" y1="200" x2="650" y2="200" className="window-line" />
          <line x1="750" y1="200" x2="750" y2="270" className="window-line" />
          <line x1="800" y1="450" x2="800" y2="550" className="window-line" />
          <line x1="200" y1="500" x2="200" y2="600" className="window-line" />
          <line x1="250" y1="700" x2="350" y2="700" className="window-line" />
          <line x1="650" y1="700" x2="750" y2="700" className="window-line" />
          
          {/* Furniture elements */}
          {/* Living Room */}
          <rect x="230" y="270" width="100" height="50" className="furniture" /> {/* Sofa */}
          <rect x="350" y="270" width="70" height="70" className="furniture" /> {/* Coffee Table */}
          
          {/* Kitchen */}
          <rect x="500" y="230" width="200" height="40" className="furniture" /> {/* Counter */}
          <circle cx="600" cy="350" r="30" className="furniture" /> {/* Kitchen Table */}
          
          {/* Bedroom 1 */}
          <rect x="220" y="450" width="150" height="80" className="furniture" /> {/* Bed */}
          <rect x="220" y="550" width="60" height="100" className="furniture" /> {/* Closet */}
          
          {/* Master Bedroom */}
          <rect x="600" y="450" width="180" height="100" className="furniture" /> {/* King Bed */}
          <rect x="600" y="570" width="80" height="100" className="furniture" /> {/* Dresser */}
          
          {/* Bathroom fixtures */}
          <circle cx="450" cy="425" r="20" className="bathroom-fixture" /> {/* Sink */}
          <rect x="425" y="490" width="30" height="40" className="bathroom-fixture" /> {/* Toilet */}
          <rect x="480" y="425" width="50" height="100" className="bathroom-fixture" /> {/* Shower */}
          
          {/* Master Bathroom fixtures */}
          <circle cx="450" cy="580" r="20" className="bathroom-fixture" /> {/* Sink */}
          <rect x="425" y="640" width="30" height="40" className="bathroom-fixture" /> {/* Toilet */}
          <rect x="480" y="570" width="50" height="120" className="bathroom-fixture" /> {/* Shower */}
          
          {/* 404 Text as architectural annotation */}
          <text x="400" y="450" className="error-label">404</text>
          
          {/* PAGE NOT FOUND text as architectural annotation */}
          <text x="430" y="500" className="error-sublabel">PAGE NOT FOUND</text>
          
          {/* Enhanced Animated Light Points */}
          {/* Light 1: Tracing the outer perimeter */}
          <circle className="light-point light-1" cx="0" cy="0" r="4">
            <animateMotion 
              path="M200,200 L800,200 L800,700 L200,700 L200,200" 
              dur="15s" 
              repeatCount="indefinite"
            />
          </circle>
          
          {/* Light 2: Moving through living areas */}
          <circle className="light-point light-2" cx="0" cy="0" r="4">
            <animateMotion 
              path="M350,200 L350,300 L500,300 L500,400 L600,400 L700,400" 
              dur="12s" 
              repeatCount="indefinite"
            />
          </circle>
          
          {/* Light 3: Following bedroom path */}
          <circle className="light-point light-3" cx="0" cy="0" r="3.5">
            <animateMotion 
              path="M200,550 L400,550 L550,550 L550,600 L650,600" 
              dur="18s" 
              repeatCount="indefinite"
            />
          </circle>
          
          {/* Light 4: Moving through doors */}
          <circle className="light-point light-4" cx="0" cy="0" r="3">
            <animateMotion 
              path="M350,220 L500,320 L250,400 L400,485 L600,400 L550,615" 
              dur="20s" 
              repeatCount="indefinite"
            />
          </circle>
          
          {/* Light 5: Window path */}
          <circle className="light-point light-5" cx="0" cy="0" r="3">
            <animateMotion 
              path="M250,200 L320,200 L550,200 L650,200 L750,235 L800,500 L200,550 L300,700 L700,700" 
              dur="25s" 
              repeatCount="indefinite"
            />
          </circle>
        </svg>
      </div>
      
      {/* Content */}
      <motion.div 
        className="container relative z-10 w-full max-w-6xl mx-auto px-4 py-10 md:py-20"
        style={{ y: translateY, opacity }}
      >
        {/* Ultra-modern 3D architectural inspired 404 content */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-0">
          {/* Left: The 404 block */}
          <motion.div 
            className="lg:col-span-5 relative flex flex-col justify-center"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Isometric architectural 404 */}
            <div className="mb-8 md:mb-0 perspective-element relative">
              {/* The "4" */}
              <motion.div 
                className="absolute top-0 left-0 w-full h-full"
                initial={{ opacity: 0, rotateY: -30, z: -50 }}
                animate={{ opacity: 1, rotateY: 0, z: 0 }}
                transition={{ duration: 1.2, delay: 0.2, ease: "easeOut" }}
              >
                <div className="number-block">4</div>
              </motion.div>
              
              {/* The "0" */}
              <motion.div 
                className="absolute top-0 left-0 w-full h-full"
                initial={{ opacity: 0, rotateY: 0, z: -100 }}
                animate={{ opacity: 1, rotateY: 0, z: 0 }}
                transition={{ duration: 1.2, delay: 0.4, ease: "easeOut" }}
              >
                <div className="number-block ml-24 md:ml-32">0</div>
              </motion.div>
              
              {/* The second "4" */}
              <motion.div 
                className="absolute top-0 left-0 w-full h-full"
                initial={{ opacity: 0, rotateY: 30, z: -50 }}
                animate={{ opacity: 1, rotateY: 0, z: 0 }}
                transition={{ duration: 1.2, delay: 0.6, ease: "easeOut" }}
              >
                <div className="number-block ml-48 md:ml-64">4</div>
              </motion.div>
              
              {/* Professional architecture angle guides */}
              <svg className="absolute -top-12 -left-4 w-full h-full opacity-40 z-0" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                <line x1="50" y1="0" x2="50" y2="200" className="guide-line" />
                <line x1="150" y1="0" x2="150" y2="200" className="guide-line" />
                <line x1="250" y1="0" x2="250" y2="200" className="guide-line" />
                <line x1="0" y1="100" x2="400" y2="100" className="guide-line" />
                <circle cx="50" cy="100" r="2" className="guide-point" />
                <circle cx="150" cy="100" r="2" className="guide-point" />
                <circle cx="250" cy="100" r="2" className="guide-point" />
              </svg>
            </div>
          </motion.div>
          
          {/* Right: Content and actions */}
          <motion.div 
            className="lg:col-span-7 flex flex-col lg:pl-16 mt-8 lg:mt-0 relative"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          >
            {/* Status bar and coordinates */}
            <div className="mb-4 flex items-center">
              <div className="h-2 w-2 rounded-full bg-secondary mr-2 animate-pulse"></div>
              <div className="text-xs font-mono text-text-secondary tracking-wider">
                STATUS 404 • <span className="text-primary">COORDINATES NOT FOUND</span>
              </div>
            </div>
            
            {/* Main headline */}
            <h1 className="text-3xl md:text-5xl font-bold tracking-tight mb-4 relative">
              <span className="text-text">PAGE</span><br />
              <div className="flex items-center">
                <div className="h-1 w-12 bg-primary mr-4"></div>
                <span className="text-primary">NOT FOUND</span>
              </div>
            </h1>
            
            {/* Dimension lines */}
            <div className="absolute -right-4 top-0 bottom-0 w-[1px] h-full border-r border-dashed border-primary/30"></div>
            <div className="absolute top-16 w-full h-[1px] border-t border-dashed border-primary/30"></div>
            
            {/* Descriptive text with technical styling */}
            <div className="bg-background/50 backdrop-blur-sm rounded-sm border-l-2 border-primary/70 pl-4 py-3 mb-8">
              <p className="text-text-secondary text-sm md:text-base leading-relaxed max-w-lg">
                The spatial coordinates you've requested do not align with our architectural database. 
                This section may be under construction or has been relocated to a different blueprint.
              </p>
            </div>
            
            {/* Modern action set */}
            <div className="mt-auto flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Button 
                variant="primary"
                size="lg"
                href="/"
                className="group flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2 transition-transform duration-300 group-hover:-translate-x-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Return to Main Structure
              </Button>
              
              <Button 
                variant="outline"
                size="lg"
                href="/contact"
                className="group flex items-center justify-center border-primary/50 hover:border-primary"
              >
                <span>Contact Architects</span>
                <svg className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14 5L21 12M21 12L14 19M21 12H3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Button>
            </div>
            
            {/* Blueprint measurement markers */}
            <div className="absolute -bottom-2 left-0 w-full flex justify-between">
              {[0, 25, 50, 75, 100].map((mark) => (
                <div key={mark} className="flex flex-col items-center">
                  <div className="h-2 w-[1px] bg-primary/50"></div>
                  <div className="text-[8px] font-mono text-primary/50 mt-1">{mark}</div>
                </div>
              ))}
            </div>
            
            {/* Project status indicator */}
            <div className="absolute -top-6 right-0 flex items-center text-xs font-mono">
              <div className="w-2 h-2 rounded-full bg-primary/70 mr-2 animate-pulse"></div>
              <span className="text-primary/70">REVISION REQUIRED</span>
            </div>
          </motion.div>
        </div>
        
        {/* Grid animation that reveals */}
        <motion.div 
          className="absolute inset-0 grid-backdrop pointer-events-none"
          initial="gridHidden"
          animate={controls}
          variants={{
            gridHidden: { opacity: 0 },
            gridVisible: { opacity: 0.15, transition: { duration: 1.5 } }
          }}
        ></motion.div>
      </motion.div>
      
      {/* Styles for architectural blueprint */}
      <style jsx>{`
        .architectural-blueprint {
          filter: blur(1.5px);
          transition: transform 0.3s ease, opacity 0.3s ease;
        }
        
        .perspective-element {
          perspective: 1000px;
          height: 240px;
        }
        
        .number-block {
          font-size: 180px;
          line-height: 1;
          font-weight: 900;
          font-family: sans-serif;
          background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          text-shadow: 3px 3px 0 rgba(var(--color-primary), 0.1);
          position: relative;
          display: inline-block;
          letter-spacing: -0.05em;
        }
        
        .number-block::after {
          content: attr(data-text);
          position: absolute;
          left: 0;
          top: 0;
          z-index: -1;
          color: rgba(var(--color-primary), 0.07);
          transform: translateZ(-10px);
        }
        
        .guide-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 1;
          stroke-opacity: 0.5;
          stroke-dasharray: 4 2;
        }
        
        .guide-point {
          fill: rgb(var(--color-primary));
          opacity: 0.7;
        }
        
        .grid-backdrop {
          background-image: linear-gradient(to right, rgba(var(--color-primary), 0.1) 1px, transparent 1px),
                            linear-gradient(to bottom, rgba(var(--color-primary), 0.1) 1px, transparent 1px);
          background-size: 20px 20px;
        }
        
        @keyframes pulseThin {
          0% { stroke-opacity: 0.12; }
          50% { stroke-opacity: 0.18; }
          100% { stroke-opacity: 0.12; }
        }
        
        @keyframes pulseMedium {
          0% { stroke-opacity: 0.18; }
          50% { stroke-opacity: 0.28; }
          100% { stroke-opacity: 0.18; }
        }
        
        @keyframes pulseThick {
          0% { stroke-opacity: 0.3; }
          50% { stroke-opacity: 0.45; }
          100% { stroke-opacity: 0.3; }
        }
        
        @keyframes pulseFill {
          0% { fill-opacity: 0.04; }
          50% { fill-opacity: 0.08; }
          100% { fill-opacity: 0.04; }
        }
        
        .grid-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 0.5;
          stroke-opacity: 0.12;
          animation: pulseThin 8s infinite ease-in-out;
        }
        
        .house-outline {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 2.5;
          stroke-opacity: 0.35;
          animation: pulseThick 10s infinite ease-in-out;
        }
        
        .room-outline {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 1.8;
          stroke-opacity: 0.3;
          animation: pulseThick 12s infinite ease-in-out;
        }
        
        .room-label {
          fill: rgb(var(--color-primary));
          font-size: 12px;
          font-family: monospace;
          text-anchor: middle;
          opacity: 0.25;
          animation: pulseMedium 10s infinite ease-in-out;
        }
        
        .error-label {
          fill: rgb(var(--color-secondary));
          font-size: 60px;
          font-family: monospace;
          font-weight: bold;
          text-anchor: middle;
          opacity: 0.25;
          animation: pulseMedium 4s infinite ease-in-out;
        }
        
        .error-sublabel {
          fill: rgb(var(--color-primary));
          font-size: 20px;
          font-family: monospace;
          font-weight: bold;
          text-anchor: middle;
          opacity: 0.25;
          animation: pulseMedium 4s infinite ease-in-out;
        }
        
        .door {
          fill: none;
          stroke: rgb(var(--color-primary));
          stroke-width: 1.4;
          stroke-opacity: 0.28;
          animation: pulseMedium 12s infinite ease-in-out;
        }
        
        .door-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 1.4;
          stroke-opacity: 0.25;
          animation: pulseMedium 12s infinite ease-in-out;
        }
        
        .window-line {
          stroke: rgb(var(--color-primary));
          stroke-width: 2.5;
          stroke-opacity: 0.35;
          animation: pulseThick 10s infinite ease-in-out;
        }
        
        .furniture {
          fill: rgb(var(--color-primary));
          fill-opacity: 0.1;
          stroke: rgb(var(--color-primary));
          stroke-width: 0.8;
          stroke-opacity: 0.22;
          animation: pulseFill 14s infinite ease-in-out, pulseMedium 14s infinite ease-in-out;
        }
        
        .bathroom-fixture {
          fill: rgb(var(--color-primary));
          fill-opacity: 0.12;
          stroke: rgb(var(--color-primary));
          stroke-width: 1;
          stroke-opacity: 0.22;
          animation: pulseFill 10s infinite ease-in-out;
        }
        
        .light-point {
          fill: #fff;
          filter: blur(3px);
          opacity: 0.9;
        }
        
        .light-1 {
          fill: rgb(var(--color-primary));
          filter: blur(8px) drop-shadow(0 0 15px rgb(var(--color-primary)));
          animation: glowEffect 10s infinite;
        }
        
        .light-2 {
          fill: rgb(var(--color-secondary));
          filter: blur(7px) drop-shadow(0 0 14px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 8s infinite;
        }
        
        .light-3 {
          fill: rgb(var(--color-primary));
          filter: blur(7px) drop-shadow(0 0 12px rgb(var(--color-primary)));
          animation: glowEffect 12s infinite;
        }
        
        .light-4 {
          fill: rgb(var(--color-secondary));
          filter: blur(6px) drop-shadow(0 0 10px rgb(var(--color-secondary)));
          animation: glowEffectSecondary 9s infinite;
        }
        
        .light-5 {
          fill: rgb(var(--color-primary));
          filter: blur(6px) drop-shadow(0 0 10px rgb(var(--color-primary)));
          animation: glowEffect 15s infinite;
        }
        
        @keyframes glowEffect {
          0% { filter: blur(6px) drop-shadow(0 0 15px rgb(var(--color-primary))); opacity: 0.8; }
          50% { filter: blur(10px) drop-shadow(0 0 20px rgb(var(--color-primary))); opacity: 1; }
          100% { filter: blur(6px) drop-shadow(0 0 15px rgb(var(--color-primary))); opacity: 0.8; }
        }
        
        @keyframes glowEffectSecondary {
          0% { filter: blur(6px) drop-shadow(0 0 15px rgb(var(--color-secondary))); opacity: 0.7; }
          50% { filter: blur(10px) drop-shadow(0 0 25px rgb(var(--color-secondary))); opacity: 0.9; }
          100% { filter: blur(6px) drop-shadow(0 0 15px rgb(var(--color-secondary))); opacity: 0.7; }
        }
      `}</style>
    </div>
  );
} 