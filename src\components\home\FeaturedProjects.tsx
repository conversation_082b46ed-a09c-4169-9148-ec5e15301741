"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { motion, useScroll, useTransform, useSpring, useInView } from "framer-motion";
import Button from "../ui/Button";
import Card from "../ui/Card";
import ArchitecturalBackground from "./ArchitecturalBackground";
import { useLanguage } from "@/contexts/LanguageContext";

// Featured Projects content interface
interface FeaturedProjectsContent {
  id: number;
  title: string;
  description: string;
  button: {
    text: string;
    link: string;
  };
  created_at: string;
  updated_at: string;
}

const FeaturedProjects = () => {
  const { locale } = useLanguage();
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, amount: 0.3 });
  const { scrollY } = useScroll();
  
  // Parallax effect for content
  const contentY = useTransform(scrollY, [0, 300], [50, 0]);
  const smoothContentY = useSpring(contentY, { stiffness: 100, damping: 30 });

  // API state management - following Hero.tsx pattern
  const [featuredProjectsContent, setFeaturedProjectsContent] = useState<FeaturedProjectsContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch featured projects content from API - following Hero.tsx pattern
  useEffect(() => {
    const fetchFeaturedProjectsContent = async () => {
      try {
        setIsLoading(true);
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
        const endpoint = `/api/home-page/featured-projects/${locale}/`;
        
        console.log('🔄 Fetching featured projects content from:', `${apiBaseUrl}${endpoint}`);
        
        const response = await fetch(`${apiBaseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();
        console.log('📥 Featured projects content response:', data);

        if (data.success && data.data) {
          setFeaturedProjectsContent(data.data);
        } else {
          console.error('Failed to fetch featured projects content:', data.message);
          // Keep featuredProjectsContent as null to fall back to static content
        }
      } catch (error) {
        console.error('Error fetching featured projects content:', error);
        // Keep featuredProjectsContent as null to fall back to static content
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedProjectsContent();
  }, [locale]);

  // Get content with fallback to static content - following Hero.tsx pattern
  const getContent = () => {
    if (featuredProjectsContent) {
      return {
        title: featuredProjectsContent.title,
        description: featuredProjectsContent.description,
        button: featuredProjectsContent.button
      };
    }
    
    // Fallback to static content
    return {
      title: locale === 'ar' ? 'المشاريع المميزة' : 'Featured Projects',
      description: locale === 'ar' 
        ? 'اكتشف تطويراتنا العقارية الاستثنائية مع المواقع المتميزة والمرافق عالمية المستوى'
        : 'Discover our exceptional real estate developments with premium locations and world-class amenities',
      button: {
        text: locale === 'ar' ? 'عرض جميع المشاريع' : 'View All Projects',
        link: '/projects'
      }
    };
  };

  const content = getContent();

  const projects = [
    {
      id: 1,
      title: "Mazaya Heights",
      location: "Downtown Dubai",
      description: "Luxury residential tower with panoramic city views",
      image: "/images/project-1.jpg", // Replace with actual image paths
      slug: "mazaya-heights",
      category: "Residential",
      status: "Completed",
      features: ["Panoramic Views", "Luxury Finishes", "Smart Home", "Fitness Center"],
    },
    {
      id: 2,
      title: "Mazaya Business Park",
      location: "Business Bay",
      description: "Premium office spaces designed for modern businesses",
      image: "/images/project-2.jpg", // Replace with actual image paths
      slug: "mazaya-business-park",
      category: "Commercial",
      status: "In Progress",
      features: ["Premium Offices", "Meeting Spaces", "Parking", "Security"],
    },
    {
      id: 3,
      title: "Mazaya Villas",
      location: "Palm Jumeirah",
      description: "Exclusive beachfront villas with private pools",
      image: "/images/project-3.jpg", // Replace with actual image paths
      slug: "mazaya-villas",
      category: "Residential",
      status: "Planned",
      features: ["Beachfront", "Private Pools", "Garden", "Gated Community"],
    },
  ];

  return (
    <section ref={sectionRef} className="relative overflow-hidden pb-16 pt-12" id="featured-projects">
      {/* Architectural Background Component */}
      <ArchitecturalBackground />
      
      {/* Remove the existing connecting elements that will be replaced */}
      {/* Custom connecting transition from Hero section */}
      <div className="absolute top-0 start-0 end-0 h-24 pointer-events-none z-0">
        {/* Transparent connector that overlaps with Hero's bottom transition */}
        <div className="absolute top-0 start-0 end-0 h-12 bg-[#0A1429]"></div>
      </div>
      
      <div className="container mx-auto px-4 relative z-10 pt-16">
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6 }}
          style={{ y: smoothContentY }}
        >
          {/* Loading state for title - following Hero.tsx pattern */}
          {isLoading ? (
            <div className="mb-4">
              <div className="h-12 w-80 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded mx-auto animate-pulse"></div>
            </div>
          ) : (
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
              {content.title}
            </h2>
          )}
          
          {/* Loading state for description - following Hero.tsx pattern */}
          {isLoading ? (
            <div className="max-w-3xl mx-auto space-y-2">
              <div className="h-4 w-full bg-white/20 rounded animate-pulse"></div>
              <div className="h-4 w-3/4 bg-white/20 rounded animate-pulse mx-auto"></div>
            </div>
          ) : (
            <p className="text-text-secondary max-w-3xl mx-auto text-lg">
              {content.description}
            </p>
          )}
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
            >
              <div className="group h-full flex flex-col rounded-xl overflow-hidden relative transition-all duration-300 hover:scale-[1.02]">
                {/* Glass effect background */}
                <div className="absolute inset-0 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl z-0 
                  transition-all duration-300 group-hover:border-white/30"></div>
                
                {/* Image container with better aspect ratio */}
                <div className="relative w-full aspect-[4/3] overflow-hidden z-10">
                  {/* Category badge */}
                  <div className="absolute top-3 start-3 z-10">
                    <span className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-white/10 backdrop-blur-md border border-white/20 text-white">
                      {project.category}
                    </span>
                  </div>
                  
                  {/* Status badge */}
                  <div className="absolute top-3 end-3 z-10">
                    <span className={`inline-block px-3 py-1 text-xs font-medium rounded-full backdrop-blur-md border border-white/20 ${
                      project.status === "Completed" 
                        ? "bg-[#0ec6e0]/80 text-white" 
                        : project.status === "In Progress" 
                        ? "bg-amber-500/80 text-white"
                        : "bg-purple-500/80 text-white"
                    }`}>
                      {project.status}
                    </span>
                  </div>
                  
                  {/* Project image */}
                  <div className="absolute inset-0 bg-[#1a1a2e] flex items-center justify-center group-hover:scale-110 transition-transform duration-700 ease-in-out">
                    <span className="text-[rgb(var(--color-text))]/70">{project.title}</span>
                  </div>
                  
                  {/* Enhanced overlay gradient - using the Hero gradient style */}
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-950 via-gray-950/40 to-transparent opacity-80 z-0 group-hover:opacity-70 transition-opacity duration-300"></div>
                </div>
                
                {/* Content */}
                <div className="flex flex-col flex-grow p-5 relative z-10">
                  {/* Title with enhanced hover effect */}
                  <h3 className="text-xl font-bold mb-1 text-white transition-all duration-300 relative inline-block">
                    <span className="relative z-10 group-hover:text-[rgb(var(--color-primary))]">{project.title}</span>
                    <span className="absolute bottom-0 start-0 w-0 h-0.5 bg-[rgb(var(--color-primary))]/70 group-hover:w-full transition-all duration-500"></span>
                  </h3>
                  
                  {/* Location */}
                  <div className="flex items-center text-gray-300 mb-2 text-sm">
                    <svg className="h-4 w-4 me-1 text-[rgb(var(--color-primary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {project.location}
                  </div>
                  
                  {/* Description */}
                  <p className="text-gray-300 text-sm mb-4 group-hover:text-gray-200 transition-colors duration-300">
                    {project.description}
                  </p>
                  
                  {/* Enhanced features */}
                  <div className="flex flex-wrap gap-2 mb-5">
                    {project.features.slice(0, 3).map((feature, index) => (
                      <span 
                        key={index} 
                        className="px-3 py-1 bg-white/10 text-white text-xs rounded-full border border-white/20 backdrop-blur-sm 
                        transition-all duration-300 hover:bg-white/15"
                      >
                        {feature}
                      </span>
                    ))}
                    {project.features.length > 3 && (
                      <span className="px-3 py-1 bg-[rgb(var(--color-primary))]/10 text-[rgb(var(--color-primary))] text-xs rounded-full border border-[rgb(var(--color-primary))]/20 backdrop-blur-sm
                      transition-all duration-300 hover:bg-[rgb(var(--color-primary))]/20">
                        +{project.features.length - 3} more
                      </span>
                    )}
                  </div>
                  
                  {/* Enhanced View Details Link - Updated to match Hero buttons */}
                  <div className="mt-auto">
                    <Link
                      href={`/projects/${project.slug}`}
                      className="inline-flex items-center rounded-full border border-white/30 bg-transparent px-4 py-2 text-sm font-medium text-white transition hover:bg-white/10"
                    >
                      <span>View Details</span>
                      <svg className="w-4 h-4 ms-1.5 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          {/* Loading state for button - following Hero.tsx pattern */}
          {isLoading ? (
            <div className="h-12 w-48 bg-white/20 rounded-full animate-pulse mx-auto"></div>
          ) : (
            <Link 
              href={content.button.link}
              className="group relative inline-flex items-center justify-center overflow-hidden rounded-full border border-transparent bg-white px-6 py-3 text-base font-medium text-gray-900 transition hover:scale-105"
            >
              <span>{content.button.text}</span>
              <svg className="ms-2 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </Link>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedProjects; 