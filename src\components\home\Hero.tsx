"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

// Hero content interface
interface HeroContent {
  id: number;
  badge: string;
  title: {
    line1: string;
    line2: string;
    line3: string;
  };
  description: string;
  buttons: {
    primary: {
      text: string;
      link: string;
    };
    secondary: {
      text: string;
      link: string;
    };
  };
  bottomText: string;
  created_at: string;
  updated_at: string;
}

// Featured project data
const getFeaturedProjects = (locale: string) => [
  {
    id: 1,
    title: t("hero.projects.project1.title", locale),
    location: t("hero.projects.project1.location", locale),
    description: t("hero.projects.project1.description", locale),
    image: "/images/hero-1.jpg",
    details: t("hero.projects.project1.details", locale),
    type: t("hero.projects.project1.type", locale)
  },
  {
    id: 2,
    title: t("hero.projects.project2.title", locale),
    location: t("hero.projects.project2.location", locale),
    description: t("hero.projects.project2.description", locale),
    image: "/images/hero-2.jpg",
    details: t("hero.projects.project2.details", locale),
    type: t("hero.projects.project2.type", locale)
  },
  {
    id: 3,
    title: t("hero.projects.project3.title", locale),
    location: t("hero.projects.project3.location", locale),
    description: t("hero.projects.project3.description", locale),
    image: "/images/hero-3.jpg",
    details: t("hero.projects.project3.details", locale),
    type: t("hero.projects.project3.type", locale)
  }
];

const Hero = () => {
  const { locale } = useLanguage();
  const featuredProjects = getFeaturedProjects(locale);
  const [current, setCurrent] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [heroContent, setHeroContent] = useState<HeroContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });
  
  const parallaxY = useTransform(scrollYProgress, [0, 1], [0, 300]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
  
  const [stars, setStars] = useState<{ id: number; size: number; top: number; start: number; delay: number; duration: number }[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);

  // Fetch hero content from API
  useEffect(() => {
    const fetchHeroContent = async () => {
      try {
        setIsLoading(true);
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
        const endpoint = `/api/home-page/hero/${locale}/`;
        
        console.log('🔄 Fetching hero content from:', `${apiBaseUrl}${endpoint}`);
        
        const response = await fetch(`${apiBaseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();
        console.log('📥 Hero content response:', data);

        if (data.success && data.data) {
          setHeroContent(data.data);
        } else {
          console.error('Failed to fetch hero content:', data.message);
          // Keep heroContent as null to fall back to translations
        }
      } catch (error) {
        console.error('Error fetching hero content:', error);
        // Keep heroContent as null to fall back to translations
      } finally {
        setIsLoading(false);
      }
    };

    fetchHeroContent();
  }, [locale]);
  
  // Auto advance carousel only when not hovering
  useEffect(() => {
    if (isHovering) return;
    
    const interval = setInterval(() => {
      setCurrent(prev => (prev === featuredProjects.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, [isHovering, featuredProjects.length]);

  // Generate stars on client-side only
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const generateStars = () => {
        const newStars = Array.from({ length: 100 }).map((_, index) => ({
          id: index,
          size: Math.random() * 2 + 0.5,
          top: Math.random() * 100,
          start: Math.random() * 100,
          delay: Math.random() * 5,
          duration: Math.floor(Math.random() * 4) + 3
        }));
        
        setStars(newStars);
        setIsHydrated(true);
      };
      
      generateStars();
    }
  }, []);

  const goToSlide = (index: number) => {
    setCurrent(index);
  };

  const nextSlide = () => {
    setCurrent(current === featuredProjects.length - 1 ? 0 : current + 1);
  };

  const prevSlide = () => {
    setCurrent(current === 0 ? featuredProjects.length - 1 : current - 1);
  };

  // Split text animation function
  const SplitText = ({ text, className }: { text: string, className?: string }) => {
    // Check if text contains Arabic characters
    const isArabic = /[\u0600-\u06FF]/.test(text);
    
    if (isArabic) {
      // For Arabic text, split by words to preserve letter connections
      const words = text.split(" ");
      return (
        <span className={className}>
          {words.map((word, wordIndex) => (
            <motion.span
              key={wordIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.6, 
                delay: 0.1 * wordIndex,
                ease: [0.215, 0.61, 0.355, 1]
              }}
              className="inline-block"
            >
              {word}
              {wordIndex < words.length - 1 && "\u00A0"}
            </motion.span>
          ))}
        </span>
      );
    } else {
      // For English text, split by characters for the original effect
      return (
        <span className={className}>
          {text.split("").map((char, index) => (
            <motion.span
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 0.6, 
                delay: 0.05 * index,
                ease: [0.215, 0.61, 0.355, 1]
              }}
              className="inline-block"
            >
              {char === " " ? "\u00A0" : char}
            </motion.span>
          ))}
        </span>
      );
    }
  };

  // Get content with fallback to translations
  const getContent = () => {
    if (heroContent) {
      return {
        badge: heroContent.badge,
        title: heroContent.title,
        description: heroContent.description,
        buttons: heroContent.buttons,
        bottomText: heroContent.bottomText
      };
    }
    
    // Fallback to translations
    return {
      badge: t("hero.tagline", locale),
      title: {
        line1: t("hero.headline.line1", locale),
        line2: t("hero.headline.line2", locale),
        line3: t("hero.headline.line3", locale)
      },
      description: t("hero.description", locale),
      buttons: {
        primary: {
          text: t("hero.cta.projects", locale),
          link: "/projects"
        },
        secondary: {
          text: t("hero.cta.contact", locale),
          link: "/contact"
        }
      },
      bottomText: t("hero.established", locale)
    };
  };

  const content = getContent();

  return (
    <motion.section 
      ref={containerRef}
      className="relative min-h-screen flex items-center overflow-hidden bg-[#0A1429] py-10 md:py-16"
      style={{ opacity }}
    >
      {/* Architectural grid overlay */}
      <div className="absolute inset-0 z-10">
        <div className="h-full w-full grid grid-cols-6 lg:grid-cols-12">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="border-s border-white/5 h-full">
              {i === 0 && <div className="border-e border-white/5 h-full w-full"></div>}
            </div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="col-span-full border-t border-white/5 h-0"></div>
          ))}
        </div>
      </div>

      {/* Subtle background patterns */}
      <motion.div 
        className="absolute top-0 start-0 w-full h-full opacity-10"
        style={{ y: parallaxY }}
      >
        <div className="absolute top-0 end-0 w-[600px] h-[600px] bg-gradient-to-b from-blue-500/30 to-transparent rounded-full filter blur-[120px] -translate-y-1/2 translate-x-1/3"></div>
        <div className="absolute bottom-0 start-0 w-[500px] h-[500px] bg-gradient-to-t from-purple-600/20 to-transparent rounded-full filter blur-[100px] translate-y-1/3 -translate-x-1/4"></div>
      </motion.div>
      
      {/* Stars effect - client-side only rendering */}
      {isHydrated && (
        <div className="star-field">
          {stars.map((star) => (
            <div 
              key={`star-${star.id}`}
              className="star"
              style={{
                width: `${star.size}px`,
                height: `${star.size}px`,
                top: `${star.top}%`,
                left: `${star.start}%`,
                animationDuration: `${star.duration}s`,
                animationDelay: `${star.delay}s`
              }}
            />
          ))}
        </div>
      )}
      
      <div className="container mx-auto px-4 relative z-20">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16">
          {/* Start column: Hero text */}
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="w-full lg:w-5/12 text-white space-y-6"
          >
            {/* Loading state for badge */}
            {isLoading ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-4"
              >
                <div className="h-4 w-48 bg-white/20 rounded animate-pulse"></div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-4"
              >
                <span className="text-sm font-medium tracking-wide">{content.badge}</span>
              </motion.div>
            )}
            
            {/* Loading state for title */}
            {isLoading ? (
              <div className="space-y-4">
                <div className="h-12 w-64 bg-white/20 rounded animate-pulse"></div>
                <div className="h-12 w-56 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded animate-pulse"></div>
                <div className="h-12 w-48 bg-white/20 rounded animate-pulse"></div>
              </div>
            ) : (
              <h1 className={`text-5xl md:text-7xl font-bold leading-tight ${locale === 'ar' ? 'tracking-normal' : 'tracking-tight'}`}>
                <SplitText text={content.title.line1} className="block text-white" />
                <span className="block h-2"></span>
                <SplitText text={content.title.line2} className="block bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-500" />
                <span className="block h-2"></span>
                <SplitText text={content.title.line3} className="block text-white" />
              </h1>
            )}
            
            {/* Loading state for description */}
            {isLoading ? (
              <div className="space-y-2">
                <div className="h-4 w-full bg-white/20 rounded animate-pulse"></div>
                <div className="h-4 w-3/4 bg-white/20 rounded animate-pulse"></div>
                <div className="h-4 w-5/6 bg-white/20 rounded animate-pulse"></div>
              </div>
            ) : (
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="text-xl text-gray-300 max-w-lg"
              >
                {content.description}
              </motion.p>
            )}
            
            {/* Loading state for buttons */}
            {isLoading ? (
              <div className="flex flex-wrap items-center gap-4 pt-2">
                <div className="h-12 w-40 bg-white/20 rounded-full animate-pulse"></div>
                <div className="h-12 w-32 bg-white/10 border border-white/30 rounded-full animate-pulse"></div>
              </div>
            ) : (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
                className="flex flex-wrap items-center gap-4 pt-2"
              >
                <Link
                  href={content.buttons.primary.link}
                  className="group relative inline-flex items-center justify-center overflow-hidden rounded-full border border-transparent bg-white px-6 py-3 text-base font-medium text-gray-900 transition hover:scale-105"
                >
                  <span>{content.buttons.primary.text}</span>
                  <svg className="ms-2 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
                <Link
                  href={content.buttons.secondary.link}
                  className="relative inline-flex items-center justify-center overflow-hidden rounded-full border border-white/30 bg-transparent px-6 py-3 text-base font-medium text-white transition hover:bg-white/10"
                >
                  <span>{content.buttons.secondary.text}</span>
                </Link>
              </motion.div>
            )}
            
            {/* Loading state for bottom text */}
            {isLoading ? (
              <div className="pt-8 hidden lg:block">
                <div className="flex items-center gap-6">
                  <div className="w-12 h-[1px] bg-white/30"></div>
                  <div className="h-4 w-48 bg-white/20 rounded animate-pulse"></div>
                </div>
              </div>
            ) : (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.9 }}
                className="pt-8 hidden lg:block"
              >
                <div className="flex items-center gap-6">
                  <div className="w-12 h-[1px] bg-white/30"></div>
                  <p className="text-white/70 uppercase tracking-wider text-sm font-medium">
                    {content.bottomText}
                  </p>
                </div>
              </motion.div>
            )}
          </motion.div>
          
          {/* End column: Project showcase */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="w-full lg:w-7/12"
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
          >
            <div className="relative w-full h-[350px] sm:h-[400px] md:h-[450px] lg:h-[500px] xl:h-[600px] rounded-xl overflow-hidden mb-12 md:mb-16">
              {/* Project carousel */}
              <AnimatePresence mode="wait">
                {featuredProjects.map((project, index) => (
                  index === current && (
                    <motion.div
                      key={project.id}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.7 }}
                      className="absolute inset-0 w-full h-full"
                    >
                      <div className="relative w-full h-full">
                        <Image
                          src={project.image}
                          alt={project.title}
                          fill
                          className="object-cover"
                          priority={index === 0}
                          sizes="(max-width: 768px) 100vw, 700px"
                        />
                        
                        {/* Gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-gray-950 via-gray-950/40 to-transparent"></div>
                        
                        {/* Project info card */}
                        <motion.div 
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: 0.3 }}
                          className="absolute bottom-0 start-0 end-0 p-4 sm:p-6 md:p-8"
                        >
                          <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-4 sm:p-6 max-w-md">
                            <div className="flex flex-wrap items-center gap-2 mb-3 md:mb-4">
                              <span className="px-2 sm:px-3 py-1 bg-blue-600/90 text-white text-xs rounded-full">
                                {project.type}
                              </span>
                              <span className="px-2 sm:px-3 py-1 bg-white/20 text-white text-xs rounded-full">
                                {project.location}
                              </span>
                            </div>
                            
                            <h3 className="text-xl sm:text-2xl font-bold text-white mb-2">{project.title}</h3>
                            <p className="text-gray-300 mb-3 md:mb-4 text-xs sm:text-sm">{project.description}</p>
                            
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                              <div className="text-xs text-white/70">
                                {project.details}
                              </div>
                              
                              <Link
                                href={`/projects/${project.id}`}
                                className="inline-flex items-center text-xs sm:text-sm font-medium text-white bg-white/10 hover:bg-white/20 px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg transition-all"
                              >
                                {t("hero.viewDetails", locale)}
                                <svg className="w-3 h-3 sm:w-4 sm:h-4 ms-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                              </Link>
                            </div>
                          </div>
                        </motion.div>
                      </div>
                    </motion.div>
                  )
                ))}
              </AnimatePresence>

              {/* Navigation arrows */}
              <div className="absolute top-1/2 start-0 end-0 flex justify-between items-center px-2 sm:px-4 z-10 transform -translate-y-1/2">
                <button
                  onClick={prevSlide}
                  className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center text-white hover:bg-black/50 transition"
                  aria-label={t("hero.navigation.previous", locale)}
                >
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={nextSlide}
                  className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-black/30 backdrop-blur-sm flex items-center justify-center text-white hover:bg-black/50 transition"
                  aria-label={t("hero.navigation.next", locale)}
                >
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
              
              {/* Carousel indicators */}
              <div className="absolute bottom-24 sm:bottom-28 md:bottom-32 end-4 sm:end-8 flex flex-col space-y-1 sm:space-y-2 z-10">
                {featuredProjects.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className="group flex items-center"
                    aria-label={t("hero.navigation.goToSlide", locale).replace("{{number}}", String(index + 1))}
                  >
                    <span className={`w-4 sm:w-6 h-[2px] me-1 sm:me-2 transition-all ${
                      current === index ? "bg-white w-8 sm:w-10" : "bg-white/40 group-hover:bg-white/60"
                    }`}></span>
                    <span className={`text-xs font-medium transition-all ${
                      current === index ? "text-white" : "text-white/40 group-hover:text-white/60"
                    }`}>0{index + 1}</span>
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
      
      {/* Scroll indicator - adjusted position */}
      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.5, duration: 1 }}
        className="absolute bottom-4 md:bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-20 pointer-events-none"
      >
        <span className="text-white/60 text-sm mb-2 text-center">{t("hero.scrollToExplore", locale)}</span>
        <motion.div 
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
          className="w-6 h-10 border-2 border-white/40 rounded-full flex justify-center p-1"
        >
          <motion.div 
            animate={{ y: [0, 12, 0] }}
            transition={{ repeat: Infinity, duration: 2 }}
            className="w-1.5 h-2 bg-white rounded-full"
          />
        </motion.div>
      </motion.div>
      
      {/* Bottom transition element for blending with FeaturedProjects */}
      <div className="absolute bottom-0 start-0 end-0 h-24 bg-gradient-to-t from-[#0A1429] to-transparent z-10 pointer-events-none">
        {/* Architectural skyline silhouette for transition */}
        <svg className="absolute bottom-0 w-full h-16" viewBox="0 0 1440 100" preserveAspectRatio="none">
          <path
            d="M0,80 L60,78 L60,55 L80,55 L80,70 L120,68 L120,50 L140,50 L140,64 L180,62 L180,70 L240,68 L240,60 L260,60 L260,72 L320,70 L320,45 L340,45 L340,64 L400,62 L400,40 L420,40 L420,58 L470,56 L470,35 L490,35 L490,60 L560,58 L560,45 L580,45 L580,64 L640,62 L640,48 L660,48 L660,68 L720,66 L720,50 L740,50 L740,76 L800,74 L800,55 L820,55 L820,64 L880,62 L880,42 L900,42 L900,66 L960,64 L960,52 L980,52 L980,68 L1040,66 L1040,45 L1060,45 L1060,58 L1120,56 L1120,40 L1140,40 L1140,60 L1200,58 L1200,48 L1220,48 L1220,64 L1280,62 L1280,40 L1300,40 L1300,56 L1360,54 L1360,38 L1380,38 L1380,58 L1440,56 L1440,80 Z"
            fill="#0A1429"
            fillOpacity="0.5"
          />
        </svg>
      </div>
    </motion.section>
  );
};

export default Hero; 