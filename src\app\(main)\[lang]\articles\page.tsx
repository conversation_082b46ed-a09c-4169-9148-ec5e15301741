"use client";

import ArticlesGrid from "@/components/articles/ArticlesGrid";
import Image from "next/image";
import { useRef, useEffect, useState } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import dynamic from "next/dynamic";

// Dynamically import AnimatedBackground with no SSR
const AnimatedBackground = dynamic(
  () => import("@/components/projects/AnimatedBackground"),
  { ssr: false }
);

export default function ArticlesPage() {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  
  // For parallax scrolling effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Parallax effect values
  const backgroundY = useTransform(scrollYProgress, [0, 1], [0, 30]);
  const textY = useTransform(scrollYProgress, [0, 0.5], [0, -50]);
  const textOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
  const decorativeElementsY = useTransform(scrollYProgress, [0, 1], [0, -80]);
  
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  return (
    <div className="flex flex-col space-y-16 pb-16">
      {/* Modern Hero Section for Articles with Parallax effects */}
      <div ref={sectionRef} className="relative overflow-hidden h-[90vh] min-h-[600px]">
        {/* Animated Background with lighting and shapes */}
        <AnimatedBackground />
        
        {/* Background gradient with parallax effect */}
        <motion.div 
          className="absolute inset-0 bg-gradient-to-r from-[#0A0F23] via-[#101736] to-[#0A0F23] z-0"
          style={{ y: backgroundY }}
        ></motion.div>
        
        {/* Grid pattern overlay with parallax */}
        <motion.div 
          className="absolute inset-0 bg-[url('/images/pattern.png')] bg-repeat opacity-5 z-1"
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, 15]) }}
        ></motion.div>
        
        {/* Decorative elements with parallax */}
        <motion.div 
          className="absolute right-0 top-0 w-1/3 h-full bg-gradient-to-l from-[rgb(var(--color-primary))]/10 to-transparent opacity-40 z-2"
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, 40]) }}
        ></motion.div>
        
        <motion.div 
          className="absolute -left-24 -bottom-24 w-96 h-96 rounded-full bg-[rgb(var(--color-secondary))]/10 blur-3xl z-2"
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, -30]) }}
        ></motion.div>
        
        {/* Glowing orbs */}
        <motion.div
          className="absolute top-1/4 left-1/5 w-32 h-32 rounded-full bg-[rgb(var(--color-primary))]/20 blur-xl z-2"
          animate={{ 
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, -50]) }}
        ></motion.div>
        
        <motion.div
          className="absolute bottom-1/3 right-1/4 w-40 h-40 rounded-full bg-[rgb(var(--color-secondary))]/15 blur-xl z-2"
          animate={{ 
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, -70]) }}
        ></motion.div>
        
        {/* Light beams */}
        <motion.div
          className="absolute -top-20 left-1/3 w-1 h-[60vh] bg-gradient-to-b from-[rgb(var(--color-primary))]/0 via-[rgb(var(--color-primary))]/30 to-[rgb(var(--color-primary))]/0 z-2 rotate-12"
          animate={{ 
            opacity: [0.1, 0.4, 0.1],
            height: ["50vh", "60vh", "50vh"],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, -100]) }}
        ></motion.div>
        
        <motion.div
          className="absolute -bottom-10 right-1/4 w-1 h-[40vh] bg-gradient-to-b from-[rgb(var(--color-secondary))]/0 via-[rgb(var(--color-secondary))]/20 to-[rgb(var(--color-secondary))]/0 z-2 -rotate-12"
          animate={{ 
            opacity: [0.1, 0.3, 0.1],
            height: ["30vh", "40vh", "30vh"],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          style={{ y: useTransform(scrollYProgress, [0, 1], [0, -80]) }}
        ></motion.div>
        
        {/* Floating article cards - decorative with animation and parallax */}
        <motion.div 
          className="absolute -right-20 top-1/4 w-64 h-48 rounded-lg bg-[rgb(var(--color-text))]/5 backdrop-blur-sm border border-[rgb(var(--color-text))]/10 rotate-6 opacity-60 hidden lg:block z-3"
          animate={{ 
            rotate: [6, 8, 6],
            y: [0, -10, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          style={{ y: decorativeElementsY }}
        ></motion.div>
        
        <motion.div 
          className="absolute -left-10 bottom-1/4 w-48 h-32 rounded-lg bg-[rgb(var(--color-text))]/5 backdrop-blur-sm border border-[rgb(var(--color-text))]/10 -rotate-3 opacity-40 hidden lg:block z-3"
          animate={{ 
            rotate: [-3, -5, -3],
            y: [0, 10, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          style={{ y: decorativeElementsY }}
        ></motion.div>
        
        {/* Content container with parallax */}
        <motion.div 
          className="relative container mx-auto px-4 py-20 md:py-28 h-full flex items-center z-10"
          style={{ 
            y: textY,
            opacity: textOpacity
          }}
        >
          <div className="flex flex-col md:flex-row items-center md:items-start justify-between w-full">
            {/* Text content */}
            <div className="max-w-2xl md:flex-1">
              <motion.div 
                className="inline-block px-4 py-1 rounded-full bg-[rgb(var(--color-primary))]/20 backdrop-blur-sm text-[rgb(var(--color-primary))] text-sm font-medium mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                Latest Insights
              </motion.div>
              
              <motion.h1 
                className="text-4xl md:text-6xl font-bold mb-6 text-white"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <span className="relative">
                  Articles & Insights
                  <motion.span 
                    className="absolute bottom-2 left-0 w-full h-1 bg-gradient-to-r from-[rgb(var(--color-primary))] to-[rgb(var(--color-secondary))]"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 0.8, delay: 0.8 }}
                  ></motion.span>
                </span>
              </motion.h1>
              
              <motion.p 
                className="text-xl leading-relaxed text-white/70 max-w-xl mb-8"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Stay updated with the latest news, industry trends, and expert insights in real estate development and investment.
              </motion.p>
              
              {/* Stats */}
              <motion.div 
                className="grid grid-cols-3 gap-4 max-w-md"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
              >
                <motion.div 
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <p className="text-3xl font-bold text-[rgb(var(--color-primary))]">6+</p>
                  <p className="text-sm text-white/70">Articles</p>
                </motion.div>
                <motion.div 
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <p className="text-3xl font-bold text-[rgb(var(--color-primary))]">5+</p>
                  <p className="text-sm text-white/70">Categories</p>
                </motion.div>
                <motion.div 
                  className="text-center"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <p className="text-3xl font-bold text-[rgb(var(--color-primary))]">12+</p>
                  <p className="text-sm text-white/70">Insights</p>
                </motion.div>
              </motion.div>
            </div>
            
            {/* Decorative image - hidden on mobile, positioned to the right */}
            <motion.div 
              className="hidden md:block relative w-80 h-80 lg:w-96 lg:h-96 md:ml-8 mt-12 md:mt-0 md:self-center"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <motion.div 
                className="relative w-full h-full overflow-hidden rounded-xl border border-white/10 backdrop-blur-sm"
                whileHover={{ scale: 1.03 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <div className="absolute inset-0 bg-[#101736] flex items-center justify-center">
                  <svg className="w-24 h-24 text-white/10" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1h2a2 2 0 012 2v11a2 2 0 01-2 2zm-9-1h10V9h-10v10zm-6 0h4V9H4v10zm8-14V5H5v2h7zm6 3v2h-4V8h4z" />
                  </svg>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                  <div className="text-xs text-white/60 mb-1">Featured</div>
                  <h3 className="text-lg font-bold text-white">Real Estate Insights & Expert Analysis</h3>
                </div>
              </motion.div>
              
              {/* Floating elements with animation */}
              <motion.div 
                className="absolute -top-6 -right-6 w-24 h-24 bg-[rgb(var(--color-primary))]/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-[rgb(var(--color-primary))]/30"
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 10, 0],
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <svg className="w-10 h-10 text-[rgb(var(--color-primary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </motion.div>
              <motion.div 
                className="absolute -bottom-4 -left-4 w-20 h-20 bg-[rgb(var(--color-secondary))]/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-[rgb(var(--color-secondary))]/30 rotate-12"
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [12, 0, 12],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              >
                <svg className="w-8 h-8 text-[rgb(var(--color-secondary))]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1h2a2 2 0 012 2v11a2 2 0 01-2 2z" />
                </svg>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
        
        {/* Scroll indicator */}
        {isMounted && (
          <motion.div 
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex flex-col items-center space-y-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.8 }}
          >
            <motion.div
              animate={{ 
                y: [0, 10, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              <p className="text-sm text-[rgb(var(--color-text))]/60">Scroll to explore</p>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-[rgb(var(--color-text))]/60 mx-auto">
                <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3" />
              </svg>
            </motion.div>
          </motion.div>
        )}
        
        {/* Bottom fade */}
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-[rgb(var(--color-background))] to-transparent z-10"></div>
      </div>
      
      {/* Articles grid */}
      <div className="container mx-auto px-4">
        <ArticlesGrid />
      </div>
    </div>
  );
} 