"use client";

import React from "react";

const ArticlesBackground = () => {
  return (
    <div className="absolute inset-0 z-0 bg-[#0A1429] overflow-hidden">
      {/* Base gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0A1429] via-[#101b36] to-[#0f1b38]"></div>
      
      {/* Smooth fluid shapes */}
      <div className="absolute top-[-30%] end-[-20%] w-[80%] h-[80%] rounded-full bg-gradient-to-b from-blue-500/10 to-purple-600/5 blur-[100px] transform rotate-12"></div>
      <div className="absolute bottom-[-20%] start-[-10%] w-[70%] h-[70%] rounded-full bg-gradient-to-tr from-purple-600/8 to-blue-400/3 blur-[120px] transform -rotate-6"></div>
      
      {/* Subtle floating orbs */}
      <div className="absolute top-[15%] start-[20%] w-[200px] h-[200px] rounded-full bg-gradient-to-r from-blue-400/10 to-cyan-300/5 blur-[50px]"></div>
      <div className="absolute bottom-[25%] end-[15%] w-[300px] h-[300px] rounded-full bg-gradient-to-l from-indigo-500/8 to-purple-400/3 blur-[60px]"></div>
      <div className="absolute top-[60%] start-[60%] w-[150px] h-[150px] rounded-full bg-gradient-to-tl from-cyan-400/8 to-blue-500/3 blur-[40px]"></div>
      
      {/* Abstract design elements */}
      <div className="absolute top-[10%] end-[30%] w-[400px] h-[400px] rounded-full border border-white/3 opacity-10 transform rotate-45"></div>
      <div className="absolute bottom-[10%] start-[25%] w-[350px] h-[350px] rounded-full border border-white/2 opacity-5 transform -rotate-12"></div>
      
      {/* Soft vignette overlay */}
      <div className="absolute inset-0 bg-radial-at-center from-transparent via-transparent to-[#0A1429]/80"></div>
    </div>
  );
};

export default ArticlesBackground;