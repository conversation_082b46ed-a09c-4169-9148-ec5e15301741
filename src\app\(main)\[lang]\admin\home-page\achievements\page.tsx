"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/hooks/useAuth';
import { authenticatedApiCall } from '@/utils/api';
import { FiSave, FiEdit3, FiX, FiEye, FiAward, FiPlus, FiTrash2, FiLoader } from 'react-icons/fi';
import IconPicker, { getIconComponent } from '@/components/admin/IconPicker';

// API interfaces matching backend structure
interface AchievementItem {
  id: number;
  icon_name: string;
  achievement_value: string;
  title: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
}

interface AchievementsSection {
  id: number;
  badge: {
    en: string;
    ar: string;
  };
  title: {
    en: string;
    ar: string;
  };
  supporting_text: {
    en: string;
    ar: string;
  };
  created_at?: string;
  updated_at?: string;
}



// Achievement Form Component
function AchievementForm({
  achievement,
  onSave,
  onCancel
}: {
  achievement: AchievementItem;
  onSave: (achievement: AchievementItem) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(achievement);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <IconPicker
            value={formData.icon_name}
            onChange={(value: string) => setFormData(prev => ({ ...prev, icon_name: value }))}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Value</label>
          <input
            type="text"
            value={formData.achievement_value}
            onChange={(e) => setFormData(prev => ({ ...prev, achievement_value: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            placeholder="e.g., 100+, 5 Years, 99%"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Achievement
        </button>
      </div>
    </form>
  );
}

export default function HomePageAchievementsManagement() {
  const router = useRouter();
  const { locale } = useLanguage();
  const { isAuthenticated, tokens, logout } = useAuth();
  const [achievementsSection, setAchievementsSection] = useState<AchievementsSection | null>(null);
  const [achievementItems, setAchievementItems] = useState<AchievementItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [editingSection, setEditingSection] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<number | null>(null);

  // Helper function to handle authentication errors (without useCallback to avoid dependency issues)
  const handleAuthError = async (error: any, operation: string) => {
    console.error(`Authentication error during ${operation}:`, error);

    // Check if it's an authentication/authorization error
    const isAuthError = error.message?.includes('Authentication') ||
                       error.message?.includes('Unauthorized') ||
                       error.message?.includes('401') ||
                       error.message?.includes('403') ||
                       error.message?.includes('Token') ||
                       error.message?.includes('expired') ||
                       error.message?.includes('credentials') ||
                       error.message?.includes('permission');

    if (isAuthError) {
      alert('Your session has expired. Please log in again.');

      // Clear authentication and redirect to login
      await logout();
      router.push('/en/login');
      return true;
    }

    return false;
  };

  // Fetch data from API
  useEffect(() => {
    let isMounted = true;
    let timeoutId: NodeJS.Timeout;

    const fetchData = async () => {
      // Only fetch if authenticated
      if (!isAuthenticated || !tokens?.access) {
        if (isMounted) {
          setIsLoading(false);
        }
        return;
      }

      if (isMounted) {
        setIsLoading(true);
      }

      try {
        console.log('🔄 Fetching achievements data...');

        // Use authenticatedApiCall for both endpoints
        const [sectionResponse, itemsResponse] = await Promise.all([
          authenticatedApiCall('/api/admin/home-page/achievements/'),
          authenticatedApiCall('/api/admin/home-page/achievements/items/')
        ]);

        if (!isMounted) return;

        console.log('📊 Section response:', sectionResponse);
        console.log('📊 Items response:', itemsResponse);

        if (sectionResponse.success && sectionResponse.data) {
          setAchievementsSection(sectionResponse.data as AchievementsSection);
        }

        if (itemsResponse.success && itemsResponse.data) {
          // Handle paginated response
          let itemsArray: any[] = [];

          if (Array.isArray(itemsResponse.data)) {
            itemsArray = itemsResponse.data;
          } else if (typeof itemsResponse.data === 'object' && 'items' in itemsResponse.data) {
            const paginatedData = itemsResponse.data as { items: any[] };
            if (Array.isArray(paginatedData.items)) {
              itemsArray = paginatedData.items;
            }
          }

          setAchievementItems(itemsArray);
        }
      } catch (error) {
        if (!isMounted) return;

        console.error('Error fetching achievements data:', error);

        // Check if it's an authentication error
        const isAuthError = await handleAuthError(error, 'fetching data');
        if (!isAuthError) {
          alert('Error loading achievements data. Please check your connection and try again.');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    // Debounce the fetch to prevent rapid successive calls
    timeoutId = setTimeout(() => {
      fetchData();
    }, 100);

    // Cleanup function
    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [locale, isAuthenticated, tokens?.access]); // Include essential auth dependencies but use debouncing

  // Save section data
  const handleSaveSection = async () => {
    if (!achievementsSection) return;

    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to save changes');
      return;
    }

    try {
      setIsSaving(true);

      // Use authenticatedApiCall for proper authentication handling
      const response = await authenticatedApiCall('/api/admin/home-page/achievements/', {
        method: 'PUT',
        body: JSON.stringify({
          badge: achievementsSection.badge,
          title: achievementsSection.title,
          supporting_text: achievementsSection.supporting_text
        })
      });

      if (response.success) {
        alert('Section saved successfully!');
        setEditingSection(false);
        // Update local state with response data
        if (response.data && typeof response.data === 'object') {
          const updatedData = response.data as AchievementsSection;
          setAchievementsSection(prev => prev ? {
            ...prev,
            ...updatedData,
            updated_at: updatedData.updated_at
          } : null);
        }
      } else {
        // Check for authentication errors in response
        const errorMessage = response.message || 'Failed to save section';
        if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
            errorMessage.includes('401') || errorMessage.includes('403')) {
          throw new Error('Authentication expired');
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error saving section:', error);

      // Check if it's an authentication error
      const isAuthError = await handleAuthError(error, 'saving section');
      if (!isAuthError) {
        alert(`Error saving section: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Add new achievement item
  const addAchievementItem = () => {
    const newItem: AchievementItem = {
      id: Date.now(), // Temporary ID
      icon_name: "FaTarget",
      achievement_value: "0",
      title: {
        en: "New Achievement",
        ar: "إنجاز جديد"
      }
    };
    setAchievementItems(prev => [...prev, newItem]);
    setEditingItem(newItem.id);
  };

  // Save achievement item
  const handleSaveItem = async (item: AchievementItem) => {
    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to save changes');
      return;
    }

    try {
      setIsSaving(true);

      // Determine if this is a new item (temporary ID) or existing item
      const isNewItem = item.id > 1000000000; // Temporary IDs are large numbers

      if (isNewItem) {
        // Create new achievement item
        const response = await authenticatedApiCall('/api/admin/home-page/achievements/items/', {
          method: 'POST',
          body: JSON.stringify({
            icon_name: item.icon_name,
            achievement_value: item.achievement_value,
            title: item.title
          })
        });

        if (response.success && response.data) {
          // Replace temporary item with real item from API
          const newItem = response.data as AchievementItem;
          setAchievementItems(prev =>
            prev.map(i => i.id === item.id ? newItem : i)
          );
          alert('Achievement created successfully!');
        } else {
          // Check for authentication errors in response
          const errorMessage = response.message || 'Failed to create achievement';
          if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
              errorMessage.includes('401') || errorMessage.includes('403')) {
            throw new Error('Authentication expired');
          }
          throw new Error(errorMessage);
        }
      } else {
        // Update existing achievement item
        const response = await authenticatedApiCall(`/api/admin/home-page/achievements/items/${item.id}/`, {
          method: 'PUT',
          body: JSON.stringify({
            icon_name: item.icon_name,
            achievement_value: item.achievement_value,
            title: item.title
          })
        });

        if (response.success && response.data) {
          // Update item in local state
          const updatedItem = response.data as AchievementItem;
          setAchievementItems(prev =>
            prev.map(i => i.id === item.id ? updatedItem : i)
          );
          alert('Achievement updated successfully!');
        } else {
          // Check for authentication errors in response
          const errorMessage = response.message || 'Failed to update achievement';
          if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
              errorMessage.includes('401') || errorMessage.includes('403')) {
            throw new Error('Authentication expired');
          }
          throw new Error(errorMessage);
        }
      }

      setEditingItem(null);
    } catch (error) {
      console.error('Error saving item:', error);

      // Check if it's an authentication error
      const isAuthError = await handleAuthError(error, 'saving achievement');
      if (!isAuthError) {
        alert(`Error saving achievement: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Delete achievement item
  const deleteAchievementItem = async (id: number) => {
    if (!confirm('Are you sure you want to delete this achievement?')) return;

    // Check if this is a temporary item (not yet saved to API)
    const isTemporaryItem = id > 1000000000;

    if (isTemporaryItem) {
      // Just remove from local state since it's not saved to API yet
      setAchievementItems(prev => prev.filter(item => item.id !== id));
      alert('Achievement removed successfully!');
      return;
    }

    if (!isAuthenticated || !tokens?.access) {
      alert('You must be logged in to delete items');
      return;
    }

    try {
      setIsSaving(true);

      // Call admin API to delete the item
      const response = await authenticatedApiCall(`/api/admin/home-page/achievements/items/${id}/`, {
        method: 'DELETE'
      });

      if (response.success) {
        // Remove from local state
        setAchievementItems(prev => prev.filter(item => item.id !== id));
        alert('Achievement deleted successfully!');
      } else {
        // Check for authentication errors in response
        const errorMessage = response.message || 'Failed to delete achievement';
        if (errorMessage.includes('Authentication') || errorMessage.includes('Unauthorized') ||
            errorMessage.includes('401') || errorMessage.includes('403')) {
          throw new Error('Authentication expired');
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('Error deleting item:', error);

      // Check if it's an authentication error
      const isAuthError = await handleAuthError(error, 'deleting achievement');
      if (!isAuthError) {
        alert(`Error deleting achievement: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setIsSaving(false);
    }
  };

  // Early authentication check
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FiLoader className="h-8 w-8 animate-spin text-[#00C2FF] mx-auto mb-4" />
          <p className="text-gray-400">Authentication required. Redirecting to login...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FiLoader className="h-8 w-8 animate-spin text-[#00C2FF]" />
        <span className="ml-2 text-white">Loading achievements data...</span>
      </div>
    );
  }

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">Home Page - Achievements Section</h1>
          <p className="text-gray-400 mt-1">Manage the achievements section content and statistics</p>
        </div>
        <button
          onClick={handleSaveSection}
          disabled={isSaving}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50"
        >
          {isSaving ? <FiLoader className="mr-2 h-4 w-4 animate-spin" /> : <FiSave className="mr-2 h-4 w-4" />}
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Section Content */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Section Content
            </h2>
            <button
              onClick={() => setEditingSection(!editingSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingSection && achievementsSection ? (
            <div className="space-y-6">
              {/* Badge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (English)</label>
                  <input
                    type="text"
                    value={achievementsSection.badge.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      badge: { ...prev.badge, en: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Badge (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsSection.badge.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      badge: { ...prev.badge, ar: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Title */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
                  <input
                    type="text"
                    value={achievementsSection.title.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      title: { ...prev.title, en: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
                  <input
                    type="text"
                    value={achievementsSection.title.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      title: { ...prev.title, ar: e.target.value }
                    }) : null)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>

              {/* Supporting Text */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Supporting Text (English)</label>
                  <textarea
                    value={achievementsSection.supporting_text.en}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      supporting_text: { ...prev.supporting_text, en: e.target.value }
                    }) : null)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">Supporting Text (Arabic)</label>
                  <textarea
                    value={achievementsSection.supporting_text.ar}
                    onChange={(e) => setAchievementsSection(prev => prev ? ({
                      ...prev,
                      supporting_text: { ...prev.supporting_text, ar: e.target.value }
                    }) : null)}
                    rows={4}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                </div>
              </div>
            </div>
          ) : (
            achievementsSection && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Badge</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-white bg-gray-700 p-3 rounded">{achievementsSection.badge.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsSection.badge.ar}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Title</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-white bg-gray-700 p-3 rounded">{achievementsSection.title.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-white bg-gray-700 p-3 rounded text-right" dir="rtl">{achievementsSection.title.ar}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-3">Current Supporting Text</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">English</span>
                      <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm">{achievementsSection.supporting_text.en}</p>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 block mb-1">Arabic</span>
                      <p className="text-gray-300 bg-gray-700 p-3 rounded text-sm text-right" dir="rtl">{achievementsSection.supporting_text.ar}</p>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}
        </div>

        {/* Achievement Items Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiAward className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Achievement Items ({achievementItems.length})
            </h2>
            <button
              onClick={addAchievementItem}
              disabled={isSaving}
              className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors disabled:opacity-50"
            >
              <FiPlus className="mr-2 h-4 w-4" />
              Add Achievement
            </button>
          </div>

          <div className="space-y-6">
            {achievementItems.map((item, index) => (
              <div key={item.id} className="bg-gray-700 rounded-lg border border-gray-600 p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    Achievement #{index + 1}
                  </h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingItem(editingItem === item.id ? null : item.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-gray-600 text-gray-300 rounded hover:bg-gray-500"
                    >
                      {editingItem === item.id ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
                      {editingItem === item.id ? 'Cancel' : 'Edit'}
                    </button>
                    <button
                      onClick={() => deleteAchievementItem(item.id)}
                      className="inline-flex items-center px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-500"
                    >
                      <FiTrash2 className="mr-1 h-4 w-4" />
                      Delete
                    </button>
                  </div>
                </div>

                {editingItem === item.id ? (
                  <AchievementForm
                    achievement={item}
                    onSave={handleSaveItem}
                    onCancel={() => setEditingItem(null)}
                  />
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Icon</label>
                      <span className="text-gray-300 text-sm capitalize">{item.icon_name}</span>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Value</label>
                      <p className="text-white font-bold text-lg">{item.achievement_value}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Title (English)</label>
                      <p className="text-gray-300 text-sm">{item.title.en}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">Title (Arabic)</label>
                      <p className="text-gray-300 text-sm text-right" dir="rtl">{item.title.ar}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Preview Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <h2 className="text-xl font-semibold text-white flex items-center mb-4">
            <FiEye className="mr-2 h-5 w-5 text-[#00C2FF]" />
            Live Preview
          </h2>
          <div className="bg-gray-900 rounded-lg p-8 border border-gray-600">
            {achievementsSection && (
              <div className="text-center mb-16">
                <div className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6">
                  <span className="text-sm font-medium tracking-wide text-white">{achievementsSection.badge.en}</span>
                </div>
                <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight text-white">
                  {achievementsSection.title.en}
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  {achievementsSection.supporting_text.en}
                </p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
              {achievementItems.map((item) => (
                <div key={item.id} className="group">
                  <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-8 overflow-hidden transition-all duration-500 hover:bg-white/15 hover:border-white/30">
                    <div className="relative z-10">
                      <div className="flex justify-center mb-6 text-[#00C2FF]">
                        <div className="text-3xl">
                          {getIconComponent(item.icon_name) || getIconComponent('FaTarget')}
                        </div>
                      </div>
                      <div className="text-5xl font-bold mb-3 text-white text-center">
                        {item.achievement_value}
                      </div>
                      <div className="text-white font-medium text-center">
                        {item.title.en}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
