"use client";

import Image from "next/image";
import { motion, useScroll, useTransform } from "framer-motion";
import { useRef, useEffect, useState } from "react";
import BackgroundEffects from "@/components/ui/BackgroundEffects";
import { useLanguage } from "@/contexts/LanguageContext";
import { t } from "@/utils/i18n";

const AboutHero = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);
  const [isMounted, setIsMounted] = useState(false);
  
  // Add language context
  const { locale } = useLanguage();
  
  // For parallax scrolling effect
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"]
  });

  // Parallax effects for images and decorative elements
  const rightImageY = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const leftImageY = useTransform(scrollYProgress, [0, 1], [0, 150]);
  const backgroundY = useTransform(scrollYProgress, [0, 1], [0, 30]);
  const textOpacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);
  const textY = useTransform(scrollYProgress, [0, 1], [0, -50]);

  // Client-side only effect for scroll indicator opacity
  useEffect(() => {
    setIsMounted(true);
    
    if (!scrollIndicatorRef.current) return;
    
    const updateScrollIndicatorOpacity = () => {
      if (!scrollIndicatorRef.current) return;
      
      const currentScrollY = window.scrollY;
      const opacity = Math.max(0, 1 - (currentScrollY / 300));
      scrollIndicatorRef.current.style.opacity = opacity.toString();
    };
    
    window.addEventListener('scroll', updateScrollIndicatorOpacity);
    
    // Initial call
    updateScrollIndicatorOpacity();
    
    return () => {
      window.removeEventListener('scroll', updateScrollIndicatorOpacity);
    };
  }, []);

  return (
    <div className="relative overflow-hidden min-h-screen" ref={sectionRef}>
      {/* Background overlay with gradient */}
      <motion.div 
        className="absolute inset-0 bg-brand-gradient opacity-80 backdrop-blur-sm z-10"
        style={{ y: backgroundY }}
      ></motion.div>
      
      {/* Background particles system for hero section only */}
      <BackgroundEffects className="absolute inset-0 z-5" />
      
      {/* Background decorative elements */}
      <motion.div className="absolute inset-0 z-0" style={{ y: backgroundY }}>
        <div className="absolute top-0 end-0 w-1/3 h-full bg-[url('/images/pattern.png')] opacity-5"></div>
        <motion.div 
          className="absolute bottom-0 start-0 w-96 h-96 rounded-full bg-[rgb(var(--color-text))]/5 blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute top-40 end-40 w-64 h-64 rounded-full bg-[rgb(var(--color-text))]/5 blur-2xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </motion.div>
      
      {/* Architectural grid overlay like in Home Hero */}
      <div className="absolute inset-0 z-0">
        <div className="h-full w-full grid grid-cols-6 lg:grid-cols-12">
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i} className="border-s border-white/5 h-full">
              {i === 0 && <div className="border-e border-white/5 h-full w-full"></div>}
            </div>
          ))}
          {Array.from({ length: 12 }).map((_, i) => (
            <div key={i + 'row'} className="col-span-full border-t border-white/5 h-0"></div>
          ))}
        </div>
      </div>
      
      {/* Content */}
      <div className="relative z-20 container mx-auto px-4 py-32">
        <div className="max-w-screen-xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div 
              className="space-y-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              style={{ opacity: textOpacity, y: textY }}
            >
              {/* New modern animated typography for the heading */}
              <div className="relative mb-8">
                {/* Background text highlight effect */}
                <motion.div
                  initial={{ width: "0%" }}
                  animate={{ width: "100%" }}
                  transition={{ 
                    duration: 1.5, 
                    ease: [0.22, 1, 0.36, 1],
                    delay: 0.5 
                  }}
                  className="absolute start-0 h-[105%] w-full bg-gradient-to-r from-[rgb(var(--color-primary))]/5 to-transparent z-0 rounded-sm"
                />
                
                {/* Main title with modern staggered reveal */}
                <div className="relative z-10">
                  <div className="overflow-hidden">
                    <motion.h1
                      initial={{ y: 100 }}
                      animate={{ y: 0 }}
                      transition={{ 
                        duration: 0.8, 
                        ease: [0.22, 1, 0.36, 1] 
                      }}
                      className="text-5xl md:text-7xl font-bold leading-tight tracking-tight text-[rgb(var(--color-text))]"
                    >
                      {t('aboutHero.title.about', locale)}
                    </motion.h1>
                  </div>
                  
                  <div className="flex flex-wrap items-end">
                    <div className="overflow-hidden me-5">
                      <motion.h1
                        initial={{ y: 100 }}
                        animate={{ y: 0 }}
                        transition={{ 
                          duration: 0.8, 
                          delay: 0.2,
                          ease: [0.22, 1, 0.36, 1] 
                        }}
                        className="text-5xl md:text-7xl font-bold leading-tight tracking-tight text-[#00e5ff]"
                      >
                        {t('aboutHero.title.mazaya', locale)}
                      </motion.h1>
                    </div>
                    
                    <div className="overflow-hidden">
                      <motion.h1
                        initial={{ y: 100 }}
                        animate={{ y: 0 }}
                        transition={{ 
                          duration: 0.8, 
                          delay: 0.4,
                          ease: [0.22, 1, 0.36, 1] 
                        }}
                        className="text-5xl md:text-7xl font-bold leading-tight tracking-tight text-[rgb(var(--color-text))]"
                      >
                        {t('aboutHero.title.capital', locale)}
                      </motion.h1>
                    </div>
                  </div>
                </div>
                
                {/* Animated decorative line */}
                <motion.div 
                  initial={{ width: 0, opacity: 0 }}
                  animate={{ width: "100%", opacity: 1 }}
                  transition={{ 
                    duration: 1.8, 
                    delay: 0.8,
                    ease: [0.22, 1, 0.36, 1]
                  }}
                  className="h-[2px] bg-gradient-to-r from-[#7928ca] via-[#00e5ff] to-[#7928ca] mt-1"
                />
                
                {/* Floating gradient blobs */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.7 }}
                  transition={{ duration: 2, delay: 1.2 }}
                  className="absolute -top-8 -end-16 w-32 h-32 rounded-full bg-gradient-to-br from-[#7928ca]/20 to-[#00e5ff]/20 blur-xl"
                />
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.5 }}
                  transition={{ duration: 2, delay: 1.5 }}
                  className="absolute -bottom-4 -start-8 w-24 h-24 rounded-full bg-gradient-to-tl from-[#00e5ff]/20 to-[#7928ca]/20 blur-xl"
                />
              </div>
              
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="text-xl text-[rgb(var(--color-text))]/80 leading-relaxed max-w-2xl"
              >
                {t('aboutHero.description', locale)}
              </motion.p>
            </motion.div>
            
            <motion.div 
              className="relative h-[500px] hidden lg:block"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {/* First image - styled like home hero with rounded corners and rotation */}
              <motion.div 
                className="absolute top-0 end-0 w-80 h-96 rounded-3xl overflow-hidden shadow-2xl border border-white/10 transform rotate-3"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                style={{ y: rightImageY }}
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-b from-blue-500/20 via-transparent to-purple-500/20 z-10 mix-blend-overlay"></div>
                
                {/* Highlight effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-50 z-10"></div>
                
                <Image 
                  src="/images/luxury-property-1.jpg" 
                  alt="Luxury Property" 
                  fill 
                  className="object-cover transition-transform hover:scale-105 duration-5000 ease-in-out"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                />
                
                {/* Inner frame */}
                <div className="absolute inset-0 border-4 border-white/5 z-10 m-3 rounded-2xl"></div>
              </motion.div>
              
              {/* Second image */}
              <motion.div 
                className="absolute bottom-0 start-0 w-80 h-96 rounded-3xl overflow-hidden shadow-2xl border border-white/10 transform -rotate-3"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 1 }}
                style={{ y: leftImageY }}
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-purple-500/20 via-transparent to-blue-500/20 z-10 mix-blend-overlay"></div>
                
                {/* Highlight effect */}
                <div className="absolute inset-0 bg-gradient-to-tl from-white/20 to-transparent opacity-50 z-10"></div>
                
                <Image 
                  src="/images/luxury-property-2.jpg" 
                  alt="Luxury Property" 
                  fill 
                  className="object-cover transition-transform hover:scale-105 duration-5000 ease-in-out"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                />
                
                {/* Inner frame */}
                <div className="absolute inset-0 border-4 border-white/5 z-10 m-3 rounded-2xl"></div>
              </motion.div>
              
              {/* Third image */}
              <motion.div 
                className="absolute top-1/2 start-1/4 w-72 h-80 rounded-3xl overflow-hidden shadow-2xl border border-white/10 transform -translate-y-1/2 rotate-6 z-20"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 1.2 }}
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/20 via-transparent to-pink-500/20 z-10 mix-blend-overlay"></div>
                
                {/* Highlight effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-50 z-10"></div>
                
                <Image 
                  src="/images/luxury-property-3.jpg" 
                  alt="Luxury Property" 
                  fill 
                  className="object-cover transition-transform hover:scale-105 duration-5000 ease-in-out"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                />
                
                {/* Inner frame */}
                <div className="absolute inset-0 border-4 border-white/5 z-10 m-3 rounded-2xl"></div>
              </motion.div>
              
              {/* Decorative glowing dots */}
              <div className="absolute top-1/4 end-1/4 w-4 h-4 rounded-full bg-blue-400/60 filter blur-sm animate-pulse-slow"></div>
              <div className="absolute bottom-1/4 start-1/4 w-5 h-5 rounded-full bg-purple-400/60 filter blur-sm animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
              <div className="absolute top-3/4 end-1/3 w-3 h-3 rounded-full bg-indigo-400/60 filter blur-sm animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
            </motion.div>
          </div>
        </div>
      </div>
      
      {/* Transition element to the next section */}
      <div className="absolute bottom-0 start-0 w-full h-32 bg-gradient-to-t from-[rgb(var(--color-background))] to-transparent z-20"></div>
      
      {/* Parallax scroll indicator - fixed for hydration */}
      <div 
        ref={scrollIndicatorRef}
        className="absolute bottom-8 start-1/2 transform -translate-x-1/2 z-30 flex flex-col items-center space-y-2"
        style={{ opacity: isMounted ? 1 : 0 }}
      >
        <motion.div
          animate={{ 
            y: [0, 10, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <p className="text-sm text-[rgb(var(--color-text))]/60">{t('aboutHero.scrollText', locale)}</p>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-[rgb(var(--color-text))]/60 mx-auto">
            <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3" />
          </svg>
        </motion.div>
      </div>
    </div>
  );
};

export default AboutHero;