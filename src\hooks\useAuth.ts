"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';
const USE_MOCK_AUTH = false; // Force real API authentication for testing

// Mock admin credentials for development
const MOCK_ADMIN_CREDENTIALS = [
  { 
    id: 1,
    email: '<EMAIL>', 
    password: 'admin123', 
    first_name: 'Admin',
    last_name: 'User',
    role: 'Super Admin',
    permissions: ['view_dashboard', 'manage_users', 'edit_content', 'manage_settings'],
    avatar: undefined
  },
  { 
    id: 2,
    email: '<EMAIL>', 
    password: 'manager123', 
    first_name: 'Manager',
    last_name: 'User',
    role: 'Manager',
    permissions: ['view_dashboard', 'edit_content', 'manage_projects'],
    avatar: undefined
  },
  { 
    id: 3,
    email: '<EMAIL>', 
    password: 'sales123', 
    first_name: 'Sales',
    last_name: 'Agent',
    role: 'Sales Agent',
    permissions: ['view_dashboard', 'manage_inquiries'],
    avatar: undefined
  },
  { 
    id: 4,
    email: '<EMAIL>', 
    password: 'editor123', 
    first_name: 'Content',
    last_name: 'Editor',
    role: 'Content Editor',
    permissions: ['view_dashboard', 'edit_content'],
    avatar: undefined
  }
];

interface AuthUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  permissions: string[];
  avatar?: string;
  last_login?: string;
}

interface AuthTokens {
  access: string;
  refresh: string;
}

interface AuthState {
  user: AuthUser | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface LoginCredentials {
  email: string;
  password: string;
  remember_me?: boolean;
}

interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export function useAuth() {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    tokens: null,
    isLoading: true,
    isAuthenticated: false,
  });
  const [forceUpdate, setForceUpdate] = useState(0);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Listen for avatar updates from other useAuth instances
  useEffect(() => {
    if (!isClient) return;

    const handleAvatarUpdate = (event: CustomEvent) => {
      const { user: updatedUser } = event.detail;
      console.log('🔄 Received avatar update event:', updatedUser);
      
      // Update the current auth state with the new user data
      setAuthState(prev => ({
        ...prev,
        user: updatedUser
      }));
      
      // Force re-render
      setForceUpdate(prev => prev + 1);
    };

    window.addEventListener('avatarUpdated', handleAvatarUpdate as EventListener);
    
    return () => {
      window.removeEventListener('avatarUpdated', handleAvatarUpdate as EventListener);
    };
  }, [isClient]);

  // API Helper function
  const apiCall = async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> => {
    try {
      const url = `${API_BASE_URL}${endpoint}`;
      console.log('🔄 Making API call to:', url);
      console.log('📤 Request options:', {
        method: options.method || 'GET',
        headers: options.headers,
        body: options.body ? JSON.parse(options.body as string) : undefined
      });
      
      // Ensure Content-Type is always set for requests with body
      const defaultHeaders: Record<string, string> = {};
      if (options.body) {
        defaultHeaders['Content-Type'] = 'application/json';
      }
      
      const response = await fetch(url, {
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers,
        },
      });

      console.log('📥 Response status:', response.status, response.statusText);
      
      const data = await response.json();
      console.log('📥 Response data:', data);
      
      return data;
    } catch (error) {
      console.error('❌ API call failed:', error);
      return {
        success: false,
        message: 'Network error occurred',
      };
    }
  };

  // Get stored auth data
  const getStoredAuth = (): { user: AuthUser; tokens: AuthTokens } | null => {
    if (!isClient) return null;
    
    try {
      const userData = localStorage.getItem('adminUser');
      const tokensData = localStorage.getItem('adminTokens');
      
      if (userData && tokensData) {
        return {
          user: JSON.parse(userData),
          tokens: JSON.parse(tokensData),
        };
      }
    } catch (error) {
      console.error('Error reading stored auth:', error);
    }
    return null;
  };

  // Store auth data
  const storeAuth = (user: AuthUser, tokens: AuthTokens) => {
    if (!isClient) return;
    localStorage.setItem('adminUser', JSON.stringify(user));
    localStorage.setItem('adminTokens', JSON.stringify(tokens));
  };

  // Clear auth data
  const clearAuth = () => {
    if (!isClient) return;
    localStorage.removeItem('adminUser');
    localStorage.removeItem('adminTokens');
    localStorage.removeItem('adminAuth'); // Clear old format too
  };

  // Check if token is expired
  const isTokenExpired = (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return Date.now() >= payload.exp * 1000;
    } catch {
      return true;
    }
  };

  // Refresh access token
  const refreshToken = async (): Promise<boolean> => {
    const storedAuth = getStoredAuth();
    if (!storedAuth?.tokens.refresh) return false;

    const response = await apiCall<{ access: string; expires_at: string }>('/api/admin/auth/refresh/', {
      method: 'POST',
      body: JSON.stringify({ refresh: storedAuth.tokens.refresh }),
    });

    if (response.success && response.data) {
      const newTokens = {
        ...storedAuth.tokens,
        access: response.data.access,
      };
      storeAuth(storedAuth.user, newTokens);
      setAuthState(prev => ({ ...prev, tokens: newTokens }));
      return true;
    }

    return false;
  };

  // Verify current user with backend
  const verifyUser = async (): Promise<boolean> => {
    const storedAuth = getStoredAuth();
    if (!storedAuth?.tokens.access) return false;

    // Check if access token is expired
    if (isTokenExpired(storedAuth.tokens.access)) {
      const refreshed = await refreshToken();
      if (!refreshed) return false;
    }

    const response = await apiCall<AuthUser>('/api/admin/auth/me/', {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${storedAuth.tokens.access}`,
      },
    });

    if (response.success && response.data) {
      setAuthState({
        user: response.data,
        tokens: storedAuth.tokens,
        isLoading: false,
        isAuthenticated: true,
      });
      return true;
    }

    return false;
  };

  // Login function
  const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message?: string }> => {
    console.log('🔐 Starting login process...');
    console.log('🔧 API_BASE_URL:', API_BASE_URL);
    console.log('🔧 USE_MOCK_AUTH:', USE_MOCK_AUTH);
    console.log('🔧 NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
    
    setAuthState(prev => ({ ...prev, isLoading: true }));

    // Use mock authentication in development or when no API URL is set
    if (USE_MOCK_AUTH) {
      console.log('🎭 Using MOCK authentication');
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockUser = MOCK_ADMIN_CREDENTIALS.find(
        user => user.email === credentials.email && user.password === credentials.password
      );

      if (mockUser) {
        const { password, ...userWithoutPassword } = mockUser;
        const user: AuthUser = {
          ...userWithoutPassword,
          last_login: new Date().toISOString()
        };

        const tokens: AuthTokens = {
          access: 'mock_access_token_' + Date.now(),
          refresh: 'mock_refresh_token_' + Date.now()
        };

        storeAuth(user, tokens);
        setAuthState({
          user,
          tokens,
          isLoading: false,
          isAuthenticated: true,
        });

        console.log('Mock login successful, auth state updated:', { user, isAuthenticated: true });
        
        // Force a re-render to ensure UI updates
        setForceUpdate(prev => prev + 1);
        
        return { success: true, message: 'Login successful' };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return {
          success: false,
          message: 'Invalid email or password',
        };
      }
    }

    // Real API authentication
    console.log('🌐 Using REAL API authentication');
    const response = await apiCall<{
      user: AuthUser;
      tokens: AuthTokens;
      session: { expires_at: string; remember_me: boolean };
    }>('/api/admin/auth/login/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data) {
      const { user, tokens } = response.data;
      
      storeAuth(user, tokens);
      setAuthState({
        user,
        tokens,
        isLoading: false,
        isAuthenticated: true,
      });

      return { success: true, message: 'Login successful' };
    }

    setAuthState(prev => ({ ...prev, isLoading: false }));
    return {
      success: false,
      message: response.message || 'Login failed',
    };
  };

  // Logout function
  const logout = async (): Promise<void> => {
    const storedAuth = getStoredAuth();
    
    if (storedAuth?.tokens.refresh && storedAuth?.tokens.access) {
      try {
        // Notify backend about logout with proper headers
        await apiCall('/api/admin/auth/logout/', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${storedAuth.tokens.access}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            refresh: storedAuth.tokens.refresh 
          }),
        });
      } catch (error) {
        console.error('Logout API call failed:', error);
        // Continue with local logout even if API call fails
      }
    }

    clearAuth();
    setAuthState({
      user: null,
      tokens: null,
      isLoading: false,
      isAuthenticated: false,
    });
  };

  // Password reset request
  const requestPasswordReset = async (email: string): Promise<{ success: boolean; message?: string }> => {
    const response = await apiCall('/api/admin/auth/password-reset/', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });

    return {
      success: response.success,
      message: response.message || (response.success ? 'Reset email sent' : 'Failed to send reset email'),
    };
  };

  // Update profile
  const updateProfile = async (profileData: { first_name: string; last_name: string }): Promise<{ success: boolean; message?: string; user?: AuthUser }> => {
    const storedAuth = getStoredAuth();
    if (!storedAuth?.tokens.access) {
      return { success: false, message: 'Not authenticated' };
    }

    const response = await apiCall<AuthUser>('/api/admin/auth/profile/', {
      method: 'PUT',
      headers: {
        Authorization: `Bearer ${storedAuth.tokens.access}`,
      },
      body: JSON.stringify(profileData),
    });

    if (response.success && response.data) {
      // Update stored user data
      const updatedAuth = {
        ...storedAuth,
        user: response.data
      };
      storeAuth(updatedAuth.user, updatedAuth.tokens);
      
      // Update state
      setAuthState(prev => ({
        ...prev,
        user: response.data!
      }));

      return { 
        success: true, 
        message: 'Profile updated successfully',
        user: response.data 
      };
    }

    return {
      success: false,
      message: response.message || 'Failed to update profile',
    };
  };

  // Upload avatar
  const uploadAvatar = async (file: File): Promise<{ success: boolean; message?: string; user?: AuthUser }> => {
    const storedAuth = getStoredAuth();
    if (!storedAuth?.tokens.access) {
      return { success: false, message: 'Not authenticated' };
    }

    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const url = `${API_BASE_URL}/api/admin/auth/avatar/`;
      console.log('🔄 Uploading avatar to:', url);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${storedAuth.tokens.access}`,
        },
        body: formData,
      });

      const data = await response.json();
      console.log('📥 Avatar upload response:', data);

      if (data.success && data.data) {
        // Update stored user data with new avatar
        const updatedUser = {
          ...storedAuth.user,
          avatar: data.data.avatar + '?t=' + Date.now() // Add cache-busting timestamp
        };
        storeAuth(updatedUser, storedAuth.tokens);
        
        // Update state to trigger re-render
        setAuthState(prev => ({
          ...prev,
          user: updatedUser
        }));

        // Force a re-render to ensure UI updates everywhere
        setForceUpdate(prev => prev + 1);

        // Dispatch custom event to notify all useAuth instances
        if (typeof window !== 'undefined') {
          console.log('📡 Dispatching avatarUpdated event with user:', updatedUser);
          window.dispatchEvent(new CustomEvent('avatarUpdated', { 
            detail: { user: updatedUser } 
          }));
        }

        return { 
          success: true, 
          message: 'Avatar updated successfully',
          user: updatedUser 
        };
      }

      return {
        success: false,
        message: data.message || 'Failed to upload avatar',
      };
    } catch (error) {
      console.error('❌ Avatar upload failed:', error);
      return {
        success: false,
        message: 'Network error occurred during avatar upload',
      };
    }
  };

  // Initialize auth state
  useEffect(() => {
    if (!isClient) return;

    const initAuth = async () => {
      console.log('Initializing auth state...');
      const storedAuth = getStoredAuth();
      console.log('Stored auth data:', storedAuth ? { email: storedAuth.user.email, hasTokens: !!storedAuth.tokens } : 'none');
      
      if (storedAuth) {
        if (USE_MOCK_AUTH) {
          // For mock auth, just validate that the stored user exists in our mock data
          const mockUser = MOCK_ADMIN_CREDENTIALS.find(
            user => user.email === storedAuth.user.email
          );
          
          if (mockUser) {
            console.log('Mock auth validated, setting authenticated state');
            setAuthState({
              user: storedAuth.user,
              tokens: storedAuth.tokens,
              isLoading: false,
              isAuthenticated: true,
            });
          } else {
            console.log('Mock user not found, clearing auth');
            clearAuth();
            setAuthState({
              user: null,
              tokens: null,
              isLoading: false,
              isAuthenticated: false,
            });
          }
        } else {
          // For real API, verify with backend
          console.log('Verifying with backend...');
          const isValid = await verifyUser();
          if (!isValid) {
            console.log('Backend verification failed, clearing auth');
            clearAuth();
            setAuthState({
              user: null,
              tokens: null,
              isLoading: false,
              isAuthenticated: false,
            });
          }
        }
      } else {
        console.log('No stored auth, setting unauthenticated state');
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    };

    initAuth();
  }, [isClient]);

  return {
    ...authState,
    login,
    logout,
    requestPasswordReset,
    refreshToken,
    forceUpdate,
    updateProfile,
    uploadAvatar,
  };
} 