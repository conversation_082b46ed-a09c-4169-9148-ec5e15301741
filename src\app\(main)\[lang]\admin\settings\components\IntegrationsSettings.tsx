"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>yeOff, FiMapPin, FiMessageCircle, FiTrendingUp, FiShare2, FiPhone, FiVideo } from 'react-icons/fi';

interface IntegrationsSettingsData {
  analytics: {
    googleAnalytics: {
      enabled: boolean;
      trackingId: string;
    };
    facebookPixel: {
      enabled: boolean;
      pixelId: string;
    };
    googleTagManager: {
      enabled: boolean;
      containerId: string;
    };
    hotjar: {
      enabled: boolean;
      siteId: string;
    };
  };
  maps: {
    googleMaps: {
      enabled: boolean;
      apiKey: string;
    };
    mapbox: {
      enabled: boolean;
      accessToken: string;
    };
  };
  chat: {
    whatsapp: {
      enabled: boolean;
      phoneNumber: string;
      welcomeMessage: {
        en: string;
        ar: string;
      };
    };
    tawk: {
      enabled: boolean;
      propertyId: string;
      widgetId: string;
    };
    intercom: {
      enabled: boolean;
      appId: string;
    };
    zendesk: {
      enabled: boolean;
      key: string;
    };
  };
  marketing: {
    mailchimp: {
      enabled: boolean;
      apiKey: string;
      audienceId: string;
    };
    hubspot: {
      enabled: boolean;
      portalId: string;
    };
    salesforce: {
      enabled: boolean;
      organizationId: string;
    };
  };
  social: {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
    youtube: string;
    tiktok: string;
    snapchat: string;
  };
  communication: {
    calendly: {
      enabled: boolean;
      username: string;
    };
    zoom: {
      enabled: boolean;
      meetingId: string;
    };
    googleMeet: {
      enabled: boolean;
      meetingLink: string;
    };
  };
  seo: {
    googleSearchConsole: {
      enabled: boolean;
      verificationCode: string;
    };
    bingWebmaster: {
      enabled: boolean;
      verificationCode: string;
    };
    yandexWebmaster: {
      enabled: boolean;
      verificationCode: string;
    };
  };
  email: {
    provider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
    smtp: {
      host: string;
      port: number;
      username: string;
      password: string;
      encryption: 'tls' | 'ssl' | 'none';
    };
    sendgrid: {
      apiKey: string;
    };
    mailgun: {
      domain: string;
      apiKey: string;
    };
    ses: {
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
    };
  };
}

interface IntegrationsSettingsProps {
  data: IntegrationsSettingsData;
  onChange: (data: IntegrationsSettingsData) => void;
}

export default function IntegrationsSettings({ data, onChange }: IntegrationsSettingsProps) {
  const [showPasswords, setShowPasswords] = useState<{[key: string]: boolean}>({});

  const updateData = (updates: Partial<IntegrationsSettingsData>) => {
    onChange({ ...data, ...updates });
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <div className="space-y-6">
      {/* Analytics & Tracking */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Analytics & Tracking
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Google Analytics */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Google Analytics</p>
                <p className="text-gray-400 text-sm">Track website analytics and user behavior</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.analytics.googleAnalytics.enabled}
                  onChange={(e) => updateData({
                    analytics: {
                      ...data.analytics,
                      googleAnalytics: { ...data.analytics.googleAnalytics, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.analytics.googleAnalytics.enabled && (
              <input
                type="text"
                value={data.analytics.googleAnalytics.trackingId}
                onChange={(e) => updateData({
                  analytics: {
                    ...data.analytics,
                    googleAnalytics: { ...data.analytics.googleAnalytics, trackingId: e.target.value }
                  }
                })}
                placeholder="G-XXXXXXXXXX"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Facebook Pixel */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Facebook Pixel</p>
                <p className="text-gray-400 text-sm">Track conversions and ads performance</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.analytics.facebookPixel.enabled}
                  onChange={(e) => updateData({
                    analytics: {
                      ...data.analytics,
                      facebookPixel: { ...data.analytics.facebookPixel, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.analytics.facebookPixel.enabled && (
              <input
                type="text"
                value={data.analytics.facebookPixel.pixelId}
                onChange={(e) => updateData({
                  analytics: {
                    ...data.analytics,
                    facebookPixel: { ...data.analytics.facebookPixel, pixelId: e.target.value }
                  }
                })}
                placeholder="123456789012345"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Google Tag Manager */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Google Tag Manager</p>
                <p className="text-gray-400 text-sm">Manage tracking codes and tags</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.analytics.googleTagManager.enabled}
                  onChange={(e) => updateData({
                    analytics: {
                      ...data.analytics,
                      googleTagManager: { ...data.analytics.googleTagManager, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.analytics.googleTagManager.enabled && (
              <input
                type="text"
                value={data.analytics.googleTagManager.containerId}
                onChange={(e) => updateData({
                  analytics: {
                    ...data.analytics,
                    googleTagManager: { ...data.analytics.googleTagManager, containerId: e.target.value }
                  }
                })}
                placeholder="GTM-XXXXXXX"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Hotjar */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Hotjar</p>
                <p className="text-gray-400 text-sm">Heatmaps and user session recordings</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.analytics.hotjar.enabled}
                  onChange={(e) => updateData({
                    analytics: {
                      ...data.analytics,
                      hotjar: { ...data.analytics.hotjar, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.analytics.hotjar.enabled && (
              <input
                type="text"
                value={data.analytics.hotjar.siteId}
                onChange={(e) => updateData({
                  analytics: {
                    ...data.analytics,
                    hotjar: { ...data.analytics.hotjar, siteId: e.target.value }
                  }
                })}
                placeholder="1234567"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>
        </div>
      </div>

      {/* Maps Integration */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiMapPin className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Maps Integration
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Google Maps */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Google Maps</p>
                <p className="text-gray-400 text-sm">Display property locations and directions</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.maps.googleMaps.enabled}
                  onChange={(e) => updateData({
                    maps: {
                      ...data.maps,
                      googleMaps: { ...data.maps.googleMaps, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.maps.googleMaps.enabled && (
              <div className="relative">
                <input
                  type={showPasswords['googleMaps'] ? 'text' : 'password'}
                  value={data.maps.googleMaps.apiKey}
                  onChange={(e) => updateData({
                    maps: {
                      ...data.maps,
                      googleMaps: { ...data.maps.googleMaps, apiKey: e.target.value }
                    }
                  })}
                  placeholder="Google Maps API Key"
                  className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('googleMaps')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                >
                  {showPasswords['googleMaps'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                </button>
              </div>
            )}
          </div>

          {/* Mapbox */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Mapbox</p>
                <p className="text-gray-400 text-sm">Alternative mapping solution</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.maps.mapbox.enabled}
                  onChange={(e) => updateData({
                    maps: {
                      ...data.maps,
                      mapbox: { ...data.maps.mapbox, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.maps.mapbox.enabled && (
              <div className="relative">
                <input
                  type={showPasswords['mapbox'] ? 'text' : 'password'}
                  value={data.maps.mapbox.accessToken}
                  onChange={(e) => updateData({
                    maps: {
                      ...data.maps,
                      mapbox: { ...data.maps.mapbox, accessToken: e.target.value }
                    }
                  })}
                  placeholder="Mapbox Access Token"
                  className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('mapbox')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                >
                  {showPasswords['mapbox'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Chat & Communication */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiMessageCircle className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Chat & Communication
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* WhatsApp */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">WhatsApp Business</p>
                <p className="text-gray-400 text-sm">Direct WhatsApp contact button</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.chat.whatsapp.enabled}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      whatsapp: { ...data.chat.whatsapp, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.chat.whatsapp.enabled && (
              <div className="space-y-3">
                <input
                  type="text"
                  value={data.chat.whatsapp.phoneNumber}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      whatsapp: { ...data.chat.whatsapp, phoneNumber: e.target.value }
                    }
                  })}
                  placeholder="+971501234567"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
                <input
                  type="text"
                  value={data.chat.whatsapp.welcomeMessage.en}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      whatsapp: {
                        ...data.chat.whatsapp,
                        welcomeMessage: { ...data.chat.whatsapp.welcomeMessage, en: e.target.value }
                      }
                    }
                  })}
                  placeholder="Welcome message (English)"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
                <input
                  type="text"
                  value={data.chat.whatsapp.welcomeMessage.ar}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      whatsapp: {
                        ...data.chat.whatsapp,
                        welcomeMessage: { ...data.chat.whatsapp.welcomeMessage, ar: e.target.value }
                      }
                    }
                  })}
                  placeholder="رسالة الترحيب (العربية)"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
            )}
          </div>

          {/* Tawk.to */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Tawk.to Live Chat</p>
                <p className="text-gray-400 text-sm">Free live chat widget</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.chat.tawk.enabled}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      tawk: { ...data.chat.tawk, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.chat.tawk.enabled && (
              <div className="space-y-3">
                <input
                  type="text"
                  value={data.chat.tawk.propertyId}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      tawk: { ...data.chat.tawk, propertyId: e.target.value }
                    }
                  })}
                  placeholder="Property ID"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
                <input
                  type="text"
                  value={data.chat.tawk.widgetId}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      tawk: { ...data.chat.tawk, widgetId: e.target.value }
                    }
                  })}
                  placeholder="Widget ID"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
            )}
          </div>

          {/* Intercom */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Intercom</p>
                <p className="text-gray-400 text-sm">Customer messaging platform</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.chat.intercom.enabled}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      intercom: { ...data.chat.intercom, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.chat.intercom.enabled && (
              <input
                type="text"
                value={data.chat.intercom.appId}
                onChange={(e) => updateData({
                  chat: {
                    ...data.chat,
                    intercom: { ...data.chat.intercom, appId: e.target.value }
                  }
                })}
                placeholder="Intercom App ID"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Zendesk */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Zendesk Chat</p>
                <p className="text-gray-400 text-sm">Customer support chat widget</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.chat.zendesk.enabled}
                  onChange={(e) => updateData({
                    chat: {
                      ...data.chat,
                      zendesk: { ...data.chat.zendesk, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.chat.zendesk.enabled && (
              <input
                type="text"
                value={data.chat.zendesk.key}
                onChange={(e) => updateData({
                  chat: {
                    ...data.chat,
                    zendesk: { ...data.chat.zendesk, key: e.target.value }
                  }
                })}
                placeholder="Zendesk Key"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>
        </div>
      </div>

      {/* Marketing & CRM */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Marketing & CRM
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Mailchimp */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Mailchimp</p>
                <p className="text-gray-400 text-sm">Email marketing automation</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.marketing.mailchimp.enabled}
                  onChange={(e) => updateData({
                    marketing: {
                      ...data.marketing,
                      mailchimp: { ...data.marketing.mailchimp, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.marketing.mailchimp.enabled && (
              <div className="space-y-3">
                <div className="relative">
                  <input
                    type={showPasswords['mailchimp'] ? 'text' : 'password'}
                    value={data.marketing.mailchimp.apiKey}
                    onChange={(e) => updateData({
                      marketing: {
                        ...data.marketing,
                        mailchimp: { ...data.marketing.mailchimp, apiKey: e.target.value }
                      }
                    })}
                    placeholder="Mailchimp API Key"
                    className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('mailchimp')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                  >
                    {showPasswords['mailchimp'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                  </button>
                </div>
                <input
                  type="text"
                  value={data.marketing.mailchimp.audienceId}
                  onChange={(e) => updateData({
                    marketing: {
                      ...data.marketing,
                      mailchimp: { ...data.marketing.mailchimp, audienceId: e.target.value }
                    }
                  })}
                  placeholder="Audience ID"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
            )}
          </div>

          {/* HubSpot */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">HubSpot</p>
                <p className="text-gray-400 text-sm">CRM and marketing platform</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.marketing.hubspot.enabled}
                  onChange={(e) => updateData({
                    marketing: {
                      ...data.marketing,
                      hubspot: { ...data.marketing.hubspot, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.marketing.hubspot.enabled && (
              <input
                type="text"
                value={data.marketing.hubspot.portalId}
                onChange={(e) => updateData({
                  marketing: {
                    ...data.marketing,
                    hubspot: { ...data.marketing.hubspot, portalId: e.target.value }
                  }
                })}
                placeholder="HubSpot Portal ID"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Salesforce */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Salesforce</p>
                <p className="text-gray-400 text-sm">Enterprise CRM solution</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.marketing.salesforce.enabled}
                  onChange={(e) => updateData({
                    marketing: {
                      ...data.marketing,
                      salesforce: { ...data.marketing.salesforce, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.marketing.salesforce.enabled && (
              <input
                type="text"
                value={data.marketing.salesforce.organizationId}
                onChange={(e) => updateData({
                  marketing: {
                    ...data.marketing,
                    salesforce: { ...data.marketing.salesforce, organizationId: e.target.value }
                  }
                })}
                placeholder="Organization ID"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>
        </div>
      </div>

      {/* Social Media Links */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiShare2 className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Social Media Links
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Facebook</label>
            <input
              type="url"
              value={data.social.facebook}
              onChange={(e) => updateData({
                social: { ...data.social, facebook: e.target.value }
              })}
              placeholder="https://facebook.com/mazayacapital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Instagram</label>
            <input
              type="url"
              value={data.social.instagram}
              onChange={(e) => updateData({
                social: { ...data.social, instagram: e.target.value }
              })}
              placeholder="https://instagram.com/mazayacapital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">LinkedIn</label>
            <input
              type="url"
              value={data.social.linkedin}
              onChange={(e) => updateData({
                social: { ...data.social, linkedin: e.target.value }
              })}
              placeholder="https://linkedin.com/company/mazaya-capital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Twitter</label>
            <input
              type="url"
              value={data.social.twitter}
              onChange={(e) => updateData({
                social: { ...data.social, twitter: e.target.value }
              })}
              placeholder="https://twitter.com/mazayacapital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">YouTube</label>
            <input
              type="url"
              value={data.social.youtube}
              onChange={(e) => updateData({
                social: { ...data.social, youtube: e.target.value }
              })}
              placeholder="https://youtube.com/mazayacapital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">TikTok</label>
            <input
              type="url"
              value={data.social.tiktok}
              onChange={(e) => updateData({
                social: { ...data.social, tiktok: e.target.value }
              })}
              placeholder="https://tiktok.com/@mazayacapital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Snapchat</label>
            <input
              type="url"
              value={data.social.snapchat}
              onChange={(e) => updateData({
                social: { ...data.social, snapchat: e.target.value }
              })}
              placeholder="https://snapchat.com/add/mazayacapital"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            />
          </div>
        </div>
      </div>

      {/* Video Conferencing */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
          <FiVideo className="mr-2 h-5 w-5 text-[#00C2FF]" />
          Video Conferencing
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Calendly */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Calendly</p>
                <p className="text-gray-400 text-sm">Appointment scheduling</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.communication.calendly.enabled}
                  onChange={(e) => updateData({
                    communication: {
                      ...data.communication,
                      calendly: { ...data.communication.calendly, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.communication.calendly.enabled && (
              <input
                type="text"
                value={data.communication.calendly.username}
                onChange={(e) => updateData({
                  communication: {
                    ...data.communication,
                    calendly: { ...data.communication.calendly, username: e.target.value }
                  }
                })}
                placeholder="your-calendly-username"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Zoom */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Zoom</p>
                <p className="text-gray-400 text-sm">Video meetings</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.communication.zoom.enabled}
                  onChange={(e) => updateData({
                    communication: {
                      ...data.communication,
                      zoom: { ...data.communication.zoom, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.communication.zoom.enabled && (
              <input
                type="text"
                value={data.communication.zoom.meetingId}
                onChange={(e) => updateData({
                  communication: {
                    ...data.communication,
                    zoom: { ...data.communication.zoom, meetingId: e.target.value }
                  }
                })}
                placeholder="123-456-7890"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Google Meet */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Google Meet</p>
                <p className="text-gray-400 text-sm">Google video calls</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.communication.googleMeet.enabled}
                  onChange={(e) => updateData({
                    communication: {
                      ...data.communication,
                      googleMeet: { ...data.communication.googleMeet, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.communication.googleMeet.enabled && (
              <input
                type="url"
                value={data.communication.googleMeet.meetingLink}
                onChange={(e) => updateData({
                  communication: {
                    ...data.communication,
                    googleMeet: { ...data.communication.googleMeet, meetingLink: e.target.value }
                  }
                })}
                placeholder="https://meet.google.com/xxx-xxxx-xxx"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>
        </div>
      </div>

      {/* SEO Tools */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">SEO & Webmaster Tools</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Google Search Console */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Google Search Console</p>
                <p className="text-gray-400 text-sm">Website verification</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.seo.googleSearchConsole.enabled}
                  onChange={(e) => updateData({
                    seo: {
                      ...data.seo,
                      googleSearchConsole: { ...data.seo.googleSearchConsole, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.seo.googleSearchConsole.enabled && (
              <input
                type="text"
                value={data.seo.googleSearchConsole.verificationCode}
                onChange={(e) => updateData({
                  seo: {
                    ...data.seo,
                    googleSearchConsole: { ...data.seo.googleSearchConsole, verificationCode: e.target.value }
                  }
                })}
                placeholder="google-site-verification=..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Bing Webmaster */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Bing Webmaster</p>
                <p className="text-gray-400 text-sm">Bing search verification</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.seo.bingWebmaster.enabled}
                  onChange={(e) => updateData({
                    seo: {
                      ...data.seo,
                      bingWebmaster: { ...data.seo.bingWebmaster, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.seo.bingWebmaster.enabled && (
              <input
                type="text"
                value={data.seo.bingWebmaster.verificationCode}
                onChange={(e) => updateData({
                  seo: {
                    ...data.seo,
                    bingWebmaster: { ...data.seo.bingWebmaster, verificationCode: e.target.value }
                  }
                })}
                placeholder="msvalidate.01=..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>

          {/* Yandex Webmaster */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white font-medium">Yandex Webmaster</p>
                <p className="text-gray-400 text-sm">Yandex search verification</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={data.seo.yandexWebmaster.enabled}
                  onChange={(e) => updateData({
                    seo: {
                      ...data.seo,
                      yandexWebmaster: { ...data.seo.yandexWebmaster, enabled: e.target.checked }
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#00C2FF]/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#00C2FF]"></div>
              </label>
            </div>
            
            {data.seo.yandexWebmaster.enabled && (
              <input
                type="text"
                value={data.seo.yandexWebmaster.verificationCode}
                onChange={(e) => updateData({
                  seo: {
                    ...data.seo,
                    yandexWebmaster: { ...data.seo.yandexWebmaster, verificationCode: e.target.value }
                  }
                })}
                placeholder="yandex-verification=..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              />
            )}
          </div>
        </div>
      </div>

      {/* Email Configuration */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Email Configuration</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Email Provider</label>
            <select
              value={data.email.provider}
              onChange={(e) => updateData({
                email: { ...data.email, provider: e.target.value as 'smtp' | 'sendgrid' | 'mailgun' | 'ses' }
              })}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
            >
              <option value="smtp">SMTP</option>
              <option value="sendgrid">SendGrid</option>
              <option value="mailgun">Mailgun</option>
              <option value="ses">Amazon SES</option>
            </select>
          </div>
          
          {data.email.provider === 'smtp' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">SMTP Host</label>
                <input
                  type="text"
                  value={data.email.smtp.host}
                  onChange={(e) => updateData({
                    email: {
                      ...data.email,
                      smtp: { ...data.email.smtp, host: e.target.value }
                    }
                  })}
                  placeholder="smtp.gmail.com"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Port</label>
                <input
                  type="number"
                  value={data.email.smtp.port}
                  onChange={(e) => updateData({
                    email: {
                      ...data.email,
                      smtp: { ...data.email.smtp, port: parseInt(e.target.value) }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Username</label>
                <input
                  type="text"
                  value={data.email.smtp.username}
                  onChange={(e) => updateData({
                    email: {
                      ...data.email,
                      smtp: { ...data.email.smtp, username: e.target.value }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Password</label>
                <div className="relative">
                  <input
                    type={showPasswords['smtp'] ? 'text' : 'password'}
                    value={data.email.smtp.password}
                    onChange={(e) => updateData({
                      email: {
                        ...data.email,
                        smtp: { ...data.email.smtp, password: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('smtp')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                  >
                    {showPasswords['smtp'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {data.email.provider === 'sendgrid' && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">SendGrid API Key</label>
              <div className="relative">
                <input
                  type={showPasswords['sendgrid'] ? 'text' : 'password'}
                  value={data.email.sendgrid.apiKey}
                  onChange={(e) => updateData({
                    email: {
                      ...data.email,
                      sendgrid: { apiKey: e.target.value }
                    }
                  })}
                  placeholder="SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
                <button
                  type="button"
                  onClick={() => togglePasswordVisibility('sendgrid')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                >
                  {showPasswords['sendgrid'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                </button>
              </div>
            </div>
          )}
          
          {data.email.provider === 'mailgun' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Domain</label>
                <input
                  type="text"
                  value={data.email.mailgun.domain}
                  onChange={(e) => updateData({
                    email: {
                      ...data.email,
                      mailgun: { ...data.email.mailgun, domain: e.target.value }
                    }
                  })}
                  placeholder="mg.yourdomain.com"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">API Key</label>
                <div className="relative">
                  <input
                    type={showPasswords['mailgun'] ? 'text' : 'password'}
                    value={data.email.mailgun.apiKey}
                    onChange={(e) => updateData({
                      email: {
                        ...data.email,
                        mailgun: { ...data.email.mailgun, apiKey: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('mailgun')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                  >
                    {showPasswords['mailgun'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                  </button>
                </div>
              </div>
            </div>
          )}

          {data.email.provider === 'ses' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Access Key ID</label>
                <div className="relative">
                  <input
                    type={showPasswords['sesAccessKey'] ? 'text' : 'password'}
                    value={data.email.ses.accessKeyId}
                    onChange={(e) => updateData({
                      email: {
                        ...data.email,
                        ses: { ...data.email.ses, accessKeyId: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('sesAccessKey')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                  >
                    {showPasswords['sesAccessKey'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Secret Access Key</label>
                <div className="relative">
                  <input
                    type={showPasswords['sesSecretKey'] ? 'text' : 'password'}
                    value={data.email.ses.secretAccessKey}
                    onChange={(e) => updateData({
                      email: {
                        ...data.email,
                        ses: { ...data.email.ses, secretAccessKey: e.target.value }
                      }
                    })}
                    className="w-full px-3 py-2 pr-10 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                  />
                  <button
                    type="button"
                    onClick={() => togglePasswordVisibility('sesSecretKey')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-white"
                  >
                    {showPasswords['sesSecretKey'] ? <FiEyeOff className="h-4 w-4" /> : <FiEye className="h-4 w-4" />}
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Region</label>
                <select
                  value={data.email.ses.region}
                  onChange={(e) => updateData({
                    email: {
                      ...data.email,
                      ses: { ...data.email.ses, region: e.target.value }
                    }
                  })}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
                >
                  <option value="us-east-1">US East (N. Virginia)</option>
                  <option value="us-west-2">US West (Oregon)</option>
                  <option value="eu-west-1">Europe (Ireland)</option>
                  <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 