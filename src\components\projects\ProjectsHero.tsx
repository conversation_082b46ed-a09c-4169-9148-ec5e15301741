"use client";

import { useEffect, useState, useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";

// Create a grid pattern component instead of using an image
const GridPattern = ({ className = "" }) => (
  <div className={`absolute inset-0 overflow-hidden ${className}`}>
    <svg
      className="absolute w-full h-full opacity-10"
      xmlns="http://www.w3.org/2000/svg"
      width="100%"
      height="100%"
    >
      <defs>
        <pattern
          id="grid"
          width="40"
          height="40"
          patternUnits="userSpaceOnUse"
        >
          <path
            d="M 40 0 L 0 0 0 40"
            fill="none"
            stroke="white"
            strokeWidth="0.5"
            strokeOpacity="0.2"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
    </svg>
  </div>
);

const ProjectsHero = () => {
  const [isMounted, setIsMounted] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end start"],
  });

  // Parallax effect values
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "30%"]);
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "100%"]);
  const textOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
  const imagesY = useTransform(scrollYProgress, [0, 1], ["0%", "15%"]);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Project categories for animation
  const categories = [
    { name: "Residential", icon: "🏡", delay: 0.3 },
    { name: "Commercial", icon: "🏢", delay: 0.5 },
    { name: "Mixed-Use", icon: "🏙️", delay: 0.7 },
    { name: "Hospitality", icon: "🏨", delay: 0.9 },
  ];

  return (
    <div className="relative overflow-hidden" ref={sectionRef} style={{ minHeight: "85vh" }}>
      {/* Background gradient overlay */}
      <motion.div 
        className="absolute inset-0 bg-brand-gradient opacity-90 z-10"
        style={{ y: backgroundY }}
      ></motion.div>
      
      {/* Decorative elements */}
      <div className="absolute inset-0 z-5">
        {/* Abstract shapes and patterns */}
        <motion.div 
          className="absolute -top-20 -end-20 w-80 h-80 rounded-full bg-white/10 blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div 
          className="absolute -bottom-40 -start-20 w-96 h-96 rounded-full bg-white/10 blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>
      
      {/* Grid pattern overlay */}
      <GridPattern className="z-5" />
      
      {/* Main content */}
      <div className="relative z-20 container mx-auto px-4 py-16 md:py-24 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10 items-center">
          {/* Text content */}
          <motion.div 
            className="text-white space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            style={{ opacity: textOpacity, y: textY }}
          >
            <motion.span 
              className="inline-block px-4 py-1 rounded-full bg-white/10 backdrop-blur-sm text-sm uppercase tracking-wider"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Premium Development Portfolio
            </motion.span>
            
            <motion.h1 
              className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              Our <span className="relative">
                Projects
                <motion.span 
                  className="absolute -bottom-2 start-0 w-full h-1 bg-white"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 0.8, delay: 0.8 }}
                ></motion.span>
              </span>
            </motion.h1>
            
            <motion.p 
              className="text-xl md:text-2xl leading-relaxed opacity-90 max-w-xl"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              Explore our diverse portfolio of premium real estate developments across the UAE, setting new standards in modern living and investment value.
            </motion.p>
            
            <motion.div 
              className="flex flex-wrap gap-4 pt-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              {categories.map((category) => (
                <motion.div
                  key={category.name}
                  className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: category.delay 
                  }}
                >
                  <span className="text-xl">{category.icon}</span>
                  <span>{category.name}</span>
                </motion.div>
              ))}
            </motion.div>
            
            <motion.div 
              className="pt-8"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <motion.div 
                className="flex items-center space-x-3 text-white/80"
                animate={{ 
                  y: [0, 5, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <span>Scroll to explore</span>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3" />
                </svg>
              </motion.div>
            </motion.div>
          </motion.div>
          
          {/* Images gallery */}
          <motion.div 
            className="relative h-[400px] md:h-[500px] hidden md:block"
            style={{ y: imagesY }}
          >
            {/* Project images collage */}
            <motion.div 
              className="absolute top-0 end-0 w-64 h-64 md:w-80 md:h-80 rounded-ts-3xl rounded-be-3xl overflow-hidden shadow-2xl border border-white/20 z-20"
              initial={{ opacity: 0, scale: 0.9, rotate: 3 }}
              animate={{ opacity: 1, scale: 1, rotate: 3 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="relative w-full h-full">
                <Image 
                  src="/images/project-1.jpg" 
                  alt="Luxury Property" 
                  fill 
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                />
              </div>
            </motion.div>
            
            <motion.div 
              className="absolute bottom-0 start-0 w-64 h-64 md:w-80 md:h-80 rounded-te-3xl rounded-bs-3xl overflow-hidden shadow-2xl border border-white/20 z-10"
              initial={{ opacity: 0, scale: 0.9, rotate: -3 }}
              animate={{ opacity: 1, scale: 1, rotate: -3 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <div className="relative w-full h-full">
                <Image 
                  src="/images/project-3.jpg" 
                  alt="Luxury Property" 
                  fill 
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                />
              </div>
            </motion.div>
            
            <motion.div 
              className="absolute top-1/2 start-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 md:w-60 md:h-60 rounded-full overflow-hidden shadow-2xl border-4 border-white/30 z-30"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="relative w-full h-full">
                <Image 
                  src="/images/project-2.jpg" 
                  alt="Luxury Property" 
                  fill 
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" 
                />
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
      
      {/* Gradient transition to content */}
      <div className="absolute bottom-0 start-0 w-full h-32 bg-gradient-to-t from-[rgb(var(--color-background))] to-transparent z-20"></div>
    </div>
  );
};

export default ProjectsHero; 