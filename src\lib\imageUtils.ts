type ImageSize = 'sm' | 'md' | 'lg' | 'xl';

interface ResponsiveImageOptions {
  path: string;
  alt: string;
  sizes?: {
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
  };
  widths?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  defaultSize?: ImageSize;
}

/**
 * Get responsive image attributes for Next.js Image component
 * 
 * @param options Image options
 * @returns Object with src, alt, sizes for Next.js Image
 */
export const getResponsiveImageProps = (options: ResponsiveImageOptions) => {
  const {
    path,
    alt,
    sizes = {
      sm: '100vw',
      md: '100vw',
      lg: '100vw',
      xl: '100vw',
    },
    widths = {
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
    },
    defaultSize = 'lg',
  } = options;

  // Determine if path is a full URL or a local path
  const isExternalImage = path.startsWith('http://') || path.startsWith('https://');

  // For external images, we can't use the loader
  if (isExternalImage) {
    return {
      src: path,
      alt,
      sizes: `
        (max-width: 640px) ${sizes.sm},
        (max-width: 768px) ${sizes.md},
        (max-width: 1024px) ${sizes.lg},
        ${sizes.xl}
      `.trim(),
      width: widths[defaultSize],
      height: undefined, // Let Next.js calculate the height
    };
  }

  // For local images
  return {
    src: path,
    alt,
    sizes: `
      (max-width: 640px) ${sizes.sm},
      (max-width: 768px) ${sizes.md},
      (max-width: 1024px) ${sizes.lg},
      ${sizes.xl}
    `.trim(),
    width: widths[defaultSize],
    height: undefined, // Let Next.js calculate the height
  };
};

/**
 * Get conditional image path based on mobile or desktop
 * 
 * @param mobilePath Path to mobile image
 * @param desktopPath Path to desktop image
 * @returns Object with src attribute for various breakpoints
 */
export const getResponsiveImageSrc = (mobilePath: string, desktopPath: string) => {
  return {
    src: desktopPath, // Default image
    srcSet: `
      ${mobilePath} 640w,
      ${desktopPath} 1280w
    `.trim(),
  };
}; 