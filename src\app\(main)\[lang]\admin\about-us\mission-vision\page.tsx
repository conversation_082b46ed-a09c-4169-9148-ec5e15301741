"use client";

import React, { useState } from 'react';
import { FiEdit3, FiTrash2, FiPlus, FiSave, FiX, FiMove, FiSun, FiEye, FiHeart } from 'react-icons/fi';

interface CoreValue {
  id: string;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  icon: string;
  order: number;
}

interface VisionPoint {
  id: string;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  icon: string;
  order: number;
}

interface MissionVisionSection {
  mission: {
    title: {
      en: string;
      ar: string;
    };
    tagline: {
      en: string;
      ar: string;
    };
    description: {
      en: string;
      ar: string;
    };
    whatDrivesUs: {
      en: string;
      ar: string;
    };
    coreValuesTitle: {
      en: string;
      ar: string;
    };
    coreValues: CoreValue[];
  };
  vision: {
    title: {
      en: string;
      ar: string;
    };
    tagline: {
      en: string;
      ar: string;
    };
    description: {
      en: string;
      ar: string;
    };
    whereWereHeaded: {
      en: string;
      ar: string;
    };
    visionPoints: VisionPoint[];
  };
}

const iconOptions = [
  { name: 'Shield', icon: '🛡️', value: 'shield' },
  { name: 'Star', icon: '⭐', value: 'star' },
  { name: 'Lightbulb', icon: '💡', value: 'lightbulb' },
  { name: 'Heart', icon: '❤️', value: 'heart' },
  { name: 'Target', icon: '🎯', value: 'target' },
  { name: 'Trophy', icon: '🏆', value: 'trophy' },
  { name: 'Handshake', icon: '🤝', value: 'handshake' },
  { name: 'Eye', icon: '👁️', value: 'eye' },
  { name: 'Leaf', icon: '🌱', value: 'leaf' },
  { name: 'Smile', icon: '😊', value: 'smile' },
  { name: 'Growth', icon: '📈', value: 'growth' },
  { name: 'Globe', icon: '🌍', value: 'globe' },
];

export default function MissionVisionManagementPage() {
  // Initial data based on current MissionVision component
  const [missionVisionData, setMissionVisionData] = useState<MissionVisionSection>({
    mission: {
      title: {
        en: "Our Mission",
        ar: "مهمتنا"
      },
      tagline: {
        en: "Transforming real estate dreams into lasting legacies",
        ar: "تحويل الأحلام العقارية إلى إرث دائم"
      },
      description: {
        en: "At Mazaya Capital, we are committed to delivering exceptional real estate solutions that exceed expectations. Our mission is to create sustainable communities that enhance quality of life while generating superior returns for our investors through innovative development practices and unwavering commitment to excellence.",
        ar: "في مزايا كابيتال، نحن ملتزمون بتقديم حلول عقارية استثنائية تتجاوز التوقعات. مهمتنا هي إنشاء مجتمعات مستدامة تعزز جودة الحياة مع تحقيق عوائد متفوقة لمستثمرينا من خلال ممارسات التطوير المبتكرة والالتزام الثابت بالتميز."
      },
      whatDrivesUs: {
        en: "What Drives Us",
        ar: "ما يحركنا"
      },
      coreValuesTitle: {
        en: "Our Core Values",
        ar: "قيمنا الأساسية"
      },
      coreValues: [
        {
          id: "excellence",
          title: { en: "Excellence", ar: "التميز" },
          description: { en: "Pursuing the highest standards in everything we do", ar: "السعي لأعلى المعايير في كل ما نقوم به" },
          icon: "shield",
          order: 1
        },
        {
          id: "integrity",
          title: { en: "Integrity", ar: "النزاهة" },
          description: { en: "Building trust through transparency and honesty", ar: "بناء الثقة من خلال الشفافية والصدق" },
          icon: "heart",
          order: 2
        },
        {
          id: "innovation",
          title: { en: "Innovation", ar: "الابتكار" },
          description: { en: "Embracing new ideas and cutting-edge solutions", ar: "احتضان الأفكار الجديدة والحلول المتطورة" },
          icon: "lightbulb",
          order: 3
        }
      ]
    },
    vision: {
      title: {
        en: "Our Vision",
        ar: "رؤيتنا"
      },
      tagline: {
        en: "To be the leading real estate developer in the Middle East",
        ar: "أن نكون المطور العقاري الرائد في الشرق الأوسط"
      },
      description: {
        en: "We envision a future where Mazaya Capital stands as the premier real estate developer in the region, renowned for creating innovative, sustainable communities that set new benchmarks for luxury, functionality, and environmental responsibility. Our developments will be synonymous with quality, trust, and exceptional value.",
        ar: "نتصور مستقبلاً حيث تقف مزايا كابيتال كالمطور العقاري الرائد في المنطقة، معروفة بإنشاء مجتمعات مبتكرة ومستدامة تضع معايير جديدة للفخامة والوظائف والمسؤولية البيئية. ستكون تطويراتنا مرادفة للجودة والثقة والقيمة الاستثنائية."
      },
      whereWereHeaded: {
        en: "Where We're Headed",
        ar: "إلى أين نتجه"
      },
      visionPoints: [
        {
          id: "trust",
          title: { en: "Trust", ar: "الثقة" },
          description: { en: "Building lasting relationships through reliability and transparency", ar: "بناء علاقات دائمة من خلال الموثوقية والشفافية" },
          icon: "handshake",
          order: 1
        },
        {
          id: "innovation",
          title: { en: "Innovation", ar: "الابتكار" },
          description: { en: "Leading the industry with groundbreaking solutions", ar: "قيادة الصناعة بحلول رائدة" },
          icon: "lightbulb",
          order: 2
        },
        {
          id: "sustainability",
          title: { en: "Sustainability", ar: "الاستدامة" },
          description: { en: "Creating environmentally responsible developments", ar: "إنشاء تطويرات مسؤولة بيئياً" },
          icon: "leaf",
          order: 3
        },
        {
          id: "experience",
          title: { en: "Experience", ar: "التجربة" },
          description: { en: "Delivering exceptional experiences at every touchpoint", ar: "تقديم تجارب استثنائية في كل نقطة تواصل" },
          icon: "smile",
          order: 4
        }
      ]
    }
  });

  const [editingMissionSection, setEditingMissionSection] = useState(false);
  const [editingVisionSection, setEditingVisionSection] = useState(false);
  const [editingCoreValue, setEditingCoreValue] = useState<string | null>(null);
  const [editingVisionPoint, setEditingVisionPoint] = useState<string | null>(null);
  const [showAddCoreValueForm, setShowAddCoreValueForm] = useState(false);
  const [showAddVisionPointForm, setShowAddVisionPointForm] = useState(false);
  const [draggedCoreValue, setDraggedCoreValue] = useState<string | null>(null);
  const [draggedVisionPoint, setDraggedVisionPoint] = useState<string | null>(null);

  const handleMissionSectionSave = (newData: Partial<MissionVisionSection['mission']>) => {
    setMissionVisionData(prev => ({
      ...prev,
      mission: { ...prev.mission, ...newData }
    }));
    setEditingMissionSection(false);
  };

  const handleVisionSectionSave = (newData: Partial<MissionVisionSection['vision']>) => {
    setMissionVisionData(prev => ({
      ...prev,
      vision: { ...prev.vision, ...newData }
    }));
    setEditingVisionSection(false);
  };

  const handleCoreValueSave = (valueId: string, newValue: CoreValue) => {
    setMissionVisionData(prev => ({
      ...prev,
      mission: {
        ...prev.mission,
        coreValues: prev.mission.coreValues.map(v => v.id === valueId ? newValue : v)
      }
    }));
    setEditingCoreValue(null);
  };

  const handleCoreValueDelete = (valueId: string) => {
    if (confirm('Are you sure you want to delete this core value?')) {
      setMissionVisionData(prev => ({
        ...prev,
        mission: {
          ...prev.mission,
          coreValues: prev.mission.coreValues.filter(v => v.id !== valueId)
        }
      }));
    }
  };

  const handleCoreValueAdd = (newValue: CoreValue) => {
    const maxOrder = Math.max(...missionVisionData.mission.coreValues.map(v => v.order), 0);
    const valueWithOrder = { ...newValue, order: maxOrder + 1 };
    setMissionVisionData(prev => ({
      ...prev,
      mission: {
        ...prev.mission,
        coreValues: [...prev.mission.coreValues, valueWithOrder]
      }
    }));
    setShowAddCoreValueForm(false);
  };

  const handleVisionPointSave = (pointId: string, newPoint: VisionPoint) => {
    setMissionVisionData(prev => ({
      ...prev,
      vision: {
        ...prev.vision,
        visionPoints: prev.vision.visionPoints.map(p => p.id === pointId ? newPoint : p)
      }
    }));
    setEditingVisionPoint(null);
  };

  const handleVisionPointDelete = (pointId: string) => {
    if (confirm('Are you sure you want to delete this vision point?')) {
      setMissionVisionData(prev => ({
        ...prev,
        vision: {
          ...prev.vision,
          visionPoints: prev.vision.visionPoints.filter(p => p.id !== pointId)
        }
      }));
    }
  };

  const handleVisionPointAdd = (newPoint: VisionPoint) => {
    const maxOrder = Math.max(...missionVisionData.vision.visionPoints.map(p => p.order), 0);
    const pointWithOrder = { ...newPoint, order: maxOrder + 1 };
    setMissionVisionData(prev => ({
      ...prev,
      vision: {
        ...prev.vision,
        visionPoints: [...prev.vision.visionPoints, pointWithOrder]
      }
    }));
    setShowAddVisionPointForm(false);
  };

  const handleCoreValueDragStart = (valueId: string) => {
    setDraggedCoreValue(valueId);
  };

  const handleCoreValueDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedCoreValue || draggedCoreValue === targetId) return;
    
    const draggedItem = missionVisionData.mission.coreValues.find(v => v.id === draggedCoreValue);
    const targetItem = missionVisionData.mission.coreValues.find(v => v.id === targetId);
    
    if (!draggedItem || !targetItem) return;
    
    setMissionVisionData(prev => ({
      ...prev,
      mission: {
        ...prev.mission,
        coreValues: prev.mission.coreValues.map(value => {
          if (value.id === draggedCoreValue) {
            return { ...value, order: targetItem.order };
          } else if (value.id === targetId) {
            return { ...value, order: draggedItem.order };
          }
          return value;
        })
      }
    }));
    
    setDraggedCoreValue(null);
  };

  const handleVisionPointDragStart = (pointId: string) => {
    setDraggedVisionPoint(pointId);
  };

  const handleVisionPointDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedVisionPoint || draggedVisionPoint === targetId) return;
    
    const draggedItem = missionVisionData.vision.visionPoints.find(p => p.id === draggedVisionPoint);
    const targetItem = missionVisionData.vision.visionPoints.find(p => p.id === targetId);
    
    if (!draggedItem || !targetItem) return;
    
    setMissionVisionData(prev => ({
      ...prev,
      vision: {
        ...prev.vision,
        visionPoints: prev.vision.visionPoints.map(point => {
          if (point.id === draggedVisionPoint) {
            return { ...point, order: targetItem.order };
          } else if (point.id === targetId) {
            return { ...point, order: draggedItem.order };
          }
          return point;
        })
      }
    }));
    
    setDraggedVisionPoint(null);
  };

  const handleSaveAll = () => {
    // Here you would typically send the data to your backend
    console.log('Saving mission & vision data:', missionVisionData);
    alert('Mission & Vision data saved successfully!');
  };

  return (
    <div>
      <div className="pb-5 border-b border-gray-700 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold leading-tight text-white">About Us - Mission & Vision</h1>
          <p className="text-gray-400 mt-1">Manage the mission, vision, core values, and vision points</p>
        </div>
        <button
          onClick={handleSaveAll}
          className="inline-flex items-center px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          <FiSave className="mr-2 h-4 w-4" />
          Save All Changes
        </button>
      </div>

      <div className="mt-6 space-y-8">
        {/* Mission Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiSun className="mr-2 h-5 w-5 text-[#00C2FF]" />
              Mission Section
            </h2>
            <button
              onClick={() => setEditingMissionSection(!editingMissionSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingMissionSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingMissionSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingMissionSection ? (
            <MissionSectionEditForm
              data={missionVisionData.mission}
              onSave={handleMissionSectionSave}
              onCancel={() => setEditingMissionSection(false)}
            />
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title</label>
                <p className="text-white">{missionVisionData.mission.title.en}</p>
                <p className="text-gray-400 text-sm">{missionVisionData.mission.title.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Tagline</label>
                <p className="text-gray-300">{missionVisionData.mission.tagline.en}</p>
                <p className="text-gray-400 text-sm">{missionVisionData.mission.tagline.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description</label>
                <p className="text-gray-300">{missionVisionData.mission.description.en}</p>
                <p className="text-gray-400 text-sm">{missionVisionData.mission.description.ar}</p>
              </div>
            </div>
          )}

          {/* Core Values */}
          <div className="mt-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">
                Core Values ({missionVisionData.mission.coreValues.length})
              </h3>
              <button
                onClick={() => setShowAddCoreValueForm(true)}
                className="inline-flex items-center px-3 py-1 text-sm bg-[#00C2FF] text-white rounded hover:bg-[#00C2FF]/90"
              >
                <FiPlus className="mr-1 h-4 w-4" />
                Add Core Value
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {missionVisionData.mission.coreValues
                .sort((a, b) => a.order - b.order)
                .map((value) => (
                  <div
                    key={value.id}
                    className="bg-gray-700 rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors"
                    draggable
                    onDragStart={() => handleCoreValueDragStart(value.id)}
                    onDrop={(e) => handleCoreValueDrop(e, value.id)}
                    onDragOver={(e) => e.preventDefault()}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">
                          {iconOptions.find(opt => opt.value === value.icon)?.icon || '🔧'}
                        </span>
                        <div>
                          <h4 className="font-medium text-white">{value.title.en}</h4>
                          <p className="text-gray-400 text-sm">{value.title.ar}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingCoreValue(value.id)}
                          className="p-1 text-gray-400 hover:text-[#00C2FF] transition-colors"
                        >
                          <FiEdit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleCoreValueDelete(value.id)}
                          className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                        <div className="p-1 text-gray-400 cursor-move">
                          <FiMove className="h-4 w-4" />
                        </div>
                      </div>
                    </div>

                    {editingCoreValue === value.id ? (
                      <CoreValueEditForm
                        value={value}
                        onSave={(newValue) => handleCoreValueSave(value.id, newValue)}
                        onCancel={() => setEditingCoreValue(null)}
                      />
                    ) : (
                      <div>
                        <p className="text-gray-300 text-sm">{value.description.en}</p>
                        <p className="text-gray-400 text-xs mt-1">{value.description.ar}</p>
                      </div>
                    )}
                  </div>
                ))}
            </div>

            {showAddCoreValueForm && (
              <div className="mt-4 p-4 bg-gray-700 rounded-lg border border-gray-600">
                <CoreValueAddForm
                  onSave={handleCoreValueAdd}
                  onCancel={() => setShowAddCoreValueForm(false)}
                />
              </div>
            )}
          </div>
        </div>

        {/* Vision Section */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FiEye className="mr-2 h-5 w-5 text-[#9747FF]" />
              Vision Section
            </h2>
            <button
              onClick={() => setEditingVisionSection(!editingVisionSection)}
              className="inline-flex items-center px-3 py-1 text-sm bg-gray-700 text-gray-300 rounded hover:bg-gray-600"
            >
              {editingVisionSection ? <FiX className="mr-1 h-4 w-4" /> : <FiEdit3 className="mr-1 h-4 w-4" />}
              {editingVisionSection ? 'Cancel' : 'Edit'}
            </button>
          </div>

          {editingVisionSection ? (
            <VisionSectionEditForm
              data={missionVisionData.vision}
              onSave={handleVisionSectionSave}
              onCancel={() => setEditingVisionSection(false)}
            />
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Title</label>
                <p className="text-white">{missionVisionData.vision.title.en}</p>
                <p className="text-gray-400 text-sm">{missionVisionData.vision.title.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Tagline</label>
                <p className="text-gray-300">{missionVisionData.vision.tagline.en}</p>
                <p className="text-gray-400 text-sm">{missionVisionData.vision.tagline.ar}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">Description</label>
                <p className="text-gray-300">{missionVisionData.vision.description.en}</p>
                <p className="text-gray-400 text-sm">{missionVisionData.vision.description.ar}</p>
              </div>
            </div>
          )}

          {/* Vision Points */}
          <div className="mt-8">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">
                Vision Points ({missionVisionData.vision.visionPoints.length})
              </h3>
              <button
                onClick={() => setShowAddVisionPointForm(true)}
                className="inline-flex items-center px-3 py-1 text-sm bg-[#9747FF] text-white rounded hover:bg-[#9747FF]/90"
              >
                <FiPlus className="mr-1 h-4 w-4" />
                Add Vision Point
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {missionVisionData.vision.visionPoints
                .sort((a, b) => a.order - b.order)
                .map((point) => (
                  <div
                    key={point.id}
                    className="bg-gray-700 rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors"
                    draggable
                    onDragStart={() => handleVisionPointDragStart(point.id)}
                    onDrop={(e) => handleVisionPointDrop(e, point.id)}
                    onDragOver={(e) => e.preventDefault()}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center">
                        <span className="text-2xl mr-3">
                          {iconOptions.find(opt => opt.value === point.icon)?.icon || '🔧'}
                        </span>
                        <div>
                          <h4 className="font-medium text-white">{point.title.en}</h4>
                          <p className="text-gray-400 text-sm">{point.title.ar}</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setEditingVisionPoint(point.id)}
                          className="p-1 text-gray-400 hover:text-[#9747FF] transition-colors"
                        >
                          <FiEdit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleVisionPointDelete(point.id)}
                          className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </button>
                        <div className="p-1 text-gray-400 cursor-move">
                          <FiMove className="h-4 w-4" />
                        </div>
                      </div>
                    </div>

                    {editingVisionPoint === point.id ? (
                      <VisionPointEditForm
                        point={point}
                        onSave={(newPoint) => handleVisionPointSave(point.id, newPoint)}
                        onCancel={() => setEditingVisionPoint(null)}
                      />
                    ) : (
                      <div>
                        <p className="text-gray-300 text-sm">{point.description.en}</p>
                        <p className="text-gray-400 text-xs mt-1">{point.description.ar}</p>
                      </div>
                    )}
                  </div>
                ))}
            </div>

            {showAddVisionPointForm && (
              <div className="mt-4 p-4 bg-gray-700 rounded-lg border border-gray-600">
                <VisionPointAddForm
                  onSave={handleVisionPointAdd}
                  onCancel={() => setShowAddVisionPointForm(false)}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Mission Section Edit Form Component
function MissionSectionEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: MissionVisionSection['mission'];
  onSave: (data: Partial<MissionVisionSection['mission']>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Tagline (English)</label>
          <input
            type="text"
            value={formData.tagline.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              tagline: { ...prev.tagline, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Tagline (Arabic)</label>
          <input
            type="text"
            value={formData.tagline.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              tagline: { ...prev.tagline, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">What Drives Us (English)</label>
          <input
            type="text"
            value={formData.whatDrivesUs.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              whatDrivesUs: { ...prev.whatDrivesUs, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">What Drives Us (Arabic)</label>
          <input
            type="text"
            value={formData.whatDrivesUs.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              whatDrivesUs: { ...prev.whatDrivesUs, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Core Values Title (English)</label>
          <input
            type="text"
            value={formData.coreValuesTitle.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              coreValuesTitle: { ...prev.coreValuesTitle, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Core Values Title (Arabic)</label>
          <input
            type="text"
            value={formData.coreValuesTitle.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              coreValuesTitle: { ...prev.coreValuesTitle, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Vision Section Edit Form Component
function VisionSectionEditForm({ 
  data, 
  onSave, 
  onCancel 
}: { 
  data: MissionVisionSection['vision'];
  onSave: (data: Partial<MissionVisionSection['vision']>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(data);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (English)</label>
          <input
            type="text"
            value={formData.title.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
          <input
            type="text"
            value={formData.title.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              title: { ...prev.title, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Tagline (English)</label>
          <input
            type="text"
            value={formData.tagline.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              tagline: { ...prev.tagline, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Tagline (Arabic)</label>
          <input
            type="text"
            value={formData.tagline.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              tagline: { ...prev.tagline, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
          <textarea
            value={formData.description.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, en: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
          <textarea
            value={formData.description.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              description: { ...prev.description, ar: e.target.value }
            }))}
            rows={4}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Where We're Headed (English)</label>
          <input
            type="text"
            value={formData.whereWereHeaded.en}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              whereWereHeaded: { ...prev.whereWereHeaded, en: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Where We're Headed (Arabic)</label>
          <input
            type="text"
            value={formData.whereWereHeaded.ar}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              whereWereHeaded: { ...prev.whereWereHeaded, ar: e.target.value }
            }))}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-[#9747FF] text-white rounded-lg hover:bg-[#9747FF]/90 transition-colors"
        >
          Save Changes
        </button>
      </div>
    </form>
  );
}

// Core Value Edit Form Component
function CoreValueEditForm({ 
  value, 
  onSave, 
  onCancel 
}: { 
  value: CoreValue;
  onSave: (value: CoreValue) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(value);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Title (English)"
          value={formData.title.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: { ...prev.title, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <input
          type="text"
          placeholder="Title (Arabic)"
          value={formData.title.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: { ...prev.title, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <div className="grid grid-cols-2 gap-2">
        <textarea
          placeholder="Description (English)"
          value={formData.description.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, en: e.target.value }
          }))}
          rows={2}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
        <textarea
          placeholder="Description (Arabic)"
          value={formData.description.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, ar: e.target.value }
          }))}
          rows={2}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
        />
      </div>

      <select
        value={formData.icon}
        onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#00C2FF]"
      >
        {iconOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.icon} {option.name}
          </option>
        ))}
      </select>

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-3 py-1 bg-[#00C2FF] text-white rounded text-sm hover:bg-[#00C2FF]/90 transition-colors"
        >
          Save
        </button>
      </div>
    </form>
  );
}

// Core Value Add Form Component
function CoreValueAddForm({ 
  onSave, 
  onCancel 
}: { 
  onSave: (value: CoreValue) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<CoreValue>({
    id: '',
    title: { en: '', ar: '' },
    description: { en: '', ar: '' },
    icon: iconOptions[0].value,
    order: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.en) {
      alert('Please enter an English title');
      return;
    }

    const newValue = {
      ...formData,
      id: `core-value-${Date.now()}`
    };

    onSave(newValue);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Core Value</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
            <input
              type="text"
              value={formData.title.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              placeholder="Enter English title"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
            <input
              type="text"
              value={formData.title.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              placeholder="العنوان بالعربية"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
            <textarea
              value={formData.description.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: { ...prev.description, en: e.target.value }
              }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              placeholder="Enter English description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
            <textarea
              value={formData.description.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: { ...prev.description, ar: e.target.value }
              }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
              placeholder="الوصف بالعربية"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <select
            value={formData.icon}
            onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#00C2FF]"
          >
            {iconOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.icon} {option.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#00C2FF] text-white rounded-lg hover:bg-[#00C2FF]/90 transition-colors"
          >
            <FiSave className="mr-2 h-4 w-4 inline" />
            Add Core Value
          </button>
        </div>
      </form>
    </div>
  );
}

// Vision Point Edit Form Component
function VisionPointEditForm({ 
  point, 
  onSave, 
  onCancel 
}: { 
  point: VisionPoint;
  onSave: (point: VisionPoint) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(point);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="grid grid-cols-2 gap-2">
        <input
          type="text"
          placeholder="Title (English)"
          value={formData.title.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: { ...prev.title, en: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#9747FF]"
        />
        <input
          type="text"
          placeholder="Title (Arabic)"
          value={formData.title.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            title: { ...prev.title, ar: e.target.value }
          }))}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#9747FF]"
        />
      </div>

      <div className="grid grid-cols-2 gap-2">
        <textarea
          placeholder="Description (English)"
          value={formData.description.en}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, en: e.target.value }
          }))}
          rows={2}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#9747FF]"
        />
        <textarea
          placeholder="Description (Arabic)"
          value={formData.description.ar}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            description: { ...prev.description, ar: e.target.value }
          }))}
          rows={2}
          className="px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#9747FF]"
        />
      </div>

      <select
        value={formData.icon}
        onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
        className="w-full px-2 py-1 bg-gray-600 border border-gray-500 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-[#9747FF]"
      >
        {iconOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.icon} {option.name}
          </option>
        ))}
      </select>

      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-500 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-3 py-1 bg-[#9747FF] text-white rounded text-sm hover:bg-[#9747FF]/90 transition-colors"
        >
          Save
        </button>
      </div>
    </form>
  );
}

// Vision Point Add Form Component
function VisionPointAddForm({ 
  onSave, 
  onCancel 
}: { 
  onSave: (point: VisionPoint) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState<VisionPoint>({
    id: '',
    title: { en: '', ar: '' },
    description: { en: '', ar: '' },
    icon: iconOptions[0].value,
    order: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.en) {
      alert('Please enter an English title');
      return;
    }

    const newPoint = {
      ...formData,
      id: `vision-point-${Date.now()}`
    };

    onSave(newPoint);
  };

  return (
    <div>
      <h3 className="text-lg font-medium text-white mb-4">Add New Vision Point</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (English) *</label>
            <input
              type="text"
              value={formData.title.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, en: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
              placeholder="Enter English title"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Title (Arabic)</label>
            <input
              type="text"
              value={formData.title.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: { ...prev.title, ar: e.target.value }
              }))}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
              placeholder="العنوان بالعربية"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Description (English)</label>
            <textarea
              value={formData.description.en}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: { ...prev.description, en: e.target.value }
              }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
              placeholder="Enter English description"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Description (Arabic)</label>
            <textarea
              value={formData.description.ar}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: { ...prev.description, ar: e.target.value }
              }))}
              rows={3}
              className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
              placeholder="الوصف بالعربية"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-2">Icon</label>
          <select
            value={formData.icon}
            onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white focus:outline-none focus:ring-2 focus:ring-[#9747FF]"
          >
            {iconOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.icon} {option.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-[#9747FF] text-white rounded-lg hover:bg-[#9747FF]/90 transition-colors"
          >
            Add Vision Point
          </button>
        </div>
      </form>
    </div>
  );
} 